<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phaser 3 Match-3 Game</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #333;
        }
        canvas {
            border: 2px solid #555;
            display: block; /* Remove extra space below canvas */
        }
    </style>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
</head>
<body>
    <div id="game-container"></div>

    <script>
        const TILE_SIZE = 60; // 方块尺寸
        const BOARD_WIDTH = 8;
        const BOARD_HEIGHT = 10;
        const GAME_WIDTH = TILE_SIZE * BOARD_WIDTH;
        const GAME_HEIGHT = TILE_SIZE * BOARD_HEIGHT;

        // 定义方块颜色
        const COLORS = [
            0xff0000, // Red
            0x00ff00, // Green
            0x0000ff, // Blue
            0xffff00, // Yellow
            0xff00ff, // Magenta
            0x00ffff  // Cyan
        ];

        class Match3Game extends Phaser.Scene {
            constructor() {
                super({ key: 'Match3Game' });
                this.board = [];
                this.selectedTile = null;
                this.isProcessing = false;
                this.score = 0;
                this.scoreText = null;
            }

            preload() {
                // 不加载外部图片，所有纹理都将通过graphics生成
            }

            create() {
                this.generateTileTextures();
                this.initBoard();
                this.drawBoard();

                this.input.on('gameobjectdown', this.onTileClicked, this);

                this.scoreText = this.add.text(10, 10, 'Score: 0', { fontSize: '24px', fill: '#fff' }).setOrigin(0);
            }

            generateTileTextures() {
                COLORS.forEach((color, index) => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(color, 1);
                    graphics.fillRect(0, 0, TILE_SIZE - 5, TILE_SIZE - 5); // 稍微小一点，留有间隔
                    graphics.lineStyle(2, 0x333333, 1); // 边框
                    graphics.strokeRect(0, 0, TILE_SIZE - 5, TILE_SIZE - 5);
                    graphics.generateTexture(`tile${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy(); // 销毁graphics对象，只保留纹理
                });
            }

            initBoard() {
                this.board = [];
                for (let y = 0; y < BOARD_HEIGHT; y++) {
                    this.board[y] = [];
                    for (let x = 0; x < BOARD_WIDTH; x++) {
                        let type;
                        do {
                            type = Phaser.Math.Between(0, COLORS.length - 1);
                        } while (this.isMatchAt(x, y, type)); // 确保初始没有三消
                        this.board[y][x] = {
                            type: type,
                            sprite: null // 稍后赋值Phaser Sprite
                        };
                    }
                }
            }

            drawBoard() {
                for (let y = 0; y < BOARD_HEIGHT; y++) {
                    for (let x = 0; x < BOARD_WIDTH; x++) {
                        const tile = this.board[y][x];
                        const sprite = this.add.sprite(
                            x * TILE_SIZE + TILE_SIZE / 2,
                            y * TILE_SIZE + TILE_SIZE / 2,
                            `tile${tile.type}`
                        );
                        sprite.setInteractive();
                        sprite.setData({ x: x, y: y });
                        this.board[y][x].sprite = sprite;
                    }
                }
            }

            isMatchAt(x, y, type) {
                // 检查水平方向
                if (x > 1 && this.board[y][x - 1]?.type === type && this.board[y][x - 2]?.type === type) {
                    return true;
                }
                if (x < BOARD_WIDTH - 2 && this.board[y][x + 1]?.type === type && this.board[y][x + 2]?.type === type) {
                    return true;
                }
                if (x > 0 && x < BOARD_WIDTH - 1 && this.board[y][x - 1]?.type === type && this.board[y][x + 1]?.type === type) {
                    return true;
                }

                // 检查垂直方向
                if (y > 1 && this.board[y - 1][x]?.type === type && this.board[y - 2][x]?.type === type) {
                    return true;
                }
                if (y < BOARD_HEIGHT - 2 && this.board[y + 1][x]?.type === type && this.board[y + 2][x]?.type === type) {
                    return true;
                }
                if (y > 0 && y < BOARD_HEIGHT - 1 && this.board[y - 1][x]?.type === type && this.board[y + 1][x]?.type === type) {
                    return true;
                }
                return false;
            }

            onTileClicked(pointer, tileSprite) {
                if (this.isProcessing) return;

                const clickedX = tileSprite.getData('x');
                const clickedY = tileSprite.getData('y');

                if (!this.selectedTile) {
                    this.selectedTile = { x: clickedX, y: clickedY, sprite: tileSprite };
                    tileSprite.setTint(0xcccccc); // 选中效果
                } else {
                    const selectedX = this.selectedTile.x;
                    const selectedY = this.selectedTile.y;

                    // 判断是否是相邻的方块
                    if (Math.abs(clickedX - selectedX) + Math.abs(clickedY - selectedY) === 1) {
                        this.isProcessing = true;
                        this.swapTiles(selectedX, selectedY, clickedX, clickedY);
                    } else {
                        // 取消选中或重新选择
                        this.selectedTile.sprite.clearTint();
                        this.selectedTile = { x: clickedX, y: clickedY, sprite: tileSprite };
                        tileSprite.setTint(0xcccccc);
                    }
                }
            }

            swapTiles(x1, y1, x2, y2) {
                const tile1 = this.board[y1][x1];
                const tile2 = this.board[y2][x2];

                // 交换 board 中的数据
                this.board[y1][x1] = tile2;
                this.board[y2][x2] = tile1;

                // 更新 sprite 的数据
                tile1.sprite.setData({ x: x2, y: y2 });
                tile2.sprite.setData({ x: x1, y: y1 });

                // 动画交换 sprite 的位置
                this.tweens.add({
                    targets: [tile1.sprite, tile2.sprite],
                    x: (target, key, value, targetIndex, totalTargets) => {
                        return target.getData('x') * TILE_SIZE + TILE_SIZE / 2;
                    },
                    y: (target, key, value, targetIndex, totalTargets) => {
                        return target.getData('y') * TILE_SIZE + TILE_SIZE / 2;
                    },
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        this.selectedTile.sprite.clearTint();
                        this.selectedTile = null;
                        this.checkMatchesAfterSwap(x1, y1, x2, y2);
                    }
                });
            }

            checkMatchesAfterSwap(x1, y1, x2, y2) {
                const matches = this.findAllMatches();
                if (matches.length > 0) {
                    this.destroyMatches(matches);
                } else {
                    // 如果没有匹配，交换回来
                    this.swapTilesBack(x1, y1, x2, y2);
                }
            }

            swapTilesBack(x1, y1, x2, y2) {
                const tile1 = this.board[y1][x1];
                const tile2 = this.board[y2][x2];

                // 交换 board 中的数据
                this.board[y1][x1] = tile2;
                this.board[y2][x2] = tile1;

                // 更新 sprite 的数据
                tile1.sprite.setData({ x: x2, y: y2 });
                tile2.sprite.setData({ x: x1, y: y1 });

                // 动画交换 sprite 的位置
                this.tweens.add({
                    targets: [tile1.sprite, tile2.sprite],
                    x: (target, key, value, targetIndex, totalTargets) => {
                        return target.getData('x') * TILE_SIZE + TILE_SIZE / 2;
                    },
                    y: (target, key, value, targetIndex, totalTargets) => {
                        return target.getData('y') * TILE_SIZE + TILE_SIZE / 2;
                    },
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        this.isProcessing = false;
                    }
                });
            }


            findAllMatches() {
                const matches = [];

                // 检查水平匹配
                for (let y = 0; y < BOARD_HEIGHT; y++) {
                    for (let x = 0; x < BOARD_WIDTH - 2; x++) {
                        const type = this.board[y][x].type;
                        if (type !== null &&
                            this.board[y][x + 1]?.type === type &&
                            this.board[y][x + 2]?.type === type) {
                            const currentMatch = [{ x: x, y: y }, { x: x + 1, y: y }, { x: x + 2, y: y }];
                            // 检查更长的匹配
                            let i = x + 3;
                            while (i < BOARD_WIDTH && this.board[y][i]?.type === type) {
                                currentMatch.push({ x: i, y: y });
                                i++;
                            }
                            matches.push(currentMatch);
                            x = i - 1; // 跳过已匹配的方块
                        }
                    }
                }

                // 检查垂直匹配
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    for (let y = 0; y < BOARD_HEIGHT - 2; y++) {
                        const type = this.board[y][x].type;
                        if (type !== null &&
                            this.board[y + 1][x]?.type === type &&
                            this.board[y + 2][x]?.type === type) {
                            const currentMatch = [{ x: x, y: y }, { x: x, y: y + 1 }, { x: x, y: y + 2 }];
                            // 检查更长的匹配
                            let i = y + 3;
                            while (i < BOARD_HEIGHT && this.board[i][x]?.type === type) {
                                currentMatch.push({ x: x, y: i });
                                i++;
                            }
                            matches.push(currentMatch);
                            y = i - 1; // 跳过已匹配的方块
                        }
                    }
                }
                return matches;
            }

            destroyMatches(matches) {
                const tilesToDestroy = new Set();
                let pointsEarned = 0;

                matches.forEach(match => {
                    match.forEach(tile => {
                        tilesToDestroy.add(`${tile.x},${tile.y}`);
                        pointsEarned += 10; // 每个消除的方块10分
                    });
                });

                tilesToDestroy.forEach(coords => {
                    const [x, y] = coords.split(',').map(Number);
                    if (this.board[y][x].sprite) {
                        this.board[y][x].sprite.destroy();
                    }
                    this.board[y][x] = { type: null, sprite: null }; // 标记为已消除
                });

                this.updateScore(pointsEarned);

                // 稍作延迟，以便玩家看到消除效果，然后下落和填充
                this.time.delayedCall(300, () => {
                    this.dropTiles();
                }, [], this);
            }

            updateScore(points) {
                this.score += points;
                this.scoreText.setText(`Score: ${this.score}`);
            }

            dropTiles() {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    let emptySpots = 0;
                    for (let y = BOARD_HEIGHT - 1; y >= 0; y--) {
                        if (this.board[y][x].type === null) {
                            emptySpots++;
                        } else if (emptySpots > 0) {
                            const tile = this.board[y][x];
                            this.board[y + emptySpots][x] = tile;
                            this.board[y][x] = { type: null, sprite: null };

                            // 动画下落
                            tile.sprite.setData({ y: y + emptySpots }); // 更新sprite的数据
                            this.tweens.add({
                                targets: tile.sprite,
                                y: (y + emptySpots) * TILE_SIZE + TILE_SIZE / 2,
                                duration: 200,
                                ease: 'Power1'
                            });
                        }
                    }
                }
                this.time.delayedCall(250, () => {
                    this.fillBoard();
                }, [], this);
            }

            fillBoard() {
                for (let x = 0; x < BOARD_WIDTH; x++) {
                    for (let y = 0; y < BOARD_HEIGHT; y++) {
                        if (this.board[y][x].type === null) {
                            const type = Phaser.Math.Between(0, COLORS.length - 1);
                            const sprite = this.add.sprite(
                                x * TILE_SIZE + TILE_SIZE / 2,
                                -TILE_SIZE / 2, // 从顶部外面落下
                                `tile${type}`
                            );
                            sprite.setInteractive();
                            sprite.setData({ x: x, y: y });
                            this.board[y][x] = { type: type, sprite: sprite };

                            // 动画下落
                            this.tweens.add({
                                targets: sprite,
                                y: y * TILE_SIZE + TILE_SIZE / 2,
                                duration: 300,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }
                }
                this.time.delayedCall(350, () => {
                    // 递归检查新的匹配
                    const newMatches = this.findAllMatches();
                    if (newMatches.length > 0) {
                        this.destroyMatches(newMatches);
                    } else {
                        this.isProcessing = false; // 所有操作完成
                    }
                }, [], this);
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            scene: Match3Game,
            physics: {
                default: 'arcade',
                arcade: {
                    debug: false // 调试模式，可以设置为true查看物理边界
                }
            }
        };

        const game = new Phaser.Game(config);
    </script>
</body>
</html>