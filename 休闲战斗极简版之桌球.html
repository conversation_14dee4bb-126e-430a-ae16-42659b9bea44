<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竖版战斗台球</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let blueBalls = [], redBalls = [], blackBalls = [];
    let cueBall;
    let playerHealth = 100, enemyHealth = 100;
    let maxPlayerHealth = 100, maxEnemyHealth = 100;
    let shotCount = 0;
    let isAiming = false;
    let aimLine;
    let powerBar;
    let gameStarted = false;
    let currentLevel = 1, currentWave = 1;

    // 碰撞控制变量 - 防止连锁反应bug
    let ballsToDestroy = []; // 待销毁的球列表
    let collisionCooldowns = new Map(); // 球的碰撞冷却时间
    let isTransforming = false; // 是否正在变身中

    // 现代混战管理系统
    class BattleManager {
        constructor() {
            this.units = new Map(); // 所有战斗单位的统一管理
            this.pendingActions = []; // 待处理的行动队列
            this.lastUpdateTime = 0;
            this.updateInterval = 50; // 每50ms更新一次，更流畅
            this.isProcessing = false; // 防止重入
            this.gameScene = null; // 保存游戏场景引用，用于动画
        }

        // 注册战斗单位
        registerUnit(unit, team) {
            if (!unit || !unit.x) return;
            const id = this.generateUnitId();
            unit.battleId = id;
            unit.team = team;
            unit.isAlive = true;
            unit.lastActionTime = 0;
            this.units.set(id, unit);
            return id;
        }

        // 移除战斗单位
        removeUnit(unitId) {
            if (this.units.has(unitId)) {
                const unit = this.units.get(unitId);
                unit.isAlive = false;
                this.units.delete(unitId);
            }
        }

        // 获取存活的敌方单位
        getEnemies(team) {
            const enemies = [];
            this.units.forEach(unit => {
                if (unit.team !== team && unit.isAlive && unit.health > 0) {
                    enemies.push(unit);
                }
            });
            return enemies;
        }

        // 获取存活的友方单位
        getAllies(team) {
            const allies = [];
            this.units.forEach(unit => {
                if (unit.team === team && unit.isAlive && unit.health > 0) {
                    allies.push(unit);
                }
            });
            return allies;
        }

        // 生成唯一ID
        generateUnitId() {
            return 'unit_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 安全的战斗更新
        update(currentTime) {
            if (this.isProcessing || currentTime - this.lastUpdateTime < this.updateInterval) {
                return;
            }

            this.isProcessing = true;
            this.lastUpdateTime = currentTime;

            try {
                // 清理死亡单位
                this.cleanupDeadUnits();

                // 更新血条位置
                this.updateHealthBars();

                // 处理战斗逻辑
                this.processBattleActions(currentTime);
            } catch (error) {
                console.warn('Error in battle update:', error);
            } finally {
                this.isProcessing = false;
            }
        }

        // 更新所有单位的血条
        updateHealthBars() {
            this.units.forEach(unit => {
                if (!unit.isAlive || !unit.healthBar || !unit.healthBarBg) return;

                try {
                    // 根据单位类型设置血条大小
                    let barWidth, barHeight, offsetY;

                    if (unit === cueBall) {
                        // 国王血条
                        barWidth = 40;
                        barHeight = 5;
                        offsetY = -30;
                    } else if (unit.maxHealth >= 20) {
                        // Boss血条（血量20+）
                        barWidth = 50;
                        barHeight = 8;
                        offsetY = -30;
                    } else {
                        // 普通小兵血条
                        barWidth = 24;
                        barHeight = 4;
                        offsetY = -18;
                    }

                    // 清除并重绘血条背景
                    unit.healthBarBg.clear();
                    unit.healthBarBg.fillStyle(0x000000);
                    unit.healthBarBg.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth, barHeight);

                    // 清除并重绘血条
                    unit.healthBar.clear();
                    const healthPercent = unit.health / unit.maxHealth;
                    const healthColor = unit.team === 'blue' ? 0x00ff00 : 0xff0000;
                    unit.healthBar.fillStyle(healthColor);
                    unit.healthBar.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth * healthPercent, barHeight);
                } catch (error) {
                    console.warn('Error updating health bar:', error);
                }
            });
        }

        // 清理死亡单位
        cleanupDeadUnits() {
            const deadUnits = [];
            this.units.forEach((unit, id) => {
                if (!unit.isAlive || unit.health <= 0 || !unit.active) {
                    deadUnits.push(id);
                }
            });
            deadUnits.forEach(id => this.removeUnit(id));
        }

        // 处理战斗行动
        processBattleActions(currentTime) {
            this.units.forEach(unit => {
                if (!unit.isAlive || unit.health <= 0 || unit.isDying) return;

                // 检查攻击冷却
                if (currentTime - unit.lastActionTime < (unit.attackCooldown || 1000)) return;

                // 寻找目标
                const enemies = this.getEnemies(unit.team);
                if (enemies.length === 0) return;

                const target = this.findNearestTarget(unit, enemies);
                if (!target) return;

                const distance = this.getDistance(unit, target);

                // 检查是否是国王（远程魔法攻击）
                const isKing = (unit === cueBall);
                const attackRange = isKing ? 150 : (unit.attackRange || 50); // 国王攻击范围更远

                if (distance <= attackRange) {
                    // 攻击
                    if (isKing) {
                        this.executeMagicAttack(unit, target);
                    } else {
                        this.executeAttack(unit, target);
                    }
                    unit.lastActionTime = currentTime;
                } else {
                    // 移动（避免身体重叠）
                    this.moveTowardsTargetWithCollisionAvoidance(unit, target);
                }
            });
        }

        // 寻找最近目标
        findNearestTarget(unit, enemies) {
            let nearest = null;
            let minDistance = Infinity;

            enemies.forEach(enemy => {
                if (!enemy.isAlive || enemy.health <= 0 || enemy.isDying) return;
                const distance = this.getDistance(unit, enemy);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = enemy;
                }
            });

            return nearest;
        }

        // 计算距离
        getDistance(unit1, unit2) {
            if (!unit1 || !unit2 || unit1.x === undefined || unit2.x === undefined) return Infinity;
            return Phaser.Math.Distance.Between(unit1.x, unit1.y, unit2.x, unit2.y);
        }

        // 执行攻击
        executeAttack(attacker, target) {
            if (!attacker || !target || target.health <= 0 || target.isDying) return;

            // 标记目标正在被攻击，防止重复攻击
            if (target.isBeingAttacked) return;
            target.isBeingAttacked = true;

            // 执行武器挥动动画
            this.playAttackAnimation(attacker, target);

            // 延迟造成伤害，配合动画
            setTimeout(() => {
                if (target.isAlive && target.health > 0) {
                    this.dealDamage(target, attacker.attackPower || 1);
                }
                target.isBeingAttacked = false;
            }, 300); // 300ms后造成伤害，给动画时间
        }

        // 播放攻击动画
        playAttackAnimation(attacker, target) {
            if (!attacker.weapon || !this.gameScene) return;

            // 标记正在攻击
            attacker.isAttacking = true;

            // 计算攻击方向
            const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

            // 武器攻击动画：更明显的摆动幅度
            const baseAngle = angle;
            const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
            const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

            // 人物抖动动画
            const originalX = attacker.x;
            const originalY = attacker.y;

            // 第一段动画：反向预备动作
            this.gameScene.tweens.add({
                targets: attacker.weapon,
                rotation: swingAngle1,
                duration: 100,
                ease: 'Power2',
                onComplete: () => {
                    // 第二段动画：快速攻击到60度 + 人物抖动
                    this.gameScene.tweens.add({
                        targets: attacker.weapon,
                        rotation: swingAngle2,
                        duration: 120,
                        ease: 'Back.easeOut',
                        onComplete: () => {
                            // 第三段动画：回到中间位置
                            this.gameScene.tweens.add({
                                targets: attacker.weapon,
                                rotation: baseAngle + Phaser.Math.DegToRad(20),
                                duration: 100,
                                ease: 'Power2',
                                onComplete: () => {
                                    // 最后回到初始角度
                                    this.gameScene.tweens.add({
                                        targets: attacker.weapon,
                                        rotation: 0,
                                        duration: 150,
                                        ease: 'Power1',
                                        onComplete: () => {
                                            attacker.isAttacking = false;
                                        }
                                    });
                                }
                            });
                        }
                    });

                    // 人物抖动效果
                    this.gameScene.tweens.add({
                        targets: attacker,
                        x: originalX + Phaser.Math.Between(-3, 3),
                        y: originalY + Phaser.Math.Between(-3, 3),
                        duration: 50,
                        yoyo: true,
                        repeat: 2,
                        onComplete: () => {
                            // 恢复原位置
                            attacker.setPosition(originalX, originalY);
                        }
                    });
                }
            });
        }

        // 造成伤害
        dealDamage(target, damage) {
            if (!target || target.health <= 0 || target.isDying) return;

            target.health = Math.max(0, target.health - damage);

            // 创建伤害特效
            this.createDamageEffect(target, damage);

            if (target.health <= 0 && !target.isDying) {
                target.isDying = true;
                target.isAlive = false;

                // 创建击杀特效
                this.createKillEffect(target);

                // 延迟销毁，避免立即删除
                setTimeout(() => {
                    this.destroyUnit(target);

                    // 单位死亡后检查战斗结果
                    if (this.gameScene && typeof checkBattleResult === 'function') {
                        checkBattleResult.call(this.gameScene);
                    }
                }, 500);
            }
        }

        // 创建伤害数字特效
        createDamageEffect(target, damage) {
            if (!this.gameScene || !target) return;

            try {
                const damageText = this.gameScene.add.text(target.x, target.y - 30, `-${damage}`, {
                    fontSize: '12px',
                    fill: '#ff0000',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.gameScene.tweens.add({
                    targets: damageText,
                    y: target.y - 50,
                    alpha: 0,
                    duration: 800,
                    onComplete: () => damageText.destroy()
                });
            } catch (error) {
                console.warn('Error creating damage effect:', error);
            }
        }

        // 创建击杀特效
        createKillEffect(target) {
            if (!this.gameScene || !target) return;

            try {
                const killText = this.gameScene.add.text(target.x, target.y, 'KILL!', {
                    fontSize: '16px',
                    fill: '#ffff00',
                    fontWeight: 'bold',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);

                this.gameScene.tweens.add({
                    targets: killText,
                    y: target.y - 60,
                    alpha: 0,
                    scale: 1.5,
                    duration: 1000,
                    onComplete: () => killText.destroy()
                });
            } catch (error) {
                console.warn('Error creating kill effect:', error);
            }
        }

        // 销毁单位
        destroyUnit(unit) {
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 从数组中安全移除
            this.removeFromArrays(unit);

            // 销毁游戏对象
            if (unit.weapon) unit.weapon.destroy();
            if (unit.healthBar) unit.healthBar.destroy();
            if (unit.healthBarBg) unit.healthBarBg.destroy();
            if (unit.active) unit.destroy();
        }

        // 从数组中移除单位
        removeFromArrays(unit) {
            try {
                if (unit === cueBall) {
                    cueBall = null;
                    return;
                }

                // 安全地从数组中移除
                const arrays = [blueBalls, redBalls, blackBalls];
                arrays.forEach(arr => {
                    if (Array.isArray(arr)) {
                        const index = arr.indexOf(unit);
                        if (index > -1) {
                            arr.splice(index, 1);
                        }
                    }
                });
            } catch (error) {
                console.warn('Error removing unit from arrays:', error);
            }
        }

        // 国王的魔法攻击
        executeMagicAttack(attacker, target) {
            if (!attacker || !target || target.health <= 0 || target.isDying) return;

            // 标记目标正在被攻击，防止重复攻击
            if (target.isBeingAttacked) return;
            target.isBeingAttacked = true;

            // 创建魔法弹特效
            this.createMagicProjectile(attacker, target);

            // 延迟造成伤害
            setTimeout(() => {
                if (target.isAlive && target.health > 0) {
                    this.dealDamage(target, attacker.attackPower || 2); // 国王伤害更高
                }
                target.isBeingAttacked = false;
            }, 500); // 魔法弹飞行时间
        }

        // 创建魔法弹特效
        createMagicProjectile(attacker, target) {
            if (!this.gameScene) return;

            try {
                // 创建小一点的魔法弹
                const projectile = this.gameScene.add.graphics();
                projectile.fillStyle(0x00ffff, 0.9); // 青色魔法弹
                projectile.fillCircle(0, 0, 4); // 半径从8改为4
                projectile.lineStyle(1, 0xffffff, 0.8);
                projectile.strokeCircle(0, 0, 4);
                projectile.setPosition(attacker.x, attacker.y);

                // 魔法弹飞向目标
                this.gameScene.tweens.add({
                    targets: projectile,
                    x: target.x,
                    y: target.y,
                    duration: 400, // 稍微快一点
                    ease: 'Power2',
                    onComplete: () => {
                        // 小一点的攻击特效
                        const hitEffect = this.gameScene.add.graphics();
                        hitEffect.fillStyle(0x00ffff, 0.8);
                        hitEffect.fillCircle(target.x, target.y, 12); // 从20改为12
                        hitEffect.lineStyle(2, 0xffffff, 1.0);
                        hitEffect.strokeCircle(target.x, target.y, 12);

                        // 快速消失的攻击效果
                        this.gameScene.tweens.add({
                            targets: hitEffect,
                            alpha: 0,
                            scaleX: 1.5, // 从2改为1.5
                            scaleY: 1.5,
                            duration: 200, // 从300改为200，更快消失
                            onComplete: () => hitEffect.destroy()
                        });

                        // 魔法弹立即消失
                        projectile.destroy();
                    }
                });
            } catch (error) {
                console.warn('Error creating magic projectile:', error);
            }
        }

        // 移动向目标 - 防止身体重叠
        moveTowardsTargetWithCollisionAvoidance(unit, target) {
            if (!unit || !target || unit.isAttacking) return;

            const dx = target.x - unit.x;
            const dy = target.y - unit.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0) {
                let moveX = (dx / distance);
                let moveY = (dy / distance);

                // 检查与其他单位的碰撞
                const avoidanceVector = this.calculateAvoidanceVector(unit);
                moveX += avoidanceVector.x;
                moveY += avoidanceVector.y;

                // 标准化移动向量
                const moveLength = Math.sqrt(moveX * moveX + moveY * moveY);
                if (moveLength > 0) {
                    moveX /= moveLength;
                    moveY /= moveLength;
                }

                const moveSpeed = 2.0;
                const newX = unit.x + moveX * moveSpeed;
                const newY = unit.y + moveY * moveSpeed;

                // 边界检查
                const boundedX = Math.max(70, Math.min(680, newX));
                const boundedY = Math.max(200, Math.min(1200, newY));

                unit.setPosition(boundedX, boundedY);
            }
        }

        // 计算避让向量，防止单位重叠
        calculateAvoidanceVector(unit) {
            let avoidX = 0;
            let avoidY = 0;
            const avoidanceRadius = 40; // 避让半径

            this.units.forEach(otherUnit => {
                if (otherUnit === unit || !otherUnit.isAlive || otherUnit.health <= 0) return;

                const dx = unit.x - otherUnit.x;
                const dy = unit.y - otherUnit.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < avoidanceRadius && distance > 0) {
                    // 计算避让力度，距离越近力度越大
                    const avoidanceStrength = (avoidanceRadius - distance) / avoidanceRadius;
                    avoidX += (dx / distance) * avoidanceStrength * 0.5;
                    avoidY += (dy / distance) * avoidanceStrength * 0.5;
                }
            });

            return { x: avoidX, y: avoidY };
        }

        // 获取战斗统计
        getBattleStats() {
            const stats = { blue: 0, red: 0 };
            this.units.forEach(unit => {
                if (unit.isAlive && unit.health > 0) {
                    if (unit.team === 'blue') {
                        stats.blue++;
                    } else if (unit.team === 'red') {
                        stats.red++;
                    }
                }
            });
            return stats;
        }
    }

    // 创建全局战斗管理器
    let battleManager = new BattleManager();

    // 确保数组安全初始化的函数
    function ensureArraysInitialized() {
        if (!Array.isArray(blueBalls)) blueBalls = [];
        if (!Array.isArray(redBalls)) redBalls = [];
        if (!Array.isArray(blackBalls)) blackBalls = [];
        if (!Array.isArray(ballsToDestroy)) ballsToDestroy = [];
    }

    // 游戏变量
    let pockets = []; // 台球袋
    let battleMode = false; // 战斗模式
    let shotsPerLevel = 3; // 每关只能击打3次
    let currentLevelShots = 0; // 当前关卡已击打次数



    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        physics: {
            default: 'matter',
            matter: {
                gravity: { y: 0 },
                debug: false
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载大背景图片
        this.load.image('gameBackground', 'images/rpg/background9.jpg');

        // 创建带高光效果的球纹理
        createBallTexture.call(this, 'blueBall', 0x3498db);
        createBallTexture.call(this, 'redBall', 0xe74c3c);
        createBallTexture.call(this, 'blackBall', 0x000000);
        createBallTexture.call(this, 'cueBall', 0xffffff);

        // 加载小兵图片
        this.load.image('blueSoldier', 'images/rpg/蓝色小兵.png');
        this.load.image('redSoldier', 'images/rpg/红色小兵.png');
        this.load.image('weapon', 'images/rpg/小兵的武器.png');

        // 加载特殊角色图片
        this.load.image('king', 'images/rpg/国王.png');
        this.load.image('kingWeapon', 'images/rpg/国王的武器.png');
        this.load.image('redLeader', 'images/rpg/红色将领.png');

        // 创建台球袋纹理
        createPocketTexture.call(this);
    }

    // 创建球的纹理（带高光效果）
    function createBallTexture(textureKey, color) {
        const ballGraphic = this.add.graphics();

        // 主球体
        ballGraphic.fillStyle(color, 1);
        ballGraphic.fillCircle(16, 16, 15);

        // 添加高光效果
        ballGraphic.fillStyle(0xffffff, 0.4);
        ballGraphic.fillCircle(11, 11, 5); // 主高光

        // 添加次高光
        ballGraphic.fillStyle(0xffffff, 0.2);
        ballGraphic.fillCircle(9, 9, 2);

        // 添加阴影效果
        ballGraphic.fillStyle(0x000000, 0.2);
        ballGraphic.fillCircle(20, 20, 3);

        ballGraphic.generateTexture(textureKey, 32, 32);
        ballGraphic.destroy(); // 销毁图形对象，避免显示在画布上
    }



    // 创建台球袋纹理
    function createPocketTexture() {
        const pocketGraphic = this.add.graphics();
        // 外圈 - 台面颜色
        pocketGraphic.fillStyle(0x0d4f3c, 1);
        pocketGraphic.fillCircle(25, 25, 25);
        // 内圈 - 黑色洞
        pocketGraphic.fillStyle(0x000000, 1);
        pocketGraphic.fillCircle(25, 25, 20);
        // 边框
        pocketGraphic.lineStyle(2, 0x8B4513, 1);
        pocketGraphic.strokeCircle(25, 25, 25);
        pocketGraphic.generateTexture('pocket', 50, 50);
        pocketGraphic.destroy();
    }

    // 创建游戏场景
    function create() {
        // 添加大背景图片
        const background = this.add.image(375, 667, 'gameBackground');
        background.setDisplaySize(750, 1334); // 设置为游戏窗口大小
        background.setDepth(-10); // 设置为最底层

        // 确保数组正确初始化
        ensureArraysInitialized();

        // 创建台球桌边界
        createTable.call(this);

        // 创建球
        createBalls.call(this);

        // 创建UI
        createUI.call(this);

        // 设置输入事件
        setupInput.call(this);

        gameStarted = true;
    }

    function update() {
        if (gameStarted) {
            // 确保数组安全
            ensureArraysInitialized();

            updateHealthBars.call(this);
            checkGameState.call(this);

            // 处理待销毁的球 - 防止连锁反应
            processBallDestruction.call(this);

            // 在战斗模式下，使用新的战斗管理器
            if (battleMode && !isTransforming) {
                try {
                    // 使用战斗管理器进行更新，而不是每帧执行复杂逻辑
                    battleManager.update(this.time.now);
                    updateWeaponPositions.call(this);
                } catch (error) {
                    console.warn('Error in battle mode update:', error);
                }
            }
        }
    }

    // 清理数组中的null和无效值
    function cleanupArrays() {
        try {
            // 确保数组存在，如果不存在则初始化为空数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

            const originalBlueLength = blueBalls.length;
            const originalRedLength = redBalls.length;
            const originalBlackLength = blackBalls.length;

            blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && unit.active);

            // 如果有清理，输出日志
            if (originalBlueLength !== blueBalls.length) {
                console.log(`清理蓝球数组: ${originalBlueLength} -> ${blueBalls.length}`);
            }
            if (originalRedLength !== redBalls.length) {
                console.log(`清理红球数组: ${originalRedLength} -> ${redBalls.length}`);
            }
            if (originalBlackLength !== blackBalls.length) {
                console.log(`清理黑球数组: ${originalBlackLength} -> ${blackBalls.length}`);
            }
        } catch (error) {
            console.warn('Error cleaning arrays:', error);
            // 如果出错，重新初始化数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];
        }
    }

    // 创建台球桌
    function createTable() {
        // 创建台球桌边界 - 根据球袋位置调整边界
        // 球袋位置：左侧x=100, 右侧x=650, 上方y=300, 中间y=680, 下方y=1140

        // 左边界 - 分段创建，避开球袋位置
        this.matter.add.rectangle(25, 200, 50, 200, { isStatic: true }); // 上段
        this.matter.add.rectangle(25, 490, 50, 380, { isStatic: true }); // 中段
        this.matter.add.rectangle(25, 910, 50, 460, { isStatic: true }); // 下段

        // 右边界 - 分段创建，避开球袋位置
        this.matter.add.rectangle(725, 200, 50, 200, { isStatic: true }); // 上段
        this.matter.add.rectangle(725, 490, 50, 380, { isStatic: true }); // 中段
        this.matter.add.rectangle(725, 910, 50, 460, { isStatic: true }); // 下段

        // 上边界 - 分段创建，避开角落球袋
        this.matter.add.rectangle(200, 225, 200, 50, { isStatic: true }); // 左段
        this.matter.add.rectangle(375, 225, 150, 50, { isStatic: true }); // 中段
        this.matter.add.rectangle(550, 225, 200, 50, { isStatic: true }); // 右段

        // 下边界 - 分段创建，避开角落球袋
        this.matter.add.rectangle(200, 1215, 200, 50, { isStatic: true }); // 左段
        this.matter.add.rectangle(375, 1215, 150, 50, { isStatic: true }); // 中段
        this.matter.add.rectangle(550, 1215, 200, 50, { isStatic: true }); // 右段

        // 绘制台球桌背景 - 调整位置适应新的边界
        const tableBg = this.add.graphics();
        // tableBg.fillStyle(0x0d4f3c);
        // tableBg.fillRect(50, 175, 650, 1084);
        // tableBg.lineStyle(6, 0x8B4513); // 加粗边框线
        // tableBg.strokeRect(50, 175, 650, 1084);

        // 绘制墙壁视觉效果
        const wallGraphics = this.add.graphics();
        // wallGraphics.lineStyle(20, 0x654321); // 棕色墙壁
        // // 左墙
        // wallGraphics.lineBetween(50, 175, 50, 1259);
        // // 右墙
        // wallGraphics.lineBetween(700, 175, 700, 1259);
        // // 上墙
        // wallGraphics.lineBetween(50, 175, 700, 175);
        // // 下墙
        // wallGraphics.lineBetween(50, 1259, 700, 1259);

        // 创建6个台球袋
        createPockets.call(this);
    }

    // 创建台球袋
    function createPockets() {
        // 清空袋子数组
        pockets = [];

        // 6个袋子的位置：四个角落 + 两个中间
        const pocketPositions = [
            { x: 100, y: 300 },   // 左上角
            { x: 650, y: 300 },  // 右上角
            { x: 100, y: 680 },   // 左中
            { x: 650, y: 680 },  // 右中
            { x: 100, y: 1140 },  // 左下角
            { x: 650, y: 1140 }  // 右下角
        ];

        pocketPositions.forEach((pos, index) => {
            // 直接绘制圆形袋子，不使用图像
            const pocketGraphics = this.add.graphics();
            // 外圈 - 台面颜色
            pocketGraphics.fillStyle(0x0d4f3c, 1);
            pocketGraphics.fillCircle(pos.x, pos.y, 30);
            // 内圈 - 黑色洞
            pocketGraphics.fillStyle(0x000000, 1);
            pocketGraphics.fillCircle(pos.x, pos.y, 25);
            // 边框
            pocketGraphics.lineStyle(3, 0x8B4513, 1);
            pocketGraphics.strokeCircle(pos.x, pos.y, 30);
            pocketGraphics.setDepth(2); // 在桌子背景上面，在球的下面

            // 创建物理传感器（不阻挡球，但能检测碰撞）
            const pocketSensor = this.matter.add.circle(pos.x, pos.y, 30, {
                isSensor: true,
                isStatic: true,
                label: 'pocket'
            });

            pocketSensor.pocketIndex = index;
            pockets.push({ graphics: pocketGraphics, sensor: pocketSensor, position: pos });
        });
    }

    // 创建球
    function createBalls() {
        // 重置关卡变量
        currentLevelShots = 0;
        battleMode = false;
        ballsToDestroy = [];

        // 创建白球（主球）- 使用更真实的台球物理参数，增加50%大小
        cueBall = this.matter.add.image(375, 1100, 'cueBall');
        cueBall.setScale(1.5); // 先设置视觉缩放
        cueBall.setCircle(24); // 碰撞体也要相应增大，匹配视觉大小
        cueBall.setBounce(0.95); // 台球的弹性系数应该很高
        cueBall.setFriction(0.1); // 台球与台面的摩擦系数
        cueBall.setFrictionAir(0.008); // 空气阻力，让球逐渐停下
        cueBall.setDensity(0.001); // 设置密度，影响碰撞效果
        cueBall.setDepth(5); // 设置白球的层级高于球袋
        cueBall.label = 'cueBall';

        // 创建三角形摆放的球 - 混合排列
        const ballPositions = [
            // 第一排（1个球）
            { x: 375, y: 400 },
            // 第二排（2个球）
            { x: 355, y: 440 },
            { x: 395, y: 440 },
            // 第三排（3个球）
            { x: 335, y: 480 },
            { x: 375, y: 480 },
            { x: 415, y: 480 },
            // 第四排（4个球）
            { x: 315, y: 520 },
            { x: 355, y: 520 },
            { x: 395, y: 520 },
            { x: 435, y: 520 },
            // 第五排（5个球）
            { x: 295, y: 560 },
            { x: 335, y: 560 },
            { x: 375, y: 560 },
            { x: 415, y: 560 },
            { x: 455, y: 560 }
        ];

        // 随机分配球的阵营 - 混合摆放，包括1个黑球
        const shuffledPositions = [...ballPositions].sort(() => Math.random() - 0.5); // 随机打乱位置
        let blackBallCreated = false; // 确保只创建1个黑球

        for (let i = 0; i < Math.min(shuffledPositions.length, 15); i++) {
            const pos = shuffledPositions[i];

            // 分配球的类型：第一个球有机会是黑球，其余50%蓝球，50%红球
            let ballType, texture, label;

            if (!blackBallCreated && Math.random() < 0.15) { // 15%概率创建黑球，且只创建1个
                ballType = 'black';
                texture = 'blackBall';
                label = 'blackBall';
                blackBallCreated = true;
            } else if (Math.random() < 0.5) {
                ballType = 'blue';
                texture = 'blueBall';
                label = 'blueBall';
            } else {
                ballType = 'red';
                texture = 'redBall';
                label = 'redBall';
            }

            const ball = this.matter.add.image(pos.x, pos.y, texture);
            ball.setScale(1.5); // 先设置视觉缩放
            ball.setCircle(24); // 碰撞体也要相应增大，匹配视觉大小
            ball.setBounce(0.95); // 高弹性系数
            ball.setFriction(0.1); // 台球标准摩擦系数
            ball.setFrictionAir(0.008); // 空气阻力
            ball.setDensity(0.001); // 设置密度
            ball.setDepth(5); // 设置球的层级高于球袋
            ball.label = label;
            ball.isInPocket = false; // 是否掉入袋中
            ball.lastCollisionTime = 0; // 上次碰撞时间
            ball.markedForDestroy = false; // 是否标记为待销毁

            if (ballType === 'blue') {
                blueBalls.push(ball);
            } else if (ballType === 'red') {
                redBalls.push(ball);
            } else {
                blackBalls.push(ball);
            }
        }

        // 如果没有创建黑球，强制在最后一个位置创建一个
        if (!blackBallCreated && shuffledPositions.length > 0) {
            const lastPos = shuffledPositions[Math.min(14, shuffledPositions.length - 1)];

            // 如果最后位置已经有球，替换它
            if (blueBalls.length > 0) {
                const lastBlueBall = blueBalls.pop();
                lastBlueBall.destroy();
            } else if (redBalls.length > 0) {
                const lastRedBall = redBalls.pop();
                lastRedBall.destroy();
            }

            const blackBall = this.matter.add.image(lastPos.x, lastPos.y, 'blackBall');
            blackBall.setScale(1.5); // 先设置视觉缩放
            blackBall.setCircle(24); // 碰撞体也要相应增大，匹配视觉大小
            blackBall.setBounce(0.95);
            blackBall.setFriction(0.1);
            blackBall.setFrictionAir(0.008);
            blackBall.setDensity(0.001);
            blackBall.setDepth(5);
            blackBall.label = 'blackBall';
            blackBall.isInPocket = false;
            blackBall.lastCollisionTime = 0;
            blackBall.markedForDestroy = false;
            blackBalls.push(blackBall);
        }



        // 设置碰撞检测
        this.matter.world.on('collisionstart', handleCollision.bind(this));
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 每关只有1个波次，不显示波次信息

        // 出杆次数显示
        this.shotText = this.add.text(375, 120, `出杆次数: ${currentLevelShots}/${shotsPerLevel}`, {
            fontSize: '18px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 关卡显示
        this.levelText = this.add.text(375, 120, `第 ${currentLevel} 关`, {
            fontSize: '20px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 游戏状态文字
        this.statusText = this.add.text(375, 145, '瞄准并点击发射白球（打3杆后变身战斗）', {
            fontSize: '16px',
            fill: '#ffff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 设置输入控制
    function setupInput() {
        // 鼠标/触摸输入
        this.input.on('pointerdown', startAiming.bind(this));
        this.input.on('pointermove', updateAiming.bind(this));
        this.input.on('pointerup', shoot.bind(this));
    }

    // 开始瞄准
    function startAiming(pointer) {
        // 使用更合理的速度检测 - 参考真实台球物理
        // 战斗模式下不能瞄准
        if (!isAiming && cueBall && cueBall.body && cueBall.body.speed < 0.2 && !battleMode) {
            isAiming = true;

            // 创建瞄准线
            if (aimLine) aimLine.destroy();
            aimLine = this.add.graphics();

            // 创建力量条
            if (powerBar) powerBar.destroy();
            powerBar = this.add.graphics();

            // 更新状态提示
            this.statusText.setText('拖拽瞄准，松开发射');
        }
    }

    // 更新瞄准
    function updateAiming(pointer) {
        if (isAiming && cueBall) {
            const dx = pointer.x - cueBall.x;
            const dy = pointer.y - cueBall.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const maxDistance = 200; // 与射击保持一致
            const power = Math.min(distance / maxDistance, 1);

            // 绘制瞄准线 - 显示预测轨迹
            aimLine.clear();
            aimLine.lineStyle(4, 0xffffff, 0.8); // 白色瞄准线

            // 标准化方向向量
            const length = Math.sqrt(dx * dx + dy * dy);
            if (length > 0) {
                const normalizedDx = dx / length;
                const normalizedDy = dy / length;

                // 绘制更长的瞄准线，显示球的预期路径
                const aimLength = 150;
                aimLine.lineBetween(cueBall.x, cueBall.y,
                    cueBall.x + normalizedDx * aimLength,
                    cueBall.y + normalizedDy * aimLength);

                // 在瞄准线末端绘制一个小圆点
                aimLine.fillStyle(0xffffff, 0.8);
                aimLine.fillCircle(cueBall.x + normalizedDx * aimLength,
                                 cueBall.y + normalizedDy * aimLength, 3);
            }

            // 绘制力量条 - 移到屏幕底部固定位置，避免遮挡
            powerBar.clear();

            // 力量条背景
            powerBar.fillStyle(0x2c3e50, 0.8);
            powerBar.fillRoundedRect(275, 1250, 200, 25, 12);
            powerBar.lineStyle(3, 0xffffff, 0.9);
            powerBar.strokeRoundedRect(275, 1250, 200, 25, 12);

            // 力量条填充 - 使用渐变色效果
            const powerColor = power < 0.2 ? 0x00ff00 :
                              power < 0.5 ? 0x7fff00 :
                              power < 0.8 ? 0xffff00 : 0xff4500;
            powerBar.fillStyle(powerColor, 0.9);
            powerBar.fillRoundedRect(277, 1252, (200 - 4) * power, 21, 10);

            // 添加力量条刻度
            powerBar.lineStyle(1, 0xffffff, 0.5);
            for (let i = 1; i < 5; i++) {
                const x = 275 + (200 * i / 5);
                powerBar.lineBetween(x, 1250, x, 1275);
            }

            // 力量百分比文字
            if (this.powerText) this.powerText.destroy();
            this.powerText = this.add.text(375, 1262, `${Math.round(power * 100)}%`, {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);
        }
    }

    // 发射球
    function shoot(pointer) {
        if (isAiming && cueBall) {
            const dx = pointer.x - cueBall.x;
            const dy = pointer.y - cueBall.y;
            const distance = Math.sqrt(dx * dx + dy * dy);
            const maxDistance = 200; // 增加最大瞄准距离
            const power = Math.min(distance / maxDistance, 1);

            // 使用更合理的力量计算，因为球变大了需要更大力量
            // 力量范围从轻推到强力击球
            const minPower = 0.012; // 最小力量增加50%
            const maxPower = 0.095; // 最大力量增加50%，适应大球
            const finalPower = minPower + (power * (maxPower - minPower));

            // 应用力到白球 - 使用标准化方向向量
            const length = Math.sqrt(dx * dx + dy * dy);
            if (length > 0) {
                const normalizedDx = dx / length;
                const normalizedDy = dy / length;
                cueBall.applyForce({
                    x: normalizedDx * finalPower,
                    y: normalizedDy * finalPower
                });
            }

            // 增加出杆次数
            shotCount++;
            currentLevelShots++;
            this.shotText.setText(`出杆次数: ${currentLevelShots}/${shotsPerLevel}`);

            // 检查是否达到3杆，触发战斗模式
            if (currentLevelShots >= shotsPerLevel && !battleMode) {
                startBattleMode.call(this);
            }

            // 清理瞄准元素
            if (aimLine) {
                aimLine.destroy();
                aimLine = null;
            }
            if (powerBar) {
                powerBar.destroy();
                powerBar = null;
            }
            if (this.powerText) {
                this.powerText.destroy();
                this.powerText = null;
            }

            isAiming = false;

            // 更新状态提示
            this.statusText.setText('白球发射中...');

            // 3秒后恢复提示
            this.time.delayedCall(3000, () => {
                if (gameStarted && !battleMode && cueBall && cueBall.body && cueBall.body.speed < 0.1) {
                    this.statusText.setText(`瞄准并点击发射白球（还需${shotsPerLevel - currentLevelShots}杆变身战斗）`);
                }
            });
        }
    }

    // 处理碰撞 - 修复连锁反应bug
    function handleCollision(event) {
        const pairs = event.pairs;
        const currentTime = this.time.now;

        for (let pair of pairs) {
            const bodyA = pair.bodyA;
            const bodyB = pair.bodyB;

            // 检查球是否掉入台球袋
            if (bodyA.label === 'pocket' || bodyB.label === 'pocket') {
                const ball = bodyA.label === 'pocket' ? bodyB.gameObject : bodyA.gameObject;
                const pocketBody = bodyA.label === 'pocket' ? bodyA : bodyB;

                if (ball && (ball.label === 'blueBall' || ball.label === 'redBall' || ball.label === 'blackBall' || ball.label === 'cueBall') && !ball.isInPocket) {
                    handleBallInPocket.call(this, ball, pocketBody.pocketIndex);
                }
            }

            // 检查白球撞击其他球
            if ((bodyA.label === 'cueBall' && bodyB.label === 'redBall') ||
                (bodyB.label === 'cueBall' && bodyA.label === 'redBall')) {

                const redBall = bodyA.label === 'redBall' ? bodyA.gameObject : bodyB.gameObject;

                // 检查碰撞冷却时间，防止重复处理
                if (redBall && !redBall.markedForDestroy &&
                    (!redBall.lastCollisionTime || currentTime - redBall.lastCollisionTime > 100)) {

                    redBall.lastCollisionTime = currentTime;
                    redBall.markedForDestroy = true;

                    // 白球撞击红球，敌方掉血
                    enemyHealth = Math.max(0, enemyHealth - 20);

                    // 添加到待销毁列表，而不是立即销毁
                    ballsToDestroy.push(redBall);

                    console.log('红球标记为待销毁，当前待销毁数量:', ballsToDestroy.length);
                }
            }

            if ((bodyA.label === 'cueBall' && bodyB.label === 'blueBall') ||
                (bodyB.label === 'cueBall' && bodyA.label === 'blueBall')) {

                const blueBall = bodyA.label === 'blueBall' ? bodyA.gameObject : bodyB.gameObject;

                // 检查碰撞冷却时间，防止重复处理
                if (blueBall && !blueBall.markedForDestroy &&
                    (!blueBall.lastCollisionTime || currentTime - blueBall.lastCollisionTime > 100)) {

                    blueBall.lastCollisionTime = currentTime;
                    blueBall.markedForDestroy = true;

                    // 白球撞击蓝球，我方掉血
                    playerHealth = Math.max(0, playerHealth - 20);

                    // 添加到待销毁列表，而不是立即销毁
                    ballsToDestroy.push(blueBall);

                    console.log('蓝球标记为待销毁，当前待销毁数量:', ballsToDestroy.length);
                }
            }

            if ((bodyA.label === 'cueBall' && bodyB.label === 'blackBall') ||
                (bodyB.label === 'cueBall' && bodyA.label === 'blackBall')) {

                const blackBall = bodyA.label === 'blackBall' ? bodyA.gameObject : bodyB.gameObject;

                // 检查碰撞冷却时间，防止重复处理
                if (blackBall && !blackBall.markedForDestroy &&
                    (!blackBall.lastCollisionTime || currentTime - blackBall.lastCollisionTime > 100)) {

                    blackBall.lastCollisionTime = currentTime;
                    blackBall.markedForDestroy = true;

                    // 白球撞击黑球，双方都掉血
                    playerHealth = Math.max(0, playerHealth - 10);
                    enemyHealth = Math.max(0, enemyHealth - 10);

                    // 添加到待销毁列表，而不是立即销毁
                    ballsToDestroy.push(blackBall);

                    console.log('黑球标记为待销毁，当前待销毁数量:', ballsToDestroy.length);
                }
            }
        }
    }

    // 处理球掉入袋中
    function handleBallInPocket(ball, pocketIndex) {
        if (!ball || ball.isInPocket) return;

        ball.isInPocket = true;

        // 从对应数组中移除（掉袋的球不参与变身战斗）
        if (ball.label === 'blueBall') {
            const index = blueBalls.indexOf(ball);
            if (index > -1) blueBalls.splice(index, 1);
        } else if (ball.label === 'redBall') {
            const index = redBalls.indexOf(ball);
            if (index > -1) redBalls.splice(index, 1);
        } else if (ball.label === 'blackBall') {
            const index = blackBalls.indexOf(ball);
            if (index > -1) blackBalls.splice(index, 1);
        } else if (ball.label === 'cueBall') {
            // 白球掉袋，重新放置在起始位置
            this.time.delayedCall(1000, () => {
                if (cueBall && cueBall.scene) {
                    cueBall.setPosition(375, 1100);
                    cueBall.setVelocity(0, 0);
                    cueBall.isInPocket = false;
                }
            });
            return; // 白球不销毁，只是重新定位
        }

        // 创建掉袋特效
        const pocketEffect = this.add.text(ball.x, ball.y, '进袋!', {
            fontSize: '16px',
            fill: '#ffff00',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: pocketEffect,
            y: ball.y - 50,
            alpha: 0,
            duration: 1000,
            onComplete: () => pocketEffect.destroy()
        });

        // 销毁球
        ball.destroy();

        console.log(`球掉入第${pocketIndex + 1}号袋，剩余蓝球:${blueBalls.length}，红球:${redBalls.length}，黑球:${blackBalls.length}`);
    }

    // 开始战斗模式
    function startBattleMode() {
        battleMode = true;

        // 更新状态提示
        this.statusText.setText('等待球停下来，准备变身...');
        this.statusText.setStyle({ fill: '#ff0000', fontSize: '18px' });

        // 开始检查球是否都停下来
        checkBallsStoppedForTransform.call(this);

        console.log('战斗模式开始！等待球停下来...');
    }

    // 检查所有球是否停下来，然后变身
    function checkBallsStoppedForTransform() {
        const allBalls = [cueBall, ...blueBalls, ...redBalls, ...blackBalls].filter(ball =>
            ball && ball.active && !ball.isInPocket
        );

        const allStopped = allBalls.every(ball => ball.body && ball.body.speed < 0.1);

        if (allStopped) {
            // 所有球都停下来了，开始变身
            this.statusText.setText('球变身为小兵！');
            transformBallsToSoldiers.call(this);
            // 注意：战斗启动现在在变身完成后自动触发
        } else {
            // 还有球在移动，继续检查
            this.time.delayedCall(100, () => {
                checkBallsStoppedForTransform.call(this);
            });
        }
    }

    // 将球变身为小兵 - 逐个生成
    // 注意：变身后的角色只是图像对象，没有物理效果
    // 战斗阶段也不使用物理效果，通过直接修改位置来实现移动和战斗
    function transformBallsToSoldiers() {
        isTransforming = true; // 开始变身，暂停武器位置更新
        let transformIndex = 0;
        const allBallsToTransform = [];

        // 收集所有需要变身的球
        if (cueBall && cueBall.active && !cueBall.isInPocket) {
            allBallsToTransform.push({type: 'king', ball: cueBall});
        }

        blueBalls.forEach((ball, index) => {
            if (ball && ball.active) {
                // 包括掉袋的球，它们也要变身参与战斗
                allBallsToTransform.push({type: 'blueSoldier', ball: ball, array: 'blue', originalIndex: index});
            }
        });

        redBalls.forEach((ball, index) => {
            if (ball && ball.active) {
                // 包括掉袋的球，它们也要变身参与战斗
                allBallsToTransform.push({type: 'redSoldier', ball: ball, array: 'red', originalIndex: index});
            }
        });

        blackBalls.forEach((ball, index) => {
            if (ball && ball.active) {
                // 包括掉袋的球，它们也要变身参与战斗
                allBallsToTransform.push({type: 'redLeader', ball: ball, array: 'black', originalIndex: index});
            }
        });

        // 逐个变身函数
        const transformNext = () => {
            if (transformIndex >= allBallsToTransform.length) {
                console.log('所有球变身完成！');
                isTransforming = false; // 变身完成，恢复武器位置更新

                // 变身完成后启动战斗
                this.time.delayedCall(200, () => {
                    startCombat.call(this);
                });
                return;
            }

            const transformData = allBallsToTransform[transformIndex];
            transformSingleUnit.call(this, transformData);
            transformIndex++;

            // 延迟100ms后变身下一个
            this.time.delayedCall(100, transformNext);
        };

        // 开始变身
        transformNext();
    }

    // 变身单个单位
    function transformSingleUnit(transformData) {
        const {type, ball, array, originalIndex} = transformData;

        if (type === 'king') {
            transformToKing.call(this, ball);
        } else if (type === 'blueSoldier') {
            transformToBlueSoldier.call(this, ball, array, originalIndex);
        } else if (type === 'redSoldier') {
            transformToRedSoldier.call(this, ball, array, originalIndex);
        } else if (type === 'redLeader') {
            transformToRedLeader.call(this, ball, array, originalIndex);
        }
    }

    // 变身为国王
    function transformToKing(ball) {
        if (!ball || !ball.active) return;

        const kingX = ball.x;
        const kingY = ball.y;

        // 安全销毁原白球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying king ball:', error);
        }

        // 创建新的国王角色（仅图像，无物理效果）
        const king = this.add.image(kingX, kingY, 'king');
        king.setScale(0.5);
        king.setDepth(5);
        king.setRotation(0); // 确保不倾斜

        // 标记为非物理对象，战斗开始前不能移动
        king.isPhysicsEnabled = false;

        // 添加国王武器
        const kingWeapon = this.add.image(kingX, kingY, 'kingWeapon');
        kingWeapon.setScale(0.5);
        kingWeapon.setDepth(6);
        kingWeapon.setRotation(0);

        // 添加血条 - 国王的血条更大
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(kingX - 20, kingY - 30, 40, 5);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00);
        healthBar.fillRect(kingX - 20, kingY - 30, 40, 5);
        healthBar.setDepth(11);

        king.weapon = kingWeapon;
        king.healthBarBg = healthBarBg;
        king.healthBar = healthBar;
        king.maxHealth = 5; // 国王血量更多
        king.health = 5;
        king.attackPower = 2; // 国王攻击力更高
        king.attackRange = 150; // 国王攻击范围更远
        king.lastAttackTime = 0;
        king.attackCooldown = 800; // 国王攻击更快
        king.isAttacking = false;
        cueBall = king;

        console.log('国王变身完成（无物理效果）');
    }

    // 变身为蓝色小兵
    function transformToBlueSoldier(ball, array, originalIndex) {
        if (!ball || !ball.active) return;

        const soldierX = ball.x;
        const soldierY = ball.y;

        // 安全销毁原球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying blue soldier ball:', error);
        }

        // 创建新的小兵角色（仅图像，无物理效果）
        const soldier = this.add.image(soldierX, soldierY, 'blueSoldier');
        soldier.setScale(0.2);
        soldier.setDepth(5);
        soldier.setRotation(0); // 确保不倾斜
        soldier.label = 'blueBall';

        // 标记为非物理对象，战斗开始前不能移动
        soldier.isPhysicsEnabled = false;

        // 添加武器在身体右侧中间
        const weapon = this.add.image(soldierX + 8, soldierY, 'weapon');
        weapon.setScale(0.2);
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - 小兵血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00); // 蓝方用绿色血条
        healthBar.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5; // 小兵血量提升到5
        soldier.health = 5;
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = blueBalls.indexOf(ball);
        if (currentIndex > -1) {
            blueBalls[currentIndex] = soldier;
        } else {
            blueBalls.push(soldier);
        }

        console.log('蓝色小兵变身完成（无物理效果）');
    }

    // 变身为红色小兵
    function transformToRedSoldier(ball, array, originalIndex) {
        if (!ball || !ball.active) return;

        const soldierX = ball.x;
        const soldierY = ball.y;

        // 安全销毁原球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying red soldier ball:', error);
        }

        // 创建新的红色小兵角色（仅图像，无物理效果）
        const soldier = this.add.image(soldierX, soldierY, 'redSoldier');
        soldier.setScale(0.2);
        soldier.setDepth(5);
        soldier.setRotation(0); // 确保不倾斜
        soldier.label = 'redBall';

        // 标记为非物理对象，战斗开始前不能移动
        soldier.isPhysicsEnabled = false;

        // 添加武器在身体左侧中间
        const weapon = this.add.image(soldierX - 8, soldierY, 'weapon');
        weapon.setScale(0.2);
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - 红方小兵血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000); // 红方用红色血条
        healthBar.fillRect(soldierX - 12, soldierY - 18, 24, 4);
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5; // 小兵血量提升到5
        soldier.health = 5;
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = redBalls.indexOf(ball);
        if (currentIndex > -1) {
            redBalls[currentIndex] = soldier;
        } else {
            redBalls.push(soldier);
        }

        console.log('红色小兵变身完成（无物理效果）');
    }

    // 变身为红色将领
    function transformToRedLeader(ball, array, originalIndex) {
        if (!ball || !ball.active) return;

        const leaderX = ball.x;
        const leaderY = ball.y;

        // 安全销毁原球（包括其物理体）
        try {
            ball.destroy();
        } catch (error) {
            console.warn('Error destroying red leader ball:', error);
        }

        // 创建新的红色将领角色（仅图像，无物理效果）
        const leader = this.add.image(leaderX, leaderY, 'redLeader');
        leader.setScale(0.5);
        leader.setDepth(5);
        leader.setRotation(0); // 确保不倾斜
        leader.label = 'blackBall';

        // 标记为非物理对象，战斗开始前不能移动
        leader.isPhysicsEnabled = false;

        // 添加武器在身体左侧中间
        const weapon = this.add.image(leaderX - 8, leaderY, 'weapon');
        weapon.setScale(0.5);
        weapon.setDepth(6);
        weapon.setRotation(0);

        // 添加血条 - Boss血条（更大更显眼）
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(leaderX - 25, leaderY - 30, 50, 8); // Boss血条更大
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000); // Boss用红色血条
        healthBar.fillRect(leaderX - 25, leaderY - 30, 50, 8);
        healthBar.setDepth(11);

        leader.weapon = weapon;
        leader.healthBarBg = healthBarBg;
        leader.healthBar = healthBar;
        leader.maxHealth = 20; // Boss血量大幅提升到20
        leader.health = 20;
        leader.attackPower = 3; // Boss攻击力也提升
        leader.attackRange = 60; // Boss攻击范围更远
        leader.lastAttackTime = 0;
        leader.attackCooldown = 1000; // Boss攻击更快
        leader.isAttacking = false;

        // 找到原球在数组中的位置并替换
        const currentIndex = blackBalls.indexOf(ball);
        if (currentIndex > -1) {
            blackBalls[currentIndex] = leader;
        } else {
            blackBalls.push(leader);
        }

        console.log('红色将领变身完成（无物理效果）');
    }

    // 启动战斗
    function startCombat() {
        this.statusText.setText('战斗开始！');
        this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });

        // 确保战斗模式已启用
        battleMode = true;

        // 重置战斗管理器并设置游戏场景引用
        battleManager = new BattleManager();
        battleManager.gameScene = this; // 设置场景引用，用于动画

        // 注册所有战斗单位到战斗管理器
        try {
            // 蓝方阵营：白球（国王）+ 蓝球
            if (cueBall && cueBall.health > 0) {
                battleManager.registerUnit(cueBall, 'blue');
                cueBall.isCombatReady = true;
                console.log('注册蓝方国王（白球）');
            }

            blueBalls.forEach(unit => {
                if (unit && unit.health > 0 && !unit.isInPocket) {
                    battleManager.registerUnit(unit, 'blue');
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册蓝方小兵（蓝球）: ${blueBalls.length}个`);

            // 红方阵营：黑球 + 红球
            blackBalls.forEach(unit => {
                if (unit && unit.health > 0 && !unit.isInPocket) {
                    battleManager.registerUnit(unit, 'red'); // 黑球属于红方
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方将领（黑球）: ${blackBalls.length}个`);

            redBalls.forEach(unit => {
                if (unit && unit.health > 0 && !unit.isInPocket) {
                    battleManager.registerUnit(unit, 'red');
                    unit.isCombatReady = true;
                }
            });
            console.log(`注册红方小兵（红球）: ${redBalls.length}个`);

            const stats = battleManager.getBattleStats();
            console.log(`战斗启动！蓝方（白球+蓝球）:${stats.blue}个单位, 红方（黑球+红球）:${stats.red}个单位`);
            console.log(`战斗模式状态: ${battleMode}, 变身状态: ${isTransforming}`);

            // 检查单方是否没有单位，直接判定胜负
            if (stats.blue === 0 && stats.red === 0) {
                this.statusText.setText('双方都没有单位！平局');
                this.statusText.setStyle({ fill: '#ffff00', fontSize: '20px' });
                battleMode = false;
                return;
            } else if (stats.blue === 0) {
                this.statusText.setText('蓝方没有单位！红方获胜');
                this.statusText.setStyle({ fill: '#ff0000', fontSize: '20px' });
                battleMode = false;
                return;
            } else if (stats.red === 0) {
                this.statusText.setText('红方没有单位！蓝方获胜');
                this.statusText.setStyle({ fill: '#0000ff', fontSize: '20px' });
                battleMode = false;
                return;
            }

            // 双方都有单位，正常开始战斗
            console.log('双方都有单位，战斗管理器开始工作...');

        } catch (error) {
            console.warn('Error starting combat:', error);
        }
    }

    // 旧的攻击系统已被战斗管理器替代
    // 保留一些辅助函数供战斗管理器使用

    // 寻找最近的敌人
    function findNearestEnemy(attacker, enemies) {
        let nearestEnemy = null;
        let minDistance = Infinity;

        enemies.forEach(enemy => {
            // 确保敌人存在、活跃、有位置、有血量且没有正在死亡
            if (enemy && enemy.active && enemy.x !== undefined && enemy.health > 0 && !enemy.isDying) {
                const distance = Phaser.Math.Distance.Between(attacker.x, attacker.y, enemy.x, enemy.y);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemy;
                }
            }
        });

        return nearestEnemy;
    }

    // 移动向敌人 - 不使用物理效果，直接修改位置
    function moveTowardsEnemy(unit, enemy) {
        if (!enemy || unit.isAttacking || !unit.isCombatReady) return;

        // 计算移动方向
        const dx = enemy.x - unit.x;
        const dy = enemy.y - unit.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            // 标准化方向向量
            const normalizedDx = dx / distance;
            const normalizedDy = dy / distance;

            // 直接修改位置，不使用物理速度
            const moveSpeed = 1.5; // 每帧移动的像素数
            const newX = unit.x + normalizedDx * moveSpeed;
            const newY = unit.y + normalizedDy * moveSpeed;

            // 确保不移出边界
            const boundedX = Math.max(70, Math.min(680, newX));
            const boundedY = Math.max(200, Math.min(1200, newY));

            unit.setPosition(boundedX, boundedY);
        }
    }

    // 攻击敌人
    function attackEnemy(attacker, target) {
        // 检查攻击者和目标的状态
        if (!attacker || !attacker.weapon || attacker.isAttacking || attacker.health <= 0 || attacker.isDying) return;
        if (!target || target.health <= 0 || target.isDying || !target.active) return;

        // 计算攻击方向
        const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

        // 开始攻击动画
        attacker.isAttacking = true;

        // 武器攻击动画：更明显的摆动幅度
        const baseAngle = angle;
        const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
        const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

        // 人物抖动动画
        const originalX = attacker.x;
        const originalY = attacker.y;

        // 第一段动画：反向预备动作
        this.tweens.add({
            targets: attacker.weapon,
            rotation: swingAngle1,
            duration: 100,
            ease: 'Power2',
            onComplete: () => {
                // 第二段动画：快速攻击到60度 + 人物抖动
                this.tweens.add({
                    targets: attacker.weapon,
                    rotation: swingAngle2,
                    duration: 120,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        // 第三段动画：回到中间位置
                        this.tweens.add({
                            targets: attacker.weapon,
                            rotation: baseAngle + Phaser.Math.DegToRad(20),
                            duration: 100,
                            ease: 'Power2',
                            onComplete: () => {
                                // 最后回到初始角度
                                this.tweens.add({
                                    targets: attacker.weapon,
                                    rotation: 0,
                                    duration: 150,
                                    ease: 'Power1',
                                    onComplete: () => {
                                        attacker.isAttacking = false;
                                    }
                                });
                            }
                        });
                    }
                });

                // 人物抖动效果
                this.tweens.add({
                    targets: attacker,
                    x: originalX + Phaser.Math.Between(-3, 3),
                    y: originalY + Phaser.Math.Between(-3, 3),
                    duration: 50,
                    yoyo: true,
                    repeat: 2,
                    onComplete: () => {
                        // 恢复原位置
                        attacker.setPosition(originalX, originalY);
                    }
                });

                // 在快速攻击时造成伤害
                dealDamageToUnit.call(this, target, attacker.attackPower);
            }
        });
    }

    // 对单位造成伤害 - 简化版，主要用于特效
    function dealDamageToUnit(target, damage) {
        if (!target || target.health <= 0 || target.isDying) return;

        // 创建伤害数字特效
        try {
            const damageText = this.add.text(target.x, target.y - 30, `-${damage}`, {
                fontSize: '12px',
                fill: '#ff0000',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: damageText,
                y: target.y - 50,
                alpha: 0,
                duration: 800,
                onComplete: () => damageText.destroy()
            });
        } catch (error) {
            console.warn('Error creating damage effect:', error);
        }

        // 实际伤害处理由战斗管理器负责
        // 这里只负责视觉效果
    }

    // 销毁单位
    function destroyUnit(unit) {
        try {
            // 防止重复销毁
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 销毁武器
            if (unit.weapon && unit.weapon.active) {
                unit.weapon.destroy();
                unit.weapon = null;
            }

            // 销毁血条
            if (unit.healthBar && unit.healthBar.active) {
                unit.healthBar.destroy();
                unit.healthBar = null;
            }
            if (unit.healthBarBg && unit.healthBarBg.active) {
                unit.healthBarBg.destroy();
                unit.healthBarBg = null;
            }

            // 从数组中移除
            if (unit === cueBall) {
                cueBall = null;
            } else {
                // 确保数组存在
                ensureArraysInitialized();

                const blueIndex = blueBalls.indexOf(unit);
                if (blueIndex > -1) {
                    blueBalls.splice(blueIndex, 1);
                    try {
                        console.log(`移除蓝色小兵，剩余: ${blueBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging blue unit removal');
                    }
                }

                const redIndex = redBalls.indexOf(unit);
                if (redIndex > -1) {
                    redBalls.splice(redIndex, 1);
                    try {
                        console.log(`移除红色小兵，剩余: ${redBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging red unit removal');
                    }
                }

                const blackIndex = blackBalls.indexOf(unit);
                if (blackIndex > -1) {
                    blackBalls.splice(blackIndex, 1);
                    try {
                        console.log(`移除红色将领，剩余: ${blackBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging black unit removal');
                    }
                }
            }

            // 清理数组中的null值
            try {
                if (Array.isArray(blueBalls)) {
                    blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(redBalls)) {
                    redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(blackBalls)) {
                    blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
            } catch (e) {
                console.warn('Error filtering arrays:', e);
            }

            // 销毁单位
            if (unit && unit.active) {
                unit.destroy();
            }
        } catch (error) {
            console.warn('Error destroying unit:', error);
        }
    }

    // 检查战斗结果
    function checkBattleResult() {
        try {
            // 使用战斗管理器获取统计信息
            const stats = battleManager.getBattleStats();
            const blueCount = stats.blue;  // 蓝方：玩家（白球国王 + 蓝球小兵）
            const redCount = stats.red;    // 红方：电脑（黑球Boss + 红球小兵）

            console.log(`当前战况 - 玩家（蓝方）:${blueCount}, 电脑（红方）:${redCount}`);

            if (redCount === 0 && blueCount > 0) {
                // 玩家获胜
                battleMode = false;
                showLevelEndScreen.call(this, true); // 显示胜利界面
                console.log('战斗结束：玩家获胜！');
            } else if (blueCount === 0 && redCount > 0) {
                // 电脑获胜
                battleMode = false;
                showLevelEndScreen.call(this, false); // 显示失败界面
                console.log('战斗结束：电脑获胜！');
            } else if (blueCount === 0 && redCount === 0) {
                // 平局（重新开始）
                battleMode = false;
                showLevelEndScreen.call(this, false); // 平局算失败
                console.log('战斗结束：平局！');
            }
            // 如果双方都还有单位，继续战斗，不做任何处理
        } catch (error) {
            console.warn('Error in checkBattleResult:', error);
        }
    }

    // 显示关卡结束界面
    function showLevelEndScreen(isVictory) {
        // 创建黑色半透明背景
        const overlay = this.add.graphics();
        overlay.fillStyle(0x000000, 0.7);
        overlay.fillRect(0, 0, 750, 1334);
        overlay.setDepth(100);

        // 创建结果面板
        const panelWidth = 400;
        const panelHeight = 300;
        const panelX = 375;
        const panelY = 667;

        const panel = this.add.graphics();
        panel.fillStyle(0xffffff, 1);
        panel.fillRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.lineStyle(4, isVictory ? 0x00ff00 : 0xff0000, 1);
        panel.strokeRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.setDepth(101);

        // 大emoji表情
        const emoji = isVictory ? '😊' : '😢';
        const emojiText = this.add.text(panelX, panelY - 80, emoji, {
            fontSize: '80px',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 成功/失败文字
        const resultText = isVictory ? '关卡成功！' : '关卡失败！';
        const resultColor = isVictory ? '#00ff00' : '#ff0000';
        const textResult = this.add.text(panelX, panelY - 10, resultText, {
            fontSize: '32px',
            fill: resultColor,
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 下一关按钮
        const buttonText = isVictory ? '下一关' : '重新开始';
        const button = this.add.graphics();
        button.fillStyle(isVictory ? 0x00aa00 : 0xaa0000, 1);
        button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.lineStyle(2, 0x000000, 1);
        button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.setDepth(102);
        button.setInteractive(new Phaser.Geom.Rectangle(panelX - 80, panelY + 50, 160, 50), Phaser.Geom.Rectangle.Contains);

        const buttonTextObj = this.add.text(panelX, panelY + 75, buttonText, {
            fontSize: '24px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(103);

        // 按钮点击事件
        button.on('pointerdown', () => {
            if (isVictory) {
                // 下一关：增加关卡数并重新开始
                currentLevel++;
                this.scene.restart();
            } else {
                // 重新开始当前关卡
                this.scene.restart();
            }
        });

        // 按钮悬停效果
        button.on('pointerover', () => {
            button.clear();
            button.fillStyle(isVictory ? 0x00cc00 : 0xcc0000, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        button.on('pointerout', () => {
            button.clear();
            button.fillStyle(isVictory ? 0x00aa00 : 0xaa0000, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        console.log(`显示关卡结束界面: ${isVictory ? '胜利' : '失败'}`);
    }

    // 更新武器和血条位置，让它们跟随角色移动
    function updateWeaponPositions() {
        // 如果正在变身中，跳过更新
        if (isTransforming) return;
        // 更新国王的武器和血条位置
        if (cueBall && cueBall.active && cueBall.weapon && cueBall.weapon.active && cueBall.x !== undefined) {
            try {
                cueBall.weapon.setPosition(cueBall.x, cueBall.y);
                if (cueBall.healthBarBg && cueBall.healthBarBg.active) {
                    cueBall.healthBarBg.clear();
                    cueBall.healthBarBg.fillStyle(0x000000);
                    cueBall.healthBarBg.fillRect(cueBall.x - 15, cueBall.y - 25, 30, 4);
                }
                if (cueBall.healthBar && cueBall.healthBar.active) {
                    cueBall.healthBar.clear();
                    cueBall.healthBar.fillStyle(0x00ff00);
                    const healthPercent = cueBall.health / cueBall.maxHealth;
                    cueBall.healthBar.fillRect(cueBall.x - 15, cueBall.y - 25, 30 * healthPercent, 4);
                }
            } catch (error) {
                console.warn('Error updating king position:', error);
            }
        }

        // 更新蓝色小兵的武器和血条位置（右侧中间）
        blueBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.weapon && soldier.weapon.active && soldier.x !== undefined) {
                try {
                    soldier.weapon.setPosition(soldier.x + 8, soldier.y);
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 10, soldier.y - 15, 20, 3);
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0x00ff00);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 10, soldier.y - 15, 20 * healthPercent, 3);
                    }
                } catch (error) {
                    console.warn(`Error updating blue soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色小兵的武器和血条位置（左侧中间）
        redBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.weapon && soldier.weapon.active && soldier.x !== undefined) {
                try {
                    soldier.weapon.setPosition(soldier.x - 8, soldier.y);
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 10, soldier.y - 15, 20, 3);
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0xff0000);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 10, soldier.y - 15, 20 * healthPercent, 3);
                    }
                } catch (error) {
                    console.warn(`Error updating red soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色将领的武器和血条位置（左侧中间）
        blackBalls.filter(leader => leader !== null && leader !== undefined).forEach((leader, index) => {
            if (leader && leader.active && leader.weapon && leader.weapon.active && leader.x !== undefined) {
                try {
                    leader.weapon.setPosition(leader.x - 8, leader.y);
                    if (leader.healthBarBg && leader.healthBarBg.active) {
                        leader.healthBarBg.clear();
                        leader.healthBarBg.fillStyle(0x000000);
                        leader.healthBarBg.fillRect(leader.x - 15, leader.y - 20, 30, 4);
                    }
                    if (leader.healthBar && leader.healthBar.active) {
                        leader.healthBar.clear();
                        leader.healthBar.fillStyle(0xff0000);
                        const healthPercent = leader.health / leader.maxHealth;
                        leader.healthBar.fillRect(leader.x - 15, leader.y - 20, 30 * healthPercent, 4);
                    }
                } catch (error) {
                    console.warn(`Error updating red leader ${index} position:`, error);
                }
            }
        });
    }

    // 处理待销毁的球 - 防止连锁反应bug
    function processBallDestruction() {
        try {
            // 确保待销毁数组存在
            if (!Array.isArray(ballsToDestroy)) ballsToDestroy = [];
            if (ballsToDestroy.length === 0) return;

            // 确保球数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

        // 每帧只处理一个球，避免同时销毁多个球
        const ballToDestroy = ballsToDestroy.shift();

        if (ballToDestroy && ballToDestroy.active && !ballToDestroy.isInPocket) {
            // 从对应数组中移除
            if (ballToDestroy.label === 'blueBall') {
                const index = blueBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blueBalls.splice(index, 1);
                    console.log('销毁蓝球，剩余蓝球数量:', blueBalls.length);
                }
            } else if (ballToDestroy.label === 'redBall') {
                const index = redBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    redBalls.splice(index, 1);
                    console.log('销毁红球，剩余红球数量:', redBalls.length);
                }
            } else if (ballToDestroy.label === 'blackBall') {
                const index = blackBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blackBalls.splice(index, 1);
                    console.log('销毁黑球，剩余黑球数量:', blackBalls.length);
                }
            }

            // 如果球有武器，也要销毁武器
            if (ballToDestroy.weapon) {
                ballToDestroy.weapon.destroy();
            }

            // 创建销毁特效
            const destroyEffect = this.add.text(ballToDestroy.x, ballToDestroy.y, '撞击!', {
                fontSize: '14px',
                fill: '#ffff00',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: destroyEffect,
                y: ballToDestroy.y - 40,
                alpha: 0,
                duration: 800,
                onComplete: () => destroyEffect.destroy()
            });

            // 销毁球
            ballToDestroy.destroy();
        }
        } catch (error) {
            console.warn('Error in processBallDestruction:', error);
        }
    }

    // 更新血条
    function updateHealthBars() {
        // 更新玩家血条
        this.playerHealthBar.clear();
        const playerHealthPercent = playerHealth / maxPlayerHealth;
        this.playerHealthBar.fillStyle(0x3498db);
        this.playerHealthBar.fillRoundedRect(100, 45, 150 * playerHealthPercent, 15, 7);

        // 更新敌方血条
        this.enemyHealthBar.clear();
        const enemyHealthPercent = enemyHealth / maxEnemyHealth;
        this.enemyHealthBar.fillStyle(0xe74c3c);
        this.enemyHealthBar.fillRoundedRect(480, 45, 150 * enemyHealthPercent, 15, 7);

        // 更新血条数值显示
        this.playerHealthText.setText(`${playerHealth}/${maxPlayerHealth}`);
        this.enemyHealthText.setText(`${enemyHealth}/${maxEnemyHealth}`);
    }

    // 检查游戏状态
    function checkGameState() {
        try {
            // 确保数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];

            if (redBalls.length === 0 && blueBalls.length > 0) {
                this.statusText.setText('红球全部清除 - 蓝方获胜！');
                this.statusText.setStyle({ fill: '#0000ff', fontSize: '18px' });
            } else if (blueBalls.length === 0 && redBalls.length > 0) {
                this.statusText.setText('蓝球全部清除 - 红方获胜！');
                this.statusText.setStyle({ fill: '#ff0000', fontSize: '18px' });
            } else if (redBalls.length === 0 && blueBalls.length === 0) {
                this.statusText.setText('所有球都清除了 - 平局！');
                this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });
            }
        } catch (error) {
            console.warn('Error in checkGameState:', error);
        }
    }









    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
