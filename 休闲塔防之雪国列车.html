
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗 - 物理碰撞版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
let isPaused = false;
let roguelikePopup = null;
let roguelikeShownInWave = -1; // 记录已经弹出肉鸽弹窗的波数
let snowflakes = []; // 雪花数组
let maxSnowflakes = 15; // 最大雪花数量，防止卡顿
let centralGear = null; // 中心传动轮
    let cityWalls = []; // 城墙数组
    let buildings = []; // 建筑数组（城内的功能建筑）
    let trainTowers = []; // 火车防御塔数组
    let train = null; // 火车对象
    let trainPath = []; // 火车路径
    let monsters = [];
    let bullets = []; // 子弹数组
    let cityHealth = 100, maxCityHealth = 100;
    let currentLevel = 1;
    let currentWave = 1;
    let totalMonstersInWave = 3;
    let gamePhase = 'day'; // 'day'(白天建造) 或 'night'(晚上防御)
    let dayNightCycle = 1; // 昼夜循环计数
    let buildingsToPlace = 10;
    let attemptsLeft = 10;
let pendingBuilding = null; // 待放置建筑对象

    let buildingInserted = 0;
    let currentBuildingIndex = 1;
    let buildingOptions = [1,2,3]; // 当前备选建筑编号
    let selectedBuildingIndex = 0; // 当前选中的备选建筑索引（0-2）
    let buildingOptionSprites = []; // 备选建筑sprite对象
let buildingShopSprites = [];
// 白天建造的齿轮工厂
let buildingShopData = [
    { id: 1, price: 80, desc: '速度齿轮', emoji: '⚙️', effect: () => { if(train) train.speed += 0.01; }, type: 'speed' },
    { id: 2, price: 120, desc: '攻击齿轮', emoji: '⚙️', effect: () => { trainTowers.forEach(t => { if(!t.isHead) t.damage += 15; }); }, type: 'attack' },
    { id: 3, price: 100, desc: '防御齿轮', emoji: '⚙️', effect: () => { maxCityHealth += 100; cityHealth += 100; }, type: 'defense' }
];

// 火车防御塔数据
let towerShopData = [
    { id: 1, price: 80, desc: '弓箭塔', emoji: '🏹', damage: 30, range: 120, type: 'arrow' },
    { id: 2, price: 150, desc: '炮塔', emoji: '💣', damage: 60, range: 100, type: 'cannon' },
    { id: 3, price: 200, desc: '激光塔', emoji: '⚡', damage: 45, range: 150, type: 'laser' }
];
let playerGold = 100000;
let selectedShopBuilding = null;

    const CITY_WIDTH = 400; // 城市宽度
    const CITY_HEIGHT = 400; // 城市高度
    const WALL_THICKNESS = 30; // 城墙厚度
    let BUILDING_DAMAGE = 25; // 定义建筑基础伤害
    const MONSTER_HIT_COOLDOWN = 500; // 怪物受伤冷却时间 (毫秒)
    const TRAIN_DAMAGE = 40; // 火车伤害

    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background3.png');
    }

    // 创建游戏场景
    function create() {
        // 重置状态变量
        cityHealth = 800; maxCityHealth = 800;
        currentLevel = 1; currentWave = 1;
        totalMonstersInWave = 10;
        gamePhase = 'day'; // 从白天开始
        dayNightCycle = 1;
        // 前面关卡建筑数量减少
        if(currentLevel<=2){
            buildingsToPlace = 6;
            attemptsLeft = 6;
        }else if(currentLevel<=4){
            buildingsToPlace = 8;
            attemptsLeft = 8;
        }else{
            buildingsToPlace = 10;
            attemptsLeft = 10;
        }
        buildingInserted = 0;
        buildings = []; trainTowers = []; monsters = []; bullets = []; pendingBuilding = null;
        clearSnowflakes(); // 清理雪花
        centralGear = null; // 重置中心动力齿轮引用

        // 创建背景
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334);

        createUI.call(this);
        createCityDefense.call(this);

        this.input.on('pointerdown', handleBuildingPlace, this);
        this.input.on('pointermove', handleBuildingDrag, this);
    }

    // 创建波次怪物
    function createWave() {
        if (gamePhase !== 'night') return;

        monsters = [];
        for (let i = 0; i < totalMonstersInWave; i++) {
            const monsterType = (i % 15) + 1;
            let startX, startY;

            // 怪物从城墙外围生成
            const side = i % 4; // 0上 1右 2下 3左
            const cityX = 375;
            const cityY = 600; // 向下移动100px

            switch(side) {
                case 0: // 上方
                    startX = cityX + (Math.random() - 0.5) * (CITY_WIDTH + 200);
                    startY = cityY - CITY_HEIGHT/2 - 100 - Math.random() * 100;
                    break;
                case 1: // 右方
                    startX = cityX + CITY_WIDTH/2 + 100 + Math.random() * 100;
                    startY = cityY + (Math.random() - 0.5) * (CITY_HEIGHT + 200);
                    break;
                case 2: // 下方
                    startX = cityX + (Math.random() - 0.5) * (CITY_WIDTH + 200);
                    startY = cityY + CITY_HEIGHT/2 + 100 + Math.random() * 100;
                    break;
                case 3: // 左方
                    startX = cityX - CITY_WIDTH/2 - 100 - Math.random() * 100;
                    startY = cityY + (Math.random() - 0.5) * (CITY_HEIGHT + 200);
                    break;
            }

            startX = Math.max(50, Math.min(700, startX));
            startY = Math.max(100, Math.min(1200, startY));

            let monster = this.add.circle(startX, startY, 15, 0xFF0000).setStrokeStyle(2, 0x000000);
            monster.health = 50 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isMoving = false;
            monster.lastHitTime = 0;
            monsters.push(monster);
        }
    }

    // 创建UI界面
    function createUI() {
        this.levelText = this.add.text(50, 20, `关卡: ${currentLevel}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.waveText = this.add.text(50, 60, `波次: ${currentWave}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.healthText = this.add.text(50, 100, `生命: ${cityHealth}/${maxCityHealth}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.gamePhaseText = this.add.text(50, 140, `时间: ${gamePhase === 'day' ? '☀️白天 - 建造阶段' : '🌙夜晚 - 防御阶段'}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.goldText = this.add.text(50, 180, `金币: ${playerGold}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.buildingText = this.add.text(50, 220, `建筑: ${buildingInserted}/${buildingsToPlace}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.dayNightText = this.add.text(50, 260, `昼夜: 第${dayNightCycle}轮`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });

        // 添加切换到夜晚的按钮（放大并向上移动50px）
        this.startNightButton = this.add.text(375, 950, '开始夜晚', { fontSize: '32px', fill: '#ffffff', backgroundColor: '#e74c3c', padding: { x: 30, y: 15 } }).setOrigin(0.5);
        this.startNightButton.setInteractive().on('pointerdown', () => {
            if (gamePhase === 'day') {
                startNightPhase.call(this);
            }
        });

        this.monsterHealthBars = [];
    }

    // 绘制铁轨围墙（简化版）
    function drawRailway(graphics, cityX, cityY) {
        const railWidth = 4; // 铁轨宽度
        const railColor = 0xC0C0C0; // 银色
        const tieColor = 0x8B4513; // 棕色枕木
        const tieWidth = 8;
        const tieHeight = WALL_THICKNESS;
        const tieSpacing = 20; // 枕木间距
        const cornerRadius = 15; // 圆角半径

        // 计算铁轨位置
        const left = cityX - CITY_WIDTH/2;
        const right = cityX + CITY_WIDTH/2;
        const top = cityY - CITY_HEIGHT/2;
        const bottom = cityY + CITY_HEIGHT/2;

        // 绘制枕木（棕色），避免拐角处重叠
        graphics.fillStyle(tieColor);

        // 上边枕木（避开拐角）
        for(let x = left + cornerRadius; x <= right - cornerRadius; x += tieSpacing) {
            graphics.fillRect(x - tieWidth/2, top - tieHeight/2, tieWidth, tieHeight);
        }

        // 下边枕木（避开拐角）
        for(let x = left + cornerRadius; x <= right - cornerRadius; x += tieSpacing) {
            graphics.fillRect(x - tieWidth/2, bottom - tieHeight/2, tieWidth, tieHeight);
        }

        // 左边枕木（避开拐角）
        for(let y = top + cornerRadius; y <= bottom - cornerRadius; y += tieSpacing) {
            graphics.fillRect(left - tieHeight/2, y - tieWidth/2, tieHeight, tieWidth);
        }

        // 右边枕木（避开拐角）
        for(let y = top + cornerRadius; y <= bottom - cornerRadius; y += tieSpacing) {
            graphics.fillRect(right - tieHeight/2, y - tieWidth/2, tieHeight, tieWidth);
        }

        // 绘制两个圆角矩形铁轨框
        graphics.lineStyle(railWidth, railColor);

        // 外圈铁轨
        graphics.strokeRoundedRect(
            left - railWidth - 3,
            top - railWidth - 3,
            CITY_WIDTH + (railWidth + 3) * 2,
            CITY_HEIGHT + (railWidth + 3) * 2,
            cornerRadius
        );

        // 内圈铁轨
        graphics.strokeRoundedRect(
            left + 3,
            top + 3,
            CITY_WIDTH - 6,
            CITY_HEIGHT - 6,
            cornerRadius
        );
    }

    // 创建城市防御系统
    function createCityDefense() {
        const cityX = 375;
        const cityY = 600; // 向下移动100px

        // 创建铁轨围墙
        const wallGraphics = this.add.graphics();

        // 绘制铁轨
        drawRailway(wallGraphics, cityX, cityY);

        // 内部区域（浅绿色）
        wallGraphics.fillStyle(0x90EE90, 0.3);
        wallGraphics.fillRect(cityX - CITY_WIDTH/2 + WALL_THICKNESS/2, cityY - CITY_HEIGHT/2 + WALL_THICKNESS/2,
                             CITY_WIDTH - WALL_THICKNESS, CITY_HEIGHT - WALL_THICKNESS);

        // 创建中心动力齿轮
        createCentralPowerGear.call(this, cityX, cityY);

        // 绘制城内格子（固定4x4）
        const gridCols = 4; // 固定4列
        const gridRows = 4; // 固定4行
        const gridSize = Math.min((CITY_WIDTH - WALL_THICKNESS) / gridCols, (CITY_HEIGHT - WALL_THICKNESS) / gridRows) * 0.95; // 格子稍微大一点，减少缝隙
        wallGraphics.lineStyle(1, 0x666666, 0.5);

        // 计算城内区域
        const innerLeft = cityX - CITY_WIDTH/2 + WALL_THICKNESS/2;
        const innerRight = cityX + CITY_WIDTH/2 - WALL_THICKNESS/2;
        const innerTop = cityY - CITY_HEIGHT/2 + WALL_THICKNESS/2;
        const innerBottom = cityY + CITY_HEIGHT/2 - WALL_THICKNESS/2;

        // 计算起始位置，使3x3格子居中
        const totalGridWidth = gridCols * gridSize;
        const totalGridHeight = gridRows * gridSize;
        const startX = cityX - totalGridWidth / 2;
        const startY = cityY - totalGridHeight / 2;

        // 垂直线
        for(let i = 0; i <= gridCols; i++) {
            const x = startX + i * gridSize;
            wallGraphics.moveTo(x, startY);
            wallGraphics.lineTo(x, startY + gridRows * gridSize);
        }

        // 水平线
        for(let i = 0; i <= gridRows; i++) {
            const y = startY + i * gridSize;
            wallGraphics.moveTo(startX, y);
            wallGraphics.lineTo(startX + gridCols * gridSize, y);
        }
        wallGraphics.strokePath();

        // 创建火车路径（沿着城墙）
        createTrainPath(cityX, cityY);

        // 创建火车
        createTrain.call(this);

        // 创建建筑商店
        createBuildingShop.call(this);
    }

    // 创建中心动力齿轮（占据一个具体格子）
    function createCentralPowerGear(cityX, cityY) {
        // 计算4x4格子系统
        const gridCols = 4;
        const gridRows = 4;
        const gridSize = Math.min((CITY_WIDTH - WALL_THICKNESS) / gridCols, (CITY_HEIGHT - WALL_THICKNESS) / gridRows) * 0.95;

        const totalGridWidth = gridCols * gridSize;
        const totalGridHeight = gridRows * gridSize;
        const startX = cityX - totalGridWidth / 2;
        const startY = cityY - totalGridHeight / 2;

        // 选择中心附近的格子作为动力齿轮位置（比如1,1格子）
        const powerGridCol = 1; // 第2列（0-3）
        const powerGridRow = 1; // 第2行（0-3）

        const gridX = startX + powerGridCol * gridSize + gridSize / 2;
        const gridY = startY + powerGridRow * gridSize + gridSize / 2;

        // 创建动力齿轮（使用普通齿轮样式但颜色不同）
        centralGear = createGearText(this, gridX, gridY, '⚙️', 100);
        centralGear.setTint(0x444444); // 深灰色表示动力齿轮

        // 添加到建筑数组
        buildings.push({
            sprite: centralGear,
            type: 'power', // 动力类型
            effect: () => {}, // 动力齿轮没有特殊效果
            isRotating: false,
            gridX: powerGridCol, // 具体格子位置
            gridY: powerGridRow,
            isPowerSource: true // 标记为动力源
        });

        // 增加建筑计数
        buildingInserted++;
    }

    // 创建建筑商店（白天显示功能建筑，夜晚隐藏）
    function createBuildingShop() {
        buildingShopSprites = [];

        // 夜晚时隐藏功能建筑商店
        if (gamePhase === 'night') {
            return;
        }

        let shopData = buildingShopData;
        let shopTitle = '功能建筑';

        // 商店半透明背景
        this.shopBackground = this.add.rectangle(375, 1200, 750, 300, 0x000000, 0.6).setOrigin(0.5);

        // 商店标题
        this.shopTitle = this.add.text(375, 1050, shopTitle, { fontSize: '28px', fill: '#ffffff', backgroundColor: '#34495e', padding: { x: 20, y: 10 } }).setOrigin(0.5);

        for(let i=0;i<3;i++){
            let x = 200 + i*180;
            let building = shopData[i];
            // 创建齿轮emoji（使用优化函数）
            let sprite = createGearText(this, x, 1200, building.emoji, 100);
            sprite.setInteractive();
            // 价格和描述
            let priceText = this.add.text(x, 1240, `💰${building.price}`, { fontSize: '20px', fill: '#ffd700' }).setOrigin(0.5);
            let descText = this.add.text(x, 1270, building.desc, { fontSize: '18px', fill: '#fff' }).setOrigin(0.5);

            // 添加拖拽功能（只在白天有效）
            sprite.on('pointerdown', (pointer, localX, localY, event) => {
                if(gamePhase==='day' && !pendingBuilding && playerGold >= building.price){
                    console.log('Building clicked!', building.desc); // 调试信息

                    // 创建拖拽中的齿轮（使用优化函数）
                    let dragBuilding = createGearText(this, sprite.x, sprite.y, building.emoji, 100);
                    dragBuilding.setAlpha(0.7);

                    // 设置拖拽状态
                    pendingBuilding = { sprite: dragBuilding, building, isDragging: true, shopIndex: i, buildingType: 'city' };

                    // 阻止事件冒泡
                    event.stopPropagation();
                }
            });
            buildingShopSprites.push({ sprite, priceText, descText });
        }
    }

// 创建火车路径（沿着城墙，顺时针方向）
function createTrainPath(cityX, cityY) {
    trainPath = [];
    const pathStep = 15; // 减小路径点间距，使移动更流畅

    // 上边（从左到右）
    const topY = cityY - CITY_HEIGHT/2;
    for(let x = cityX - CITY_WIDTH/2; x <= cityX + CITY_WIDTH/2; x += pathStep) {
        trainPath.push({ x: x, y: topY });
    }

    // 右边（从上到下）
    const rightX = cityX + CITY_WIDTH/2;
    for(let y = cityY - CITY_HEIGHT/2 + pathStep; y <= cityY + CITY_HEIGHT/2; y += pathStep) {
        trainPath.push({ x: rightX, y: y });
    }

    // 下边（从右到左）
    const bottomY = cityY + CITY_HEIGHT/2;
    for(let x = cityX + CITY_WIDTH/2 - pathStep; x >= cityX - CITY_WIDTH/2; x -= pathStep) {
        trainPath.push({ x: x, y: bottomY });
    }

    // 左边（从下到上）
    const leftX = cityX - CITY_WIDTH/2;
    for(let y = cityY + CITY_HEIGHT/2 - pathStep; y >= cityY - CITY_HEIGHT/2 + pathStep; y -= pathStep) {
        trainPath.push({ x: leftX, y: y });
    }
}

// 创建火车
function createTrain() {
    train = {
        segments: [],
        towers: [], // 每个车厢上的防御塔
        currentPathIndex: 0,
        targetPathIndex: 0,
        speed: 0.04, // 提高移动速度，使移动更流畅
        segmentCount: 5,
        moveProgress: 0, // 移动进度 0-1
        segmentSpacing: 4, // 车厢间距（路径点数）
        isMoving: false
    };

    // 创建火车车厢和防御塔
    for(let i = 0; i < train.segmentCount; i++) {
        if (i === 0) {
            // 第一个是火车头，只有图标，没有背景
            let segment = null; // 火车头没有背景
            train.segments.push(segment);

            let trainHead = this.add.text(0, 0, '🚂', { fontSize: '48px' }).setOrigin(0.5);
            train.towers.push({
                sprite: trainHead,
                damage: 0, // 火车头没有攻击力
                range: 0,
                lastAttack: 0,
                level: 1,
                type: 'train_head',
                carIndex: i,
                isHead: true
            });
        } else {
            // 其他车厢有背景和炮塔（车厢作为底座）
            let segment = this.add.rectangle(0, 0, 45, 30, 0x808080); // 改为灰色
            segment.setStrokeStyle(3, 0x000000);
            train.segments.push(segment);

            // 在车厢上绘制圆形炮台
            let cannonBase = this.add.circle(0, 0, 12, 0x666666);
            cannonBase.setStrokeStyle(2, 0x000000);

            // 在炮台中心绘制炮管（从炮台中间伸出）
            let cannonBarrel = this.add.rectangle(12, 0, 25, 6, 0x444444); // X偏移12，让炮管从炮台中心伸出
            cannonBarrel.setStrokeStyle(1, 0x000000);
            cannonBarrel.setOrigin(0, 0.5); // 设置原点为左中，这样炮管从炮台中心开始

            // 让车厢可以点击升级
            segment.setInteractive();
            segment.on('pointerdown', () => {
                if (gamePhase === 'day' && playerGold >= 100) {
                    upgradeTowerOnTrain(i);
                }
            });

            train.towers.push({
                sprite: cannonBarrel, // 炮管作为sprite
                base: cannonBase, // 炮台底座
                segment: segment, // 保存车厢引用
                damage: 40 + i * 5, // 每个车厢伤害递增
                range: 300 + i * 20, // 大幅增加射程，能覆盖整个城内
                lastAttack: 0,
                level: 1,
                type: 'train_cannon',
                carIndex: i,
                isHead: false,
                targetAngle: 0 // 炮塔瞄准角度
            });
        }
    }

    // 初始化火车位置
    updateTrainPosition();
}

// 刷新齿轮工厂商店
function refreshBuildingShop(index) {
    const gearTypes = [
        { emoji: '⚙️', desc: '速度齿轮', effect: () => { if(train) train.speed += 0.01; }, type: 'speed' },
        { emoji: '⚙️', desc: '攻击齿轮', effect: () => { trainTowers.forEach(t => { if(!t.isHead) t.damage += 15; }); }, type: 'attack' },
        { emoji: '⚙️', desc: '防御齿轮', effect: () => { maxCityHealth += 100; cityHealth += 100; }, type: 'defense' },
        { emoji: '⚙️', desc: '射程齿轮', effect: () => { trainTowers.forEach(t => { if(!t.isHead) t.range += 25; }); }, type: 'range' },
        { emoji: '⚙️', desc: '金币齿轮', effect: () => { playerGold += 150; }, type: 'gold' },
        { emoji: '⚙️', desc: '修复齿轮', effect: () => { cityHealth = Math.min(maxCityHealth, cityHealth + 200); }, type: 'repair' }
    ];

    let randomType = gearTypes[Phaser.Math.Between(0, gearTypes.length - 1)];
    let newPrice = Phaser.Math.Between(80, 180);
    let spriteObj = buildingShopSprites[index];
    spriteObj.sprite.setText(randomType.emoji);
    spriteObj.priceText.setText(`💰${newPrice}`);
    spriteObj.descText.setText(randomType.desc);

    // 更新数据
    buildingShopData[index] = { id: index, price: newPrice, desc: randomType.desc, emoji: randomType.emoji, effect: randomType.effect, type: randomType.type };

    // 重新绑定点击事件
    spriteObj.sprite.removeAllListeners();
    spriteObj.sprite.setInteractive();
    spriteObj.sprite.on('pointerdown', (pointer, localX, localY, event) => {
        let currentBuilding = buildingShopData[index];
        if(gamePhase==='day' && !pendingBuilding && playerGold >= currentBuilding.price){
            let dragBuilding = createGearText(this, spriteObj.sprite.x, spriteObj.sprite.y, currentBuilding.emoji, 100);
            dragBuilding.setAlpha(0.7);
            pendingBuilding = { sprite: dragBuilding, building: currentBuilding, isDragging: true, shopIndex: index, buildingType: 'city' };
            event.stopPropagation();
        }
    });
}

// 刷新防御塔商店
function refreshTowerShop(index) {
    const towerTypes = [
        { emoji: '🏹', desc: '弓箭塔', damage: 30, range: 120, type: 'arrow' },
        { emoji: '💣', desc: '炮塔', damage: 60, range: 100, type: 'cannon' },
        { emoji: '⚡', desc: '激光塔', damage: 45, range: 150, type: 'laser' },
        { emoji: '🔥', desc: '火焰塔', damage: 35, range: 80, type: 'fire' },
        { emoji: '❄️', desc: '冰霜塔', damage: 25, range: 110, type: 'ice' },
        { emoji: '⭐', desc: '星光塔', damage: 50, range: 130, type: 'star' }
    ];

    let randomType = towerTypes[Phaser.Math.Between(0, towerTypes.length - 1)];
    let newPrice = Phaser.Math.Between(80, 250);
    let spriteObj = buildingShopSprites[index];
    spriteObj.sprite.setText(randomType.emoji);
    spriteObj.priceText.setText(`💰${newPrice}`);
    spriteObj.descText.setText(randomType.desc);

    // 更新数据
    towerShopData[index] = { id: index, price: newPrice, desc: randomType.desc, emoji: randomType.emoji, damage: randomType.damage, range: randomType.range, type: randomType.type };

    // 重新绑定点击事件
    spriteObj.sprite.removeAllListeners();
    spriteObj.sprite.setInteractive();
    spriteObj.sprite.on('pointerdown', (pointer, localX, localY, event) => {
        let currentTower = towerShopData[index];
        if(gamePhase==='night' && !pendingBuilding && playerGold >= currentTower.price){
            let dragTower = this.add.text(spriteObj.sprite.x, spriteObj.sprite.y, currentTower.emoji, { fontSize: '32px' }).setOrigin(0.5);
            dragTower.setAlpha(0.7);
            pendingBuilding = { sprite: dragTower, building: currentTower, isDragging: true, shopIndex: index, buildingType: 'tower' };
            event.stopPropagation();
        }
    });
}

// 升级火车车厢上的防御塔
function upgradeTowerOnTrain(carIndex) {
    if (!train || !train.towers[carIndex]) return;

    let tower = train.towers[carIndex];

    // 火车头不能升级
    if (tower.isHead) return;

    let upgradeCost = 100 * tower.level;

    if (playerGold >= upgradeCost) {
        playerGold -= upgradeCost;
        tower.level++;
        tower.damage += 20;
        tower.range += 15;

        // 升级视觉效果（炮塔升级）
        let upgradeEmojis = ['�', '🚀', '�', '⚡', '🔥', '⭐', '💎'];
        tower.sprite.setText(upgradeEmojis[Math.min(tower.level - 1, upgradeEmojis.length - 1)]);

        // 升级特效
        this.tweens.add({
            targets: tower.sprite,
            scaleX: 1.5, scaleY: 1.5,
            duration: 300,
            yoyo: true,
            ease: 'Power2'
        });

        // 升级粒子效果
        let upgradeText = this.add.text(tower.sprite.x, tower.sprite.y - 30, `升级! Lv.${tower.level}`,
            { fontSize: '16px', fill: '#ffff00' }).setOrigin(0.5);
        this.tweens.add({
            targets: upgradeText,
            y: tower.sprite.y - 60,
            alpha: 0,
            duration: 1000,
            onComplete: () => upgradeText.destroy()
        });
    }
}

    // 处理建筑放置
    function handleBuildingPlace(pointer) {
        if (!pendingBuilding || !pendingBuilding.isDragging) return;

        const cityX = 375;
        const cityY = 600; // 向下移动100px

        // 白天放置功能建筑到城内
        if (pendingBuilding.buildingType === 'city' && gamePhase === 'day') {
            // 检查是否在城内区域
            if (pointer.x > cityX - CITY_WIDTH/2 + WALL_THICKNESS &&
                pointer.x < cityX + CITY_WIDTH/2 - WALL_THICKNESS &&
                pointer.y > cityY - CITY_HEIGHT/2 + WALL_THICKNESS &&
                pointer.y < cityY + CITY_HEIGHT/2 - WALL_THICKNESS) {

                // 对齐到4x4格子中心
                const gridCols = 4;
                const gridRows = 4;
                const gridSize = Math.min((CITY_WIDTH - WALL_THICKNESS) / gridCols, (CITY_HEIGHT - WALL_THICKNESS) / gridRows) * 0.95;

                const totalGridWidth = gridCols * gridSize;
                const totalGridHeight = gridRows * gridSize;
                const startX = cityX - totalGridWidth / 2;
                const startY = cityY - totalGridHeight / 2;

                const gridCol = Math.floor((pointer.x - startX) / gridSize);
                const gridRow = Math.floor((pointer.y - startY) / gridSize);

                // 确保在有效格子范围内
                if (gridCol >= 0 && gridCol < gridCols && gridRow >= 0 && gridRow < gridRows) {
                    // 放置在格子中心
                    const gridX = startX + gridCol * gridSize + gridSize / 2;
                    const gridY = startY + gridRow * gridSize + gridSize / 2;

                    // 检查是否与其他建筑重叠
                    let canPlace = true;
                    for(let building of buildings) {
                        const distance = Phaser.Math.Distance.Between(gridX, gridY, building.sprite.x, building.sprite.y);
                        if(distance < 35) {
                            canPlace = false;
                            break;
                        }
                    }

                    if(canPlace) {
                        // 扣除金币
                        playerGold -= pendingBuilding.building.price;
                        pendingBuilding.building.effect();

                        // 放置齿轮到格子中心位置
                        pendingBuilding.sprite.x = gridX;
                        pendingBuilding.sprite.y = gridY;
                        pendingBuilding.sprite.setAlpha(1.0);

                        buildings.push({
                            sprite: pendingBuilding.sprite,
                            type: pendingBuilding.building.type,
                            effect: pendingBuilding.building.effect,
                            isRotating: false, // 初始不旋转
                            gridX: gridCol,
                            gridY: gridRow,
                            distanceFromCenter: Phaser.Math.Distance.Between(gridX, gridY, cityX, cityY)
                        });

                        buildingInserted++;

                        // 刷新商店
                        refreshBuildingShop.call(this, pendingBuilding.shopIndex);

                        // 清除拖拽状态
                        pendingBuilding = null;
                    } else {
                        // 不能放置，销毁拖拽的建筑
                        pendingBuilding.sprite.destroy();
                        pendingBuilding = null;
                    }
                } else {
                    // 不在有效格子范围内，销毁拖拽的建筑
                    pendingBuilding.sprite.destroy();
                    pendingBuilding = null;
                }
            } else {
                // 不在城内区域，销毁拖拽的建筑
                pendingBuilding.sprite.destroy();
                pendingBuilding = null;
            }
        }
    }

    // 处理建筑拖拽
    function handleBuildingDrag(pointer) {
        if(!pendingBuilding || !pendingBuilding.isDragging) return;

        const cityX = 375;
        const cityY = 600; // 向下移动100px

        // 白天拖拽功能建筑
        if (pendingBuilding.buildingType === 'city' && gamePhase === 'day') {
            // 检查是否在城内区域
            if (pointer.x > cityX - CITY_WIDTH/2 + WALL_THICKNESS &&
                pointer.x < cityX + CITY_WIDTH/2 - WALL_THICKNESS &&
                pointer.y > cityY - CITY_HEIGHT/2 + WALL_THICKNESS &&
                pointer.y < cityY + CITY_HEIGHT/2 - WALL_THICKNESS) {

                // 对齐到4x4格子中心
                const gridCols = 4;
                const gridRows = 4;
                const gridSize = Math.min((CITY_WIDTH - WALL_THICKNESS) / gridCols, (CITY_HEIGHT - WALL_THICKNESS) / gridRows) * 0.95;

                const totalGridWidth = gridCols * gridSize;
                const totalGridHeight = gridRows * gridSize;
                const startX = cityX - totalGridWidth / 2;
                const startY = cityY - totalGridHeight / 2;

                const gridCol = Math.floor((pointer.x - startX) / gridSize);
                const gridRow = Math.floor((pointer.y - startY) / gridSize);

                // 确保在有效格子范围内
                if (gridCol >= 0 && gridCol < gridCols && gridRow >= 0 && gridRow < gridRows) {
                    // 放置在格子中心
                    const gridX = startX + gridCol * gridSize + gridSize / 2;
                    const gridY = startY + gridRow * gridSize + gridSize / 2;

                    pendingBuilding.sprite.x = gridX;
                    pendingBuilding.sprite.y = gridY;
                    // 保持原色，不变色
                } else {
                    pendingBuilding.sprite.x = pointer.x;
                    pendingBuilding.sprite.y = pointer.y;
                    // 保持原色，不变色
                }
            } else {
                pendingBuilding.sprite.x = pointer.x;
                pendingBuilding.sprite.y = pointer.y;
                // 保持原色，不变红色
            }
        }
    }

    // 更新火车位置
    function updateTrainPosition() {
        if (!train || !trainPath.length) return;

        for(let i = 0; i < train.segments.length; i++) {
            // 计算每个车厢的路径位置（车头在前，车厢跟随）
            // 车头的完整路径位置
            let headPosition = train.currentPathIndex + train.moveProgress;

            // 每个车厢相对于车头的偏移（车厢在车头后面）
            let segmentOffset = i * train.segmentSpacing;
            let segmentPosition = headPosition - segmentOffset;

            // 处理负数位置（绕一圈）
            while(segmentPosition < 0) {
                segmentPosition += trainPath.length;
            }

            // 获取当前和下一个路径点的索引
            let currentIndex = Math.floor(segmentPosition) % trainPath.length;
            let nextIndex = (currentIndex + 1) % trainPath.length;

            // 计算在两个路径点之间的插值进度
            let localProgress = segmentPosition - Math.floor(segmentPosition);

            if(trainPath[currentIndex] && trainPath[nextIndex]) {
                const current = trainPath[currentIndex];
                const next = trainPath[nextIndex];

                // 计算位置和角度
                const x = current.x + (next.x - current.x) * localProgress;
                const y = current.y + (next.y - current.y) * localProgress;
                const angle = Math.atan2(next.y - current.y, next.x - current.x);

                // 更新车厢位置和旋转（如果有背景）
                if(train.segments[i]) {
                    train.segments[i].x = x;
                    train.segments[i].y = y;
                    train.segments[i].rotation = angle;
                }

                // 同步更新车厢上的防御塔位置和旋转
                if(train.towers[i]) {
                    train.towers[i].sprite.x = x;
                    train.towers[i].sprite.y = y;

                    // 更新炮台底座位置
                    if(train.towers[i].base) {
                        train.towers[i].base.x = x;
                        train.towers[i].base.y = y;
                    }

                    // 火车头跟随车厢旋转
                    if(train.towers[i].isHead) {
                        train.towers[i].sprite.rotation = angle;
                    }
                    // 炮管不跟随车厢旋转，保持独立瞄准
                    // 炮管的旋转由攻击逻辑控制
                }
            }
        }
    }

    // 创建子弹
    function createBullet(startX, startY, targetX, targetY, damage, towerIndex) {
        let bullet = this.add.circle(startX, startY, 3, 0xFFFF00);
        bullet.setStrokeStyle(1, 0xFF6600);

        // 计算子弹飞行方向和速度
        const angle = Math.atan2(targetY - startY, targetX - startX);
        const speed = 300; // 子弹速度

        bullet.velocityX = Math.cos(angle) * speed;
        bullet.velocityY = Math.sin(angle) * speed;
        bullet.damage = damage;
        bullet.targetX = targetX;
        bullet.targetY = targetY;
        bullet.towerIndex = towerIndex;
        bullet.startTime = this.time.now;

        bullets.push(bullet);

        return bullet;
    }

    // 更新子弹
    function updateBullets(delta) {
        for (let i = bullets.length - 1; i >= 0; i--) {
            let bullet = bullets[i];

            // 安全检查：确保子弹存在且有效
            if (!bullet || !bullet.x || bullet.destroyed) {
                bullets.splice(i, 1);
                continue;
            }

            // 移动子弹
            bullet.x += bullet.velocityX * delta / 1000;
            bullet.y += bullet.velocityY * delta / 1000;

            // 检查子弹是否击中目标区域或超时
            const distanceToTarget = Phaser.Math.Distance.Between(bullet.x, bullet.y, bullet.targetX, bullet.targetY);
            const timeElapsed = this.time.now - bullet.startTime;

            if (distanceToTarget < 20 || timeElapsed > 2000) {
                // 子弹击中目标区域，寻找附近的怪物
                let hitMonster = null;
                let minDistance = 30; // 击中范围

                monsters.forEach(monster => {
                    const distance = Phaser.Math.Distance.Between(bullet.x, bullet.y, monster.x, monster.y);
                    if (distance < minDistance) {
                        hitMonster = monster;
                        minDistance = distance;
                    }
                });

                if (hitMonster) {
                    // 造成伤害
                    hitMonster.health -= bullet.damage;

                    // 击中特效
                    this.tweens.add({
                        targets: hitMonster,
                        alpha: 0.5,
                        duration: 100,
                        yoyo: true
                    });

                    // 检查怪物是否死亡
                    if (hitMonster.health <= 0) {
                        playerGold += 12 + currentLevel * 2;

                        // 清理所有瞄准这个怪物的子弹
                        for (let j = bullets.length - 1; j >= 0; j--) {
                            let otherBullet = bullets[j];

                            // 安全检查
                            if (!otherBullet || !otherBullet.targetX || otherBullet.destroyed) {
                                bullets.splice(j, 1);
                                continue;
                            }

                            let distanceToDeadMonster = Phaser.Math.Distance.Between(
                                otherBullet.targetX, otherBullet.targetY,
                                hitMonster.x, hitMonster.y
                            );
                            if (distanceToDeadMonster < 50) { // 如果子弹目标接近死亡怪物
                                otherBullet.destroyed = true; // 标记为已销毁
                                otherBullet.destroy();
                                bullets.splice(j, 1);
                            }
                        }

                        hitMonster.destroy();
                    }
                }

                // 移除子弹
                bullet.destroyed = true; // 标记为已销毁
                bullet.destroy();
                bullets.splice(i, 1);
            }
        }
    }

    // 创建雪花
    function createSnowflake(scene) {
        if (snowflakes.length >= maxSnowflakes) return;

        let snowflake = scene.add.circle(
            Phaser.Math.Between(0, 750), // 随机X位置
            -10, // 从屏幕上方开始
            Phaser.Math.Between(2, 4), // 随机大小2-4像素
            0xFFFFFF, // 白色
            0.8 // 80%透明度
        );

        // 雪花属性
        snowflake.fallSpeed = Phaser.Math.FloatBetween(30, 60); // 下落速度
        snowflake.swaySpeed = Phaser.Math.FloatBetween(0.5, 1.5); // 摆动速度
        snowflake.swayAmount = Phaser.Math.FloatBetween(10, 30); // 摆动幅度
        snowflake.startX = snowflake.x; // 记录起始X位置
        snowflake.time = 0; // 时间计数器

        snowflakes.push(snowflake);
    }

    // 更新雪花
    function updateSnowflakes(scene, delta) {
        for (let i = snowflakes.length - 1; i >= 0; i--) {
            let snowflake = snowflakes[i];

            // 更新时间
            snowflake.time += delta / 1000;

            // 下落
            snowflake.y += snowflake.fallSpeed * delta / 1000;

            // 左右摆动
            snowflake.x = snowflake.startX + Math.sin(snowflake.time * snowflake.swaySpeed) * snowflake.swayAmount;

            // 检查是否超出屏幕底部
            if (snowflake.y > 1400) { // 屏幕高度 + 缓冲区
                snowflake.destroy();
                snowflakes.splice(i, 1);
            }
        }

        // 随机创建新雪花
        if (Math.random() < 0.3 && snowflakes.length < maxSnowflakes) { // 30%概率创建
            createSnowflake(scene);
        }
    }

    // 清理所有雪花
    function clearSnowflakes() {
        snowflakes.forEach(snowflake => {
            if (snowflake && snowflake.destroy) {
                snowflake.destroy();
            }
        });
        snowflakes = [];
    }

    // 创建优化的齿轮文本
    function createGearText(scene, x, y, emoji, fontSize = 100, color = null) {
        let gearText = scene.add.text(x, y, emoji, {
            fontSize: fontSize + 'px',
            fontFamily: 'Arial, "Segoe UI Emoji", "Apple Color Emoji", sans-serif',
            padding: { x: 15, y: 15 },
            resolution: 2
        }).setOrigin(0.5);

        // 不应用颜色，保持emoji原始颜色
        // 齿轮emoji本身就有颜色，不需要额外的tint

        return gearText;
    }

    // 启动齿轮旋转（由中心动力齿轮驱动）
    function startGearRotation() {
        buildings.forEach(building => {
            if (building.sprite && !building.isRotating) {
                building.isRotating = true;

                let rotationDirection = 1;
                let duration = 4000; // 默认4秒一圈

                if (building.isPowerSource) {
                    // 动力齿轮：恒定转速，顺时针
                    rotationDirection = 1;
                    duration = 3000; // 3秒一圈，稍快
                } else {
                    // 计算与动力齿轮的距离
                    let distanceFromPower = Math.sqrt(
                        Math.pow(building.gridX - 1.5, 2) +
                        Math.pow(building.gridY - 1.5, 2)
                    );

                    // 根据距离和位置决定旋转方向和速度
                    rotationDirection = ((Math.floor(building.gridX) + Math.floor(building.gridY)) % 2 === 0) ? -1 : 1;

                    // 距离越远转速越慢
                    let speedMultiplier = Math.max(0.5, 1 / (1 + distanceFromPower * 0.3));
                    duration = 4000 / speedMultiplier;
                }

                // 创建持续旋转动画
                this.tweens.add({
                    targets: building.sprite,
                    rotation: rotationDirection * Math.PI * 2,
                    duration: duration,
                    repeat: -1,
                    ease: 'Linear'
                });

                // 添加发光效果（动力齿轮更亮）
                let glowAlpha = building.isPowerSource ? 0.9 : 0.7;
                this.tweens.add({
                    targets: building.sprite,
                    alpha: glowAlpha,
                    duration: 1000,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });

                // 添加齿轮作用提示
                showGearEffect.call(this, building);
            }
        });
    }

    // 显示齿轮效果提示
    function showGearEffect(building) {
        // 根据齿轮类型显示不同的效果文字
        let effectText = '';
        let effectColor = '#ffffff';

        switch(building.type) {
            case 'power':
                effectText = '动力源';
                effectColor = '#ff6600';
                break;
            case 'speed':
                effectText = '+速度';
                effectColor = '#00ff00';
                break;
            case 'attack':
                effectText = '+攻击';
                effectColor = '#ff0000';
                break;
            case 'defense':
                effectText = '+防御';
                effectColor = '#0000ff';
                break;
            case 'range':
                effectText = '+射程';
                effectColor = '#ffff00';
                break;
            case 'gold':
                effectText = '+金币';
                effectColor = '#ffd700';
                break;
            case 'repair':
                effectText = '+修复';
                effectColor = '#00ffff';
                break;
            default:
                effectText = '+效果';
                effectColor = '#ffffff';
        }

        // 创建飘浮数字效果
        let floatingText = this.add.text(building.sprite.x, building.sprite.y - 30, effectText, {
            fontSize: '16px',
            fill: effectColor,
            fontWeight: 'bold',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 飘浮动画
        this.tweens.add({
            targets: floatingText,
            y: building.sprite.y - 60,
            alpha: 0,
            duration: 2000,
            ease: 'Power2',
            onComplete: () => floatingText.destroy()
        });

        // 每3秒重复一次
        this.time.delayedCall(3000, () => {
            if (building.isRotating) {
                showGearEffect.call(this, building);
            }
        });
    }

    // 停止齿轮旋转
    function stopGearRotation() {
        buildings.forEach(building => {
            if (building.sprite && building.isRotating) {
                building.isRotating = false;

                // 停止旋转动画
                this.tweens.killTweensOf(building.sprite);

                // 恢复正常状态
                building.sprite.setAlpha(1.0);
                building.sprite.setRotation(0);
            }
        });
    }

    // 开始夜晚阶段（防御阶段）
    function startNightPhase() {
        gamePhase = 'night';
        this.gamePhaseText.setText('时间: 🌙夜晚 - 防御阶段');



        // 隐藏白天按钮和商店背景
        this.startNightButton.setVisible(false);
        if (this.shopBackground) this.shopBackground.setVisible(false);

        // 启动齿轮旋转
        startGearRotation.call(this);

        // 清除建筑商店并重新创建防御塔商店
        buildingShopSprites.forEach(shopItem => {
            shopItem.sprite.destroy();
            shopItem.priceText.destroy();
            shopItem.descText.destroy();
        });
        if (this.shopTitle) this.shopTitle.destroy();

        createBuildingShop.call(this);

        // 添加结束夜晚的按钮
        this.endNightButton = this.add.text(600, 100, '结束夜晚', { fontSize: '20px', fill: '#ffffff', backgroundColor: '#8e44ad', padding: { x: 15, y: 8 } }).setOrigin(0.5);
        this.endNightButton.setInteractive().on('pointerdown', () => {
            if (gamePhase === 'night') {
                startDayPhase.call(this);
            }
        });

        createWave.call(this);
    }

    // 开始白天阶段（建造阶段）
    function startDayPhase() {
        gamePhase = 'day';
        dayNightCycle++;
        this.gamePhaseText.setText('时间: ☀️白天 - 建造阶段');

        // 清除怪物和子弹
        monsters.forEach(m => m.destroy());
        monsters = [];
        bullets.forEach(b => b.destroy());
        bullets = [];
        // 雪花继续下，不清理

        // 停止齿轮旋转
        stopGearRotation.call(this);

        // 隐藏夜晚按钮
        if (this.endNightButton) this.endNightButton.setVisible(false);

        // 显示白天按钮和商店背景
        this.startNightButton.setVisible(true);
        if (this.shopBackground) this.shopBackground.setVisible(true);

        // 清除防御塔商店并重新创建功能建筑商店
        buildingShopSprites.forEach(shopItem => {
            shopItem.sprite.destroy();
            shopItem.priceText.destroy();
            shopItem.descText.destroy();
        });
        if (this.shopTitle) this.shopTitle.destroy();

        createBuildingShop.call(this);

        // 白天开始时，所有功能建筑产生效果
        buildings.forEach(building => {
            if (building.effect) {
                building.effect();
            }
        });

        // 增加一些基础金币收入
        playerGold += 50 + currentLevel * 10;
    }

    // 重置到新关卡
    function resetToNewLevel() {
        // 清除所有建筑和防御塔
        buildings.forEach(building => { if (building.sprite) building.sprite.destroy(); });
        trainTowers.forEach(tower => { if (tower.sprite) tower.sprite.destroy(); });
        buildings = [];
        trainTowers = [];
        buildingInserted = 0;

        if (pendingBuilding) {
            pendingBuilding.sprite.destroy();
            pendingBuilding = null;
        }

        // 重新创建火车（保持火车车厢上的防御塔）
        if(train) {
            train.segments.forEach(s=>{if(s) s.destroy();});
            train.towers.forEach(t=>{
                t.sprite.destroy();
                if(t.base) t.base.destroy(); // 销毁炮台底座
            });
        }
        createTrain.call(this);

        // 重置到白天
        gamePhase = 'day';
        dayNightCycle = 1;
        this.gamePhaseText.setText('时间: ☀️白天 - 建造阶段');

        // 清除商店并重新创建
        buildingShopSprites.forEach(shopItem => {
            shopItem.sprite.destroy();
            shopItem.priceText.destroy();
            shopItem.descText.destroy();
        });
        if (this.shopTitle) this.shopTitle.destroy();
        if (this.endNightButton) this.endNightButton.setVisible(false);
        this.startNightButton.setVisible(true);
        if (this.shopBackground) this.shopBackground.setVisible(true);

        createBuildingShop.call(this);

        totalMonstersInWave = Math.min(20, (5 + Math.floor(currentLevel / 2)) * 2);
        cityHealth = Math.min(maxCityHealth, cityHealth + 50);
        playerGold += 100; // 新关卡奖励
    }
    
    // 游戏更新循环
    function update(time, delta) {
        if (gamePhase === 'gameOver' || isPaused) return;
        updateCityDefense.call(this, delta);
        if (gamePhase === 'night') {
            updateBattle.call(this, time);
            updateBullets.call(this, delta);
        }

        // 更新雪花特效
        updateSnowflakes(this, delta);
        updateUI.call(this);
        checkWaveComplete.call(this);
        if (cityHealth <= 0 && gamePhase !== 'gameOver') {
            handleGameOver.call(this);
        }
    }
    
    function handleGameOver() {
        gamePhase = 'gameOver';
        monsters.forEach(m=>m.destroy());
        monsters = [];
        bullets.forEach(b=>b.destroy());
        bullets = [];
        clearSnowflakes(); // 游戏结束时清理雪花
        buildings.forEach(b=>b.sprite.destroy());
        buildings = [];
        trainTowers.forEach(t=>t.sprite.destroy());
        trainTowers = [];
        if(train) {
            train.segments.forEach(s=>{if(s) s.destroy();});
            train.towers.forEach(t=>{
                t.sprite.destroy();
                if(t.base) t.base.destroy(); // 销毁炮台底座
            });
            train = null;
        }

        this.add.text(375, 667, 'GAME OVER', { fontSize: '48px', fill: '#e74c3c', fontFamily: 'Arial' }).setOrigin(0.5);
        const restartButton = this.add.text(375, 750, '点击重新开始', { fontSize: '24px', fill: '#ffffff', backgroundColor: '#3498db', padding: { x: 20, y: 10 } }).setOrigin(0.5);
        restartButton.setInteractive().on('pointerdown', () => this.scene.restart());
    }

    // 更新城市防御系统
    function updateCityDefense(delta) {
        // 更新火车移动（流畅移动）
        if (train && trainPath.length > 0) {
            train.moveProgress += train.speed;

            if (train.moveProgress >= 1) {
                train.moveProgress = 0;
                train.currentPathIndex = (train.currentPathIndex + 1) % trainPath.length;
            }

            updateTrainPosition();
        }

        // 绘制城市血条
        drawCityHealthBar(this);
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 每一波弹出一次肉鸽弹窗
        if (gamePhase === 'night' && !isPaused && roguelikeShownInWave !== currentLevel) {
            roguelikeShownInWave = currentLevel; // 记录当前波数
            showRoguelikePopup.call(this);
        }

        // 火车防御塔攻击怪物
        trainTowers.forEach(tower => {
            if (time > tower.lastAttack + 800) { // 防御塔攻击频率
                // 寻找范围内最近的怪物
                let nearestMonster = null;
                let nearestDistance = tower.range;

                monsters.forEach(monster => {
                    const distance = Phaser.Math.Distance.Between(
                        tower.sprite.x, tower.sprite.y,
                        monster.x, monster.y
                    );
                    if (distance < nearestDistance) {
                        nearestMonster = monster;
                        nearestDistance = distance;
                    }
                });

                if (nearestMonster) {
                    // 攻击怪物
                    nearestMonster.health -= tower.damage;
                    tower.lastAttack = time;

                    // 攻击特效
                    this.tweens.add({
                        targets: tower.sprite,
                        scaleX: 1.3, scaleY: 1.3,
                        duration: 150,
                        yoyo: true
                    });

                    // 受伤特效
                    this.tweens.add({
                        targets: nearestMonster,
                        alpha: 0.5,
                        duration: 100,
                        yoyo: true
                    });

                    // 攻击线条特效
                    let attackLine = this.add.line(0, 0, tower.sprite.x, tower.sprite.y, nearestMonster.x, nearestMonster.y, 0xffff00);
                    attackLine.setLineWidth(3);
                    this.time.delayedCall(100, () => attackLine.destroy());

                    // 检查怪物是否死亡
                    if (nearestMonster.health <= 0) {
                        playerGold += 15 + currentLevel * 3;
                        nearestMonster.destroy();
                    }
                }
            }
        });

        // 火车车厢防御塔攻击怪物
        if (train && train.towers.length > 0) {
            train.towers.forEach((tower, index) => {
                // 火车头不参与攻击
                if (tower.isHead || tower.damage <= 0) return;

                if (time > tower.lastAttack + 600) { // 火车防御塔攻击频率更快
                    // 寻找范围内最近的怪物
                    let nearestMonster = null;
                    let nearestDistance = tower.range;

                    monsters.forEach(monster => {
                        const distance = Phaser.Math.Distance.Between(
                            tower.sprite.x, tower.sprite.y,
                            monster.x, monster.y
                        );
                        if (distance < nearestDistance) {
                            nearestMonster = monster;
                            nearestDistance = distance;
                        }
                    });

                    if (nearestMonster) {
                        // 计算炮塔旋转角度
                        const targetAngle = Math.atan2(nearestMonster.y - tower.sprite.y, nearestMonster.x - tower.sprite.x);
                        tower.targetAngle = targetAngle;

                        // 炮塔旋转到目标方向
                        tower.sprite.rotation = targetAngle;

                        // 发射子弹
                        createBullet.call(this, tower.sprite.x, tower.sprite.y, nearestMonster.x, nearestMonster.y, tower.damage, index);
                        tower.lastAttack = time;

                        // 火车防御塔攻击特效
                        this.tweens.add({
                            targets: tower.sprite,
                            scaleX: 1.4, scaleY: 1.4,
                            duration: 120,
                            yoyo: true
                        });

                        // 车厢震动特效（如果有背景）
                        if(train.segments[index]) {
                            this.tweens.add({
                                targets: train.segments[index],
                                scaleX: 1.1, scaleY: 1.1,
                                duration: 120,
                                yoyo: true
                            });
                        }
                    }
                }
                // 炮管攻击后保持瞄准角度，不归位
            });
        }
// 肉鸽3选1弹窗
function showRoguelikePopup() {
    console.log('Showing roguelike popup');
    isPaused = true;
    if (roguelikePopup) {
        roguelikePopup.destroy();
        roguelikePopup = null;
    }

    // 创建容器，所有弹窗元素都放在这里
    roguelikePopup = this.add.container(0, 0);
    roguelikePopup.setDepth(1000); // 设置高层级确保在最上层

    // 创建遮罩
    let mask = this.add.rectangle(375, 667, 750, 1334, 0x000000, 0.7).setOrigin(0.5);

    // 创建弹窗背景
    let bg = this.add.rectangle(375, 667, 600, 400, 0x2c3e50, 1).setOrigin(0.5);
    bg.setStrokeStyle(4, 0xffffff);

    // 标题
    let title = this.add.text(375, 520, '选择一个强化', { fontSize: '32px', fill: '#fff', fontWeight: 'bold' }).setOrigin(0.5);

    let options = [
        { text: '齿轮效果+50%', emoji: '⚙️', effect: () => {
            buildings.forEach(b => {
                if(b.effect) b.effect(); // 再次触发齿轮效果
            });
        }},
        { text: '城市生命+200', emoji: '🛡️', effect: () => {
            cityHealth += 200;
            maxCityHealth += 200;
        }},
        { text: '火车攻击+50', emoji: '🚂', effect: () => {
            train.towers.forEach(t => {
                if(!t.isHead) t.damage += 50;
            });
        }}
    ];

    let btnWidth = 160, btnHeight = 200;
    let spacing = 180;
    let startX = 375 - spacing;

    // 先把遮罩和背景添加到容器
    roguelikePopup.add(mask);
    roguelikePopup.add(bg);
    roguelikePopup.add(title);

    for(let i=0;i<3;i++){
        let x = startX + i*spacing;

        // 按钮底色
        let btn = this.add.rectangle(x, 667, btnWidth, btnHeight, 0x34495e, 1);
        btn.setStrokeStyle(4, 0xf39c12);
        btn.setInteractive();

        // emoji图标
        let emoji = this.add.text(x, 620, options[i].emoji, { fontSize: '64px' }).setOrigin(0.5);

        // 选项文字
        let txt = this.add.text(x, 700, options[i].text, {
            fontSize: '20px',
            fill: '#fff',
            fontWeight: 'bold',
            align: 'center',
            wordWrap: { width: btnWidth-20 }
        }).setOrigin(0.5);

        // 按钮点击事件
        btn.on('pointerdown', ((index, container, scene) => {
            return () => {
                console.log('Button clicked:', index); // 调试信息
                options[index].effect();

                // 简单有效的销毁弹窗
                console.log('Destroying roguelike popup container');

                // 防止重复点击
                if (!roguelikePopup) {
                    console.log('Popup already destroyed');
                    return;
                }

                // 立即隐藏并销毁容器
                roguelikePopup.setVisible(false);
                roguelikePopup.destroy();
                roguelikePopup = null;

                // 恢复游戏状态
                isPaused = false;
                // 不重置roguelikeShownInWave，确保每波只弹出一次
                console.log('Roguelike popup destroyed, game resumed');
            };
        })(i, roguelikePopup, this));

        // 悬停效果
        btn.on('pointerover', () => {
            btn.setFillStyle(0x3498db);
        });

        btn.on('pointerout', () => {
            btn.setFillStyle(0x34495e);
        });

        // 把所有元素添加到容器
        roguelikePopup.add(btn);
        roguelikePopup.add(emoji);
        roguelikePopup.add(txt);
    }
}


        // 从数组中移除已死亡的怪物
        monsters = monsters.filter(m => m.active);

        // 怪物移动与攻击城市的逻辑
        const cityX = 375;
        const cityY = 600; // 向下移动100px

        monsters.forEach(monster => {
            const distanceToCity = Phaser.Math.Distance.Between(monster.x, monster.y, cityX, cityY);

            // 怪物移动到城墙边缘
            if (distanceToCity > 220 && !monster.isMoving) {
                monster.isMoving = true;
                const angle = Math.atan2(cityY - monster.y, cityX - monster.x);
                // 移动到城墙外围，距离城墙约30像素
                const wallDistance = 220;
                const targetX = cityX - Math.cos(angle) * wallDistance;
                const targetY = cityY - Math.sin(angle) * wallDistance;
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: Math.max(2000, Math.min(5000, distanceToCity * 10)),
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        monster.isAttackingWall = true; // 到达城墙后开始攻击城墙
                    }
                });
            }

            // 怪物攻击城墙
            if (distanceToCity <= 240 && monster.isAttackingWall && time - monster.lastAttack > 1500) {
                // 初始化城墙血量
                if (!monster.wallHealth) {
                    monster.wallHealth = 100; // 城墙血量
                }

                // 攻击城墙
                monster.wallHealth -= 20;
                monster.lastAttack = time;

                // 城墙受攻击特效
                this.cameras.main.shake(50, 0.005);

                // 显示城墙受损效果
                let damageText = this.add.text(monster.x, monster.y - 20, '-20',
                    { fontSize: '16px', fill: '#ff0000' }).setOrigin(0.5);
                this.tweens.add({
                    targets: damageText,
                    y: monster.y - 40,
                    alpha: 0,
                    duration: 800,
                    onComplete: () => damageText.destroy()
                });

                // 城墙被摧毁，怪物进入城内攻击城市
                if (monster.wallHealth <= 0) {
                    monster.isAttackingWall = false;
                    monster.isAttackingCity = true;

                    // 移动到城市中心
                    this.tweens.add({
                        targets: monster,
                        x: cityX + (Math.random() - 0.5) * 100,
                        y: cityY + (Math.random() - 0.5) * 100,
                        duration: 1000,
                        ease: 'Power2'
                    });
                }
            }

            // 怪物攻击城市（城墙被摧毁后）
            if (monster.isAttackingCity && time - monster.lastAttack > 2000) {
                cityHealth -= 25 + currentLevel * 3; // 进入城内后伤害更高
                monster.lastAttack = time;

                // 城市受攻击特效
                this.cameras.main.shake(100, 0.01);

                // 显示城市受损效果
                let cityDamageText = this.add.text(cityX, cityY - 50, `-${25 + currentLevel * 3}`,
                    { fontSize: '20px', fill: '#ff0000', fontWeight: 'bold' }).setOrigin(0.5);
                this.tweens.add({
                    targets: cityDamageText,
                    y: cityY - 80,
                    alpha: 0,
                    duration: 1000,
                    onComplete: () => cityDamageText.destroy()
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        this.levelText.setText(`关卡: ${currentLevel}`);
        this.waveText.setText(`波次: ${currentWave}`);
        this.healthText.setText(`生命: ${Math.max(0, Math.floor(cityHealth))}/${maxCityHealth}`);
        this.gamePhaseText.setText(`时间: ${gamePhase === 'day' ? '☀️白天 - 建造阶段' : '🌙夜晚 - 防御阶段'}`);
        this.goldText.setText(`金币: ${playerGold}`);
        this.buildingText.setText(`建筑: ${buildingInserted} | 防御塔: ${trainTowers.length}`);
        this.dayNightText.setText(`昼夜: 第${dayNightCycle}轮`);

        // 更新按钮显示状态
        if (this.startNightButton) {
            this.startNightButton.setVisible(gamePhase === 'day');
        }
        if (this.endNightButton) {
            this.endNightButton.setVisible(gamePhase === 'night');
        }

        this.monsterHealthBars.forEach(bar => bar.destroy());
        this.monsterHealthBars = [];
        monsters.forEach(monster => {
            const monsterHealthPercent = monster.health / monster.maxHealth;
            const barBg = this.add.rectangle(monster.x, monster.y - 25, 40, 6, 0x333333);
            this.monsterHealthBars.push(barBg);
            if (monsterHealthPercent > 0) {
                 const bar = this.add.rectangle(monster.x - 20 + (40 * monsterHealthPercent) / 2, monster.y - 25, 40 * monsterHealthPercent, 6, monsterHealthPercent > 0.5 ? 0x27ae60 : 0xe74c3c);
                 this.monsterHealthBars.push(bar);
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        if (monsters.length === 0 && gamePhase === 'night') {
            gamePhase = 'wave_transition';
            currentWave++;
            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                const levelCompleteText = this.add.text(375, 607, `关卡 ${currentLevel - 1} 完成！`, { fontSize: '36px', fill: '#ffffff', fontFamily: 'Arial' }).setOrigin(0.5);
                this.time.delayedCall(2000, () => {
                    levelCompleteText.destroy();
                    resetToNewLevel.call(this);
                });
            } else {
                const waveCompleteText = this.add.text(375, 667, `波次 ${currentWave - 1} 完成！`, { fontSize: '28px', fill: '#ffffff', fontFamily: 'Arial' }).setOrigin(0.5);
                this.time.delayedCall(1500, () => {
                    waveCompleteText.destroy();
                    gamePhase = 'night';
                    createWave.call(this);
                });
            }
        }
    }
    
    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };

    // 城市血条绘制函数
    function drawCityHealthBar(scene) {
        const cityX = 375;
        const cityY = 600; // 向下移动100px

        if (!scene.cityHealthBar) {
            scene.cityHealthBar = scene.add.graphics();
        }
        scene.cityHealthBar.clear();

        // 血条背景
        scene.cityHealthBar.fillStyle(0x333333);
        scene.cityHealthBar.fillRect(cityX - 100, cityY - CITY_HEIGHT/2 - 40, 200, 20);

        // 血条前景
        let percent = Math.max(0, cityHealth / maxCityHealth);
        let color = percent > 0.5 ? 0x27ae60 : (percent > 0.25 ? 0xf39c12 : 0xe74c3c);
        scene.cityHealthBar.fillStyle(color);
        scene.cityHealthBar.fillRect(cityX - 100, cityY - CITY_HEIGHT/2 - 40, 200 * percent, 20);

        // 血条边框
        scene.cityHealthBar.lineStyle(2, 0xffffff);
        scene.cityHealthBar.strokeRect(cityX - 100, cityY - CITY_HEIGHT/2 - 40, 200, 20);
    }
</script>

</body>
</html>