<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>俄罗斯方块弹球</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        #game-container {
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 防止多次初始化
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            // 逻辑分辨率（放大游戏区域）
            const GAME_WIDTH = 600;
            const GAME_HEIGHT = 900;

            // Phaser配置，直接用逻辑分辨率，添加物理引擎
            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000000',
                physics: {
                    default: 'matter',
                    matter: {
                        gravity: { y: 0 }, // 默认关闭重力
                        debug: false,
                        enableSleeping: true,
                        render: {
                            visible: false // 关闭Matter.js的默认渲染
                        }
                    }
                },
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let gameGrid = [];
            let currentPiece = null;
            let nextPiece = null;
            let score = 0;
            let level = 1;
            let lines = 0;
            // 记录每种方块被弹球消除的数量
            let blockCollectCount = [0, 0, 0, 0, 0, 0, 0, 0]; // 0号不用
            let dropTime = 0;
            let dropInterval = 1000;
            let gameOver = false;
            let isPaused = false;
            let fastDrop = false; // 快速下落状态
            let isClearingLines = false; // 是否正在消除行
            let clearingLines = []; // 正在消除的行
            let fallingRows = []; // 正在下落的行
            let fallAnimationStartTime = 0; // 下落动画开始时间
            let fallDropDistances = []; // 每行下落距离

            // 物理相关变量
            let physicsBlocks = []; // 物理方块数组
            let walls = []; // 墙壁数组
            let autoFall = false; // 自动下落开关，默认关闭
            let gravityTimer = null; // 重力定时器
            let waitingForDrop = false; // 等待空格键下落

            // 动画参数
            const LINE_CLEAR_FLASH_DURATION = 150; // 行消除闪烁时长（更快）
            const LINE_CLEAR_FLASH_COUNT = 1; // 闪烁次数（2下）
            const FALL_ANIMATION_DURATION = 400; // 下落动画时长

            // 游戏区域配置（放大格子并居中）
            const GRID_WIDTH = 10;
            const GRID_HEIGHT = 19;
            const CELL_SIZE = 40;
            const GRID_X = Math.floor((GAME_WIDTH - GRID_WIDTH * CELL_SIZE) / 2) - 80;
            const GRID_Y = 120;

            // 方块颜色
            const COLORS = [
                0x000000, // 空
                0xFF0000, // 红色 - I
                0x00FF00, // 绿色 - O  
                0x0000FF, // 蓝色 - T
                0xFFFF00, // 黄色 - S
                0xFF00FF, // 紫色 - Z
                0x00FFFF, // 青色 - J
                0xFFA500  // 橙色 - L
            ];

            // 方块形状定义
            const PIECES = [
                // I 形状
                {
                    shape: [
                        [0,0,0,0],
                        [1,1,1,1],
                        [0,0,0,0],
                        [0,0,0,0]
                    ],
                    color: 1
                },
                // O 形状
                {
                    shape: [
                        [2,2],
                        [2,2]
                    ],
                    color: 2
                },
                // T 形状
                {
                    shape: [
                        [0,3,0],
                        [3,3,3],
                        [0,0,0]
                    ],
                    color: 3
                },
                // S 形状
                {
                    shape: [
                        [0,4,4],
                        [4,4,0],
                        [0,0,0]
                    ],
                    color: 4
                },
                // Z 形状
                {
                    shape: [
                        [5,5,0],
                        [0,5,5],
                        [0,0,0]
                    ],
                    color: 5
                },
                // J 形状
                {
                    shape: [
                        [6,0,0],
                        [6,6,6],
                        [0,0,0]
                    ],
                    color: 6
                },
                // L 形状
                {
                    shape: [
                        [0,0,7],
                        [7,7,7],
                        [0,0,0]
                    ],
                    color: 7
                }
            ];

            let scene;
            let graphics;
            let parentSizer, logicSizer;

            // UI文本对象（避免每帧重新创建）
            let controlHintText1, controlHintText2;
            let fpsText;
            let collectTexts = []; // 方块收集统计文本数组
            let lastTime = 0;
            let frameCount = 0;
            let fps = 0;

            function preload() {
                scene = this;
            }

            function create() {
                graphics = scene.add.graphics();

                // 逻辑区域和父容器的 Size 适配
                parentSizer = new Phaser.Structs.Size(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer = new Phaser.Structs.Size(GAME_WIDTH, GAME_HEIGHT, Phaser.Structs.Size.FIT, parentSizer);
                parentSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);

                // 监听resize事件
                this.scale.on('resize', resizeGame, this);

                // 创建物理墙壁
                createWalls();

                // 初始化游戏网格
                initGrid();

                // 创建UI
                createUI();

                // 创建控制提示文本对象（初始隐藏）
                controlHintText1 = scene.add.text(GAME_WIDTH/2, GRID_Y - 60, '← → 移动方块', {
                    fontSize: '20px',
                    fill: '#00ffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setVisible(false);

                controlHintText2 = scene.add.text(GAME_WIDTH/2, GRID_Y - 30, '空格键开始下落', {
                    fontSize: '24px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setVisible(false);

                // 创建FPS显示
                fpsText = scene.add.text(10, 10, 'FPS: 60', {
                    fontSize: '16px',
                    fill: '#ffffff',
                    backgroundColor: '#000000',
                    padding: { x: 5, y: 2 }
                });

                // 创建方块收集统计文本对象
                const collectY = 350;
                for (let i = 1; i < COLORS.length; i++) {
                    collectTexts[i] = scene.add.text(rightPanelX + 35, collectY + (i-1)*30, `x ${blockCollectCount[i]}`, {
                        fontSize: '16px',
                        fill: '#ffffff'
                    }).setOrigin(0, 0);
                }

                // 生成第一个方块
                spawnPiece();

                // 设置输入（只保留暂停功能）
                setupInput();
            }

            function resizeGame(gameSize) {
                if (!parentSizer || !logicSizer) return;
                const width = gameSize.width;
                const height = gameSize.height;
                parentSizer.setSize(width, height);
                logicSizer.setSize(width, height);
            }

            // 创建物理墙壁
            function createWalls() {
                const wallThickness = 20;

                // 左墙
                const leftWall = scene.matter.add.rectangle(
                    GRID_X - wallThickness/2,
                    GRID_Y + (GRID_HEIGHT * CELL_SIZE)/2,
                    wallThickness,
                    GRID_HEIGHT * CELL_SIZE,
                    {
                        isStatic: true,
                        friction: 0.9,
                        restitution: 0.1
                    }
                );
                walls.push(leftWall);

                // 右墙
                const rightWall = scene.matter.add.rectangle(
                    GRID_X + GRID_WIDTH * CELL_SIZE + wallThickness/2,
                    GRID_Y + (GRID_HEIGHT * CELL_SIZE)/2,
                    wallThickness,
                    GRID_HEIGHT * CELL_SIZE,
                    {
                        isStatic: true,
                        friction: 0.9,
                        restitution: 0.1
                    }
                );
                walls.push(rightWall);

                // 底墙 - 加厚防止穿透
                const bottomWall = scene.matter.add.rectangle(
                    GRID_X + (GRID_WIDTH * CELL_SIZE)/2,
                    GRID_Y + GRID_HEIGHT * CELL_SIZE + wallThickness,
                    GRID_WIDTH * CELL_SIZE + wallThickness * 2,
                    wallThickness * 2,
                    {
                        isStatic: true,
                        friction: 0.9,
                        restitution: 0.1
                    }
                );
                walls.push(bottomWall);
            }

            function initGrid() {
                gameGrid = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    gameGrid[y] = [];
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        gameGrid[y][x] = 0;
                    }
                }
            }

            // 右侧面板X坐标，提升为全局变量，供createUI和render使用
            const rightPanelX = GAME_WIDTH - 160;
            function createUI() {
                // 标题
                scene.add.text(GAME_WIDTH * 0.5, 40, '俄罗斯方块++', {
                    fontSize: Math.floor(40) + 'px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    resolution: window.devicePixelRatio
                }).setOrigin(0.5);

                // 传统俄罗斯方块顶部信息
                scene.scoreText = scene.add.text(60, 80, `积分: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.linesText = scene.add.text(220, 80, `行数: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#00ffff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.levelText = scene.add.text(380, 80, `等级: 1`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ff00ff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                // 下一个方块预览区域（放在右上角）
                scene.add.text(rightPanelX, 130, '下一个:', {
                    fontSize: Math.floor(20) + 'px',
                    fill: '#ffffff'
                });
                // 玩法说明（更新为空格下落）
                const ruleY = 220;
                const ruleX = rightPanelX;
                const ruleText = [
                    '玩法说明:',
                    '1. 方块生成后静止在顶部',
                    '2. 左右箭头键移动方块',
                    '3. 按空格键开始下落',
                    '4. P键暂停游戏',
                    '5. 统计收集每种方块'
                ];
                for (let i = 0; i < ruleText.length; i++) {
                    scene.add.text(ruleX, ruleY + i * 26, ruleText[i], {
                        fontSize: Math.floor(i === 0 ? 18 : 16) + 'px',
                        fill: i === 0 ? '#ffff00' : '#ffffff'
                    });
                }
            }

            function setupInput() {
                // 暂停功能、空格下落功能和左右移动功能
                scene.input.keyboard.on('keydown', (event) => {
                    if (gameOver || isPaused) return;

                    switch(event.code) {
                        case 'KeyP':
                            togglePause();
                            break;
                        case 'Space':
                            if (waitingForDrop && currentPiece && !currentPiece.isPhysics) {
                                physicsDropPiece();
                            }
                            break;
                        case 'ArrowLeft':
                            if (waitingForDrop && currentPiece && !currentPiece.isPhysics) {
                                movePiece(-1);
                            }
                            break;
                        case 'ArrowRight':
                            if (waitingForDrop && currentPiece && !currentPiece.isPhysics) {
                                movePiece(1);
                            }
                            break;
                    }
                });
            }

            // 移动方块函数（仅在静止状态下有效）
            function movePiece(direction) {
                if (!currentPiece || currentPiece.isPhysics || !waitingForDrop) return;

                // 检查移动后是否会发生碰撞
                if (!checkCollision(currentPiece, direction, 0)) {
                    currentPiece.x += direction;
                }
            }

            // 物理下落函数
            function physicsDropPiece() {
                if (gameOver || isPaused || !currentPiece || currentPiece.isPhysics) return;

                // 清除之前的重力定时器
                if (gravityTimer) {
                    clearTimeout(gravityTimer);
                    gravityTimer = null;
                }

                // 开启重力（增加重力值让下落更快）
                scene.matter.world.engine.world.gravity.y = 1.5;

                // 标记当前方块为物理状态，不再等待下落
                currentPiece.isPhysics = true;
                currentPiece.physicsBlocks = [];
                waitingForDrop = false;

                // 创建复合物理体 - 将整个方块作为一个整体
                const bodies = [];
                const blockPositions = [];

                // 收集所有方块位置并创建物理体
                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x] !== 0) {
                            const gridX = currentPiece.x + x;
                            const gridY = currentPiece.y + y;

                            if (gridY >= 0) {
                                // 创建单个方块的物理体
                                const block = scene.matter.add.rectangle(
                                    GRID_X + gridX * CELL_SIZE + CELL_SIZE/2,
                                    GRID_Y + gridY * CELL_SIZE + CELL_SIZE/2,
                                    CELL_SIZE - 2,
                                    CELL_SIZE - 2,
                                    {
                                        restitution: 0.2,
                                        friction: 0.8,
                                        frictionAir: 0.01
                                    }
                                );

                                bodies.push(block);
                                blockPositions.push({ gridX, gridY, x, y, body: block });
                            }
                        }
                    }
                }

                // 使用约束将所有方块连接成一个整体
                if (bodies.length > 1) {
                    for (let i = 0; i < bodies.length - 1; i++) {
                        // 创建刚性约束连接相邻的方块
                        const constraint = scene.matter.add.constraint(bodies[i], bodies[i + 1], {
                            length: 0,
                            stiffness: 1,
                            render: { visible: false }
                        });
                    }

                    // 额外的交叉约束增强稳定性
                    if (bodies.length > 2) {
                        for (let i = 0; i < bodies.length - 2; i++) {
                            const constraint = scene.matter.add.constraint(bodies[i], bodies[i + 2], {
                                length: Math.sqrt(2) * CELL_SIZE,
                                stiffness: 0.8,
                                render: { visible: false }
                            });
                        }
                    }
                }

                // 存储方块信息
                for (const pos of blockPositions) {
                    currentPiece.physicsBlocks.push({
                        body: pos.body,
                        color: currentPiece.color,
                        gridX: pos.gridX,
                        gridY: pos.gridY
                    });

                    // 添加到全局物理方块数组
                    physicsBlocks.push({
                        body: pos.body,
                        color: currentPiece.color,
                        gridX: pos.gridX,
                        gridY: pos.gridY
                    });
                }

                // 1.5秒后关闭重力并生成新方块
                gravityTimer = setTimeout(() => {
                    scene.matter.world.engine.world.gravity.y = 0;
                    currentPiece = null;
                    spawnPiece();
                    gravityTimer = null;
                }, 1500);
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }

                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;

                nextPiece = createRandomPiece();

                // 检查游戏结束
                if (checkCollision(currentPiece, 0, 0)) {
                    gameOver = true;
                    showGameOver();
                    return;
                }

                // 设置等待下落状态，不立即进入物理系统
                waitingForDrop = true;
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                return {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0,
                    y: 0,
                    isPhysics: false,
                    physicsBlocks: []
                };
            }

            // 碰撞检测函数（保留用于游戏结束判断）
            function checkCollision(piece, offsetX, offsetY) {
                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const newX = piece.x + x + offsetX;
                            const newY = piece.y + y + offsetY;
                            
                            // 检查边界碰撞
                            if (newX < 0 || newX >= GRID_WIDTH || newY >= GRID_HEIGHT) {
                                return true;
                            }
                            
                            // 检查与已有方块碰撞（只在非物理状态下检查）
                            if (newY >= 0 && !piece.isPhysics && gameGrid[newY][newX] !== 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            function togglePause() {
                isPaused = !isPaused;
                if (isPaused) {
                    scene.matter.world.engine.pause();
                } else {
                    scene.matter.world.engine.resume();
                }
            }

            function showGameOver() {
                scene.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, 400, 200, 0x000000, 0.8).setOrigin(0.5);
                scene.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 30, '游戏结束', {
                    fontSize: '40px',
                    fill: '#ff0000',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scene.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 20, '按刷新键重新开始', {
                    fontSize: '24px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
                scene.matter.world.engine.pause();
            }

            function update(time, delta) {
                // 计算FPS
                frameCount++;
                if (time - lastTime >= 1000) {
                    fps = Math.round(frameCount * 1000 / (time - lastTime));
                    fpsText.setText(`FPS: ${fps}`);
                    frameCount = 0;
                    lastTime = time;
                }

                if (gameOver || isPaused) return;

                // 渲染游戏
                render();
            }

            function render() {
                graphics.clear();

                // 绘制游戏区域边框
                graphics.lineStyle(2, 0x555555);
                graphics.strokeRect(GRID_X, GRID_Y, GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE);

                // 绘制当前静止的方块（如果存在且未进入物理状态）
                if (currentPiece && !currentPiece.isPhysics && waitingForDrop) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;

                                if (gridY >= 0 && gridX >= 0 && gridX < GRID_WIDTH && gridY < GRID_HEIGHT) {
                                    graphics.fillStyle(COLORS[currentPiece.color]);
                                    graphics.fillRect(
                                        GRID_X + gridX * CELL_SIZE + 1,
                                        GRID_Y + gridY * CELL_SIZE + 1,
                                        CELL_SIZE - 2,
                                        CELL_SIZE - 2
                                    );

                                    // 绘制边框
                                    graphics.lineStyle(1, 0xffffff, 0.3);
                                    graphics.strokeRect(
                                        GRID_X + gridX * CELL_SIZE + 1,
                                        GRID_Y + gridY * CELL_SIZE + 1,
                                        CELL_SIZE - 2,
                                        CELL_SIZE - 2
                                    );
                                }
                            }
                        }
                    }
                }

                // 绘制物理方块
                for (let i = 0; i < physicsBlocks.length; i++) {
                    const block = physicsBlocks[i];
                    const body = block.body;

                    graphics.fillStyle(COLORS[block.color]);
                    graphics.fillRect(
                        body.position.x - CELL_SIZE/2 + 1,
                        body.position.y - CELL_SIZE/2 + 1,
                        CELL_SIZE - 2,
                        CELL_SIZE - 2
                    );

                    // 绘制边框
                    graphics.lineStyle(1, 0xffffff, 0.3);
                    graphics.strokeRect(
                        body.position.x - CELL_SIZE/2 + 1,
                        body.position.y - CELL_SIZE/2 + 1,
                        CELL_SIZE - 2,
                        CELL_SIZE - 2
                    );
                }

                // 绘制下一个方块预览
                if (nextPiece) {
                    const previewX = rightPanelX + 40;
                    const previewY = 160;
                    const shape = nextPiece.shape;
                    
                    // 计算预览方块的居中偏移
                    const offsetX = Math.floor((4 - shape[0].length) / 2) * CELL_SIZE;
                    const offsetY = Math.floor((4 - shape.length) / 2) * CELL_SIZE;
                    
                    for (let y = 0; y < shape.length; y++) {
                        for (let x = 0; x < shape[y].length; x++) {
                            if (shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * CELL_SIZE + offsetX + 1,
                                    previewY + y * CELL_SIZE + offsetY + 1,
                                    CELL_SIZE - 2,
                                    CELL_SIZE - 2
                                );
                                
                                // 绘制边框
                                graphics.lineStyle(1, 0xffffff, 0.3);
                                graphics.strokeRect(
                                    previewX + x * CELL_SIZE + offsetX + 1,
                                    previewY + y * CELL_SIZE + offsetY + 1,
                                    CELL_SIZE - 2,
                                    CELL_SIZE - 2
                                );
                            }
                        }
                    }
                }

                // 显示/隐藏控制提示（如果方块在等待下落）
                const showHints = waitingForDrop && currentPiece && !currentPiece.isPhysics;
                controlHintText1.setVisible(showHints);
                controlHintText2.setVisible(showHints);

                // 绘制方块收集统计
                const collectY = 350;
                for (let i = 1; i < COLORS.length; i++) {
                    // 绘制方块样本
                    graphics.fillStyle(COLORS[i]);
                    graphics.fillRect(rightPanelX + 5, collectY + (i-1)*30 + 1, 20, 20);

                    // 更新数量文本（而不是重新创建）
                    if (collectTexts[i]) {
                        collectTexts[i].setText(`x ${blockCollectCount[i]}`);
                    }
                }
            }

            // 启动游戏
            const game = new Phaser.Game(config);
        });
    </script>
</body>
</html>