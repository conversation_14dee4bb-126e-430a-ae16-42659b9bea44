<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍉 合成大西瓜</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.85.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #2c3e50;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            touch-action: manipulation;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            max-width: 100vw;
            max-height: 100vh;
        }

        .loading {
            color: white;
            font-size: 24px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="gameCanvas">
        <div class="loading">游戏加载中...</div>
    </div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const CONTAINER_WIDTH = 600;
        const CONTAINER_HEIGHT = 900;
        const WALL_THICKNESS = 15;
        const UI_HEIGHT = 200;

        // 水果配置
        const FRUITS = [
            { emoji: '🍒', size: 32, points: 1, color: 0xff6b6b },
            { emoji: '🍓', size: 40, points: 3, color: 0xff8e8e },
            { emoji: '🍇', size: 48, points: 6, color: 0x9b59b6 },
            { emoji: '🍊', size: 56, points: 10, color: 0xf39c12 },
            { emoji: '🍋', size: 64, points: 15, color: 0xf1c40f },
            { emoji: '🍎', size: 72, points: 21, color: 0xe74c3c },
            { emoji: '🍑', size: 80, points: 28, color: 0xfd79a8 },
            { emoji: '🥭', size: 88, points: 36, color: 0xfdcb6e },
            { emoji: '🍍', size: 96, points: 45, color: 0xe17055 },
            { emoji: '🥥', size: 104, points: 55, color: 0x8b4513 },
            { emoji: '🍉', size: 112, points: 66, color: 0x00b894 }
        ];

        let game;
        let score = 0;
        let highScore = 0;

        try {
            highScore = parseInt(sessionStorage.getItem('watermelonHighScore') || '0');
        } catch (e) {
            highScore = 0;
        }

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.fruits = [];
                this.currentFruit = null;
                this.nextFruitType = 0;
                this.dropLine = null;
                this.gameOver = false;
                this.merging = new Set();
                this.containerBounds = null;
            }

            create() {
                this.createBackground();
                this.createUI();

                // 参考demo的简洁设置
                this.matter.world.setBounds();


                this.createContainer();
                this.createDropLine();
                this.createNextFruit();
                this.setupClickControl();
                this.matter.world.on('collisionstart', this.handleCollision, this);
            }

            createBackground() {
                const graphics = this.add.graphics();
                graphics.fillGradientStyle(0x667eea, 0x667eea, 0x764ba2, 0x764ba2, 1);
                graphics.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

                graphics.fillStyle(0x2c3e50, 0.9);
                graphics.fillRoundedRect(20, 20, GAME_WIDTH - 40, UI_HEIGHT - 40, 25);

                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;
                graphics.fillStyle(0x87CEEB, 0.3);
                graphics.fillRoundedRect(containerX - 30, containerY - 30, CONTAINER_WIDTH + 60, CONTAINER_HEIGHT + 60, 30);
            }

            createUI() {
                this.add.text(GAME_WIDTH / 2, 70, '🍉 合成大西瓜 🍉', {
                    fontSize: '48px',
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.scoreText = this.add.text(60, 110, `得分: ${score}`, {
                    fontSize: '32px',
                    fill: '#4ecdc4',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                });

                this.highScoreText = this.add.text(GAME_WIDTH - 60, 110, `最高分: ${highScore}`, {
                    fontSize: '32px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(1, 0);

                this.add.text(GAME_WIDTH / 2, 150, '下一个:', {
                    fontSize: '24px',
                    fill: '#bdc3c7',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH / 2, GAME_HEIGHT - 80, '按住拖拽移动 | 松开投放 | ← → 键移动 | 空格投放', {
                    fontSize: '24px',
                    fill: '#95a5a6',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }

            createContainer() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;

                this.containerBounds = {
                    left: containerX,
                    right: containerX + CONTAINER_WIDTH,
                    top: containerY,
                    bottom: containerY + CONTAINER_HEIGHT
                };

                const wallOptions = { 
                    isStatic: true, 
                    label: 'wall',
                    friction: 0.5,
                    restitution: 0.3
                };

                this.matter.add.rectangle(
                    containerX - WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH + WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH/2, 
                    containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2, 
                    CONTAINER_WIDTH + WALL_THICKNESS*2, 
                    WALL_THICKNESS, 
                    wallOptions
                );

                const graphics = this.add.graphics();
                graphics.lineStyle(6, 0x4ecdc4, 0.8);
                graphics.strokeRoundedRect(containerX - 3, containerY - 3, CONTAINER_WIDTH + 6, CONTAINER_HEIGHT + 6, 15);
            }

            createDropLine() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const dropLineY = UI_HEIGHT + 150;

                // 使用graphics来创建警戒线，更简单直接
                this.dropLine = this.add.graphics();
                this.dropLine.lineStyle(4, 0xff4757, 0.9);
                this.dropLine.moveTo(containerX, dropLineY);
                this.dropLine.lineTo(containerX + CONTAINER_WIDTH, dropLineY);
                this.dropLine.strokePath();

                // 添加闪烁动画
                this.tweens.add({
                    targets: this.dropLine,
                    alpha: 0.3,
                    duration: 800,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Power2'
                });

                // 添加警戒线标签
                this.add.text(GAME_WIDTH / 2, dropLineY - 25, '⚠️ 警戒线 ⚠️', {
                    fontSize: '20px',
                    fill: '#FFFFFF'
                }).setOrigin(0.5);
            }

            createNextFruit() {
                this.nextFruitType = Phaser.Math.Between(0, 4);

                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const startX = containerX + CONTAINER_WIDTH / 2;
                const startY = UI_HEIGHT + 100;

                const fruitConfig = FRUITS[this.nextFruitType];

                // 创建圆形水果
                this.currentFruit = this.add.circle(startX, startY, fruitConfig.size, fruitConfig.color);

                // 添加物理体
                this.matter.add.gameObject(this.currentFruit, {
                    shape: 'circle',
                    radius: fruitConfig.size,
                    isStatic: true
                });

                // 添加emoji文本
                const emojiText = this.add.text(startX, startY, fruitConfig.emoji, {
                    fontSize: `${fruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                this.currentFruit.emojiText = emojiText;
                this.currentFruit.fruitType = this.nextFruitType;
                this.fruits.push(this.currentFruit);

                console.log('创建水果:', fruitConfig.emoji, '物理体存在:', !!this.currentFruit.body, '静态状态:', this.currentFruit.body?.isStatic);

                // 水果创建为静态状态，等待点击屏幕释放
                console.log('水果已创建，点击屏幕释放下落');

                this.updateNextFruitPreview();
            }

            setupClickControl() {
                this.isPressed = false;
                this.startX = 0;

                // 鼠标/触摸按下
                this.input.on('pointerdown', (pointer) => {
                    if (this.currentFruit && this.currentFruit.body && this.currentFruit.body.isStatic) {
                        this.isPressed = true;
                        this.startX = pointer.x;
                        console.log('开始拖拽水果');
                    }
                });

                // 鼠标/触摸移动
                this.input.on('pointermove', (pointer) => {
                    if (this.isPressed && this.currentFruit && this.currentFruit.body && this.currentFruit.body.isStatic) {
                        // 计算移动距离
                        const deltaX = pointer.x - this.startX;
                        const newX = this.currentFruit.x + deltaX;

                        // 限制在容器范围内
                        const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                        const fruitRadius = this.currentFruit.body.circleRadius;
                        const minX = containerX + fruitRadius;
                        const maxX = containerX + CONTAINER_WIDTH - fruitRadius;

                        const clampedX = Phaser.Math.Clamp(newX, minX, maxX);

                        // 更新水果位置
                        this.currentFruit.x = clampedX;
                        this.currentFruit.body.position.x = clampedX;

                        // 更新emoji文本位置
                        if (this.currentFruit.emojiText) {
                            this.currentFruit.emojiText.x = clampedX;
                        }

                        this.startX = pointer.x;
                    }
                });

                // 鼠标/触摸松开
                this.input.on('pointerup', () => {
                    if (this.isPressed && this.currentFruit && this.currentFruit.body && this.currentFruit.body.isStatic) {
                        this.isPressed = false;
                        console.log('松开，水果开始下落');

                        // 保存水果信息
                        const x = this.currentFruit.x;
                        const y = this.currentFruit.y;
                        const size = this.currentFruit.body.circleRadius;
                        const color = this.currentFruit.fillColor;
                        const fruitType = this.currentFruit.fruitType;
                        const emoji = FRUITS[fruitType].emoji;

                        // 销毁emoji文本
                        if (this.currentFruit.emojiText) {
                            this.currentFruit.emojiText.destroy();
                        }

                        // 从物理世界移除
                        this.matter.world.remove(this.currentFruit.body);
                        this.currentFruit.destroy();

                        // 重新创建为动态物体
                        this.currentFruit = this.add.circle(x, y, size, color);
                        this.matter.add.gameObject(this.currentFruit, {
                            shape: 'circle',
                            radius: size,
                            isStatic: false  // 创建为动态
                        });

                        // 重新创建emoji文本
                        const emojiText = this.add.text(x, y, emoji, {
                            fontSize: `${size * 1.2}px`
                        }).setOrigin(0.5);

                        this.currentFruit.emojiText = emojiText;
                        this.currentFruit.fruitType = fruitType;

                        // 添加到fruits数组
                        this.fruits.push(this.currentFruit);

                        this.currentFruit = null;

                        // 1秒后创建下一个水果
                        this.time.delayedCall(1000, () => {
                            if (!this.gameOver) {
                                this.createNextFruit();
                            }
                        });
                    }
                });

                // 键盘控制（可选）
                this.cursors = this.input.keyboard.createCursorKeys();
                this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
            }

            updateNextFruitPreview() {
                if (this.nextFruitPreview) {
                    this.nextFruitPreview.destroy();
                }

                const nextType = Phaser.Math.Between(0, 4);
                const previewConfig = FRUITS[nextType];

                this.nextFruitPreview = this.add.text(GAME_WIDTH / 2 + 80, 150, previewConfig.emoji, {
                    fontSize: '36px'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: this.nextFruitPreview,
                    scaleX: 1.1,
                    scaleY: 1.1,
                    duration: 500,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Power2'
                });
            }



            handleCollision(event) {
                const pairs = event.pairs;

                for (let i = 0; i < pairs.length; i++) {
                    const pair = pairs[i];
                    const fruitA = pair.bodyA.gameObject;
                    const fruitB = pair.bodyB.gameObject;

                    // 检查是否是两个水果碰撞
                    if (fruitA && fruitB &&
                        typeof fruitA.fruitType === 'number' &&
                        typeof fruitB.fruitType === 'number') {

                        // 相同类型的水果才能合成
                        if (fruitA.fruitType === fruitB.fruitType &&
                            fruitA.fruitType < FRUITS.length - 1 &&
                            !this.merging.has(fruitA) &&
                            !this.merging.has(fruitB)) {

                            // 标记为正在合成，防止重复
                            this.merging.add(fruitA);
                            this.merging.add(fruitB);

                            // 延迟合成，避免物理引擎冲突
                            this.time.delayedCall(50, () => {
                                if (fruitA.active && fruitB.active) {
                                    this.mergeFruits(fruitA, fruitB, fruitA.fruitType);
                                }
                            });
                        }
                    }
                }
            }





            mergeFruits(fruitA, fruitB, fruitType) {
                // 计算合成位置
                const mergeX = (fruitA.x + fruitB.x) / 2;
                const mergeY = (fruitA.y + fruitB.y) / 2;

                // 移除旧水果
                this.removeFruit(fruitA);
                this.removeFruit(fruitB);

                // 创建新水果
                const newFruitType = fruitType + 1;
                if (newFruitType >= FRUITS.length) return; // 防止超出数组范围

                const newFruitConfig = FRUITS[newFruitType];
                const newFruit = this.add.circle(mergeX, mergeY, newFruitConfig.size, newFruitConfig.color);

                // 添加物理体
                this.matter.add.gameObject(newFruit, {
                    shape: 'circle',
                    radius: newFruitConfig.size,
                    isStatic: false
                });

                // 添加emoji文本
                const emojiText = this.add.text(mergeX, mergeY, newFruitConfig.emoji, {
                    fontSize: `${newFruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                newFruit.emojiText = emojiText;
                newFruit.fruitType = newFruitType;
                this.fruits.push(newFruit);

                // 计算并添加积分
                const points = newFruitConfig.points;
                this.updateScore(points);

                // 添加合成特效
                this.createMergeEffect(mergeX, mergeY, newFruitConfig.emoji, points);
            }

            createMergeEffect(x, y, emoji, points = 0) {
                // 创建合成特效文字
                const effect = this.add.text(x, y, emoji, {
                    fontSize: '60px'
                }).setOrigin(0.5);

                // 主要动画效果
                this.tweens.add({
                    targets: effect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    y: y - 80,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        effect.destroy();
                    }
                });

                // 创建积分显示
                if (points > 0) {
                    const scoreEffect = this.add.text(x, y + 60, `+${points}`, {
                        fontSize: '32px',
                        fill: '#f39c12',
                        fontStyle: 'bold',
                        stroke: '#ffffff',
                        strokeThickness: 2
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: scoreEffect,
                        y: y - 20,
                        alpha: 0,
                        scaleX: 1.5,
                        scaleY: 1.5,
                        duration: 1200,
                        ease: 'Power2',
                        onComplete: () => {
                            scoreEffect.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(x, y, 30);

                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });

                // 创建星星粒子效果
                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const star = this.add.text(x, y, '✨', {
                        fontSize: '24px'
                    }).setOrigin(0.5);

                    const targetX = x + Math.cos(angle) * 100;
                    const targetY = y + Math.sin(angle) * 100;

                    this.tweens.add({
                        targets: star,
                        x: targetX,
                        y: targetY,
                        alpha: 0,
                        duration: 800,
                        ease: 'Power2',
                        onComplete: () => {
                            star.destroy();
                        }
                    });
                }
            }

            removeFruit(fruit) {
                // 从合成标记中移除
                this.merging.delete(fruit);

                // 从水果数组中移除
                const index = this.fruits.indexOf(fruit);
                if (index > -1) {
                    this.fruits.splice(index, 1);
                }

                // 销毁emoji文本
                if (fruit.emojiText) {
                    fruit.emojiText.destroy();
                }

                // 销毁水果对象
                fruit.destroy();
            }

            updateScore(points) {
                score += points;
                this.scoreText.setText(`得分: ${score}`);

                if (score > highScore) {
                    highScore = score;
                    try {
                        sessionStorage.setItem('watermelonHighScore', highScore.toString());
                    } catch (e) {
                        console.log('无法保存最高分');
                    }
                    this.highScoreText.setText(`最高分: ${highScore}`);
                }
            }



            checkGameOver() {
                const gameOverLine = UI_HEIGHT + 150;

                for (let fruit of this.fruits) {
                    // 检查fruit和body是否存在
                    if (fruit && fruit.body &&
                        fruit.y < gameOverLine && 
                        fruit !== this.currentFruit &&
                        !fruit.body.isStatic) {
                        
                        this.gameOver = true;
                        this.showGameOver();
                        return;
                    }
                }
            }

            showGameOver() {
                const overlay = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.8);
                overlay.setInteractive();

                const panel = this.add.graphics();
                panel.fillStyle(0x2c3e50, 0.95);
                panel.fillRoundedRect(GAME_WIDTH/2 - 250, GAME_HEIGHT/2 - 200, 500, 400, 30);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 120, '🎮 游戏结束 🎮', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 40, `最终得分: ${score}`, {
                    fontSize: '36px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const restartButton = this.add.graphics();
                restartButton.fillStyle(0x4ecdc4, 0.8);
                restartButton.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60, 30);

                const restartText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 110, '🔄 重新开始', {
                    fontSize: '28px',
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const buttonBounds = new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60);
                restartButton.setInteractive(buttonBounds, Phaser.Geom.Rectangle.Contains);

                restartButton.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });

                overlay.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });
            }

            update() {
                if (this.gameOver) return;

                // 过滤无效水果
                this.fruits = this.fruits.filter(fruit => fruit && fruit.active);

                // 同步emoji文本位置
                this.fruits.forEach(fruit => {
                    if (fruit && fruit.emojiText && fruit.body && fruit.body.position) {
                        fruit.emojiText.x = fruit.body.position.x;
                        fruit.emojiText.y = fruit.body.position.y;
                    }
                });

                // 键盘控制
                if (this.currentFruit && this.currentFruit.body && this.currentFruit.body.isStatic) {
                    const moveSpeed = 5;
                    let moved = false;

                    if (this.cursors.left.isDown) {
                        const newX = this.currentFruit.x - moveSpeed;
                        const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                        const fruitRadius = this.currentFruit.body.circleRadius;
                        const minX = containerX + fruitRadius;

                        if (newX >= minX) {
                            this.currentFruit.x = newX;
                            this.currentFruit.body.position.x = newX;
                            if (this.currentFruit.emojiText) {
                                this.currentFruit.emojiText.x = newX;
                            }
                            moved = true;
                        }
                    }

                    if (this.cursors.right.isDown) {
                        const newX = this.currentFruit.x + moveSpeed;
                        const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                        const fruitRadius = this.currentFruit.body.circleRadius;
                        const maxX = containerX + CONTAINER_WIDTH - fruitRadius;

                        if (newX <= maxX) {
                            this.currentFruit.x = newX;
                            this.currentFruit.body.position.x = newX;
                            if (this.currentFruit.emojiText) {
                                this.currentFruit.emojiText.x = newX;
                            }
                            moved = true;
                        }
                    }

                    // 空格键投放
                    if (Phaser.Input.Keyboard.JustDown(this.spaceKey)) {
                        this.dropCurrentFruit();
                    }
                }
            }

            dropCurrentFruit() {
                if (this.currentFruit && this.currentFruit.body && this.currentFruit.body.isStatic) {
                    console.log('空格键投放水果');

                    // 保存水果信息
                    const x = this.currentFruit.x;
                    const y = this.currentFruit.y;
                    const size = this.currentFruit.body.circleRadius;
                    const color = this.currentFruit.fillColor;
                    const fruitType = this.currentFruit.fruitType;
                    const emoji = FRUITS[fruitType].emoji;

                    // 销毁emoji文本
                    if (this.currentFruit.emojiText) {
                        this.currentFruit.emojiText.destroy();
                    }

                    // 从物理世界移除
                    this.matter.world.remove(this.currentFruit.body);
                    this.currentFruit.destroy();

                    // 重新创建为动态物体
                    this.currentFruit = this.add.circle(x, y, size, color);
                    this.matter.add.gameObject(this.currentFruit, {
                        shape: 'circle',
                        radius: size,
                        isStatic: false  // 创建为动态
                    });

                    // 重新创建emoji文本
                    const emojiText = this.add.text(x, y, emoji, {
                        fontSize: `${size * 1.2}px`
                    }).setOrigin(0.5);

                    this.currentFruit.emojiText = emojiText;
                    this.currentFruit.fruitType = fruitType;

                    // 添加到fruits数组
                    this.fruits.push(this.currentFruit);

                    this.currentFruit = null;

                    // 1秒后创建下一个水果
                    this.time.delayedCall(1000, () => {
                        if (!this.gameOver) {
                            this.createNextFruit();
                        }
                    });
                }
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'gameCanvas',
            backgroundColor: '#2c3e50',
            physics: {
                default: 'matter',
                matter: {
                    debug: false
                }
            },
            scene: GameScene,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            input: {
                touch: {
                    capture: true
                },
                activePointers: 1
            }
        };

        function startGame() {
            console.log('开始初始化游戏...');
            
            if (typeof Phaser === 'undefined') {
                console.error('Phaser没有加载成功');
                document.getElementById('gameCanvas').innerHTML = '<div class="loading">游戏加载失败，请刷新重试</div>';
                return;
            }
            
            console.log('Phaser版本:', Phaser.VERSION);
            
            document.getElementById('gameCanvas').innerHTML = '';
            game = new Phaser.Game(config);
            console.log('游戏已创建');
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startGame);
        } else {
            startGame();
        }

        window.addEventListener('load', () => {
            if (!game) {
                startGame();
            }
        });

    </script>
</body>
</html>
