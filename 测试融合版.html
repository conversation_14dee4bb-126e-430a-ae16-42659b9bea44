<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试融合版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'playing';
    let monstersKilled = 0;
    let totalMonstersInWave = 3;
    let isCreatingWave = false;

    // 合成大西瓜相关变量
    let fruits = [];
    let merging = new Set();
    let containerBounds = null;
    let storageSlots = [];
    let storageArea = null;
    let score = 0;

    // 水果配置
    const FRUITS = [
        { emoji: '🍒', size: 16, points: 1, color: 0xff6b6b },
        { emoji: '🍓', size: 20, points: 3, color: 0xff8e8e },
        { emoji: '🍇', size: 24, points: 6, color: 0x9b59b6 },
        { emoji: '🍊', size: 28, points: 10, color: 0xf39c12 },
        { emoji: '🍋', size: 32, points: 15, color: 0xf1c40f }
    ];

    // 合成大西瓜游戏配置
    const CONTAINER_WIDTH = 300;
    const CONTAINER_HEIGHT = 350;
    const WALL_THICKNESS = 8;
    const STORAGE_HEIGHT = 60;
    const STORAGE_SLOTS = 7;

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        physics: {
            default: 'matter',
            matter: {
                debug: false,
                gravity: { y: 0.8 }
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 创建简单的图形纹理
        this.add.graphics()
            .fillStyle(0x3498db)
            .fillRect(0, 0, 50, 50)
            .generateTexture('player', 50, 50);

        this.add.graphics()
            .fillStyle(0xe74c3c)
            .fillRect(0, 0, 40, 40)
            .generateTexture('monster', 40, 40);

        this.add.graphics()
            .fillStyle(0xf39c12)
            .fillRect(0, 0, 30, 10)
            .generateTexture('weapon', 30, 10);
    }

    // 创建游戏场景
    function create() {
        // 创建主角
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6);
        player.setOrigin(0.5, 1);
        player.setDepth(100 + player.y * 0.1);

        // 创建武器
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'weapon');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建合成大西瓜游戏区域
        createWatermelonGame.call(this);
    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 创建新怪物
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y;

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            let monster = this.add.image(xPos, yPos, 'monster');
            monster.setScale(0.25);
            monster.setOrigin(0.5, 1);
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monsters.push(monster);
        }

        monstersKilled = 0;
        isCreatingWave = false;
    }

    // 创建合成大西瓜游戏区域
    function createWatermelonGame() {
        // 初始化变量
        fruits = [];
        merging = new Set();
        storageSlots = [];
        score = 0;
        
        // 设置物理世界边界
        this.matter.world.setBounds();

        // 创建容器区域
        createFruitContainer.call(this);
        
        // 创建暂存区
        createFruitStorageArea.call(this);
        
        // 设置碰撞检测
        this.matter.world.on('collisionstart', handleFruitCollision, this);
    }

    // 创建水果容器
    function createFruitContainer() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const containerY = 700;

        containerBounds = {
            left: containerX,
            right: containerX + CONTAINER_WIDTH,
            top: containerY,
            bottom: containerY + CONTAINER_HEIGHT
        };

        const wallOptions = { 
            isStatic: true, 
            label: 'wall',
            friction: 0.5,
            restitution: 0.3
        };

        // 创建物理墙壁
        this.matter.add.rectangle(
            containerX - WALL_THICKNESS/2, 
            containerY + CONTAINER_HEIGHT/2, 
            WALL_THICKNESS, 
            CONTAINER_HEIGHT, 
            wallOptions
        );

        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH + WALL_THICKNESS/2, 
            containerY + CONTAINER_HEIGHT/2, 
            WALL_THICKNESS, 
            CONTAINER_HEIGHT, 
            wallOptions
        );

        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH/2, 
            containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2, 
            CONTAINER_WIDTH + WALL_THICKNESS*2, 
            WALL_THICKNESS, 
            wallOptions
        );

        // 绘制容器边框
        const graphics = this.add.graphics();
        graphics.lineStyle(3, 0x4ecdc4, 0.8);
        graphics.strokeRoundedRect(containerX - 2, containerY - 2, CONTAINER_WIDTH + 4, CONTAINER_HEIGHT + 4, 8);
    }

    // 创建暂存区
    function createFruitStorageArea() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const storageY = 700 + CONTAINER_HEIGHT + 20;
        const slotWidth = CONTAINER_WIDTH / STORAGE_SLOTS;

        storageSlots = new Array(STORAGE_SLOTS).fill(null);

        const graphics = this.add.graphics();
        graphics.fillStyle(0x34495e, 0.8);
        graphics.fillRoundedRect(containerX, storageY, CONTAINER_WIDTH, STORAGE_HEIGHT, 5);

        storageArea = {
            x: containerX,
            y: storageY,
            width: CONTAINER_WIDTH,
            height: STORAGE_HEIGHT,
            slotWidth: slotWidth
        };
    }

    // 怪物死亡掉落水果
    function dropFruitFromMonster(monster) {
        if (!containerBounds) return;
        
        const fruitType = Phaser.Math.Between(0, 4);
        const fruitConfig = FRUITS[fruitType];
        
        const dropX = monster.x;
        const dropY = monster.y;
        
        const targetX = Phaser.Math.Between(
            containerBounds.left + fruitConfig.size,
            containerBounds.right - fruitConfig.size
        );
        const targetY = containerBounds.top + 50;

        const fruit = this.add.circle(dropX, dropY, fruitConfig.size, fruitConfig.color);
        
        const emojiText = this.add.text(dropX, dropY, fruitConfig.emoji, {
            fontSize: `${fruitConfig.size * 1.5}px`
        }).setOrigin(0.5);

        fruit.emojiText = emojiText;
        fruit.fruitType = fruitType;

        setupFruitClick.call(this, fruit);

        this.tweens.add({
            targets: [fruit, emojiText],
            x: targetX,
            y: targetY,
            duration: 800,
            ease: 'Bounce.easeOut',
            onComplete: () => {
                this.matter.add.gameObject(fruit, {
                    shape: 'circle',
                    radius: fruitConfig.size,
                    isStatic: false
                });
                
                fruits.push(fruit);
            }
        });
    }

    // 设置水果点击事件
    function setupFruitClick(fruit) {
        fruit.setInteractive();
        fruit.on('pointerdown', () => {
            console.log('Fruit clicked!');
            // 简化版本：直接增加分数
            score += 10;
            if (this.scoreText) {
                this.scoreText.setText(`合成得分: ${score}`);
            }
            
            // 移除水果
            if (fruit.emojiText) fruit.emojiText.destroy();
            fruit.destroy();
            
            const index = fruits.indexOf(fruit);
            if (index > -1) {
                fruits.splice(index, 1);
            }
        });
    }

    // 处理水果碰撞（简化版本）
    function handleFruitCollision(event) {
        // 简化版本，暂时不处理合成
    }

    // 创建UI元素
    function createUI() {
        // 玩家血条
        this.add.text(50, 50, '玩家血量:', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        });

        this.playerHealthText = this.add.text(50, 80, `${playerHealth}/${maxPlayerHealth}`, {
            fontSize: '18px',
            fill: '#4ecdc4',
            fontFamily: 'Arial'
        });

        // 关卡信息
        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 65, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 合成大西瓜得分显示
        this.scoreText = this.add.text(375, 650, `合成得分: ${score}`, {
            fontSize: '24px',
            fill: '#4ecdc4',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 游戏说明
        this.add.text(375, 1300, '击杀怪物掉落水果 | 点击水果获得分数', {
            fontSize: '18px',
            fill: '#95a5a6',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 主角攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            const target = monsters[0];
            
            // 创建攻击特效
            const weaponRightX = playerWeapon.x + 20;
            const weaponCenterY = playerWeapon.y - 10;
            const projectile = this.add.circle(weaponRightX, weaponCenterY, 5, 0xffff00);

            this.tweens.add({
                targets: projectile,
                x: target.x - 30,
                y: target.y - 30,
                duration: 200,
                onComplete: () => {
                    projectile.destroy();

                    let damage = playerStats.attackDamage + currentLevel * 2;
                    target.health -= damage;

                    // 检查怪物是否死亡
                    if (target.health <= 0) {
                        // 怪物死亡时掉落水果
                        dropFruitFromMonster.call(this, target);
                        
                        target.destroy();
                        const index = monsters.indexOf(target);
                        if (index > -1) {
                            monsters.splice(index, 1);
                            monstersKilled++;
                        }
                    }
                }
            });
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }

        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }

        if (gameState === 'playing') {
            // 主角自动攻击
            if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
            if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
                playerAttack.call(this);
                this.lastPlayerAttack = time;
            }

            // 检查波次完成
            if (monsters.length === 0 && !isCreatingWave) {
                isCreatingWave = true;
                currentWave++;

                if (currentWave > 3) {
                    currentLevel++;
                    currentWave = 1;
                    totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
                }

                setTimeout(() => {
                    createWave.call(this);
                }, 1000);
            }
        }

        // 更新水果位置同步
        fruits = fruits.filter(fruit => fruit && fruit.active);
        fruits.forEach(fruit => {
            if (fruit && fruit.emojiText && fruit.body && fruit.body.position) {
                fruit.emojiText.x = fruit.body.position.x;
                fruit.emojiText.y = fruit.body.position.y;
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
