<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>方块了个方块 - 经典三消游戏</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/font-awesome@4.7.0/css/font-awesome.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#8B5CF6',
                        secondary: '#10B981',
                        accent: '#F59E0B',
                        danger: '#EF4444',
                        warning: '#FBBF24',
                        info: '#3B82F6',
                        light: '#F3F4F6',
                        dark: '#1F2937',
                    },
                    fontFamily: {
                        game: ['"Comic Sans MS"', '"Chalkboard SE"', 'sans-serif'],
                    },
                }
            }
        }
    </script>
    <style type="text/tailwindcss">
        @layer utilities {
            .content-auto {
                content-visibility: auto;
            }
            .block-shadow {
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
            }
            .block-selected {
                box-shadow: 0 0 0 3px #FFFFFF, 0 0 0 6px #3B82F6;
                transform: scale(1.05);
            }
            .block-vanish {
                animation: vanish 0.3s ease-out forwards;
            }
            @keyframes vanish {
                0% { transform: scale(1); opacity: 1; }
                50% { transform: scale(1.2); opacity: 0.5; }
                100% { transform: scale(0); opacity: 0; }
            }
            .pulse-animation {
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-purple-50 to-blue-100 min-h-screen font-game text-dark overflow-x-hidden">
    <!-- 游戏容器 -->
    <div class="container mx-auto px-4 py-6 max-w-5xl">
        <!-- 游戏标题 -->
        <header class="text-center mb-6">
            <h1 class="text-[clamp(2rem,5vw,3.5rem)] font-bold text-primary mb-2 pulse-animation">方块了个方块</h1>
            <p class="text-gray-600 text-lg">点击选择三个相同方块进行消除，清空所有方块即可获胜！</p>
        </header>

        <!-- 游戏状态信息 -->
        <div class="flex flex-wrap justify-between items-center mb-6 gap-4">
            <div class="bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 shadow-md flex items-center gap-3">
                <i class="fa fa-clock-o text-accent text-xl"></i>
                <span class="font-bold">时间: </span>
                <span id="timer" class="text-lg">00:00</span>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 shadow-md flex items-center gap-3">
                <i class="fa fa-square text-primary text-xl"></i>
                <span class="font-bold">已消除: </span>
                <span id="score" class="text-lg">0</span>
            </div>
            <div class="bg-white/80 backdrop-blur-sm rounded-lg px-4 py-2 shadow-md flex items-center gap-3">
                <i class="fa fa-heart text-danger text-xl"></i>
                <span class="font-bold">生命: </span>
                <span id="lives" class="text-lg">3</span>
            </div>
            <button id="restart-btn" class="bg-primary hover:bg-primary/90 text-white font-bold py-2 px-6 rounded-full shadow-lg transition-all transform hover:scale-105 flex items-center gap-2">
                <i class="fa fa-refresh"></i>
                <span>重新开始</span>
            </button>
        </div>

        <!-- 选中区域 -->
        <div id="selected-area" class="bg-white/80 backdrop-blur-sm rounded-xl p-4 mb-6 min-h-[100px] flex items-center justify-center gap-4 shadow-lg">
            <div class="text-gray-400 italic">选择的方块将显示在这里...</div>
        </div>

        <!-- 游戏主区域 -->
        <div id="game-container" class="relative bg-white/80 backdrop-blur-sm rounded-xl p-4 md:p-6 shadow-xl overflow-hidden min-h-[400px] md:min-h-[500px] mb-6">
            <!-- 游戏网格将通过JS动态生成 -->
            <div id="game-grid" class="w-full h-full grid grid-cols-7 grid-rows-6 gap-2 md:gap-3 relative">
                <!-- 加载提示 -->
                <div id="loading-screen" class="absolute inset-0 bg-white/90 backdrop-blur-sm flex flex-col items-center justify-center z-10">
                    <div class="w-16 h-16 border-4 border-primary border-t-transparent rounded-full animate-spin mb-4"></div>
                    <p class="text-lg text-gray-600">游戏加载中...</p>
                </div>
            </div>
        </div>

        <!-- 游戏说明 -->
        <div class="bg-white/80 backdrop-blur-sm rounded-xl p-4 shadow-lg mb-6">
            <h2 class="text-xl font-bold text-primary mb-2 flex items-center gap-2"><i class="fa fa-info-circle"></i> 游戏说明</h2>
            <ul class="list-disc list-inside text-gray-700 space-y-1">
                <li>点击方块将其添加到选择区，集齐三个相同方块即可消除</li>
                <li>方块有多层分布，上层方块移开后才能看到下层方块</li>
                <li>如果选择区超过7个方块且无法消除，游戏失败</li>
                <li>清空所有方块即可获胜！</li>
            </ul>
        </div>
    </div>

    <!-- 游戏结果弹窗 -->
    <div id="result-modal" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 hidden">
        <div class="bg-white rounded-2xl p-8 max-w-md w-full mx-4 shadow-2xl transform transition-all">
            <h2 id="result-title" class="text-3xl font-bold text-center mb-4"></h2>
            <p id="result-message" class="text-xl text-center mb-6 text-gray-600"></p>
            <div class="flex justify-center">
                <button id="play-again-btn" class="bg-primary hover:bg-primary/90 text-white font-bold py-3 px-8 rounded-full shadow-lg transition-all transform hover:scale-105">
                    再玩一次
                </button>
            </div>
        </div>
    </div>

    <script>
        // 游戏配置
        const GAME_CONFIG = {
            blockTypes: [
                { color: 'bg-red-500', icon: 'fa-diamond' },
                { color: 'bg-blue-500', icon: 'fa-square' },
                { color: 'bg-green-500', icon: 'fa-circle' },
                { color: 'bg-yellow-500', icon: 'fa-star' },
                { color: 'bg-purple-500', icon: 'fa-heart' },
                { color: 'bg-pink-500', icon: 'fa-bolt' },
                { color: 'bg-indigo-500', icon: 'fa-moon-o' },
                { color: 'bg-orange-500', icon: 'fa-sun-o' },
            ],
            gridSize: { rows: 6, cols: 7 },
            maxSelected: 7,
            baseLayers: 3,
            baseBlockCount: 30,
        };

        // 游戏状态
        let gameState = {
            blocks: [],
            selectedBlocks: [],
            score: 0,
            lives: 3,
            gameTime: 0,
            timerInterval: null,
            gameOver: false,
            isWin: false,
        };

        // DOM元素
        const elements = {
            gameGrid: document.getElementById('game-grid'),
            selectedArea: document.getElementById('selected-area'),
            scoreElement: document.getElementById('score'),
            timerElement: document.getElementById('timer'),
            livesElement: document.getElementById('lives'),
            restartBtn: document.getElementById('restart-btn'),
            resultModal: document.getElementById('result-modal'),
            resultTitle: document.getElementById('result-title'),
            resultMessage: document.getElementById('result-message'),
            playAgainBtn: document.getElementById('play-again-btn'),
            loadingScreen: document.getElementById('loading-screen'),
        };

        // 初始化游戏
        function initGame() {
            // 重置游戏状态
            gameState = {
                blocks: [],
                selectedBlocks: [],
                score: 0,
                lives: 3,
                gameTime: 0,
                timerInterval: null,
                gameOver: false,
                isWin: false,
            };

            // 更新UI显示
            updateScore();
            updateLives();
            elements.selectedArea.innerHTML = '<div class="text-gray-400 italic">选择的方块将显示在这里...</div>';
            elements.resultModal.classList.add('hidden');
            elements.loadingScreen.classList.remove('hidden');

            // 生成游戏方块
            generateBlocks();

            // 启动计时器
            startTimer();

            // 隐藏加载屏幕
            setTimeout(() => {
                elements.loadingScreen.classList.add('hidden');
            }, 800);
        }

        // 生成游戏方块
        function generateBlocks() {
            elements.gameGrid.innerHTML = '';
            gameState.blocks = [];

            // 创建基础网格
            for (let row = 0; row < GAME_CONFIG.gridSize.rows; row++) {
                gameState.blocks[row] = [];
                for (let col = 0; col < GAME_CONFIG.gridSize.cols; col++) {
                    gameState.blocks[row][col] = null;
                }
            }

            // 随机生成方块
            let blockCount = 0;
            const totalBlocks = GAME_CONFIG.baseBlockCount + Math.floor(Math.random() * 10);

            while (blockCount < totalBlocks) {
                const row = Math.floor(Math.random() * GAME_CONFIG.gridSize.rows);
                const col = Math.floor(Math.random() * GAME_CONFIG.gridSize.cols);
                const layer = Math.floor(Math.random() * GAME_CONFIG.baseLayers) + 1;
                const type = Math.floor(Math.random() * GAME_CONFIG.blockTypes.length);

                // 确保位置未被占用
                if (!gameState.blocks[row][col]) {
                    const blockId = `block-${row}-${col}`;
                    gameState.blocks[row][col] = {
                        id: blockId,
                        row,
                        col,
                        layer,
                        type,
                        visible: true,
                        isTopLayer: true,
                    };
                    blockCount++;
                }
            }

            // 确定顶层方块
            determineTopLayerBlocks();

            // 渲染方块
            renderBlocks();
        }

        // 确定顶层方块
        function determineTopLayerBlocks() {
            // 简单实现：假设所有可见方块都是顶层
            // 实际游戏中需要更复杂的逻辑来判断方块是否被覆盖
            gameState.blocks.forEach(row => {
                row.forEach(block => {
                    if (block) block.isTopLayer = true;
                });
            });
        }

        // 渲染方块
        function renderBlocks() {
            elements.gameGrid.innerHTML = '';

            gameState.blocks.forEach((row, rowIndex) => {
                row.forEach((block, colIndex) => {
                    if (block && block.visible) {
                        const blockType = GAME_CONFIG.blockTypes[block.type];
                        const blockElement = document.createElement('div');
                        blockElement.id = block.id;
                        blockElement.className = `block-shadow rounded-lg flex items-center justify-center cursor-pointer transition-all duration-200 ${blockType.color} text-white text-2xl md:text-3xl transform hover:scale-105 z-${block.layer}`;
                        blockElement.style.gridRow = rowIndex + 1;
                        blockElement.style.gridColumn = colIndex + 1;
                        blockElement.innerHTML = `<i class="fa ${blockType.icon}"></i>`;
                        blockElement.addEventListener('click', () => selectBlock(block));
                        elements.gameGrid.appendChild(blockElement);
                    }
                });
            });
        }

        // 选择方块
        function selectBlock(block) {
            if (gameState.gameOver || !block.isTopLayer) return;

            // 添加到选中区域
            gameState.selectedBlocks.push(block);

            // 如果超过最大选中数，游戏失败
            if (gameState.selectedBlocks.length > GAME_CONFIG.maxSelected) {
                gameOver(false);
                return;
            }

            // 更新选中区域显示
            updateSelectedArea();

            // 检查是否可以消除
            checkForMatch();
        }

        // 更新选中区域
        function updateSelectedArea() {
            if (gameState.selectedBlocks.length === 0) {
                elements.selectedArea.innerHTML = '<div class="text-gray-400 italic">选择的方块将显示在这里...</div>';
                return;
            }

            elements.selectedArea.innerHTML = '';
            gameState.selectedBlocks.forEach((block, index) => {
                const blockType = GAME_CONFIG.blockTypes[block.type];
                const blockElement = document.createElement('div');
                blockElement.className = `block-shadow rounded-lg flex items-center justify-center w-12 h-12 text-white text-xl block-selected ${blockType.color}`;
                blockElement.innerHTML = `<i class="fa ${blockType.icon}"></i>`;
                blockElement.addEventListener('click', () => {
                    // 允许取消选择
                    gameState.selectedBlocks.splice(index, 1);
                    updateSelectedArea();
                });
                elements.selectedArea.appendChild(blockElement);
            });
        }

        // 检查是否匹配
        function checkForMatch() {
            if (gameState.selectedBlocks.length < 3) return;

            // 按类型分组
            const typeGroups = {};
            gameState.selectedBlocks.forEach(block => {
                if (!typeGroups[block.type]) {
                    typeGroups[block.type] = [];
                }
                typeGroups[block.type].push(block);
            });

            // 查找有3个相同类型的组
            let matchedType = null;
            Object.keys(typeGroups).forEach(type => {
                if (typeGroups[type].length >= 3) {
                    matchedType = type;
                }
            });

            if (matchedType !== null) {
                // 消除匹配的方块
                const matchedBlocks = typeGroups[matchedType];
                eliminateBlocks(matchedBlocks);

                // 从选中区域移除
                gameState.selectedBlocks = gameState.selectedBlocks.filter(
                    block => block.type !== parseInt(matchedType)
                );
                updateSelectedArea();

                // 更新分数
                gameState.score += matchedBlocks.length;
                updateScore();

                // 检查游戏是否胜利
                checkWinCondition();
            }
        }

        // 消除方块
        function eliminateBlocks(blocks) {
            blocks.forEach(block => {
                const blockElement = document.getElementById(block.id);
                if (blockElement) {
                    blockElement.classList.add('block-vanish');
                    setTimeout(() => {
                        block.visible = false;
                        renderBlocks();
                    }, 300);
                }
            });
        }

        // 检查胜利条件
        function checkWinCondition() {
            // 检查是否还有可见方块
            let hasVisibleBlocks = false;
            gameState.blocks.forEach(row => {
                row.forEach(block => {
                    if (block && block.visible) {
                        hasVisibleBlocks = true;
                    }
                });
            });

            if (!hasVisibleBlocks) {
                gameOver(true);
            }
        }

        // 游戏结束
        function gameOver(isWin) {
            gameState.gameOver = true;
            gameState.isWin = isWin;
            clearInterval(gameState.timerInterval);

            // 更新结果弹窗
            elements.resultTitle.textContent = isWin ? '恭喜获胜！' : '游戏失败';
            elements.resultTitle.className = isWin ? 'text-3xl font-bold text-center mb-4 text-secondary' : 'text-3xl font-bold text-center mb-4 text-danger';
            elements.resultMessage.textContent = isWin ? 
                `你成功消除了所有方块！用时: ${formatTime(gameState.gameTime)}，得分: ${gameState.score}` : 
                `方块槽已满，无法继续游戏！得分: ${gameState.score}`;

            elements.resultModal.classList.remove('hidden');
        }

        // 更新分数
        function updateScore() {
            elements.scoreElement.textContent = gameState.score;
        }

        // 更新生命
        function updateLives() {
            elements.livesElement.textContent = gameState.lives;
        }

        // 启动计时器
        function startTimer() {
            clearInterval(gameState.timerInterval);
            gameState.gameTime = 0;
            elements.timerElement.textContent = '00:00';

            gameState.timerInterval = setInterval(() => {
                gameState.gameTime++;
                elements.timerElement.textContent = formatTime(gameState.gameTime);
            }, 1000);
        }

        // 格式化时间
        function formatTime(seconds) {
            const mins = Math.floor(seconds / 60);
            const secs = seconds % 60;
            return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // 事件监听
        elements.restartBtn.addEventListener('click', initGame);
        elements.playAgainBtn.addEventListener('click', initGame);

        // 初始化游戏
        window.addEventListener('load', initGame);
    </script>
</body>
</html>