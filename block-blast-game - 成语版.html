<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Blast - 方块爆破</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        
        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        
        canvas {
            display: block;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 360,
            height: 640,
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: {
                preload: preload,
                create: create,
                update: update
            },
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            input: {
                activePointers: 3 // 支持多点触控
            }
        };

        // 游戏变量
        let game;
        let gameScene;
        let grid = [];
        let gridSize = 8;
        let cellSize = 35;
        let gridOffsetX = 50;
        let gridOffsetY = 120;
        let score = 0;
        let scoreText;
        let bestScore = 0;
        let bestScoreText;
        let currentBlocks = [];
        let nextBlocks = [];
        let draggedBlock = null;
        let blockPreviewY = 450;

        // 新增：棋盘内汉字方块拖动功能
        let draggingGridBlock = null;
        let dragPreview = null;
        let dragPreviewText = null;
        let dragOrigin = null;

        // 新增：棋盘组合整体拖动
        let draggingGroup = null;
        let dragGroupPreview = null;
        let dragGroupOrigin = null;
        // 新增：拖动时鼠标始终在点击的那个格子中心
        let dragGroupClickOffset = { dRow: 0, dCol: 0 };

        // 新增：记录已消除的目标成语
        let clearedIdioms = [];

        // 新增：拖动高亮预览
        let dragGroupPreviewCells = [];

        // 新增：记录被点击格子在组合内的相对偏移
        let dragGroupClickCellOffset = { dRow: 0, dCol: 0 };

        // 新增：记录鼠标相对被点击格子左上角的像素偏移
        let dragGroupPointerOffset = { offsetX: 0, offsetY: 0 };

        // 成语目标与汉字池
        const idioms = ["画龙点睛", "一马当先", "四面楚歌", "心旷神怡", "滥竽充数", "杯弓蛇影", "亡羊补牢", "掩耳盗铃", "对牛弹琴", "指鹿为马", "守株待兔", "画蛇添足", "刻舟求剑", "盲人摸象", "纸上谈兵", "自相矛盾", "井底之蛙", "狐假虎威", "鹤立鸡群", "如鱼得水"];
        let targetIdioms = [];
        let idiomChars = [];
        let idiomTextObjs = [];
        
        // 方块形状定义
        const blockShapes = [
            // 单个方块
            [[1]],
            // 直线方块
            [[1, 1]],
            [[1, 1, 1]],
            [[1, 1, 1, 1]],
            [[1, 1, 1, 1, 1]],
            [[1], [1]],
            [[1], [1], [1]],
            [[1], [1], [1], [1]],
            [[1], [1], [1], [1], [1]],
            // L形方块
            [[1, 0], [1, 1]],
            [[1, 1], [1, 0]],
            [[1, 1], [0, 1]],
            [[0, 1], [1, 1]],
            [[1, 0, 0], [1, 1, 1]],
            [[0, 0, 1], [1, 1, 1]],
            [[1, 1, 1], [1, 0, 0]],
            [[1, 1, 1], [0, 0, 1]],
            // 正方形方块
            [[1, 1], [1, 1]],
            [[1, 1, 1], [1, 1, 1], [1, 1, 1]],
            // T形方块
            [[1, 1, 1], [0, 1, 0]],
            [[0, 1], [1, 1], [0, 1]],
            [[0, 1, 0], [1, 1, 1]],
            [[1, 0], [1, 1], [1, 0]],
            // Z形方块
            [[1, 1, 0], [0, 1, 1]],
            [[0, 1, 1], [1, 1, 0]],
            [[1, 0], [1, 1], [0, 1]],
            [[0, 1], [1, 1], [1, 0]]
        ];

        // 组合颜色
        const groupColors = [0x3498db, 0xe67e22, 0x2ecc71, 0xe74c3c, 0x9b59b6, 0xf1c40f, 0x1abc9c, 0x34495e];
        let groupIdCounter = 1;
        let groups = {}; // groupId: { color, cells: [{row,col,char}], container }

        function preload() {
            // Phaser会自动处理预加载
        }

        function create() {
            gameScene = this;

            // 加载最高分
            loadBestScore();

            // 创建方块纹理
            createBlockTextures();

            // 初始化网格
            initGrid();

            // 创建UI
            createUI();

            // 生成初始方块
            generateNewBlocks();

            // 设置输入事件
            setupInput();
        }

        function update() {
            // 游戏主循环
        }

        function createBlockTextures() {
            const graphics = gameScene.add.graphics();

            // 创建普通方块纹理
            graphics.clear();
            graphics.fillStyle(0x3498db);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.lineStyle(2, 0x2980b9);
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.generateTexture('block', cellSize, cellSize);

            // 创建网格背景纹理
            graphics.clear();
            graphics.fillStyle(0x34495e);
            graphics.fillRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
            graphics.lineStyle(1, 0x2c3e50);
            graphics.strokeRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
            graphics.generateTexture('gridCell', cellSize, cellSize);

            // 创建高亮方块纹理
            graphics.clear();
            graphics.fillStyle(0xe74c3c);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.lineStyle(2, 0xc0392b);
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.generateTexture('highlightBlock', cellSize, cellSize);

            graphics.destroy();
        }

        function initGrid() {
            // 初始化游戏网格数据
            grid = [];
            for (let row = 0; row < gridSize; row++) {
                grid[row] = [];
                for (let col = 0; col < gridSize; col++) {
                    grid[row][col] = {
                        filled: false,
                        sprite: null,
                        char: null, // 新增：格子内汉字
                        text: null,  // 新增：格子内汉字文本对象
                        groupId: null // 新增：所属组合id
                    };
                }
            }
            
            // 创建网格视觉效果
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    const x = gridOffsetX + col * cellSize;
                    const y = gridOffsetY + row * cellSize;
                    const cell = gameScene.add.image(x, y, 'gridCell');
                    cell.setOrigin(0, 0);
                }
            }
        }

        function createUI() {
            // 标题
            const title = gameScene.add.text(180, 30, 'Block Blast - 成语版', {
                fontSize: '24px',
                fontWeight: 'bold',
                fill: '#ecf0f1',
                align: 'center'
            });
            title.setOrigin(0.5, 0);
            title.setShadow(2, 2, '#34495e', 2, true, true);

            // 顶部目标成语
            targetIdioms = Phaser.Utils.Array.Shuffle(idioms).slice(0, 4);
            idiomChars = targetIdioms.join('').split('');
            idiomTextObjs = [];
            for (let i = 0; i < 4; i++) {
                const idiomText = gameScene.add.text(40 + i * 80, 65, targetIdioms[i], {
                    fontSize: '20px',
                    fill: '#ffe066',
                    fontWeight: 'bold',
                    align: 'center',
                    backgroundColor: '#2c3e50',
                    padding: { left: 6, right: 6, top: 2, bottom: 2 },
                    borderRadius: 6
                });
                idiomText.setOrigin(0, 0);
                idiomText.setShadow(1, 1, '#2c3e50', 1, true, true);
                idiomTextObjs.push(idiomText);
            }

            // 得分显示
            scoreText = gameScene.add.text(180, 100, 'Score: 0', {
                fontSize: '18px',
                fill: '#ecf0f1',
                align: 'center'
            });
            scoreText.setOrigin(0.5, 0);
            scoreText.setShadow(1, 1, '#2c3e50', 1, true, true);

            // 最高分显示
            bestScoreText = gameScene.add.text(180, 125, 'Best: ' + bestScore, {
                fontSize: '14px',
                fill: '#f39c12',
                align: 'center'
            });
            bestScoreText.setOrigin(0.5, 0);
            bestScoreText.setShadow(1, 1, '#2c3e50', 1, true, true);

            // 下一个方块区域背景
            const previewBg = gameScene.add.graphics();
            previewBg.fillStyle(0x34495e, 0.8);
            previewBg.fillRoundedRect(20, blockPreviewY - 20, 320, 120, 10);
            previewBg.lineStyle(2, 0x2c3e50);
            previewBg.strokeRoundedRect(20, blockPreviewY - 20, 320, 120, 10);

            // 添加游戏说明文本
            const instructionText = gameScene.add.text(180, blockPreviewY - 40, '拖动方块到棋盘，拼成目标成语消除', {
                fontSize: '14px',
                fill: '#bdc3c7',
                align: 'center'
            });
            instructionText.setOrigin(0.5, 0);
        }

        function generateNewBlocks() {
            // 清除当前方块
            currentBlocks.forEach(blockGroup => {
                if (blockGroup.container) {
                    blockGroup.container.destroy();
                }
            });
            currentBlocks = [];

            // 生成3个新方块
            // 优先从idiomChars池抽取，保证有解
            let availableChars = idiomChars.slice();
            for (let i = 0; i < 3; i++) {
                const shapeIndex = Phaser.Math.Between(0, blockShapes.length - 1);
                const shape = blockShapes[shapeIndex];
                // 统计需要几个汉字
                let needCount = 0;
                for (let row = 0; row < shape.length; row++) {
                    for (let col = 0; col < shape[row].length; col++) {
                        if (shape[row][col] === 1) needCount++;
                    }
                }
                // 从池中抽取汉字，不够则随机补充
                let chars = [];
                for (let j = 0; j < needCount; j++) {
                    if (availableChars.length > 0) {
                        chars.push(availableChars.shift());
                    } else {
                        // 随机补充目标成语内的字
                        const allChars = targetIdioms.join('').split('');
                        chars.push(allChars[Phaser.Math.Between(0, allChars.length - 1)]);
                    }
                }
                const blockGroup = createBlockGroup(shape, i, chars);
                currentBlocks.push(blockGroup);
            }
        }

        function createBlockGroup(shape, index, chars) {
            const startX = 60 + index * 100;
            const startY = blockPreviewY;
            // 分配颜色
            const color = groupColors[(groupIdCounter + index) % groupColors.length];
            const container = gameScene.add.container(startX, startY);
            const blocks = [];
            let charIdx = 0;
            // 创建方块组
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const block = gameScene.add.image(
                            col * cellSize,
                            row * cellSize,
                            'block'
                        );
                        block.setOrigin(0, 0);
                        block.setTint(color); // 预览区也用组合颜色
                        // 给每个方块分配一个汉字
                        const char = chars ? chars[charIdx++] : '';
                        block.blockChar = char;
                        // 在方块上添加汉字文本
                        const text = gameScene.add.text(
                            col * cellSize + cellSize / 2,
                            row * cellSize + cellSize / 2,
                            char,
                            {
                                fontSize: Math.floor(cellSize * 0.7) + 'px',
                                color: '#fff',
                                fontWeight: 'bold',
                                stroke: '#222',
                                strokeThickness: 3,
                                align: 'center'
                            }
                        );
                        text.setOrigin(0.5, 0.5);
                        container.add([block, text]);
                        // 给每个方块添加交互事件
                        block.setInteractive();
                        block.blockGroupIndex = index;
                        block.shapeRow = row;
                        block.shapeCol = col;
                        // 添加悬停效果
                        block.on('pointerover', function() {
                            if (currentBlocks[index] && !currentBlocks[index].used && !currentBlocks[index].isDragging) {
                                this.setTint(0xaaaaaa);
                                gameScene.input.setDefaultCursor('pointer');
                            }
                        });
                        block.on('pointerout', function() {
                            if (currentBlocks[index] && !currentBlocks[index].used) {
                                this.clearTint();
                                gameScene.input.setDefaultCursor('default');
                                this.setTint(color); // 恢复原色
                            }
                        });
                        blocks.push({block, text, row, col, char});
                    }
                }
            }
            // 设置容器大小但不设置拖拽（改为通过单个方块处理）
            container.setSize(shape[0].length * cellSize, shape.length * cellSize);
            return {
                container: container,
                shape: shape,
                blocks: blocks,
                color: color, // 新增：组合颜色
                originalX: startX,
                originalY: startY,
                used: false,
                isDragging: false
            };
        }

        function setupInput() {
            let dragStartPos = { x: 0, y: 0 };
            let dragOffset = { x: 0, y: 0 };

            // 鼠标/触摸按下
            gameScene.input.on('pointerdown', function (pointer, currentlyOver) {
                if (currentlyOver.length > 0) {
                    const gameObject = currentlyOver[0];

                    // 检查是否点击了方块
                    if (gameObject.blockGroupIndex !== undefined) {
                        const blockGroup = currentBlocks[gameObject.blockGroupIndex];

                        if (blockGroup && !blockGroup.used) {
                            // 检查是否在方块预览区域内
                            if (pointer.y >= blockPreviewY - 50 && pointer.y <= blockPreviewY + 100) {
                                draggedBlock = blockGroup;
                                draggedBlock.isDragging = true;

                                // 记录拖拽开始位置和偏移
                                dragStartPos.x = pointer.x;
                                dragStartPos.y = pointer.y;

                                // 计算点击位置相对于容器的偏移
                                dragOffset.x = pointer.x - draggedBlock.container.x;
                                dragOffset.y = pointer.y - draggedBlock.container.y;

                                // 视觉效果
                                draggedBlock.container.setScale(1.1);
                                draggedBlock.container.setDepth(100);
                                draggedBlock.container.setAlpha(0.8);

                                // 添加拖拽开始的震动效果
                                gameScene.tweens.add({
                                    targets: draggedBlock.container,
                                    scaleX: 1.15,
                                    scaleY: 1.15,
                                    duration: 100,
                                    yoyo: true,
                                    ease: 'Power2'
                                });

                                // 清除所有方块的悬停效果
                                draggedBlock.blocks.forEach(block => {
                                    block.clearTint();
                                });

                                // 触觉反馈（如果支持）
                                if (navigator.vibrate) {
                                    navigator.vibrate(50);
                                }
                            }
                        }
                    }
                }
            });

            // 鼠标/触摸移动
            gameScene.input.on('pointermove', function (pointer) {
                if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                    // 更新容器位置
                    let newX = pointer.x - dragOffset.x;
                    let newY = pointer.y - dragOffset.y;

                    // 边界检测，防止拖拽到屏幕外
                    const shapeWidth = draggedBlock.shape[0].length * cellSize;
                    const shapeHeight = draggedBlock.shape.length * cellSize;

                    newX = Math.max(-shapeWidth/2, Math.min(360 - shapeWidth/2, newX));
                    newY = Math.max(0, Math.min(640 - shapeHeight, newY));

                    // 磁性对齐效果：当接近网格时自动吸附
                    const gridPos = screenToGrid(newX, newY);
                    const alignedPos = gridToScreen(gridPos.row, gridPos.col);
                    const snapDistance = cellSize * 0.3; // 吸附距离

                    if (Math.abs(newX - alignedPos.x) < snapDistance &&
                        Math.abs(newY - alignedPos.y) < snapDistance &&
                        canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                        // 在吸附范围内且可以放置，则吸附到网格
                        newX = alignedPos.x;
                        newY = alignedPos.y;
                    }

                    draggedBlock.container.x = newX;
                    draggedBlock.container.y = newY;

                    // 检查是否可以放置并显示预览
                    checkPlacementPreview(newX, newY);
                }
            });

            // 鼠标/触摸释放
            gameScene.input.on('pointerup', function (pointer) {
                if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                    const canPlace = tryPlaceBlock(draggedBlock.container.x, draggedBlock.container.y);

                    if (canPlace) {
                        placeBlock();

                        // 保存对当前拖拽方块的引用，避免异步回调中的null引用
                        const currentDraggedBlock = draggedBlock;
                        currentDraggedBlock.used = true;

                        // 放置成功的视觉反馈
                        gameScene.tweens.add({
                            targets: currentDraggedBlock.container,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 200,
                            ease: 'Power2.easeIn',
                            onComplete: () => {
                                if (currentDraggedBlock.container) {
                                    currentDraggedBlock.container.setVisible(false);
                                }
                            }
                        });

                        // 触觉反馈
                        if (navigator.vibrate) {
                            navigator.vibrate([30, 10, 30]);
                        }

                        // 检查消除
                        setTimeout(() => {
                            checkAndClearLines();

                            // 检查是否所有方块都已使用
                            if (currentBlocks.every(b => b.used)) {
                                setTimeout(() => {
                                    generateNewBlocks();

                                    // 生成新方块后再检查游戏结束
                                    setTimeout(() => {
                                        if (isGameOver()) {
                                            showGameOver();
                                        }
                                    }, 100);
                                }, 300);
                            } else {
                                // 如果还有未使用的方块，立即检查游戏结束
                                if (isGameOver()) {
                                    showGameOver();
                                }
                            }
                        }, 200);
                    } else {
                        // 返回原位置
                        const currentDraggedBlock = draggedBlock;
                        gameScene.tweens.add({
                            targets: currentDraggedBlock.container,
                            x: currentDraggedBlock.originalX,
                            y: currentDraggedBlock.originalY,
                            duration: 300,
                            ease: 'Back.easeOut'
                        });
                    }

                    // 恢复视觉效果
                    if (draggedBlock && draggedBlock.container) {
                        draggedBlock.container.setScale(1);
                        draggedBlock.container.setDepth(0);
                        draggedBlock.container.setAlpha(1);
                        draggedBlock.isDragging = false;
                    }
                    clearPlacementPreview();
                }

                draggedBlock = null;
            });
        }

        function checkPlacementPreview(x, y) {
            clearPlacementPreview();

            if (!draggedBlock) return;

            // 计算方块形状的中心点偏移
            const shapeWidth = draggedBlock.shape[0].length;
            const shapeHeight = draggedBlock.shape.length;

            // 使用容器的左上角位置来计算网格位置
            const gridPos = screenToGrid(x, y);

            if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                showPlacementPreview(draggedBlock.shape, gridPos.row, gridPos.col);
                return true;
            }
            return false;
        }

        function clearPlacementPreview() {
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    if (grid[row][col].previewSprite) {
                        grid[row][col].previewSprite.destroy();
                        grid[row][col].previewSprite = null;
                    }
                }
            }
        }

        function showPlacementPreview(shape, startRow, startCol) {
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = startRow + row;
                        const gridCol = startCol + col;

                        if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                            const x = gridOffsetX + gridCol * cellSize;
                            const y = gridOffsetY + gridRow * cellSize;

                            const preview = gameScene.add.image(x, y, 'highlightBlock');
                            preview.setOrigin(0, 0);
                            preview.setAlpha(0.6);
                            grid[gridRow][gridCol].previewSprite = preview;
                        }
                    }
                }
            }
        }

        function screenToGrid(screenX, screenY) {
            // 添加半个格子的偏移来改善对齐
            const col = Math.round((screenX - gridOffsetX) / cellSize);
            const row = Math.round((screenY - gridOffsetY) / cellSize);
            return { row, col };
        }

        function gridToScreen(row, col) {
            return {
                x: gridOffsetX + col * cellSize,
                y: gridOffsetY + row * cellSize
            };
        }

        function canPlaceBlockAt(shape, startRow, startCol) {
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = startRow + row;
                        const gridCol = startCol + col;

                        // 检查边界
                        if (gridRow < 0 || gridRow >= gridSize || gridCol < 0 || gridCol >= gridSize) {
                            return false;
                        }

                        // 检查是否已被占用
                        if (grid[gridRow][gridCol].filled) {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        function tryPlaceBlock(screenX, screenY) {
            if (!draggedBlock || !draggedBlock.container) return false;

            const gridPos = screenToGrid(screenX, screenY);

            // 检查是否可以在计算出的网格位置放置方块
            if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                // 如果可以放置，将容器位置对齐到网格
                const alignedPos = gridToScreen(gridPos.row, gridPos.col);
                draggedBlock.container.x = alignedPos.x;
                draggedBlock.container.y = alignedPos.y;
                return true;
            }

            return false;
        }

        function placeBlock() {
            if (!draggedBlock || !draggedBlock.container) return;
            const gridPos = screenToGrid(draggedBlock.container.x, draggedBlock.container.y);
            const shape = draggedBlock.shape;
            // 分配groupId和颜色
            const groupId = groupIdCounter++;
            const color = draggedBlock.color; // 用组合自带颜色
            groups[groupId] = { color, cells: [] };
            // 在网格中放置方块
            let blockIdx = 0;
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = gridPos.row + row;
                        const gridCol = gridPos.col + col;
                        if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                            grid[gridRow][gridCol].filled = true;
                            grid[gridRow][gridCol].groupId = groupId;
                            // 创建方块精灵（用组合颜色）
                            const x = gridOffsetX + gridCol * cellSize;
                            const y = gridOffsetY + gridRow * cellSize;
                            const blockSprite = gameScene.add.image(x, y, 'block');
                            blockSprite.setOrigin(0, 0);
                            blockSprite.setTint(color);
                            // 添加汉字文本
                            const char = draggedBlock.blocks[blockIdx].char;
                            grid[gridRow][gridCol].char = char;
                            const text = gameScene.add.text(x + cellSize / 2, y + cellSize / 2, char, {
                                fontSize: Math.floor(cellSize * 0.7) + 'px',
                                color: '#fff',
                                fontWeight: 'bold',
                                stroke: '#222',
                                strokeThickness: 3,
                                align: 'center'
                            });
                            text.setOrigin(0.5, 0.5);
                            grid[gridRow][gridCol].sprite = blockSprite;
                            grid[gridRow][gridCol].text = text;
                            // 记录到组合cells
                            groups[groupId].cells.push({ row: gridRow, col: gridCol, char });
                            blockIdx++;
                        }
                    }
                }
            }
            enableGroupDragForAll();
        }

        function checkAndClearLines() {
            // 检查所有横/竖连续4格，若拼成目标成语则消除
            let found = false;
            // 横向
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col <= gridSize - 4; col++) {
                    let chars = [];
                    let filled = true;
                    for (let k = 0; k < 4; k++) {
                        if (!grid[row][col + k].filled || !grid[row][col + k].char) {
                            filled = false;
                            break;
                        }
                        chars.push(grid[row][col + k].char);
                    }
                    if (filled) {
                        const word = chars.join('');
                        const idx = targetIdioms.indexOf(word);
                        if (idx !== -1) { // 移除!clearedIdioms.includes(word)
                            clearIdiomCells([{row, col: col}, {row, col: col+1}, {row, col: col+2}, {row, col: col+3}], word);
                            updateScore(1);
                            found = true;
                        }
                    }
                }
            }
            // 纵向
            for (let col = 0; col < gridSize; col++) {
                for (let row = 0; row <= gridSize - 4; row++) {
                    let chars = [];
                    let filled = true;
                    for (let k = 0; k < 4; k++) {
                        if (!grid[row + k][col].filled || !grid[row + k][col].char) {
                            filled = false;
                            break;
                        }
                        chars.push(grid[row + k][col].char);
                    }
                    if (filled) {
                        const word = chars.join('');
                        const idx = targetIdioms.indexOf(word);
                        if (idx !== -1) { // 移除!clearedIdioms.includes(word)
                            clearIdiomCells([{row: row, col}, {row: row+1, col}, {row: row+2, col}, {row: row+3, col}], word);
                            updateScore(1);
                            found = true;
                        }
                    }
                }
            }
            if (found) {
                playLinesClearEffect([]); // 可自定义特效
            }
        }

        function clearIdiomCells(cells, idiomWord) {
            cells.forEach(({row, col}) => {
                const groupId = grid[row][col].groupId;
                if (grid[row][col].sprite) {
                    gameScene.tweens.add({
                        targets: grid[row][col].sprite,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2.easeIn',
                        onComplete: () => {
                            if (grid[row][col].sprite) {
                                grid[row][col].sprite.destroy();
                                grid[row][col].sprite = null;
                            }
                        }
                    });
                }
                if (grid[row][col].text) {
                    gameScene.tweens.add({
                        targets: grid[row][col].text,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2.easeIn',
                        onComplete: () => {
                            if (grid[row][col].text) {
                                grid[row][col].text.destroy();
                                grid[row][col].text = null;
                            }
                        }
                    });
                }
                grid[row][col].filled = false;
                grid[row][col].char = null;
                grid[row][col].groupId = null;
                // 从groups中移除该格
                if (groupId && groups[groupId]) {
                    groups[groupId].cells = groups[groupId].cells.filter(cell => !(cell.row === row && cell.col === col));
                    // 若组合已无格子，彻底删除该组合
                    if (groups[groupId].cells.length === 0) {
                        delete groups[groupId];
                    }
                }
            });
            // 处理顶部目标成语
            if (idiomWord && targetIdioms.includes(idiomWord)) {
                const idx = targetIdioms.indexOf(idiomWord);
                if (idiomTextObjs[idx]) {
                    idiomTextObjs[idx].setAlpha(0.3);
                    idiomTextObjs[idx].setText(idiomWord + ' ✓');
                    idiomTextObjs[idx].setStyle({ fontStyle: 'italic', color: '#aaa', textDecoration: 'line-through' });
                }
                clearedIdioms.push(idiomWord);
            }
        }

        function updateScore(linesCleared) {
            // 基础分数：每行/列10分
            let baseScore = linesCleared * 10;

            // 连击奖励
            if (linesCleared > 1) {
                baseScore *= linesCleared; // 多行消除有额外奖励
            }

            score += baseScore;
            scoreText.setText('Score: ' + score);

            // 分数动画效果
            gameScene.tweens.add({
                targets: scoreText,
                scaleX: 1.2,
                scaleY: 1.2,
                duration: 200,
                yoyo: true,
                ease: 'Power2'
            });
        }

        function playLinesClearEffect(linesToClear) {
            // 创建粒子效果
            linesToClear.forEach(line => {
                if (line.type === 'row') {
                    createRowClearEffect(line.index);
                } else {
                    createColumnClearEffect(line.index);
                }
            });
        }

        function createRowClearEffect(rowIndex) {
            const y = gridOffsetY + rowIndex * cellSize + cellSize / 2;

            for (let i = 0; i < 10; i++) {
                const particle = gameScene.add.circle(
                    gridOffsetX + Math.random() * (gridSize * cellSize),
                    y,
                    3,
                    0xf39c12
                );

                gameScene.tweens.add({
                    targets: particle,
                    alpha: 0,
                    scaleX: 2,
                    scaleY: 2,
                    duration: 500,
                    onComplete: () => particle.destroy()
                });
            }
        }

        function createColumnClearEffect(colIndex) {
            const x = gridOffsetX + colIndex * cellSize + cellSize / 2;

            for (let i = 0; i < 10; i++) {
                const particle = gameScene.add.circle(
                    x,
                    gridOffsetY + Math.random() * (gridSize * cellSize),
                    3,
                    0xf39c12
                );

                gameScene.tweens.add({
                    targets: particle,
                    alpha: 0,
                    scaleX: 2,
                    scaleY: 2,
                    duration: 500,
                    onComplete: () => particle.destroy()
                });
            }
        }

        function isGameOver() {
            // 检查是否还有可用的方块可以放置
            let hasUnusedBlocks = false;
            let canPlaceAnyBlock = false;

            console.log('Checking game over. Current blocks:', currentBlocks.length);

            for (let blockGroup of currentBlocks) {
                if (!blockGroup.used) {
                    hasUnusedBlocks = true;
                    console.log('Found unused block, checking placement...');
                    // 检查这个方块是否可以放置在网格的任何位置
                    for (let row = 0; row < gridSize; row++) {
                        for (let col = 0; col < gridSize; col++) {
                            if (canPlaceBlockAt(blockGroup.shape, row, col)) {
                                canPlaceAnyBlock = true;
                                console.log('Block can be placed at', row, col);
                                return false; // 还有可放置的位置
                            }
                        }
                    }
                }
            }

            console.log('Game over check result:', hasUnusedBlocks, !canPlaceAnyBlock);
            // 只有当有未使用的方块但无法放置任何方块时才算游戏结束
            return hasUnusedBlocks && !canPlaceAnyBlock;
        }

        function showGameOver() {
            // 更新最高分
            updateBestScore();

            // 创建游戏结束遮罩
            const overlay = gameScene.add.graphics();
            overlay.fillStyle(0x000000, 0.7);
            overlay.fillRect(0, 0, 360, 640);
            overlay.setDepth(200);

            // 游戏结束文本
            const gameOverText = gameScene.add.text(180, 250, 'Game Over', {
                fontSize: '36px',
                fontWeight: 'bold',
                fill: '#e74c3c',
                align: 'center'
            });
            gameOverText.setOrigin(0.5);
            gameOverText.setDepth(201);

            // 最终得分
            const finalScoreText = gameScene.add.text(180, 300, 'Final Score: ' + score, {
                fontSize: '24px',
                fill: '#ecf0f1',
                align: 'center'
            });
            finalScoreText.setOrigin(0.5);
            finalScoreText.setDepth(201);

            // 重新开始按钮
            const restartButton = gameScene.add.text(180, 360, 'Restart', {
                fontSize: '24px',
                fill: '#3498db',
                align: 'center',
                backgroundColor: '#2c3e50',
                padding: { x: 20, y: 10 }
            });
            restartButton.setOrigin(0.5);
            restartButton.setDepth(201);
            restartButton.setInteractive();

            // 重新开始游戏
            restartButton.on('pointerdown', function() {
                restartGame();
            });

            // 动画效果
            gameScene.tweens.add({
                targets: [gameOverText, finalScoreText, restartButton],
                alpha: { from: 0, to: 1 },
                y: { from: '+=50', to: '-=50' },
                duration: 500,
                ease: 'Back.easeOut'
            });
        }

        function restartGame() {
            // 重置游戏状态
            score = 0;
            scoreText.setText('Score: 0');

            // 清空网格
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    if (grid[row][col].sprite) {
                        grid[row][col].sprite.destroy();
                        grid[row][col].sprite = null;
                    }
                    if (grid[row][col].previewSprite) {
                        grid[row][col].previewSprite.destroy();
                        grid[row][col].previewSprite = null;
                    }
                    grid[row][col].filled = false;
                    grid[row][col].char = null;
                    grid[row][col].text = null;
                    grid[row][col].groupId = null; // 重置groupId
                }
            }

            // 清除当前方块
            currentBlocks.forEach(blockGroup => {
                if (blockGroup.container) {
                    blockGroup.container.destroy();
                }
            });

            // 重新开始游戏
            gameScene.scene.restart();
            enableGroupDragForAll(); // 重新开始后启用所有拖拽
            clearedIdioms = []; // 重置已消除成语列表
            if (idiomTextObjs && idiomTextObjs.length) {
                for (let i = 0; i < idiomTextObjs.length; i++) {
                    if (idiomTextObjs[i]) {
                        idiomTextObjs[i].setAlpha(1);
                        idiomTextObjs[i].setText(targetIdioms[i]);
                        idiomTextObjs[i].setStyle({ fontStyle: 'normal', color: '#ffe066', textDecoration: 'none' });
                    }
                }
            }
        }

        function loadBestScore() {
            const saved = localStorage.getItem('blockBlastBestScore');
            if (saved) {
                bestScore = parseInt(saved);
            }
        }

        function saveBestScore() {
            localStorage.setItem('blockBlastBestScore', bestScore.toString());
        }

        function updateBestScore() {
            if (score > bestScore) {
                bestScore = score;
                bestScoreText.setText('Best: ' + bestScore);
                saveBestScore();

                // 新纪录动画
                gameScene.tweens.add({
                    targets: bestScoreText,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    duration: 300,
                    yoyo: true,
                    ease: 'Power2'
                });
            }
        }

        // 新增：棋盘内汉字方块拖动功能
        // 移除单格拖动相关函数（enableGridBlockDrag/enableAllGridBlockDrags）

        // 新增：棋盘组合整体拖动
        function enableGroupDragForAll() {
            for (let groupId in groups) {
                groups[groupId].cells.forEach(cell => {
                    const { row, col } = cell;
                    if (grid[row][col].sprite) {
                        grid[row][col].sprite.setInteractive({ cursor: 'pointer' });
                        grid[row][col].sprite.on('pointerdown', function(pointer) {
                            if (!grid[row][col].filled || draggingGroup) return;
                            // 记录鼠标相对被点击格子左上角的像素偏移
                            dragGroupPointerOffset = {
                                offsetX: pointer.x - (gridOffsetX + col * cellSize),
                                offsetY: pointer.y - (gridOffsetY + row * cellSize)
                            };
                            startGroupDrag(groupId, pointer, row, col);
                        });
                    }
                });
            }
        }

        function startGroupDrag(groupId, pointer, clickRow, clickCol) {
            draggingGroup = groupId;
            dragGroupOrigin = groups[groupId].cells.map(cell => ({ ...cell }));
            const minRow = Math.min(...groups[groupId].cells.map(c => c.row));
            const minCol = Math.min(...groups[groupId].cells.map(c => c.col));
            dragGroupPreview = gameScene.add.container();
            groups[groupId].cells.forEach(cell => {
                const dx = (cell.col - minCol) * cellSize;
                const dy = (cell.row - minRow) * cellSize;
                const block = gameScene.add.image(dx, dy, 'block');
                block.setOrigin(0, 0);
                block.setTint(groups[groupId].color);
                const text = gameScene.add.text(dx + cellSize/2, dy + cellSize/2, cell.char, {
                    fontSize: Math.floor(cellSize * 0.7) + 'px',
                    color: '#fff',
                    fontWeight: 'bold',
                    stroke: '#222',
                    strokeThickness: 3,
                    align: 'center'
                });
                text.setOrigin(0.5, 0.5);
                dragGroupPreview.add([block, text]);
            });
            dragGroupPreview.setDepth(999);
            groups[groupId].cells.forEach(cell => {
                if (grid[cell.row][cell.col].sprite) grid[cell.row][cell.col].sprite.setVisible(false);
                if (grid[cell.row][cell.col].text) grid[cell.row][cell.col].text.setVisible(false);
                grid[cell.row][cell.col].filled = false;
            });
            // 设置初始位置
            dragGroupPreview.x = pointer.x - dragGroupPointerOffset.offsetX;
            dragGroupPreview.y = pointer.y - dragGroupPointerOffset.offsetY;
            gameScene.input.on('pointermove', onGroupDragMove);
            gameScene.input.on('pointerup', onGroupDragUp);
        }

        function onGroupDragMove(pointer) {
            if (!draggingGroup || !dragGroupPreview) return;
            // 浮动组合左上角
            dragGroupPreview.x = pointer.x - dragGroupPointerOffset.offsetX;
            dragGroupPreview.y = pointer.y - dragGroupPointerOffset.offsetY;
            // 高亮区域左上角
            const { row: leftTopRow, col: leftTopCol } = screenToGrid(dragGroupPreview.x, dragGroupPreview.y);
            showGroupPlacementPreview(leftTopRow, leftTopCol, dragGroupOrigin);
        }

        function showGroupPlacementPreview(targetRow, targetCol, groupCells) {
            clearGroupPlacementPreview();
            // 计算左上角
            let minRow = Math.min(...groupCells.map(c => c.row));
            let minCol = Math.min(...groupCells.map(c => c.col));
            for (let i = 0; i < groupCells.length; i++) {
                const dRow = groupCells[i].row - minRow;
                const dCol = groupCells[i].col - minCol;
                const row = targetRow + dRow;
                const col = targetCol + dCol;
                if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
                    const x = gridOffsetX + col * cellSize;
                    const y = gridOffsetY + row * cellSize;
                    const preview = gameScene.add.image(x, y, 'highlightBlock');
                    preview.setOrigin(0, 0);
                    preview.setAlpha(0.5);
                    dragGroupPreviewCells.push(preview);
                }
            }
        }
        function clearGroupPlacementPreview() {
            dragGroupPreviewCells.forEach(p => p.destroy());
            dragGroupPreviewCells = [];
        }

        function onGroupDragUp(pointer) {
            if (!draggingGroup || !dragGroupPreview) return;
            clearGroupPlacementPreview();
            // 浮动组合左上角
            const leftTopX = pointer.x - dragGroupPointerOffset.offsetX;
            const leftTopY = pointer.y - dragGroupPointerOffset.offsetY;
            const { row: leftTopRow, col: leftTopCol } = screenToGrid(leftTopX, leftTopY);
            const minRow = Math.min(...dragGroupOrigin.map(c => c.row));
            const minCol = Math.min(...dragGroupOrigin.map(c => c.col));
            const dRow = leftTopRow - minRow;
            const dCol = leftTopCol - minCol;
            // 检查目标区域是否可放置
            let canMove = true;
            let newCells = [];
            for (let i = 0; i < dragGroupOrigin.length; i++) {
                const nRow = dragGroupOrigin[i].row + dRow;
                const nCol = dragGroupOrigin[i].col + dCol;
                if (
                    nRow < 0 || nRow >= gridSize || nCol < 0 || nCol >= gridSize ||
                    (grid[nRow][nCol].filled && grid[nRow][nCol].groupId !== draggingGroup)
                ) {
                    canMove = false;
                    break;
                }
                newCells.push({ row: nRow, col: nCol, char: dragGroupOrigin[i].char });
            }
            // 移动或回原位
            if (canMove) {
                // 清除原位置
                dragGroupOrigin.forEach(cell => {
                    if (grid[cell.row][cell.col].sprite) grid[cell.row][cell.col].sprite.destroy();
                    if (grid[cell.row][cell.col].text) grid[cell.row][cell.col].text.destroy();
                    grid[cell.row][cell.col].sprite = null;
                    grid[cell.row][cell.col].text = null;
                    grid[cell.row][cell.col].filled = false;
                    grid[cell.row][cell.col].char = null;
                    grid[cell.row][cell.col].groupId = null;
                });
                // 放到新位置
                for (let i = 0; i < newCells.length; i++) {
                    const { row, col, char } = newCells[i];
                    grid[row][col].filled = true;
                    grid[row][col].char = char;
                    grid[row][col].groupId = draggingGroup;
                    const x = gridOffsetX + col * cellSize;
                    const y = gridOffsetY + row * cellSize;
                    const blockSprite = gameScene.add.image(x, y, 'block');
                    blockSprite.setOrigin(0, 0);
                    blockSprite.setTint(groups[draggingGroup].color);
                    const text = gameScene.add.text(x + cellSize/2, y + cellSize/2, char, {
                        fontSize: Math.floor(cellSize * 0.7) + 'px',
                        color: '#fff',
                        fontWeight: 'bold',
                        stroke: '#222',
                        strokeThickness: 3,
                        align: 'center'
                    });
                    text.setOrigin(0.5, 0.5);
                    grid[row][col].sprite = blockSprite;
                    grid[row][col].text = text;
                }
                // 更新组合cells
                groups[draggingGroup].cells = newCells;
            } else {
                // 回原位
                for (let i = 0; i < dragGroupOrigin.length; i++) {
                    const { row, col, char } = dragGroupOrigin[i];
                    grid[row][col].filled = true;
                    grid[row][col].char = char;
                    grid[row][col].groupId = draggingGroup;
                    if (grid[row][col].sprite) grid[row][col].sprite.setVisible(true);
                    if (grid[row][col].text) grid[row][col].text.setVisible(true);
                }
            }
            // 清理预览
            dragGroupPreview.destroy();
            dragGroupPreview = null;
            draggingGroup = null;
            dragGroupOrigin = null;
            gameScene.input.off('pointermove', onGroupDragMove);
            gameScene.input.off('pointerup', onGroupDragUp);
            // 检查消除
            checkAndClearLines();
            // 重新绑定拖拽
            enableGroupDragForAll();
        }

        // 在placeBlock和restartGame后只调用enableGroupDragForAll()
        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('Game Error:', e.error);
            // 可以在这里添加错误报告或恢复逻辑
        });

        // 启动游戏
        try {
            game = new Phaser.Game(config);
        } catch (error) {
            console.error('Failed to start game:', error);
            document.getElementById('game-container').innerHTML =
                '<div style="color: white; text-align: center; padding: 50px;">游戏加载失败，请刷新页面重试</div>';
        }
    </script>
</body>
</html>
