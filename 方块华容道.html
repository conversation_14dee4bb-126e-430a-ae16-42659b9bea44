<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>华容道</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/remixicon/4.6.0/remixicon.min.css">
    <script src="https://cdn.tailwindcss.com/3.4.16"></script>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <script>tailwind.config = { theme: { extend: { colors: { primary: '#4A6FFF', secondary: '#FF6B6B' }, borderRadius: { 'none': '0px', 'sm': '4px', DEFAULT: '8px', 'md': '12px', 'lg': '16px', 'xl': '20px', '2xl': '24px', '3xl': '32px', 'full': '9999px', 'button': '8px' } } } }</script>
    <style>
        :where([class^="ri-"])::before {
            content: "\f3c2";
        }

        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #f8f9fa;
            touch-action: manipulation;
        }

        #game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            max-width: 375px;
            max-height: 762px;
            margin: 0 auto;
            overflow: hidden;
        }

        .level-select-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .level-select-content {
            background-color: white;
            padding: 1.5rem;
            border-radius: 8px;
            width: 80%;
            max-width: 320px;
            max-height: 80vh;
            overflow-y: auto;
        }

        .level-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 0.5rem;
        }

        .level-btn {
            background-color: #4A6FFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 0;
            font-size: 1rem;
            cursor: pointer;
        }

        .level-btn.locked {
            background-color: #ccc;
            color: #888;
        }

        .modal-close {
            position: absolute;
            top: 1rem;
            right: 1rem;
            font-size: 1.5rem;
            cursor: pointer;
            color: #333;
        }

        .win-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }

        .win-content {
            background-color: white;
            padding: 2rem;
            border-radius: 8px;
            text-align: center;
            width: 80%;
            max-width: 320px;
        }

        .win-buttons {
            display: flex;
            justify-content: space-around;
            margin-top: 1.5rem;
        }

        .win-btn {
            background-color: #4A6FFF;
            color: white;
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-size: 1rem;
            cursor: pointer;
        }
    </style>
</head>

<body>
    <div id="game-container" class="bg-white"></div>
    <!-- 所有UI都在Phaser游戏引擎内 -->
    <script id="gameLogic">
        window.onload = function () {
            const gameWidth = 375;
            const gameHeight = 762;
            // 华容道关卡设计
            const levels = [
                // 关卡1：简单入门 - 只有2个小方块
                {
                    grid: [
                        [0, 0, 0, 0],
                        [0, 1, 2, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 1, height: 1 },  // 红色方块 (blockId=1 -> colorIndex=0 -> 红色)
                        2: { width: 1, height: 1 }   // 蓝色方块 (blockId=2 -> colorIndex=1 -> 蓝色)
                    },
                    exits: [
                        { side: 'bottom', col: 1, width: 1, color: 0xff6b6b, blockId: 1 }, // 红色出口
                        { side: 'bottom', col: 2, width: 1, color: 0x4a6fff, blockId: 2 }  // 蓝色出口
                    ]
                },
                // 关卡2：增加难度 - 4个小方块
                {
                    grid: [
                        [0, 0, 0, 0],
                        [1, 2, 3, 4],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 1, height: 1 },  // 红色方块 (blockId=1 -> 红色)
                        2: { width: 1, height: 1 },  // 蓝色方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 }   // 黄色方块 (blockId=4 -> 黄色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 1, color: 0xff6b6b, blockId: 1 },    // 红色出口
                        { side: 'top', col: 1, width: 1, color: 0x4a6fff, blockId: 2 },    // 蓝色出口
                        { side: 'bottom', col: 2, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口
                        { side: 'bottom', col: 3, width: 1, color: 0xfcc419, blockId: 4 }  // 黄色出口
                    ]
                },
                // 关卡3：混合大小 - 大方块和小方块
                {
                    grid: [
                        [0, 0, 0, 0],
                        [1, 1, 0, 2],
                        [1, 1, 0, 3],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 1, height: 1 },  // 蓝色小方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 }   // 绿色小方块 (blockId=3 -> 绿色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'bottom', col: 2, width: 1, color: 0x4a6fff, blockId: 2 }, // 蓝色出口
                        { side: 'bottom', col: 3, width: 1, color: 0x51cf66, blockId: 3 }  // 绿色出口
                    ]
                },
                // 关卡4：复杂布局 - 多个方块需要协调移动
                {
                    grid: [
                        [0, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [3, 4, 5, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 2, height: 2 },  // 蓝色大方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色小方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 },  // 黄色小方块 (blockId=4 -> 黄色)
                        5: { width: 1, height: 1 }   // 红色小方块 (blockId=5 -> 红色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'top', col: 2, width: 2, color: 0x4a6fff, blockId: 2 },    // 蓝色大出口
                        { side: 'bottom', col: 0, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口
                        { side: 'bottom', col: 1, width: 1, color: 0xfcc419, blockId: 4 }, // 黄色出口
                        { side: 'bottom', col: 2, width: 1, color: 0xff6b6b, blockId: 5 }  // 红色出口
                    ]
                },
                // 关卡5：终极挑战 - 最复杂的布局
                {
                    grid: [
                        [0, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [3, 4, 5, 6],
                        [3, 7, 5, 6]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 2, height: 2 },  // 蓝色大方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 2 },  // 绿色长方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 },  // 黄色小方块 (blockId=4 -> 黄色)
                        5: { width: 1, height: 2 },  // 红色长方块 (blockId=5 -> 红色)
                        6: { width: 1, height: 2 },  // 蓝色长方块 (blockId=6 -> 蓝色)
                        7: { width: 1, height: 1 }   // 绿色小方块 (blockId=7 -> 绿色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'top', col: 2, width: 2, color: 0x4a6fff, blockId: 2 },    // 蓝色大出口
                        { side: 'bottom', col: 0, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口 (方块3)
                        { side: 'bottom', col: 1, width: 1, color: 0xfcc419, blockId: 4 }, // 黄色出口 (方块4)
                        { side: 'bottom', col: 2, width: 1, color: 0xff6b6b, blockId: 5 }, // 红色出口 (方块5)
                        { side: 'bottom', col: 3, width: 1, color: 0x4a6fff, blockId: 6 }, // 蓝色出口 (方块6)
                        { side: 'top', col: 1, width: 1, color: 0x51cf66, blockId: 7 }     // 绿色出口 (方块7)
                    ]
                }
            ];
            // 方块颜色（只使用4种颜色）
            const blockColors = [
                0xff6b6b, // 红色
                0x4a6fff, // 蓝色
                0x51cf66, // 绿色
                0xfcc419  // 黄色
            ];

            // 根据方块ID获取颜色的函数
            function getBlockColor(blockId) {
                const colorIndex = (blockId - 1) % blockColors.length;
                return blockColors[colorIndex];
            }
            // 游戏状态变量
            let currentLevel = 0;
            let steps = 0;
            let moveHistory = [];
            let maxUnlockedLevel = 0;
            // 尝试从本地存储加载最大解锁关卡
            try {
                const savedMaxLevel = localStorage.getItem('huarongdao_maxLevel');
                if (savedMaxLevel !== null) {
                    maxUnlockedLevel = parseInt(savedMaxLevel, 10);
                }
            } catch (e) {
                console.error('无法访问本地存储:', e);
            }
            // 创建Phaser游戏配置
            const config = {
                type: Phaser.AUTO,
                width: gameWidth,
                height: gameHeight,
                parent: 'game-container',
                backgroundColor: '#ffffff',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                }
            };
            // 创建游戏实例
            const game = new Phaser.Game(config);
            // 游戏变量
            let gameScene;
            let blocks = [];
            let exits = [];
            let cellSize;
            let boardX;
            let boardY;
            let boardWidth;
            let boardHeight;
            let grid;
            let currentBlockSizes;
            let stepText;
            let levelText;
            let selectedBlock = null;
            let startDragPosition = null;
            function preload() {
                // No resources to preload
            }
            function create() {
                gameScene = this;
                // 设置游戏布局
                setupGameLayout();
                // 加载当前关卡
                loadLevel(currentLevel);
                // 创建UI元素
                createUI();
            }
            function update() {
                // 游戏更新逻辑
            }
            function setupGameLayout() {
                // 计算游戏板尺寸和位置
                const headerHeight = gameHeight * 0.1;
                const footerHeight = gameHeight * 0.15;
                boardHeight = gameHeight - headerHeight - footerHeight;
                // 华容道是4列5行的网格
                const cols = 4;
                const rows = 5;
                // 计算单元格大小，确保游戏板能完全适应屏幕
                cellSize = Math.min(gameWidth / cols, boardHeight / rows) * 0.80;
                // 计算游戏板的宽度和位置，使其居中
                boardWidth = cellSize * cols;
                boardX = (gameWidth - boardWidth) / 2;
                boardY = headerHeight + (boardHeight - cellSize * rows) / 2;
                // 绘制游戏背景
                gameScene.add.rectangle(gameWidth / 2, headerHeight / 2, gameWidth, headerHeight, 0x4A6FFF, 0.9)
                   .setOrigin(0.5, 0.5);
                // 绘制游戏板背景
                gameScene.add.rectangle(gameWidth / 2, headerHeight + boardHeight / 2, boardWidth + cellSize * 0.1, cellSize * rows + cellSize * 0.1, 0xEEEEEE)
                   .setOrigin(0.5, 0.5)
                   .setStrokeStyle(2, 0xDDDDDD);
                // 绘制底部控制区背景
                gameScene.add.rectangle(gameWidth / 2, gameHeight - footerHeight / 2, gameWidth, footerHeight, 0xF8F9FA)
                   .setOrigin(0.5, 0.5);
            }
            function loadLevel(levelIndex) {
                // 清除现有方块和出口
                blocks.forEach(block => block.destroy());
                blocks = [];
                exits.forEach(exit => exit.graphics.destroy());
                exits = [];
                // 重置步数
                steps = 0;
                moveHistory = [];
                // 获取当前关卡数据
                const levelData = levels[levelIndex];
                grid = JSON.parse(JSON.stringify(levelData.grid)); // 深拷贝网格
                currentBlockSizes = levelData.blockSizes;
                // 创建出口
                createExits(levelData.exits);
                // 创建方块
                createBlocks();
                // 更新UI
                if (stepText) stepText.setText(`步数: ${steps}`);
                if (levelText) levelText.setText(`关卡: ${levelIndex + 1}/${levels.length}`);
            }
            function createExits(exitData) {
                exitData.forEach(exit => {
                    const { side, col, row, width, color } = exit;
                    let x, y, exitWidth, exitHeight;

                    if (side === 'top') {
                        x = boardX + col * cellSize;
                        y = boardY - cellSize * 0.3; // 出口更窄
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'bottom') {
                        x = boardX + col * cellSize;
                        y = boardY + 5 * cellSize;
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'left') {
                        x = boardX - cellSize * 0.3;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize; // 这里width表示高度
                    } else if (side === 'right') {
                        x = boardX + 4 * cellSize;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize;
                    }

                    const exitBlock = gameScene.add.graphics();
                    exitBlock.fillStyle(color, 1);
                    exitBlock.fillRect(x, y, exitWidth, exitHeight);

                    exits.push({
                        graphics: exitBlock,
                        side,
                        col: col || 0,
                        row: row || 0,
                        width: width || 1,
                        color,
                        blockId: exit.blockId
                    });
                });
            }
            function createBlocks() {
                // 创建方块对象
                const blockIds = new Set();
                // 首先找出所有方块ID
                for (let row = 0; row < grid.length; row++) {
                    for (let col = 0; col < grid[row].length; col++) {
                        const blockId = grid[row][col];
                        if (blockId > 0) {
                            blockIds.add(blockId);
                        }
                    }
                }
                // 为每个方块ID创建方块
                blockIds.forEach(blockId => {
                    // 找到方块的起始位置
                    let startRow = -1, startCol = -1;
                    outerLoop: for (let row = 0; row < grid.length; row++) {
                        for (let col = 0; col < grid[row].length; col++) {
                            if (grid[row][col] === blockId) {
                                startRow = row;
                                startCol = col;
                                break outerLoop;
                            }
                        }
                    }
                    if (startRow >= 0 && startCol >= 0) {
                        const blockSize = currentBlockSizes[blockId];
                        const width = blockSize.width;
                        const height = blockSize.height;
                        // 计算方块的像素位置和大小
                        const x = boardX + startCol * cellSize;
                        const y = boardY + startRow * cellSize;
                        const blockWidth = width * cellSize;
                        const blockHeight = height * cellSize;
                        // 选择方块颜色
                        const colorIndex = (blockId - 1) % blockColors.length;
                        const color = blockColors[colorIndex];
                        // 创建方块图形
                        const block = gameScene.add.graphics();
                        // 绘制方块
                        block.fillStyle(color, 1);
                        block.lineStyle(2, 0xffffff, 1);
                        block.fillRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        block.strokeRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        // 创建方块容器
                        const blockContainer = gameScene.add.container(x + 2, y + 2);
                        blockContainer.add(block);
                        // 存储方块数据
                        blockContainer.setData('id', blockId);
                        blockContainer.setData('row', startRow);
                        blockContainer.setData('col', startCol);
                        blockContainer.setData('width', width);
                        blockContainer.setData('height', height);
                        blockContainer.setData('color', color);
                        // 使方块可交互
                        blockContainer.setInteractive(new Phaser.Geom.Rectangle(0, 0, blockWidth - 4, blockHeight - 4), Phaser.Geom.Rectangle.Contains);
                        // 添加拖动事件
                        gameScene.input.setDraggable(blockContainer);
                        // 将方块添加到数组
                        blocks.push(blockContainer);
                    }
                });
                // 设置拖动事件
                gameScene.input.on('dragstart', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                    startDragPosition = { x: pointer.x, y: pointer.y };
                });
                gameScene.input.on('drag', function (pointer, gameObject, dragX, dragY) {
                    // 不直接更新位置，只记录拖动方向
                });
                gameScene.input.on('dragend', function (pointer, gameObject) {
                    if (startDragPosition) {
                        const dx = pointer.x - startDragPosition.x;
                        const dy = pointer.y - startDragPosition.y;
                        // 确定主要拖动方向
                        if (Math.abs(dx) > Math.abs(dy)) {
                            // 水平拖动
                            if (dx > cellSize / 3) {
                                moveBlock(gameObject, 'right');
                            } else if (dx < -cellSize / 3) {
                                moveBlock(gameObject, 'left');
                            }
                        } else {
                            // 垂直拖动
                            if (dy > cellSize / 3) {
                                moveBlock(gameObject, 'down');
                            } else if (dy < -cellSize / 3) {
                                moveBlock(gameObject, 'up');
                            }
                        }
                        startDragPosition = null;
                    }
                    selectedBlock = null;
                });
                // 添加点击事件
                gameScene.input.on('gameobjectdown', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                });
            }
            function moveBlock(block, direction) {
                const blockId = block.getData('id');
                const startRow = block.getData('row');
                const startCol = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');
                let newRow = startRow;
                let newCol = startCol;
                // 根据方向计算新位置
                switch (direction) {
                    case 'up':
                        newRow--;
                        break;
                    case 'down':
                        newRow++;
                        break;
                    case 'left':
                        newCol--;
                        break;
                    case 'right':
                        newCol++;
                        break;
                }
                // 检查移动是否有效
                if (isValidMove(blockId, startRow, startCol, newRow, newCol, width, height)) {
                    // 记录移动历史
                    moveHistory.push({
                        blockId,
                        fromRow: startRow,
                        fromCol: startCol,
                        toRow: newRow,
                        toCol: newCol
                    });
                    // 更新网格
                    updateGrid(blockId, startRow, startCol, newRow, newCol, width, height);
                    // 更新方块位置
                    block.setData('row', newRow);
                    block.setData('col', newCol);
                    // 动画移动方块
                    gameScene.tweens.add({
                        targets: block,
                        x: boardX + newCol * cellSize + 2,
                        y: boardY + newRow * cellSize + 2,
                        duration: 150,
                        ease: 'Power2',
                        onComplete: function() {
                            if (!gameScene || !gameScene.scene || !gameScene.scene.isActive()) return;
                            // 检查方块是否到达出口
                            checkExitCondition(block);
                            // 检查是否过关
                            checkWinCondition();
                        }
                    });
                    // 增加步数
                    steps++;
                    stepText.setText(`步数: ${steps}`);
                } else {
                    // 无效移动，播放轻微震动动画
                    gameScene.tweens.add({
                        targets: block,
                        x: block.x + (direction === 'right' ? 5 : (direction === 'left' ? -5 : 0)),
                        y: block.y + (direction === 'down' ? 5 : (direction === 'up' ? -5 : 0)),
                        duration: 50,
                        yoyo: true,
                        repeat: 1
                    });
                }
            }
            function isValidMove(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 检查是否超出边界
                if (newRow < 0 || newCol < 0 || newRow + height > grid.length || newCol + width > grid[0].length) {
                    return false;
                }
                // 检查新位置是否被占用
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        const currentCell = grid[startRow + r][startCol + c];
                        const newCell = grid[newRow + r][newCol + c];
                        // 如果新位置不是当前方块或空位，则无法移动
                        if (newCell !== 0 && newCell !== blockId) {
                            return false;
                        }
                    }
                }
                return true;
            }
            function updateGrid(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 清除原位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        if (grid[startRow + r][startCol + c] === blockId) {
                            grid[startRow + r][startCol + c] = 0;
                        }
                    }
                }
                // 设置新位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        grid[newRow + r][newCol + c] = blockId;
                    }
                }
            }
            function checkExitCondition(block) {
                const blockId = block.getData('id');
                const row = block.getData('row');
                const col = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');
                const blockColor = block.getData('color');
                
                levels[currentLevel].exits.forEach(exit => {
                    const { side, col: exitCol, width: exitWidth, color, blockId: exitBlockId } = exit;

                    // 检查方块是否到达出口位置
                    if (side === 'top' && row === 0) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    } else if (side === 'bottom' && row + height === 5) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    }
                });
            }
            
            function executeExitAnimation(block, side, exitCol) {
                const blockId = block.getData('id');
                const width = block.getData('width');
                const height = block.getData('height');
                
                // 首先播放闪烁效果
                gameScene.tweens.add({
                    targets: block,
                    alpha: 0.5,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    onComplete: function() {
                        // 闪烁结束后，执行淡出动画
                        // 获取方块位置信息（在销毁前获取）
                        const row = block.getData('row');
                        const col = block.getData('col');

                        const exitCompleteCallback = function() {
                            // 从blocks数组中移除方块
                            blocks = blocks.filter(b => b !== block);
                            // 更新网格（在销毁前更新）
                            for (let i = 0; i < height; i++) {
                                for (let j = 0; j < width; j++) {
                                    if (grid[row + i] && grid[row + i][col + j] !== undefined) {
                                        grid[row + i][col + j] = 0;
                                    }
                                }
                            }
                            // 销毁方块
                            block.destroy();
                            // 检查是否过关
                            checkWinCondition();
                        };

                        if (side === 'top') {
                            gameScene.tweens.add({
                                targets: block,
                                y: boardY - cellSize * 2,
                                alpha: 0,
                                duration: 500,
                                ease: 'Power2',
                                onComplete: exitCompleteCallback
                            });
                        } else if (side === 'bottom') {
                            gameScene.tweens.add({
                                targets: block,
                                y: boardY + cellSize * 7,
                                alpha: 0,
                                duration: 500,
                                ease: 'Power2',
                                onComplete: exitCompleteCallback
                            });
                        } else {
                            // 处理其他可能的方向，这里添加默认动画
                            gameScene.tweens.add({
                                targets: block,
                                alpha: 0,
                                duration: 500,
                                ease: 'Power2',
                                onComplete: exitCompleteCallback
                            });
                        }
                    }
                });
            }
            
            function checkWinCondition() {
                // 检查是否所有方块都已经出去
                if (blocks.length === 0) {
                    // 所有方块都已消失，游戏胜利
                    // 解锁下一关
                    if (currentLevel === maxUnlockedLevel && currentLevel < levels.length - 1) {
                        maxUnlockedLevel = currentLevel + 1;
                        try {
                            localStorage.setItem('huarongdao_maxLevel', maxUnlockedLevel);
                        } catch (e) {
                            console.error('无法访问本地存储:', e);
                        }
                        // 新关卡已解锁，模态框会在下次打开时自动更新
                    }
                    // 延迟一点显示胜利对话框，确保动画完成
                    setTimeout(() => {
                        showWinModal();
                    }, 100);
                }
            }
            // 游戏内UI变量
            let levelSelectModal = null;
            let winModal = null;

            function createUI() {
                // 创建顶部状态栏
                levelText = gameScene.add.text(20, gameHeight * 0.05, `关卡: ${currentLevel + 1}/${levels.length}`, {
                    fontFamily: 'Arial',
                    fontSize: '18px',
                    color: '#ffffff'
                }).setOrigin(0, 0.5);
                stepText = gameScene.add.text(gameWidth / 2, gameHeight * 0.05, `步数: ${steps}`, {
                    fontFamily: 'Arial',
                    fontSize: '18px',
                    color: '#ffffff'
                }).setOrigin(0.5, 0.5);

                // 创建底部控制区
                const footerY = gameHeight - gameHeight * 0.075;
                const buttonSpacing = gameWidth / 4;

                // 撤销按钮
                const undoBtn = createButton(buttonSpacing * 0.8, footerY, '↩', '撤销', undoLastMove);

                // 重置按钮
                const restartBtn = createButton(buttonSpacing * 1.6, footerY, '↺', '重置', function () {
                    loadLevel(currentLevel);
                });

                // 关卡选择按钮
                const levelsBtn = createButton(buttonSpacing * 2.4, footerY, '≡', '关卡', showLevelSelectModal);

                // 菜单按钮
                const menuBtn = createButton(buttonSpacing * 3.2, footerY, '⋮', '菜单', showGameMenu);
            }

            function createButton(x, y, icon, label, callback) {
                const container = gameScene.add.container(x, y);

                // 按钮背景
                const bg = gameScene.add.circle(0, 0, 25, 0x4A6FFF)
                    .setInteractive()
                    .on('pointerdown', callback)
                    .on('pointerover', function() {
                        this.setScale(1.1);
                    })
                    .on('pointerout', function() {
                        this.setScale(1);
                    });

                // 按钮图标
                const iconText = gameScene.add.text(0, 0, icon, {
                    fontFamily: 'Arial',
                    fontSize: '20px',
                    color: '#ffffff'
                }).setOrigin(0.5, 0.5);

                // 按钮标签
                const labelText = gameScene.add.text(0, 35, label, {
                    fontFamily: 'Arial',
                    fontSize: '12px',
                    color: '#333333'
                }).setOrigin(0.5, 0.5);

                container.add([bg, iconText, labelText]);
                return container;
            }
            function undoLastMove() {
                if (moveHistory.length > 0) {
                    const lastMove = moveHistory.pop();
                    // 找到对应的方块
                    const block = blocks.find(b => b.getData('id') === lastMove.blockId);
                    if (block) {
                        const width = block.getData('width');
                        const height = block.getData('height');
                        // 更新网格
                        updateGrid(lastMove.blockId, lastMove.toRow, lastMove.toCol, lastMove.fromRow, lastMove.fromCol, width, height);
                        // 更新方块位置
                        block.setData('row', lastMove.fromRow);
                        block.setData('col', lastMove.fromCol);
                        // 动画移动方块
                        gameScene.tweens.add({
                            targets: block,
                            x: boardX + lastMove.fromCol * cellSize + 2,
                            y: boardY + lastMove.fromRow * cellSize + 2,
                            duration: 150,
                            ease: 'Power2'
                        });
                        // 减少步数
                        steps--;
                        stepText.setText(`步数: ${steps}`);
                    }
                }
            }

            function showLevelSelectModal() {
                if (levelSelectModal) {
                    levelSelectModal.destroy();
                    levelSelectModal = null;
                }

                // 创建模态框背景
                const modalBg = gameScene.add.rectangle(gameWidth / 2, gameHeight / 2, gameWidth, gameHeight, 0x000000, 0.7)
                    .setInteractive()
                    .on('pointerdown', function() {
                        levelSelectModal.setVisible(false);
                    });

                // 创建模态框内容背景
                const contentBg = gameScene.add.rectangle(gameWidth / 2, gameHeight / 2, gameWidth * 0.9, gameHeight * 0.7, 0xffffff)
                    .setStrokeStyle(2, 0x4A6FFF);

                // 标题
                const title = gameScene.add.text(gameWidth / 2, gameHeight * 0.25, '选择关卡', {
                    fontFamily: 'Arial',
                    fontSize: '24px',
                    color: '#333333',
                    fontStyle: 'bold'
                }).setOrigin(0.5, 0.5);

                // 关卡按钮网格
                const gridContainer = gameScene.add.container(gameWidth / 2, gameHeight / 2);
                const cols = 3;
                const buttonSize = 50;
                const spacing = 70;

                for (let i = 0; i < levels.length; i++) {
                    const row = Math.floor(i / cols);
                    const col = i % cols;
                    const x = (col - 1) * spacing;
                    const y = (row - 1) * spacing;

                    const isUnlocked = i <= maxUnlockedLevel;
                    const buttonColor = isUnlocked ? 0x4A6FFF : 0xCCCCCC;
                    const textColor = isUnlocked ? '#ffffff' : '#666666';

                    const levelBtn = gameScene.add.circle(x, y, buttonSize / 2, buttonColor);
                    const levelText = gameScene.add.text(x, y, (i + 1).toString(), {
                        fontFamily: 'Arial',
                        fontSize: '18px',
                        color: textColor,
                        fontStyle: 'bold'
                    }).setOrigin(0.5, 0.5);

                    if (isUnlocked) {
                        levelBtn.setInteractive()
                            .on('pointerdown', function() {
                                currentLevel = i;
                                loadLevel(i);
                                levelSelectModal.setVisible(false);
                            })
                            .on('pointerover', function() {
                                this.setScale(1.1);
                            })
                            .on('pointerout', function() {
                                this.setScale(1);
                            });
                    }

                    gridContainer.add([levelBtn, levelText]);
                }

                // 关闭按钮
                const closeBtn = gameScene.add.circle(gameWidth * 0.85, gameHeight * 0.25, 20, 0xff6b6b)
                    .setInteractive()
                    .on('pointerdown', function() {
                        levelSelectModal.setVisible(false);
                    });
                const closeIcon = gameScene.add.text(gameWidth * 0.85, gameHeight * 0.25, '×', {
                    fontFamily: 'Arial',
                    fontSize: '24px',
                    color: '#ffffff'
                }).setOrigin(0.5, 0.5);

                levelSelectModal = gameScene.add.container(0, 0);
                levelSelectModal.add([modalBg, contentBg, title, gridContainer, closeBtn, closeIcon]);
            }

            function showWinModal() {
                if (winModal) {
                    winModal.destroy();
                }

                // 创建模态框背景
                const modalBg = gameScene.add.rectangle(gameWidth / 2, gameHeight / 2, gameWidth, gameHeight, 0x000000, 0.7)
                    .setInteractive();

                // 创建模态框内容背景
                const contentBg = gameScene.add.rectangle(gameWidth / 2, gameHeight / 2, gameWidth * 0.8, gameHeight * 0.5, 0xffffff)
                    .setStrokeStyle(3, 0x4A6FFF);

                // 标题
                const title = gameScene.add.text(gameWidth / 2, gameHeight * 0.35, '🎉 恭喜过关！', {
                    fontFamily: 'Arial',
                    fontSize: '24px',
                    color: '#4A6FFF',
                    fontStyle: 'bold'
                }).setOrigin(0.5, 0.5);

                // 步数信息
                const stepsInfo = gameScene.add.text(gameWidth / 2, gameHeight * 0.45, `你用了 ${steps} 步完成了关卡 ${currentLevel + 1}！`, {
                    fontFamily: 'Arial',
                    fontSize: '16px',
                    color: '#333333'
                }).setOrigin(0.5, 0.5);

                // 按钮容器
                const buttonContainer = gameScene.add.container(gameWidth / 2, gameHeight * 0.58);

                // 下一关按钮
                if (currentLevel < levels.length - 1) {
                    const nextBtn = createModalButton(-80, 0, '下一关', 0x4A6FFF, function() {
                        currentLevel++;
                        loadLevel(currentLevel);
                        winModal.destroy();
                        winModal = null;
                    });
                    buttonContainer.add(nextBtn);
                }

                // 关卡选择按钮
                const selectBtn = createModalButton(80, 0, '关卡选择', 0x51cf66, function() {
                    winModal.destroy();
                    winModal = null;
                    showLevelSelectModal();
                });
                buttonContainer.add(selectBtn);

                winModal = gameScene.add.container(0, 0);
                winModal.add([modalBg, contentBg, title, stepsInfo, buttonContainer]);
            }

            function createModalButton(x, y, text, color, callback) {
                const container = gameScene.add.container(x, y);

                const bg = gameScene.add.rectangle(0, 0, 120, 40, color)
                    .setInteractive()
                    .on('pointerdown', callback)
                    .on('pointerover', function() {
                        this.setScale(1.05);
                    })
                    .on('pointerout', function() {
                        this.setScale(1);
                    });

                const label = gameScene.add.text(0, 0, text, {
                    fontFamily: 'Arial',
                    fontSize: '14px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5, 0.5);

                container.add([bg, label]);
                return container;
            }

            function showGameMenu() {
                // 简单的游戏菜单，可以扩展更多功能
                showLevelSelectModal();
            }
            // 所有UI现在都在Phaser游戏引擎内
        };
    </script>
</body>

</html>
