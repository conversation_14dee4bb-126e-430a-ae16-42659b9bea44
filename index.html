<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI小游戏合集 - 精彩游戏等你来玩</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            color: white;
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .games-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .game-category {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .game-category:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        }

        .category-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 15px;
            color: #4a5568;
            border-bottom: 2px solid #667eea;
            padding-bottom: 8px;
        }

        .game-list {
            list-style: none;
        }

        .game-item {
            margin-bottom: 8px;
        }

        .game-link {
            display: block;
            padding: 10px 15px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .game-link:hover {
            transform: scale(1.02);
            box-shadow: 0 4px 15px rgba(240, 147, 251, 0.4);
        }

        .stats {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-top: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }

        .stats h3 {
            color: #4a5568;
            margin-bottom: 10px;
        }

        .stats-number {
            font-size: 2rem;
            font-weight: bold;
            color: #667eea;
        }

        @media (max-width: 768px) {
            .header h1 {
                font-size: 2rem;
            }

            .games-grid {
                grid-template-columns: 1fr;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 AI小游戏合集</h1>
            <p>精彩纷呈的小游戏世界，总有一款适合你！</p>
        </div>

        <div class="games-grid">
            <!-- 经典游戏类 -->
            <div class="game-category">
                <h2 class="category-title">🕹️ 经典游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="tetris.html" class="game-link">俄罗斯方块</a>
                    </li>
                    <li class="game-item">
                        <a href="tetris贪吃蛇.html" class="game-link">俄罗斯方块贪吃蛇</a>
                    </li>
                    <li class="game-item">
                        <a href="俄罗斯方块弹球.html" class="game-link">俄罗斯方块弹球</a>
                    </li>
                    <li class="game-item">
                        <a href="俄罗斯三消.html" class="game-link">俄罗斯三消</a>
                    </li>
                </ul>
            </div>

            <!-- 三消游戏类 -->
            <div class="game-category">
                <h2 class="category-title">💎 三消游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="match3-game.html" class="game-link">经典三消</a>
                    </li>
                    <li class="game-item">
                        <a href="match3-game融合版.html" class="game-link">三消融合版</a>
                    </li>
                    <li class="game-item">
                        <a href="三消塔防.html" class="game-link">三消塔防</a>
                    </li>
                    <li class="game-item">
                        <a href="堆叠数字三消.html" class="game-link">堆叠数字三消</a>
                    </li>
                </ul>
            </div>

            <!-- 方块游戏类 -->
            <div class="game-category">
                <h2 class="category-title">🧩 方块游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="block-blast-game.html" class="game-link">方块爆破</a>
                    </li>
                    <li class="game-item">
                        <a href="block-blast-game - 成语版.html" class="game-link">方块爆破成语版</a>
                    </li>
                    <li class="game-item">
                        <a href="方块华容道.html" class="game-link">方块华容道</a>
                    </li>
                    <li class="game-item">
                        <a href="方块合合.html" class="game-link">方块合合</a>
                    </li>
                    <li class="game-item">
                        <a href="方块排序.html" class="game-link">方块排序</a>
                    </li>
                    <li class="game-item">
                        <a href="方块消消消灭星星.html" class="game-link">方块消消消灭星星</a>
                    </li>
                </ul>
            </div>

            <!-- 塔防游戏类 -->
            <div class="game-category">
                <h2 class="category-title">🏰 塔防游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="2048塔防.html" class="game-link">2048塔防</a>
                    </li>
                    <li class="game-item">
                        <a href="tetris塔防.html" class="game-link">俄罗斯方块塔防</a>
                    </li>
                    <li class="game-item">
                        <a href="蛇蛇塔防.html" class="game-link">蛇蛇塔防</a>
                    </li>
                </ul>
            </div>

            <!-- 数字游戏类 -->
            <div class="game-category">
                <h2 class="category-title">🔢 数字游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="合成10.html" class="game-link">合成10</a>
                    </li>
                    <li class="game-item">
                        <a href="数字华容道.html" class="game-link">数字华容道</a>
                    </li>
                    <li class="game-item">
                        <a href="数字排序消消.html" class="game-link">数字排序消消</a>
                    </li>
                    <li class="game-item">
                        <a href="正负数消消乐.html" class="game-link">正负数消消乐</a>
                    </li>
                    <li class="game-item">
                        <a href="正负数消消乐进阶版.html" class="game-link">正负数消消乐进阶版</a>
                    </li>
                    <li class="game-item">
                        <a href="数轴飞机大战.html" class="game-link">数轴飞机大战</a>
                    </li>
                </ul>
            </div>

            <!-- 益智游戏类 -->
            <div class="game-category">
                <h2 class="category-title">🧠 益智游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="数独大师版.html" class="game-link">数独大师版</a>
                    </li>
                    <li class="game-item">
                        <a href="颜色数独.html" class="game-link">颜色数独</a>
                    </li>
                    <li class="game-item">
                        <a href="幻方游戏.html" class="game-link">幻方游戏</a>
                    </li>
                    <li class="game-item">
                        <a href="tetris华容道.html" class="game-link">俄罗斯方块华容道</a>
                    </li>
                    <li class="game-item">
                        <a href="水排序.html" class="game-link">水排序</a>
                    </li>
                    <li class="game-item">
                        <a href="水排序物理版.html" class="game-link">水排序物理版</a>
                    </li>
                </ul>
            </div>

            <!-- 文字游戏类 -->
            <div class="game-category">
                <h2 class="category-title">📝 文字游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="成语合合.html" class="game-link">成语合合</a>
                    </li>
                    <li class="game-item">
                        <a href="拼字游戏.html" class="game-link">拼字游戏</a>
                    </li>
                </ul>
            </div>

            <!-- 特色游戏类 -->
            <div class="game-category">
                <h2 class="category-title">⭐ 特色游戏</h2>
                <ul class="game-list">
                    <li class="game-item">
                        <a href="sheep-game.html" class="game-link">羊了个羊</a>
                    </li>
                    <li class="game-item">
                        <a href="square-sheep-game.html" class="game-link">方块羊了个羊</a>
                    </li>
                    <li class="game-item">
                        <a href="tetris收集星星.html" class="game-link">俄罗斯方块收集星星</a>
                    </li>
                    <li class="game-item">
                        <a href="tetris球球.html" class="game-link">俄罗斯方块球球</a>
                    </li>
                </ul>
            </div>
        </div>

        <div class="stats">
            <h3>游戏统计</h3>
            <div class="stats-number" id="gameCount">32</div>
            <p>款精彩游戏等你体验</p>
        </div>
    </div>

    <script>
        // 添加一些交互效果
        document.addEventListener('DOMContentLoaded', function() {
            // 统计游戏数量
            const gameLinks = document.querySelectorAll('.game-link');
            document.getElementById('gameCount').textContent = gameLinks.length;

            // 为游戏链接添加点击效果
            gameLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    // 添加点击动画
                    this.style.transform = 'scale(0.95)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1.02)';
                    }, 100);
                });
            });

            // 添加页面加载动画
            const categories = document.querySelectorAll('.game-category');
            categories.forEach((category, index) => {
                category.style.opacity = '0';
                category.style.transform = 'translateY(20px)';
                setTimeout(() => {
                    category.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    category.style.opacity = '1';
                    category.style.transform = 'translateY(0)';
                }, index * 100);
            });
        });
    </script>
</body>
</html>