<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>塔防建造游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2d5016 0%, #1a3009 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .ui-text {
            font-family: 'Arial', sans-serif;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 60; // 建造格子尺寸
        const TOWER_SLOT_COUNT = 5; // 防御塔槽位数量
        const GRID_SIZE = 7; // 建造区域大小 7x7

        // 建筑类型
        const BUILDING_TYPES = [
            { id: 0, color: 0x8B4513, emoji: '🏠️', name: '基地', level: 0, canMerge: false, isDefense: false },
            { id: 1, color: 0xFFD700, emoji: '⚡', name: '电厂', level: 1, canMerge: true, isDefense: false },
            { id: 2, color: 0x708090, emoji: '⛏️', name: '矿厂', level: 1, canMerge: true, isDefense: false },
            { id: 3, color: 0x8B4513, emoji: '🔫', name: '简单机枪塔', level: 1, canMerge: true, isDefense: true },
            { id: 4, color: 0xFF4500, emoji: '💣', name: '炮塔', level: 2, canMerge: true, isDefense: true },
            { id: 5, color: 0x9932CC, emoji: '🔬', name: '激光塔', level: 3, canMerge: true, isDefense: true },
            // 合成后的高级建筑
            { id: 6, color: 0xFFD700, emoji: '⚡⚡', name: '高级电厂', level: 2, canMerge: true, isDefense: false },
            { id: 7, color: 0x708090, emoji: '⛏️⛏️', name: '高级矿厂', level: 2, canMerge: true, isDefense: false },
            { id: 8, color: 0x8B4513, emoji: '🔫🔫', name: '双管机枪塔', level: 2, canMerge: true, isDefense: true },
            { id: 9, color: 0xFF4500, emoji: '💣💣', name: '重型炮塔', level: 3, canMerge: true, isDefense: true },
            { id: 10, color: 0x9932CC, emoji: '🔬🔬', name: '超级激光塔', level: 4, canMerge: false, isDefense: true }
        ];

        // 防御塔槽位显示的类型（只显示防御建筑）
        const DEFENSE_TOWER_TYPES = BUILDING_TYPES.filter(building => building.isDefense);

        // 敌人类型
        const ENEMY_TYPES = [
            { color: 0xFF0000, emoji: '👹', name: '小恶魔', hp: 30, speed: 1.5 },
            { color: 0x8B0000, emoji: '🧟', name: '僵尸', hp: 50, speed: 1.0 },
            { color: 0x006400, emoji: '🐲', name: '小龙', hp: 80, speed: 2.0 },
            { color: 0x4B0082, emoji: '👻', name: '幽灵', hp: 40, speed: 2.5 },
            { color: 0xFF8C00, emoji: '🦅', name: '飞鹰', hp: 25, speed: 3.0 }
        ];

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.buildGrid = []; // 7x7建造网格
                this.towerSlots = []; // 防御塔槽位
                this.enemies = []; // 敌人数组
                this.buildings = []; // 已建造的建筑
                this.gold = 200; // 初始金币
                this.wave = 1; // 当前波次
                this.enemiesKilled = 0; // 击杀数
                this.isWaveActive = false;
                this.selectedBuilding = null; // 选中的建筑（用于拖拽到防御塔区域）
                this.basePosition = { row: 3, col: 3 }; // 基地位置（中心）
            }

            preload() {
                // 创建建筑和敌人纹理
                this.createBuildingTextures();
                this.createEnemyTextures();
            }

            create() {
                // 设置背景（草地色调）
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x228B22);

                // 创建设置按钮（左上角）
                this.createSettingsButton();

                // 创建金币显示（右上角）
                this.goldText = this.add.text(GAME_WIDTH - 30, 30, '🪙 200', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);

                // 创建波次显示（屏幕中央上方，大数字）
                this.waveText = this.add.text(GAME_WIDTH/2, 50, '第1波', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建击杀数显示
                this.killText = this.add.text(30, 100, '击杀: 0', {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0, 0);

                // 创建敌人区域（显示小怪）
                this.createEnemyArea();

                // 创建防御塔选择区域
                this.createTowerSlotArea();

                // 建造区域（7x7网格，向下移动）
                this.buildArea = {
                    x: 40,
                    y: 400,
                    width: GAME_WIDTH - 80,
                    height: 420
                };

                // 创建建造网格
                this.createBuildGrid();

                // 创建控制按钮区域
                this.createControlArea();

                // 初始化游戏
                this.initializeGame();

                // 设置输入事件（现在由各个格子自己处理点击事件）
            }

            createBuildingTextures() {
                BUILDING_TYPES.forEach((buildingType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆角矩形背景
                    graphics.fillStyle(buildingType.color);
                    graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);

                    // 添加边框
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeRoundedRect(2, 2, TILE_SIZE-4, TILE_SIZE-4, 6);

                    graphics.generateTexture(`building_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }

            createEnemyTextures() {
                ENEMY_TYPES.forEach((enemyType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆形背景
                    graphics.fillStyle(enemyType.color);
                    graphics.fillCircle(25, 25, 20);

                    // 添加边框
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeCircle(25, 25, 18);

                    graphics.generateTexture(`enemy_${index}`, 50, 50);
                    graphics.destroy();
                });
            }

            createEnemyArea() {
                // 清理之前的敌人
                if (this.enemies) {
                    this.enemies.forEach(enemy => {
                        if (enemy.sprite) enemy.sprite.destroy();
                    });
                }

                // 敌人显示区域（右侧）
                const enemyAreaX = GAME_WIDTH / 2 + 20;
                const enemyAreaY = 180;
                const enemyAreaWidth = GAME_WIDTH / 2 - 60;
                const enemyAreaHeight = 200;

                // 创建敌人区域背景
                const enemyBg = this.add.graphics();
                enemyBg.fillStyle(0x8B0000, 0.2);
                enemyBg.fillRoundedRect(enemyAreaX, enemyAreaY, enemyAreaWidth, enemyAreaHeight, 15);
                enemyBg.lineStyle(2, 0x8B0000, 0.5);
                enemyBg.strokeRoundedRect(enemyAreaX, enemyAreaY, enemyAreaWidth, enemyAreaHeight, 15);

                // 创建敌人标题
                this.add.text(enemyAreaX + enemyAreaWidth/2, enemyAreaY - 20, '即将到来的敌人', {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FF4444',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 重置敌人数组
                this.enemies = [];

                // 根据波次生成敌人预览
                this.generateEnemyPreview(enemyAreaX + 10, enemyAreaY + 20, enemyAreaWidth - 20, enemyAreaHeight - 40);
            }

            generateEnemyPreview(areaX, areaY, areaWidth, areaHeight) {
                // 根据当前波次确定敌人数量和类型
                const enemyCount = Math.min(5 + this.wave * 2, 20); // 最多20个敌人
                const rows = 4;
                const cols = Math.ceil(enemyCount / rows);
                const enemySize = 30;
                const spacing = 8;

                const totalWidth = cols * enemySize + (cols - 1) * spacing;
                const startX = areaX + (areaWidth - totalWidth) / 2;

                for (let i = 0; i < enemyCount; i++) {
                    const row = i % rows;
                    const col = Math.floor(i / rows);

                    const x = startX + col * (enemySize + spacing);
                    const y = areaY + row * (enemySize + spacing);

                    // 随机选择敌人类型，后期波次有更强的敌人
                    const enemyTypeIndex = this.getEnemyTypeForWave(this.wave);
                    const enemyType = ENEMY_TYPES[enemyTypeIndex];

                    // 创建敌人预览
                    const enemyContainer = this.add.container(x + enemySize/2, y + enemySize/2);

                    // 敌人背景
                    const enemyBg = this.add.image(0, 0, `enemy_${enemyTypeIndex}`);
                    enemyBg.setScale(0.8);

                    // 敌人表情
                    const enemyEmoji = this.add.text(0, 0, enemyType.emoji, {
                        fontSize: '24px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    enemyContainer.add([enemyBg, enemyEmoji]);

                    // 存储敌人信息
                    this.enemies.push({
                        sprite: enemyContainer,
                        type: enemyTypeIndex,
                        hp: enemyType.hp,
                        maxHp: enemyType.hp,
                        speed: enemyType.speed,
                        x: x + enemySize/2,
                        y: y + enemySize/2,
                        isAlive: true
                    });
                }
            }

            getEnemyTypeForWave(wave) {
                // 根据波次返回敌人类型
                if (wave <= 2) {
                    return Phaser.Math.Between(0, 1); // 前两波只有小恶魔和僵尸
                } else if (wave <= 4) {
                    return Phaser.Math.Between(0, 2); // 3-4波加入小龙
                } else if (wave <= 6) {
                    return Phaser.Math.Between(0, 3); // 5-6波加入幽灵
                } else {
                    return Phaser.Math.Between(0, 4); // 7波以后所有敌人类型
                }
            }

            createSettingsButton() {
                // 设置按钮位置和大小
                const buttonSize = 50;
                const buttonX = 30;
                const buttonY = 30;

                // 创建设置按钮背景
                const settingsBg = this.add.graphics();
                settingsBg.fillStyle(0x34495E, 0.9);
                settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                settingsBg.lineStyle(2, 0x2C3E50, 1);
                settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);

                // 设置按钮可交互
                settingsBg.setInteractive(new Phaser.Geom.Rectangle(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize), Phaser.Geom.Rectangle.Contains);

                // 创建设置图标
                const settingsIcon = this.add.text(buttonX, buttonY, '⚙️', {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加悬停效果
                settingsBg.on('pointerover', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x5D6D7E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x34495E, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.1);
                });

                settingsBg.on('pointerout', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x34495E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x2C3E50, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.0);
                });

                // 添加点击事件（暂时只显示提示）
                settingsBg.on('pointerdown', () => {
                    this.showToolTip('设置', buttonX, buttonY - 35);
                });
            }

            createInstructionArea() {
                // 显示加载游戏文字，2秒后消失
                const loadingText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2, '加载游戏中...', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                loadingText.setDepth(1000);

                // 2秒后让加载文字消失
                this.time.delayedCall(2000, () => {
                    this.tweens.add({
                        targets: loadingText,
                        alpha: 0,
                        duration: 500,
                        ease: 'Power2',
                        onComplete: () => {
                            loadingText.destroy();
                        }
                    });
                });
            }

            createTowerSlotArea() {
                // 防御塔区域（左侧）
                const slotAreaX = 40;
                const slotAreaY = 180;
                const slotAreaWidth = GAME_WIDTH / 2 - 60;
                const slotAreaHeight = 200;

                // 创建防御塔区域背景
                const slotBg = this.add.graphics();
                slotBg.fillStyle(0x8B4513, 0.3);
                slotBg.fillRoundedRect(slotAreaX, slotAreaY, slotAreaWidth, slotAreaHeight, 15);
                slotBg.lineStyle(2, 0x8B4513, 0.8);
                slotBg.strokeRoundedRect(slotAreaX, slotAreaY, slotAreaWidth, slotAreaHeight, 15);

                // 设置整个区域可交互（用于接收拖拽）
                slotBg.setInteractive(new Phaser.Geom.Rectangle(slotAreaX, slotAreaY, slotAreaWidth, slotAreaHeight), Phaser.Geom.Rectangle.Contains);
                slotBg.on('pointerdown', () => {
                    this.onTowerAreaClick();
                });

                // 创建标题
                this.add.text(slotAreaX + slotAreaWidth/2, slotAreaY - 20, '防御塔区域', {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#8B4513',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建防御塔槽位（垂直排列）
                this.towerSlots = [];
                const slotSpacing = 10;
                const slotsPerRow = 2;
                const rows = Math.ceil(TOWER_SLOT_COUNT / slotsPerRow);
                const startX = slotAreaX + (slotAreaWidth - (slotsPerRow * TILE_SIZE + (slotsPerRow - 1) * slotSpacing)) / 2 + TILE_SIZE / 2;
                const startY = slotAreaY + 30;

                for (let i = 0; i < TOWER_SLOT_COUNT; i++) {
                    const row = Math.floor(i / slotsPerRow);
                    const col = i % slotsPerRow;
                    const slotX = startX + col * (TILE_SIZE + slotSpacing);
                    const slotY = startY + row * (TILE_SIZE + slotSpacing);

                    // 创建空的防御塔槽位
                    const slotBg = this.add.graphics();
                    slotBg.fillStyle(0x666666, 0.5);
                    slotBg.fillRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                    slotBg.lineStyle(2, 0x444444, 0.8);
                    slotBg.strokeRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);

                    // 设置交互
                    slotBg.setInteractive(new Phaser.Geom.Rectangle(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE), Phaser.Geom.Rectangle.Contains);

                    const slot = {
                        x: slotX,
                        y: slotY,
                        background: slotBg,
                        building: null,
                        isEmpty: true,
                        index: i
                    };

                    // 添加点击事件
                    slotBg.on('pointerdown', () => {
                        this.onTowerSlotClick(slot);
                    });

                    this.towerSlots.push(slot);
                }
            }

            onTowerAreaClick() {
                // 如果有选中的建筑，将其移动到防御塔区域
                if (this.selectedBuilding && this.selectedBuilding.building && this.selectedBuilding.building.isDefense) {
                    // 找到空的槽位
                    const emptySlot = this.towerSlots.find(slot => slot.isEmpty);
                    if (emptySlot) {
                        this.moveBuildingToTowerSlot(this.selectedBuilding, emptySlot);
                    } else {
                        this.showMessage('防御塔区域已满！', '#FF4444');
                    }
                }
            }

            onTowerSlotClick(slot) {
                if (this.selectedBuilding && this.selectedBuilding.building && this.selectedBuilding.building.isDefense) {
                    if (slot.isEmpty) {
                        this.moveBuildingToTowerSlot(this.selectedBuilding, slot);
                    } else {
                        this.showMessage('该槽位已被占用！', '#FF4444');
                    }
                }
            }

            moveBuildingToTowerSlot(buildingCell, towerSlot) {
                const building = buildingCell.building;
                const buildingType = BUILDING_TYPES[building.type];

                // 销毁原建筑
                building.container.destroy();

                // 在防御塔槽位创建新的建筑显示
                const towerContainer = this.add.container(towerSlot.x, towerSlot.y);

                // 建筑背景
                const towerBg = this.add.image(0, 0, `building_${building.type}`);

                // 建筑表情
                const towerEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                towerContainer.add([towerBg, towerEmoji]);

                // 更新槽位状态
                towerSlot.isEmpty = false;
                towerSlot.building = {
                    container: towerContainer,
                    type: building.type,
                    level: building.level,
                    originalCell: buildingCell
                };

                // 更新槽位背景
                towerSlot.background.clear();
                towerSlot.background.fillStyle(buildingType.color, 0.8);
                towerSlot.background.fillRoundedRect(towerSlot.x - TILE_SIZE/2, towerSlot.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                towerSlot.background.lineStyle(2, buildingType.color, 1.0);
                towerSlot.background.strokeRoundedRect(towerSlot.x - TILE_SIZE/2, towerSlot.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);

                // 清空原格子
                buildingCell.building = null;
                buildingCell.isEmpty = true;
                buildingCell.background.clear();
                buildingCell.background.fillStyle(0xFFFFFF, 0.1);
                buildingCell.background.fillRoundedRect(buildingCell.x - TILE_SIZE/2, buildingCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                buildingCell.background.lineStyle(1, 0x228B22, 0.5);
                buildingCell.background.strokeRoundedRect(buildingCell.x - TILE_SIZE/2, buildingCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 添加移动动画
                towerContainer.setScale(0);
                this.tweens.add({
                    targets: towerContainer,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 300,
                    ease: 'Back.easeOut'
                });

                this.selectedBuilding = null;
                this.showMessage(`${buildingType.name} 已部署到防御塔区域！`, '#44FF44');
            }



            createBuildGrid() {
                // 创建7x7建造网格
                this.buildGrid = [];
                const buildAreaY = 400;
                const buildAreaHeight = 720;
                const gridSpacing = (GAME_WIDTH - 80) / GRID_SIZE;
                const startX = 40 + gridSpacing / 2;
                const startY = buildAreaY + 20 + gridSpacing / 2;

                // 创建网格背景
                const gridBg = this.add.graphics();
                gridBg.fillStyle(0x90EE90, 0.3);
                gridBg.fillRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);
                gridBg.lineStyle(2, 0x228B22, 0.8);
                gridBg.strokeRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);

                for (let row = 0; row < GRID_SIZE; row++) {
                    this.buildGrid[row] = [];
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const x = startX + col * gridSpacing;
                        const y = startY + row * gridSpacing;
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);

                        // 创建网格格子
                        const gridCell = this.add.graphics();

                        if (isCenter) {
                            // 基地格子
                            gridCell.fillStyle(0x8B4513, 0.8);
                            gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            gridCell.lineStyle(2, 0x654321, 0.9);
                            gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        } else {
                            // 空格子
                            gridCell.fillStyle(0xFFFFFF, 0.1);
                            gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            gridCell.lineStyle(1, 0x228B22, 0.5);
                            gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        }

                        // 设置交互
                        gridCell.setInteractive(new Phaser.Geom.Rectangle(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE), Phaser.Geom.Rectangle.Contains);

                        const cell = {
                            x: x,
                            y: y,
                            row: row,
                            col: col,
                            background: gridCell,
                            building: null,
                            isEmpty: !isCenter,
                            isBase: isCenter
                        };

                        // 如果是基地，创建基地建筑
                        if (isCenter) {
                            this.createBuilding(cell, 0); // 基地的ID是0
                        }

                        // 添加点击事件处理
                        gridCell.on('pointerdown', () => {
                            this.onCellClick(cell);
                        });

                        // 添加悬停效果（只对空格子）
                        if (!isCenter) {
                            gridCell.on('pointerover', () => {
                                if (cell.isEmpty) {
                                    gridCell.clear();
                                    gridCell.fillStyle(0x90EE90, 0.6);
                                    gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                    gridCell.lineStyle(2, 0x228B22, 0.8);
                                    gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                }
                            });

                            gridCell.on('pointerout', () => {
                                if (cell.isEmpty) {
                                    gridCell.clear();
                                    gridCell.fillStyle(0xFFFFFF, 0.1);
                                    gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                    gridCell.lineStyle(1, 0x228B22, 0.5);
                                    gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                }
                            });
                        }

                        this.buildGrid[row][col] = cell;
                    }
                }
            }

            createBuilding(cell, buildingTypeId) {
                const buildingType = BUILDING_TYPES[buildingTypeId];

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑背景
                const buildingBg = this.add.image(0, 0, `building_${buildingTypeId}`);

                // 建筑表情
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                buildingContainer.add([buildingBg, buildingEmoji]);

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: buildingTypeId,
                    level: buildingType.level,
                    canMerge: buildingType.canMerge,
                    isDefense: buildingType.isDefense
                };

                // 如果不是基地，添加建造动画
                if (buildingTypeId !== 0) {
                    buildingContainer.setScale(0);
                    this.tweens.add({
                        targets: buildingContainer,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }

                return cell.building;
            }

            onCellClick(cell) {
                if (this.isWaveActive) return;

                if (cell.isEmpty) {
                    // 点击空格子，检查是否相邻基地或其他建筑
                    if (this.isAdjacentToBuilding(cell)) {
                        this.generateRandomBuilding(cell);
                    }
                } else if (cell.isBase) {
                    // 点击基地，在周围生成建筑，扣除100金币
                    this.generateBuildingAroundBase(cell);
                } else if (cell.building) {
                    // 点击已有建筑
                    if (this.selectedBuilding === cell) {
                        // 如果已经选中，取消选中
                        this.deselectBuilding();
                    } else {
                        // 选中建筑（用于移动）
                        this.selectBuilding(cell);
                    }
                }
            }

            generateBuildingAroundBase(baseCell) {
                // 检查金币是否足够
                if (this.gold < 100) {
                    this.showMessage('金币不足！需要100金币', '#FF4444');
                    return;
                }

                // 找到基地周围的空格子
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                const emptyAdjacentCells = [];
                for (let dir of directions) {
                    const newRow = baseCell.row + dir.row;
                    const newCol = baseCell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];
                        if (adjacentCell.isEmpty) {
                            emptyAdjacentCells.push(adjacentCell);
                        }
                    }
                }

                if (emptyAdjacentCells.length === 0) {
                    this.showMessage('基地周围没有空位！', '#FF4444');
                    return;
                }

                // 扣除金币
                this.gold -= 100;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 随机选择一个空格子生成建筑
                const randomCell = emptyAdjacentCells[Phaser.Math.Between(0, emptyAdjacentCells.length - 1)];
                this.generateRandomBuilding(randomCell);

                this.showMessage('花费100金币生成建筑！', '#FFD700');
            }

            selectBuilding(cell) {
                // 取消之前的选择
                this.deselectBuilding();

                // 选中当前建筑
                this.selectedBuilding = cell;

                // 添加选中效果
                cell.building.container.setTint(0xFFFF00); // 黄色高亮

                this.showMessage(`选中了 ${BUILDING_TYPES[cell.building.type].name}，点击其他格子移动`, '#FFFF44');
            }

            deselectBuilding() {
                if (this.selectedBuilding) {
                    // 移除选中效果
                    this.selectedBuilding.building.container.clearTint();
                    this.selectedBuilding = null;
                }
            }

            isAdjacentToBuilding(cell) {
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];
                        if (!adjacentCell.isEmpty) {
                            return true;
                        }
                    }
                }
                return false;
            }

            generateRandomBuilding(cell) {
                // 检查是否有选中的建筑要移动
                if (this.selectedBuilding && this.selectedBuilding !== cell) {
                    this.moveBuildingToCell(this.selectedBuilding, cell);
                    return;
                }

                // 随机生成基础建筑（电厂、矿厂、简单机枪塔）
                const basicBuildings = [1, 2, 3]; // 电厂、矿厂、简单机枪塔
                const randomBuildingId = basicBuildings[Phaser.Math.Between(0, basicBuildings.length - 1)];

                this.createBuilding(cell, randomBuildingId);

                // 更新格子背景
                cell.background.clear();
                const buildingType = BUILDING_TYPES[randomBuildingId];
                cell.background.fillStyle(buildingType.color, 0.3);
                cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell.background.lineStyle(2, buildingType.color, 0.8);
                cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                this.showMessage(`生成了 ${buildingType.name}！`, '#44FF44');
            }

            moveBuildingToCell(fromCell, toCell) {
                if (!fromCell.building || !toCell.isEmpty) {
                    this.showMessage('无法移动到该位置！', '#FF4444');
                    return;
                }

                const building = fromCell.building;
                const buildingType = BUILDING_TYPES[building.type];

                // 销毁原位置的建筑显示
                building.container.destroy();

                // 在新位置创建建筑
                this.createBuilding(toCell, building.type);

                // 更新新格子背景
                toCell.background.clear();
                toCell.background.fillStyle(buildingType.color, 0.3);
                toCell.background.fillRoundedRect(toCell.x - TILE_SIZE/2, toCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                toCell.background.lineStyle(2, buildingType.color, 0.8);
                toCell.background.strokeRoundedRect(toCell.x - TILE_SIZE/2, toCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 清空原格子
                fromCell.building = null;
                fromCell.isEmpty = true;
                fromCell.background.clear();
                fromCell.background.fillStyle(0xFFFFFF, 0.1);
                fromCell.background.fillRoundedRect(fromCell.x - TILE_SIZE/2, fromCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                fromCell.background.lineStyle(1, 0x228B22, 0.5);
                fromCell.background.strokeRoundedRect(fromCell.x - TILE_SIZE/2, fromCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 取消选中
                this.selectedBuilding = null;

                this.showMessage(`${buildingType.name} 已移动！`, '#44FF44');
            }

            tryMergeBuilding(cell) {
                const building = cell.building;
                if (!building.canMerge) return;

                // 寻找相邻的相同建筑
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];

                        if (!adjacentCell.isEmpty &&
                            adjacentCell.building &&
                            adjacentCell.building.type === building.type &&
                            adjacentCell.building.canMerge) {

                            // 执行合成
                            this.mergeBuildings(cell, adjacentCell);
                            return;
                        }
                    }
                }

                // 如果是防御建筑，可以拖拽到防御塔区域
                if (building.isDefense) {
                    this.selectedBuilding = cell;
                    this.showMessage(`选中了 ${BUILDING_TYPES[building.type].name}，可拖拽到防御塔区域`, '#FFFF44');
                }
            }

            mergeBuildings(cell1, cell2) {
                const buildingType = cell1.building.type;
                const currentLevel = cell1.building.level;

                // 销毁两个建筑
                cell1.building.container.destroy();
                cell2.building.container.destroy();

                // 清空第二个格子
                cell2.building = null;
                cell2.isEmpty = true;
                cell2.background.clear();
                cell2.background.fillStyle(0xFFFFFF, 0.1);
                cell2.background.fillRoundedRect(cell2.x - TILE_SIZE/2, cell2.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell2.background.lineStyle(1, 0x228B22, 0.5);
                cell2.background.strokeRoundedRect(cell2.x - TILE_SIZE/2, cell2.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 确定合成后的建筑类型
                let newBuildingId;
                switch(buildingType) {
                    case 1: newBuildingId = 6; break; // 电厂 -> 高级电厂
                    case 2: newBuildingId = 7; break; // 矿厂 -> 高级矿厂
                    case 3: newBuildingId = 8; break; // 简单机枪塔 -> 双管机枪塔
                    case 4: newBuildingId = 9; break; // 炮塔 -> 重型炮塔
                    case 5: newBuildingId = 10; break; // 激光塔 -> 超级激光塔
                    case 6: newBuildingId = 6; break; // 高级电厂保持不变
                    case 7: newBuildingId = 7; break; // 高级矿厂保持不变
                    case 8: newBuildingId = 4; break; // 双管机枪塔 -> 炮塔
                    case 9: newBuildingId = 5; break; // 重型炮塔 -> 激光塔
                    default: newBuildingId = buildingType; break;
                }

                // 在第一个格子创建新建筑
                this.createBuilding(cell1, newBuildingId);

                // 更新格子背景
                const newBuildingType = BUILDING_TYPES[newBuildingId];
                cell1.background.clear();
                cell1.background.fillStyle(newBuildingType.color, 0.3);
                cell1.background.fillRoundedRect(cell1.x - TILE_SIZE/2, cell1.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell1.background.lineStyle(2, newBuildingType.color, 0.8);
                cell1.background.strokeRoundedRect(cell1.x - TILE_SIZE/2, cell1.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                this.showMessage(`合成了 ${newBuildingType.name}！`, '#FFD700');
            }

            createControlArea() {
                // 控制按钮区域（在屏幕最下方）
                const controlAreaY = GAME_HEIGHT - 80;
                const buttonWidth = 120;
                const buttonHeight = 60;
                const buttonSpacing = 40;
                const totalButtonsWidth = 2 * buttonWidth + buttonSpacing;
                const startButtonX = (GAME_WIDTH - totalButtonsWidth) / 2;

                // 创建开始波次按钮
                const startWaveButton = this.createControlButton(
                    startButtonX, controlAreaY, buttonWidth, buttonHeight,
                    '开始波次', 0x4CAF50, () => this.startWave()
                );

                // 创建重置按钮
                const resetButton = this.createControlButton(
                    startButtonX + buttonWidth + buttonSpacing, controlAreaY, buttonWidth, buttonHeight,
                    '重置', 0xFF5722, () => this.resetGame()
                );

                this.controlButtons = {
                    startWave: startWaveButton,
                    reset: resetButton
                };
            }

            createControlButton(x, y, width, height, text, color, callback) {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(color);
                buttonBg.fillRoundedRect(x, y, width, height, 10);
                buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x333333);
                buttonBg.strokeRoundedRect(x, y, width, height, 10);

                // 设置按钮可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(x, y, width, height), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(x + width/2, y + height/2, text, {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加点击事件
                buttonBg.on('pointerdown', callback);

                // 添加悬停效果
                buttonBg.on('pointerover', () => {
                    buttonBg.clear();
                    buttonBg.fillStyle(color + 0x222222);
                    buttonBg.fillRoundedRect(x, y, width, height, 10);
                    buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x111111);
                    buttonBg.strokeRoundedRect(x, y, width, height, 10);
                });

                buttonBg.on('pointerout', () => {
                    buttonBg.clear();
                    buttonBg.fillStyle(color);
                    buttonBg.fillRoundedRect(x, y, width, height, 10);
                    buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x333333);
                    buttonBg.strokeRoundedRect(x, y, width, height, 10);
                });

                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            showMessage(text, color = '#FFFFFF') {
                // 显示临时消息
                const message = this.add.text(GAME_WIDTH/2, 150, text, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: color,
                    fontStyle: 'bold',
                    backgroundColor: '#000000',
                    padding: { x: 15, y: 8 },
                    resolution: 2
                }).setOrigin(0.5);
                message.setDepth(1000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (message) {
                        this.tweens.add({
                            targets: message,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                message.destroy();
                            }
                        });
                    }
                });
            }

            getFruitTypeForPlate(plateIndex, fruitIndex) {
                // 根据关卡和水果盘索引生成水果类型
                const level = this.level;

                if (level === 1) {
                    // 第1关：1个水果盘，4个相同水果
                    return 7; // 使用樱桃（最后一个水果类型）
                }

                if (level === 2) {
                    // 第2关：2个水果盘，每个盘子4个相同水果，但两个盘子不同
                    return plateIndex === 0 ? 7 : 6; // 第一个盘子樱桃，第二个盘子猕猴桃
                }

                if (level === 3) {
                    // 第3关：3个水果盘，每个盘子4个相同水果
                    const fruitTypes = [7, 6, 5]; // 樱桃、猕猴桃、草莓
                    return fruitTypes[plateIndex];
                }

                // 第4关及以后：更复杂的组合
                if (level === 4) {
                    // 第4关：4个水果盘，前两个盘子相同水果，后两个盘子混合水果
                    if (plateIndex < 2) {
                        return plateIndex === 0 ? 7 : 6; // 樱桃和猕猴桃
                    } else {
                        // 后两个盘子：2个樱桃 + 2个草莓
                        return fruitIndex < 2 ? 7 : 5;
                    }
                }

                // 第5关及以后：完全随机但保证有解
                const availableTypes = [7, 6, 5, 4]; // 使用4种水果类型
                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getRandomFruitTypeForLevel() {
                // 根据关卡返回随机的可用水果类型（前5关只有1种干扰水果）
                let availableTypes = [];

                if (this.level === 1) {
                    // 第1关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6]; // 樱桃（目标） + 猕猴桃（干扰）
                } else if (this.level === 2) {
                    // 第2关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5]; // 樱桃、猕猴桃（目标） + 草莓（干扰）
                } else if (this.level === 3) {
                    // 第3关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4]; // 樱桃、猕猴桃、草莓（目标） + 葡萄（干扰）
                } else if (this.level === 4) {
                    // 第4关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 3]; // 4种目标水果 + 香蕉（干扰）
                } else if (this.level === 5) {
                    // 第5关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 2]; // 使用橙子作为干扰
                } else {
                    // 第6关及以后：使用更多干扰水果
                    availableTypes = [];
                    for (let i = 0; i < TILE_TYPES.length; i++) {
                        availableTypes.push(i);
                    }
                }

                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getGridSizeForLevel() {
                // 根据关卡返回网格大小
                if (this.level === 1) {
                    return { rows: 4, cols: 4 }; // 第1关：4x4
                } else if (this.level === 2) {
                    return { rows: 4, cols: 5 }; // 第2关：4x5
                } else if (this.level === 3) {
                    return { rows: 5, cols: 5 }; // 第3关：5x5
                } else if (this.level === 4) {
                    return { rows: 5, cols: 6 }; // 第4关：5x6
                } else {
                    return { rows: 6, cols: 6 }; // 第5关及以后：6x6
                }
            }

            getTargetFruitTypes() {
                // 获取当前关卡目标水果盘中需要的所有水果类型
                const targetTypes = new Set();

                if (this.fruitPlates) {
                    this.fruitPlates.forEach(plate => {
                        plate.fruits.forEach(fruit => {
                            targetTypes.add(fruit.getData('typeIndex'));
                        });
                    });
                }

                return Array.from(targetTypes);
            }

            createToolArea() {
                // 道具区域位置（在屏幕最下方）
                const toolAreaHeight = 100;
                const toolAreaY = GAME_HEIGHT - toolAreaHeight;
                const toolSize = 80;
                const toolSpacing = 60;
                const totalToolsWidth = 3 * toolSize + 2 * toolSpacing;
                const startToolX = (GAME_WIDTH - totalToolsWidth) / 2;

                // 创建道具背景区域
                const toolBgHeight = toolAreaHeight - 20; // 留10像素上下边距
                const toolBgY = toolAreaY + 10; // 从顶部留10像素开始

                const toolBg = this.add.graphics();
                toolBg.fillStyle(0x2C3E50, 0.8);
                toolBg.fillRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);
                toolBg.lineStyle(2, 0x34495E, 0.9);
                toolBg.strokeRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);

                // 道具信息
                const tools = [
                    { icon: '🔨', name: '锤子' },
                    { icon: '↶', name: '撤销' },
                    { icon: '🔄', name: '交换' }
                ];

                // 创建道具按钮（在背景框内上下居中）
                tools.forEach((tool, index) => {
                    const toolX = startToolX + index * (toolSize + toolSpacing);
                    const toolY = toolBgY + toolBgHeight / 2 - toolSize / 2; // 在背景框内垂直居中

                    // 创建道具按钮背景
                    const buttonBg = this.add.graphics();
                    buttonBg.fillStyle(0x3498DB, 0.9);
                    buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    buttonBg.lineStyle(3, 0x2980B9, 1);
                    buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);

                    // 设置按钮可交互
                    buttonBg.setInteractive(new Phaser.Geom.Rectangle(toolX, toolY, toolSize, toolSize), Phaser.Geom.Rectangle.Contains);

                    // 创建道具图标
                    const toolIcon = this.add.text(toolX + toolSize/2, toolY + toolSize/2, tool.icon, {
                        fontSize: '48px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    // 添加按钮悬停效果
                    buttonBg.on('pointerover', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x5DADE2, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x3498DB, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        toolIcon.setScale(1.1);
                    });

                    buttonBg.on('pointerout', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x3498DB, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x2980B9, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        toolIcon.setScale(1.0);
                    });

                    // 添加点击事件（暂时只显示提示）
                    buttonBg.on('pointerdown', () => {
                        this.showToolTip(tool.name, toolX + toolSize/2, toolY - 20);
                    });
                });
            }

            showToolTip(toolName, x, y) {
                // 显示道具提示
                const tooltip = this.add.text(x, y, `${toolName}功能开发中...`, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    backgroundColor: '#2C3E50',
                    padding: { x: 10, y: 5 },
                    resolution: 2
                }).setOrigin(0.5);
                tooltip.setDepth(2000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (tooltip) {
                        this.tweens.add({
                            targets: tooltip,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                tooltip.destroy();
                            }
                        });
                    }
                });
            }

            spawnTargetFruits() {
                // 重新创建水果盘区域（createEnemyArea会处理清理工作）
                this.createEnemyArea();
            }



            initializeGame() {
                // 重置游戏状态
                this.gold = 200;
                this.wave = 1;
                this.enemiesKilled = 0;
                this.isWaveActive = false;
                this.selectedBuilding = null;

                // 更新UI
                this.goldText.setText(`🪙 ${this.gold}`);
                this.waveText.setText(`第${this.wave}波`);
                this.killText.setText(`击杀: ${this.enemiesKilled}`);

                // 清理已建造的建筑
                this.buildings.forEach(building => {
                    if (building.container) building.container.destroy();
                });
                this.buildings = [];

                // 重置建造网格
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);

                        if (cell.building && !isCenter) {
                            cell.building.container.destroy();
                            cell.building = null;
                        }

                        if (!isCenter) {
                            cell.isEmpty = true;
                            // 重置格子背景
                            cell.background.clear();
                            cell.background.fillStyle(0xFFFFFF, 0.1);
                            cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            cell.background.lineStyle(1, 0x228B22, 0.5);
                            cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        }
                    }
                }

                // 重置防御塔槽位
                this.towerSlots.forEach(slot => {
                    if (slot.building) {
                        slot.building.container.destroy();
                        slot.building = null;
                    }
                    slot.isEmpty = true;

                    // 重置槽位背景
                    slot.background.clear();
                    slot.background.fillStyle(0x666666, 0.5);
                    slot.background.fillRoundedRect(slot.x - TILE_SIZE/2, slot.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                    slot.background.lineStyle(2, 0x444444, 0.8);
                    slot.background.strokeRoundedRect(slot.x - TILE_SIZE/2, slot.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                });

                // 重新生成敌人预览
                this.createEnemyArea();
            }

            startWave() {
                if (this.isWaveActive) return;

                this.isWaveActive = true;
                this.showMessage(`第${this.wave}波开始！`, '#FFD700');

                // 开始敌人移动和战斗逻辑
                this.spawnEnemies();
            }

            spawnEnemies() {
                // 这里可以添加敌人移动和战斗逻辑
                // 为了简化，我们直接模拟战斗结果
                this.time.delayedCall(3000, () => {
                    this.endWave();
                });
            }

            endWave() {
                this.isWaveActive = false;

                // 给予奖励金币
                const reward = 50 + this.wave * 10;
                this.gold += reward;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 增加击杀数
                this.enemiesKilled += this.enemies.length;
                this.killText.setText(`击杀: ${this.enemiesKilled}`);

                this.showMessage(`波次完成！获得 ${reward} 金币`, '#44FF44');

                // 进入下一波
                this.wave++;
                this.waveText.setText(`第${this.wave}波`);

                // 重新生成敌人预览
                this.createEnemyArea();
            }

            resetGame() {
                this.initializeGame();
                this.showMessage('游戏已重置', '#FFFF44');
            }





            selectGridTile(tile, row, col) {
                this.isAnimating = true;

                // 找到空的槽位（跳过锁住的槽位）
                const emptySlot = this.slots.find(slot => slot.tile === null && !slot.isLocked);

                if (!emptySlot) {
                    // 槽位已满，游戏结束
                    this.gameOver = true;
                    this.showGameOver();
                    this.isAnimating = false;
                    return;
                }

                // 从网格中移除方块
                this.tileGrid[row][col] = null;

                // 移动方块到槽位，同时缩小以匹配暂存区格子大小
                this.tweens.add({
                    targets: tile,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    scaleX: 0.83, // 从1.2缩小到1.0 (1.0/1.2 ≈ 0.83)
                    scaleY: 0.83,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 将方块放入槽位
                        emptySlot.tile = tile;

                        // 检查是否有三个相同的方块
                        this.checkForMatches();

                        // 执行列向上移动和补充新方块
                        this.moveColumnUp(col);

                        // 检查暂存区是否已满（游戏失败条件）
                        this.checkGameOverCondition();

                        this.isAnimating = false;

                        // 检查胜利条件（目标水果盘完成）
                        // 这里暂时不检查胜利条件，因为胜利逻辑在handlePlateComplete中处理
                    }
                });
            }

            moveColumnUp(col) {
                const gridSize = this.getGridSizeForLevel();
                const rows = gridSize.rows;
                const cols = gridSize.cols;
                const tileSpacing = 102; // 使用与createStackLayout一致的间距
                const startX = this.stackArea.x + (this.stackArea.width - (cols - 1) * tileSpacing) / 2;
                const startY = this.stackArea.y + 20; // 与createStackLayout保持一致

                // 将该列的所有方块向上移动一格
                for (let row = 0; row < rows - 1; row++) {
                    if (this.tileGrid[row + 1][col]) {
                        const tile = this.tileGrid[row + 1][col];
                        this.tileGrid[row][col] = tile;
                        this.tileGrid[row + 1][col] = null;

                        // 更新方块的网格位置数据
                        tile.setData('gridRow', row);

                        // 动画移动到新位置
                        const newY = startY + row * tileSpacing;
                        this.tweens.add({
                            targets: tile,
                            y: newY,
                            duration: 200,
                            ease: 'Power2'
                        });
                    }
                }

                // 在最底部补充新方块（根据关卡限制水果种类）
                const newTileType = this.getRandomFruitTypeForLevel();
                const newX = startX + col * tileSpacing;
                const newY = startY + (rows - 1) * tileSpacing;
                const newTile = this.createGridTile(newX, newY, newTileType, rows - 1, col);
                this.tileGrid[rows - 1][col] = newTile;

                // 新方块从下方滑入的动画
                newTile.y += 100;
                newTile.setAlpha(0);
                this.tweens.add({
                    targets: newTile,
                    y: newY,
                    alpha: 1,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 更新可点击状态
                        this.updateGridClickableStates();
                    }
                });
            }



            checkForMatches() {
                // 统计暂存区每种类型的水果数量
                const typeCounts = {};

                this.slots.forEach(slot => {
                    if (slot.tile) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        typeCounts[typeIndex] = (typeCounts[typeIndex] || 0) + 1;
                    }
                });

                // 更新水果盘中小水果的状态（变暗/变亮）
                this.updateFruitPlateStates(typeCounts);

                // 检查是否有完整的水果盘可以消除
                this.checkCompletePlates(typeCounts);
            }

            updateFruitPlateStates(typeCounts) {
                // 首先重置所有剩余水果盘中小水果的匹配状态
                this.fruitPlates.forEach(plate => {
                    plate.fruits.forEach(fruit => {
                        const wasMatched = fruit.getData('isMatched');
                        fruit.setData('isMatched', false);
                        fruit.setData('matchedSlotIndex', -1);

                        if (wasMatched) {
                            // 如果之前是亮的，现在变暗
                            fruit.setAlpha(0.6);
                            fruit.setData('isDark', true);
                        }
                    });
                });

                // 为每个暂存区的水果找到对应的小水果进行一对一匹配
                this.slots.forEach((slot, slotIndex) => {
                    if (!slot.tile) return;

                    const slotFruitType = slot.tile.getData('typeIndex');

                    // 找到第一个未匹配的、类型相同的小水果
                    let foundMatch = false;
                    for (let plate of this.fruitPlates) {
                        if (foundMatch) continue;

                        for (let fruit of plate.fruits) {
                            if (foundMatch) break;

                            const fruitType = fruit.getData('typeIndex');
                            const isMatched = fruit.getData('isMatched');

                            if (fruitType === slotFruitType && !isMatched) {
                                // 找到匹配，让小水果变亮
                                fruit.setAlpha(1.0);
                                fruit.setData('isDark', false);
                                fruit.setData('isMatched', true);
                                fruit.setData('matchedSlotIndex', slotIndex);
                                foundMatch = true;
                            }
                        }
                    }
                });
            }

            checkCompletePlates(typeCounts) {
                // 检查是否有水果盘的所有小水果都被匹配了（变亮了）
                // 需要从后往前遍历，因为完成的水果盘会从数组中移除
                for (let plateIndex = this.fruitPlates.length - 1; plateIndex >= 0; plateIndex--) {
                    const plate = this.fruitPlates[plateIndex];

                    // 检查这个水果盘是否所有小水果都被匹配了（都变亮了）
                    const allMatched = plate.fruits.every(fruit => fruit.getData('isMatched'));

                    if (allMatched) {
                        // 收集这个水果盘需要的暂存区水果
                        const tilesToRemove = [];
                        plate.fruits.forEach(fruit => {
                            const slotIndex = fruit.getData('matchedSlotIndex');
                            if (slotIndex >= 0 && this.slots[slotIndex].tile) {
                                tilesToRemove.push({
                                    tile: this.slots[slotIndex].tile,
                                    slot: this.slots[slotIndex],
                                    targetFruit: fruit
                                });
                                this.slots[slotIndex].tile = null;
                            }
                        });

                        if (tilesToRemove.length === 4) { // 确保有4个水果
                            this.completeFruitPlate(plateIndex, tilesToRemove);
                        }
                    }
                }
            }

            completeFruitPlate(plateIndex, tilesToRemove) {
                const plate = this.fruitPlates[plateIndex];

                // 播放消除动画
                this.playFruitPlateCompleteAnimation(tilesToRemove, plate);
            }

            playFruitPlateCompleteAnimation(tilesToRemove, plate) {
                // 让水果飞向盘子里对应的小水果位置
                let completedCount = 0;

                tilesToRemove.forEach((item, index) => {
                    const targetFruit = item.targetFruit;

                    // 延迟不同时间让水果飞向对应的小水果位置
                    this.time.delayedCall(index * 100, () => {
                        this.tweens.add({
                            targets: item.tile,
                            x: targetFruit.x,
                            y: targetFruit.y,
                            scaleX: 0.6,
                            scaleY: 0.6,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                // 显示完成特效在小水果位置
                                this.showCompleteAnimation(targetFruit.x, targetFruit.y);

                                // 暂存区水果消失
                                item.tile.destroy();

                                completedCount++;

                                // 如果是最后一个水果，处理完成逻辑
                                if (completedCount === tilesToRemove.length) {
                                    this.time.delayedCall(300, () => {
                                        this.handlePlateComplete(plate);
                                    });
                                }
                            }
                        });
                    });
                });
            }

            handlePlateComplete(plate) {
                // 计算分数
                const points = 100;
                this.score += points;
                this.scoreText.setText(`🪙 ${this.score}`);

                // 显示金币飞行动画
                this.showCoinFlyAnimation(plate.x, plate.y, points);

                this.showScoreAnimation(points);

                // 让完成的水果盘立即消失
                this.tweens.add({
                    targets: [plate.background, ...plate.fruits],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        // 销毁水果盘元素
                        plate.background.destroy();
                        plate.fruits.forEach(fruit => fruit.destroy());

                        // 从水果盘数组中移除这个盘子
                        const plateIndex = this.fruitPlates.indexOf(plate);
                        if (plateIndex > -1) {
                            this.fruitPlates.splice(plateIndex, 1);
                        }

                        // 重新排列剩余水果盘居中
                        this.time.delayedCall(300, () => {
                            this.reorganizeFruitPlates();
                        });
                    }
                });

                // 整理暂存区槽位
                this.reorganizeSlots();

                // 检查是否所有水果盘都完成了
                this.time.delayedCall(600, () => {
                    if (this.fruitPlates.length === 0) {
                        // 所有水果盘都消失了，进入下一关或显示胜利
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                    }
                });
            }

            completeLevel() {
                // 关卡完成处理
                this.level++;
                this.levelText.setText(`${this.level}`);

                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '🤣', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示关卡完成文字
                const levelCompleteText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, `第${this.level - 1}关完成！`, {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                levelCompleteText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建下一关按钮
                const nextButton = this.createNextLevelButton();

                // 启动散花特效
                this.startConfettiEffect();

                // 存储界面元素以便清理
                this.levelCompleteUI = {
                    overlay,
                    bigEmoji,
                    levelCompleteText,
                    scoreText,
                    nextButton
                };
            }

            createNextLevelButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0x4CAF50);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0x45A049);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100,
                    this.level > 10 ? '游戏完成' : '下一关', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onNextLevelClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            disableGameInput() {
                // 禁用游戏输入
                this.gameInputDisabled = true;

                // 禁用所有方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.disableInteractive();
                    });
                }
            }

            enableGameInput() {
                // 启用游戏输入
                this.gameInputDisabled = false;

                // 重新启用方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.setInteractive();
                    });
                }
            }

            onNextLevelClick() {
                // 清理关卡完成界面
                if (this.levelCompleteUI) {
                    Object.values(this.levelCompleteUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.levelCompleteUI = null;
                }

                // 停止散花特效
                this.stopConfettiEffect();

                // 重新启用游戏输入
                this.enableGameInput();

                // 检查是否达到最大关卡
                if (this.level > 10) {
                    this.showVictory();
                } else {
                    // 重新初始化游戏进入下一关
                    this.initializeGame();
                }
            }

            startConfettiEffect() {
                // 创建散花特效
                this.confettiParticles = [];
                this.confettiTimer = this.time.addEvent({
                    delay: 100,
                    callback: this.createConfettiParticle,
                    callbackScope: this,
                    loop: true
                });

                // 3秒后停止生成新的散花
                this.time.delayedCall(3000, () => {
                    if (this.confettiTimer) {
                        this.confettiTimer.destroy();
                        this.confettiTimer = null;
                    }
                });
            }

            createConfettiParticle() {
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0xFECA57, 0xFF9FF3, 0xA8E6CF];
                const shapes = ['●', '★', '♦', '▲'];

                // 随机选择颜色和形状
                const color = colors[Phaser.Math.Between(0, colors.length - 1)];
                const shape = shapes[Phaser.Math.Between(0, shapes.length - 1)];

                // 在屏幕顶部随机位置创建粒子
                const x = Phaser.Math.Between(0, GAME_WIDTH);
                const y = -20;

                const particle = this.add.text(x, y, shape, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: `#${color.toString(16).padStart(6, '0')}`,
                    resolution: 2
                }).setOrigin(0.5);
                particle.setDepth(1003);

                // 随机旋转和缩放
                particle.setRotation(Phaser.Math.Between(0, 360) * Math.PI / 180);
                particle.setScale(Phaser.Math.FloatBetween(0.5, 1.5));

                // 添加下落和旋转动画
                this.tweens.add({
                    targets: particle,
                    y: GAME_HEIGHT + 50,
                    x: x + Phaser.Math.Between(-100, 100),
                    rotation: particle.rotation + Phaser.Math.Between(2, 6) * Math.PI,
                    alpha: 0,
                    duration: Phaser.Math.Between(2000, 4000),
                    ease: 'Power2',
                    onComplete: () => {
                        particle.destroy();
                        // 从数组中移除
                        const index = this.confettiParticles.indexOf(particle);
                        if (index > -1) {
                            this.confettiParticles.splice(index, 1);
                        }
                    }
                });

                this.confettiParticles.push(particle);
            }

            stopConfettiEffect() {
                // 停止生成新的散花
                if (this.confettiTimer) {
                    this.confettiTimer.destroy();
                    this.confettiTimer = null;
                }

                // 清理现有的散花粒子
                if (this.confettiParticles) {
                    this.confettiParticles.forEach(particle => {
                        if (particle && particle.destroy) {
                            particle.destroy();
                        }
                    });
                    this.confettiParticles = [];
                }
            }

            showCoinFlyAnimation(startX, startY, points) {
                // 创建多个金币图标飞向右上角的金币显示位置
                const coinCount = Math.min(3, Math.ceil(points / 50)); // 减少金币数量，根据分数决定，最多3个

                // 获取金币显示文字的准确位置
                const scoreTextBounds = this.scoreText.getBounds();
                const targetX = scoreTextBounds.x + 18; // 金币图标在文字中的大概位置
                const targetY = scoreTextBounds.y + scoreTextBounds.height / 2; // 垂直居中

                for (let i = 0; i < coinCount; i++) {
                    // 减少延迟时间，让金币更快飞出
                    this.time.delayedCall(i * 50, () => {
                        // 创建金币图标
                        const coin = this.add.text(startX, startY, '🪙', {
                            fontSize: '32px',
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        coin.setDepth(1000);

                        // 添加初始的轻微随机偏移
                        const randomOffsetX = Phaser.Math.Between(-15, 15);
                        const randomOffsetY = Phaser.Math.Between(-15, 15);
                        coin.x += randomOffsetX;
                        coin.y += randomOffsetY;

                        // 金币飞向目标位置的动画
                        this.tweens.add({
                            targets: coin,
                            x: targetX,
                            y: targetY,
                            scaleX: 0.9,
                            scaleY: 0.9,
                            duration: 600, // 减少飞行时间
                            ease: 'Power2',
                            onComplete: () => {
                                // 到达目标位置后的闪烁效果
                                this.tweens.add({
                                    targets: coin,
                                    scaleX: 1.3,
                                    scaleY: 1.3,
                                    alpha: 0,
                                    duration: 150,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        coin.destroy();

                                        // 让金币显示文字闪烁一下
                                        if (i === coinCount - 1) { // 最后一个金币到达时
                                            this.tweens.add({
                                                targets: this.scoreText,
                                                scaleX: 1.15,
                                                scaleY: 1.15,
                                                duration: 120,
                                                ease: 'Power2',
                                                yoyo: true,
                                                repeat: 1
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        // 添加旋转动画
                        this.tweens.add({
                            targets: coin,
                            rotation: Math.PI * 1.5, // 减少旋转角度
                            duration: 600,
                            ease: 'Linear'
                        });
                    });
                }
            }

            showCompleteAnimation(x, y) {
                // 显示完成特效文字
                const completeText = this.add.text(x, y - 30, '完成!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#00FF00',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                completeText.setDepth(500);

                this.tweens.add({
                    targets: completeText,
                    y: completeText.y - 30,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        completeText.destroy();
                    }
                });

                // 添加光环特效
                const halo = this.add.graphics();
                halo.lineStyle(4, 0x00FF00, 0.8);
                halo.strokeCircle(x, y, 30);
                halo.setDepth(499);

                this.tweens.add({
                    targets: halo,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        halo.destroy();
                    }
                });
            }









            reorganizeFruitPlates() {
                // 重新排列剩余的水果盘，让它们居中显示
                if (this.fruitPlates.length === 0) return;

                const plateWidth = 140;
                const plateSpacing = 20;
                const remainingCount = this.fruitPlates.length;
                const totalPlatesWidth = remainingCount * plateWidth + (remainingCount - 1) * plateSpacing;
                const startPlateX = (GAME_WIDTH - totalPlatesWidth) / 2;
                const enemyAreaY = 220; // 与createEnemyArea中的位置保持一致

                // 为每个剩余的水果盘计算新位置并添加移动动画
                this.fruitPlates.forEach((plate, index) => {
                    const newPlateX = startPlateX + index * (plateWidth + plateSpacing);
                    const newCenterX = newPlateX + plateWidth / 2;

                    // 重新绘制水果盘背景到新位置
                    plate.background.clear();
                    plate.background.fillStyle(0x8B4513, 0.3);
                    plate.background.fillRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);
                    plate.background.lineStyle(2, 0x8B4513, 0.8);
                    plate.background.strokeRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);

                    // 更新水果盘中心位置
                    plate.x = newCenterX;

                    // 移动水果盘中的所有小水果
                    const smallFruitSize = 50;
                    const fruitSpacing = 10;
                    const startFruitX = newPlateX + (plateWidth - (2 * smallFruitSize + fruitSpacing)) / 2;
                    const startFruitY = enemyAreaY - (smallFruitSize + fruitSpacing / 2);

                    plate.fruits.forEach((fruit, fruitIndex) => {
                        const row = Math.floor(fruitIndex / 2);
                        const col = fruitIndex % 2;
                        const newFruitX = startFruitX + col * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;
                        const newFruitY = startFruitY + row * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;

                        this.tweens.add({
                            targets: fruit,
                            x: newFruitX,
                            y: newFruitY,
                            duration: 400,
                            ease: 'Power2'
                        });
                    });
                });
            }

            areAllTargetsFulfilled() {
                // 检查是否所有水果盘都已消失
                return this.fruitPlates.length === 0;
            }

            calculateStars() {
                // 根据分数计算星星数量
                const baseScore = this.level * 100; // 基础分数
                const scoreRatio = this.score / baseScore;

                if (scoreRatio >= 2.0) {
                    return 3; // 3星：分数达到基础分数的200%
                } else if (scoreRatio >= 1.5) {
                    return 2; // 2星：分数达到基础分数的150%
                } else {
                    return 1; // 1星：完成关卡即可
                }
            }



            checkGameOverCondition() {
                // 检查暂存区是否已满
                const isSlotsFull = this.slots.every(slot => slot.tile !== null);

                if (isSlotsFull) {
                    // 检查是否还有剩余的水果盘
                    if (this.fruitPlates.length === 0) {
                        // 没有水果盘了，关卡完成
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                        return;
                    }

                    // 在一对一匹配的情况下，检查是否有任何剩余水果盘可以完成
                    let canCompleteAnyPlate = false;

                    // 检查每个剩余的水果盘
                    this.fruitPlates.forEach(plate => {
                        // 统计这个水果盘需要的水果类型
                        const requiredTypes = [];
                        plate.fruits.forEach(fruit => {
                            requiredTypes.push(fruit.getData('typeIndex'));
                        });

                        // 统计暂存区的水果类型
                        const availableTypes = [];
                        this.slots.forEach(slot => {
                            if (slot.tile) {
                                availableTypes.push(slot.tile.getData('typeIndex'));
                            }
                        });

                        // 检查是否能一对一匹配完成这个水果盘
                        const tempAvailable = [...availableTypes];
                        let matchCount = 0;

                        requiredTypes.forEach(requiredType => {
                            const index = tempAvailable.indexOf(requiredType);
                            if (index >= 0) {
                                tempAvailable.splice(index, 1); // 移除已匹配的
                                matchCount++;
                            }
                        });

                        if (matchCount === 4) { // 如果能匹配完整个水果盘
                            canCompleteAnyPlate = true;
                        }
                    });

                    // 如果暂存区满了且无法完成任何剩余水果盘，游戏结束
                    if (!canCompleteAnyPlate) {
                        this.gameOver = true;
                        this.time.delayedCall(500, () => {
                            this.showGameOver();
                        });
                    }
                }
            }

            reorganizeSlots() {
                // 收集所有非空的方块
                const remainingTiles = [];
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        remainingTiles.push(slot.tile);
                        slot.tile = null;
                    }
                });

                // 重新排列到左侧
                remainingTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.tile = tile;

                    this.tweens.add({
                        targets: tile,
                        x: slot.x,
                        y: slot.y,
                        duration: 200,
                        ease: 'Power2'
                    });
                });
            }

            showScoreAnimation(points) {
                const scorePopup = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 100, `+${points}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scorePopup.setDepth(500); // 设置较高深度确保分数动画可见

                this.tweens.add({
                    targets: scorePopup,
                    y: scorePopup.y - 50,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scorePopup.destroy();
                    }
                });
            }

            showVictory() {
                // 立即清空暂存区，防止显示残留动物
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 计算关卡评星
                const stars = this.calculateStars();

                // 显示胜利信息
                const victoryBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                victoryBg.setDepth(1000); // 设置最高深度确保在最上层

                const victoryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 180, '恭喜过关！', {
                    fontSize: '90px', // 150% (48 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                victoryText.setDepth(1001); // 设置最高深度确保在最上层

                // 显示星星评级
                this.showStarRating(stars);

                const nextButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, 240, 72, 0x27AE60); // 120%按钮
                nextButton.setStrokeStyle(3, 0x2ECC71);
                nextButton.setInteractive();
                nextButton.setDepth(1001); // 设置最高深度确保在最上层

                const nextText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, '下一关', {
                    fontSize: '39px', // 150% (26 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                nextText.setDepth(1002); // 设置最高深度确保在最上层

                // 在UI界面上添加撒花庆祝特效
                this.showConfettiCelebration();

                nextButton.on('pointerdown', () => {
                    victoryBg.destroy();
                    victoryText.destroy();
                    nextButton.destroy();
                    nextText.destroy();
                    this.nextLevel();
                });
            }

            showConfettiCelebration() {
                // 创建撒花庆祝特效，2次爆炸效果，共2秒
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0xA8E6CF, 0xFD79A8];
                const centerX = GAME_WIDTH / 2;
                const centerY = GAME_HEIGHT / 2 - 80; // "恭喜过关"字上方位置
                const explosionCount = 2; // 2次爆炸
                const explosionInterval = 1000; // 1秒间隔（0秒和1秒时爆炸）

                for (let explosion = 0; explosion < explosionCount; explosion++) {
                    this.time.delayedCall(explosion * explosionInterval, () => {
                        // 每次爆炸创建多个花瓣
                        const confettiPerExplosion = 25; // 增加每次爆炸的花瓣数量
                        for (let i = 0; i < confettiPerExplosion; i++) {
                            this.createConfettiExplosion(centerX, centerY, colors);
                        }
                    });
                }
            }

            createConfettiExplosion(centerX, centerY, colors) {
                // 随机选择颜色
                const color = colors[Math.floor(Math.random() * colors.length)];

                // 创建花瓣形状（更大的尺寸）
                const confetti = this.add.graphics();
                confetti.fillStyle(color);

                // 随机选择花瓣形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形花瓣
                    confetti.fillCircle(0, 0, Math.random() * 8 + 6);
                } else if (shapeType === 1) {
                    // 方形花瓣
                    const size = Math.random() * 12 + 8;
                    confetti.fillRect(-size/2, -size/2, size, size);
                } else {
                    // 三角形花瓣
                    const size = Math.random() * 10 + 8;
                    confetti.fillTriangle(0, -size, -size/2, size/2, size/2, size/2);
                }

                // 从中心点开始
                confetti.x = centerX;
                confetti.y = centerY;
                confetti.setDepth(1003); // 确保在UI界面之上

                // 随机的爆炸方向和距离
                const angle = Math.random() * Math.PI * 2; // 随机角度
                const explosionRadius = Math.random() * 150 + 100; // 爆炸半径
                const explosionX = centerX + Math.cos(angle) * explosionRadius;
                const explosionY = centerY + Math.sin(angle) * explosionRadius;

                // 第一阶段：爆炸扩散
                this.tweens.add({
                    targets: confetti,
                    x: explosionX,
                    y: explosionY,
                    rotation: (Math.random() - 0.5) * 360 * Math.PI / 180,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段：重力下落
                        const fallDistance = GAME_HEIGHT - explosionY + 50;
                        const horizontalDrift = (Math.random() - 0.5) * 100;

                        this.tweens.add({
                            targets: confetti,
                            x: explosionX + horizontalDrift,
                            y: explosionY + fallDistance,
                            rotation: confetti.rotation + (Math.random() - 0.5) * 720 * Math.PI / 180,
                            alpha: 0,
                            duration: 1300, // 调整为1300ms，总时长1.5秒
                            ease: 'Power1',
                            onComplete: () => {
                                confetti.destroy();
                            }
                        });
                    }
                });

                // 添加轻微的摆动效果
                this.tweens.add({
                    targets: confetti,
                    scaleX: 0.8,
                    scaleY: 1.2,
                    duration: 300 + Math.random() * 100,
                    yoyo: true,
                    repeat: 2, // 重复2次，总共约1.2秒
                    ease: 'Sine.easeInOut'
                });
            }

            showStarRating(stars) {
                // 显示星星评级
                const starY = GAME_HEIGHT/2 - 80;
                const starSize = 60;
                const starSpacing = 80;
                const totalWidth = 3 * starSize + 2 * starSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2 + starSize / 2;

                // 创建3个星星位置
                for (let i = 0; i < 3; i++) {
                    const starX = startX + i * (starSize + starSpacing);
                    const isLit = i < stars; // 是否点亮这颗星星

                    // 延迟显示每颗星星
                    this.time.delayedCall(500 + i * 300, () => {
                        this.createAnimatedStar(starX, starY, starSize, isLit);
                    });
                }

                // 显示评级文字
                this.time.delayedCall(1400, () => {
                    let ratingText = '';
                    let ratingColor = '';

                    switch(stars) {
                        case 3:
                            ratingText = '完美通关！';
                            ratingColor = '#FFD700';
                            break;
                        case 2:
                            ratingText = '表现优秀！';
                            ratingColor = '#C0C0C0';
                            break;
                        case 1:
                            ratingText = '成功通关！';
                            ratingColor = '#CD7F32';
                            break;
                    }

                    const rating = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 10, ratingText, {
                        fontSize: '36px',
                        fontFamily: 'Arial, sans-serif',
                        color: ratingColor,
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);
                    rating.setDepth(1003);

                    // 评级文字出现动画
                    rating.setScale(0);
                    this.tweens.add({
                        targets: rating,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 400,
                        ease: 'Back.easeOut'
                    });
                });
            }

            createAnimatedStar(x, y, size, isLit) {
                // 创建星星图形
                const star = this.add.graphics();
                star.setDepth(1003);

                // 设置星星颜色
                const fillColor = isLit ? 0xFFD700 : 0x666666; // 金色或灰色
                const strokeColor = isLit ? 0xFFA500 : 0x444444; // 橙色或深灰色

                star.fillStyle(fillColor);
                star.lineStyle(3, strokeColor);

                // 绘制五角星
                const points = [];
                const outerRadius = size / 2;
                const innerRadius = outerRadius * 0.4;

                for (let i = 0; i < 10; i++) {
                    const angle = (i * Math.PI) / 5;
                    const radius = i % 2 === 0 ? outerRadius : innerRadius;
                    const px = x + Math.cos(angle - Math.PI / 2) * radius;
                    const py = y + Math.sin(angle - Math.PI / 2) * radius;
                    points.push(px, py);
                }

                star.fillPoints(points);
                star.strokePoints(points);

                // 星星出现动画
                star.setScale(0);
                star.setRotation(0);

                this.tweens.add({
                    targets: star,
                    scaleX: 1,
                    scaleY: 1,
                    rotation: Math.PI * 2,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 如果是点亮的星星，添加闪烁效果
                if (isLit) {
                    this.time.delayedCall(600, () => {
                        this.tweens.add({
                            targets: star,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 200,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 添加光芒效果
                        const glow = this.add.graphics();
                        glow.setDepth(1002);
                        glow.fillStyle(0xFFD700, 0.3);
                        glow.fillCircle(x, y, size);
                        glow.setScale(0);

                        this.tweens.add({
                            targets: glow,
                            scaleX: 1.5,
                            scaleY: 1.5,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });
                    });
                }
            }

            showGameOver() {
                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '😭', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示游戏结束文字
                const gameOverText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, '游戏结束！', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#E74C3C',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                gameOverText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建重新开始按钮
                const retryButton = this.createRetryButton();

                // 存储界面元素以便清理
                this.gameOverUI = {
                    overlay,
                    bigEmoji,
                    gameOverText,
                    scoreText,
                    retryButton
                };
            }

            createRetryButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0xE74C3C);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0xC0392B);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100, '重新开始', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onRetryClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            onRetryClick() {
                // 清理游戏结束界面
                if (this.gameOverUI) {
                    Object.values(this.gameOverUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.gameOverUI = null;
                }

                // 重新启用游戏输入
                this.enableGameInput();

                // 重新开始游戏
                this.restartGame();
            }

            nextLevel() {
                this.level++;
                this.levelText.setText(`${this.level}`);
                this.gameOver = false;
                this.isAnimating = false; // 重置动画状态

                // 强制清空暂存区（确保清空）
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }



            restartGame() {
                this.score = 0;
                this.level = 1;
                this.scoreText.setText('🪙 0');
                this.levelText.setText('1');
                this.gameOver = false;
                this.isAnimating = false;

                // 清空暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 清空敌人区域
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#2C3E50',
            scene: GameScene,
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false,
                transparent: false,
                clearBeforeRender: true,
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
                powerPreference: "high-performance"
            },
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>

