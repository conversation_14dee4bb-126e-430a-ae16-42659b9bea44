<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三消游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        #game-container {
            text-align: center;
            position: relative;
            width: 1280px;
            height: 720px;
        }
        #score {
            color: white;
            font-size: 32px;
            font-weight: bold;
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            transition: all 0.2s ease;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
        }
        canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="score">分数: 0</div>
        <div id="game"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        class Match3Game extends Phaser.Scene {
            constructor() {
                super({ key: 'Match3Game' });
                this.GRID_WIDTH = 16;  // 增加宽度适应横屏
                this.GRID_HEIGHT = 9;  // 减少高度适应横屏
                this.TILE_SIZE = 60;   // 稍微增大方块尺寸
                this.COLORS = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
                this.grid = [];
                this.selectedTile = null;
                this.score = 0;
                this.isProcessing = false;
            }

            preload() {
                // 创建方块纹理
                this.createTileTextures();
            }

            create() {
                // 初始化网格
                this.initGrid();
                
                // 创建游戏板
                this.createBoard();
                
                // 添加输入处理
                this.input.on('gameobjectdown', this.onTileClick, this);
                
                // 初始检查和填充
                this.fillEmptySpaces();
            }

            createTileTextures() {
                this.COLORS.forEach(color => {
                    const graphics = this.add.graphics();

                    // 创建主体颜色填充
                    const mainColor = this.getColorValue(color, 1.0);
                    const lightColor = this.getColorValue(color, 1.3);
                    const darkColor = this.getColorValue(color, 0.7);

                    // 绘制主体方块 - 使用纯色填充
                    graphics.fillStyle(mainColor, 1.0);
                    graphics.fillRoundedRect(2, 2, this.TILE_SIZE - 4, this.TILE_SIZE - 4, 8);

                    // 添加顶部高光
                    graphics.fillStyle(lightColor, 0.8);
                    graphics.fillRoundedRect(4, 4, this.TILE_SIZE - 8, this.TILE_SIZE / 3, 6);

                    // 添加底部阴影
                    graphics.fillStyle(darkColor, 0.6);
                    graphics.fillRoundedRect(4, this.TILE_SIZE * 2/3, this.TILE_SIZE - 8, this.TILE_SIZE / 3 - 2, 6);

                    // 添加白色边框
                    graphics.lineStyle(3, 0xffffff, 0.9);
                    graphics.strokeRoundedRect(2, 2, this.TILE_SIZE - 4, this.TILE_SIZE - 4, 8);

                    // 添加内部高光点
                    graphics.fillStyle(0xffffff, 0.6);
                    graphics.fillCircle(this.TILE_SIZE * 0.3, this.TILE_SIZE * 0.25, 4);

                    // 添加颜色标识符号或图案
                    this.addColorPattern(graphics, color);

                    graphics.generateTexture(color, this.TILE_SIZE, this.TILE_SIZE);
                    graphics.destroy();
                });
            }

            addColorPattern(graphics, color) {
                const centerX = this.TILE_SIZE / 2;
                const centerY = this.TILE_SIZE / 2;
                const size = 8;

                graphics.fillStyle(0xffffff, 0.8);
                graphics.lineStyle(2, 0xffffff, 0.8);

                switch(color) {
                    case 'red':
                        // 绘制心形
                        graphics.fillCircle(centerX - 4, centerY - 2, 4);
                        graphics.fillCircle(centerX + 4, centerY - 2, 4);
                        graphics.fillTriangle(centerX - 8, centerY + 2, centerX + 8, centerY + 2, centerX, centerY + 10);
                        break;
                    case 'blue':
                        // 绘制钻石
                        graphics.fillTriangle(centerX, centerY - size, centerX - size, centerY, centerX, centerY + size);
                        graphics.fillTriangle(centerX, centerY - size, centerX + size, centerY, centerX, centerY + size);
                        break;
                    case 'green':
                        // 绘制叶子
                        graphics.fillEllipse(centerX, centerY, size * 1.5, size);
                        graphics.strokePath();
                        break;
                    case 'yellow':
                        // 绘制星星
                        const points = [];
                        for (let i = 0; i < 5; i++) {
                            const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
                            const outerRadius = size;
                            const innerRadius = size * 0.4;

                            points.push(centerX + Math.cos(angle) * outerRadius);
                            points.push(centerY + Math.sin(angle) * outerRadius);

                            const innerAngle = angle + Math.PI / 5;
                            points.push(centerX + Math.cos(innerAngle) * innerRadius);
                            points.push(centerY + Math.sin(innerAngle) * innerRadius);
                        }
                        graphics.fillPoints(points, true);
                        break;
                    case 'purple':
                        // 绘制六边形
                        const hexPoints = [];
                        for (let i = 0; i < 6; i++) {
                            const angle = (i * Math.PI * 2) / 6;
                            hexPoints.push(centerX + Math.cos(angle) * size);
                            hexPoints.push(centerY + Math.sin(angle) * size);
                        }
                        graphics.fillPoints(hexPoints, true);
                        break;
                    case 'orange':
                        // 绘制圆形
                        graphics.fillCircle(centerX, centerY, size);
                        graphics.fillCircle(centerX, centerY, size * 0.6);
                        break;
                }
            }

            getColorValue(colorName, brightness = 1) {
                const colors = {
                    red: 0xff3333,      // 鲜红色
                    blue: 0x3366ff,     // 鲜蓝色
                    green: 0x33cc33,    // 鲜绿色
                    yellow: 0xffcc00,   // 鲜黄色
                    purple: 0xcc33ff,   // 鲜紫色
                    orange: 0xff6600    // 鲜橙色
                };

                let color = colors[colorName] || 0xffffff;
                if (brightness !== 1) {
                    const r = Math.min(255, Math.max(0, Math.floor(((color >> 16) & 0xff) * brightness)));
                    const g = Math.min(255, Math.max(0, Math.floor(((color >> 8) & 0xff) * brightness)));
                    const b = Math.min(255, Math.max(0, Math.floor((color & 0xff) * brightness)));
                    color = (r << 16) | (g << 8) | b;
                }
                return color;
            }

            initGrid() {
                this.grid = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        this.grid[row][col] = null;
                    }
                }
            }

            createBoard() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;
                
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        
                        const color = this.getRandomColor();
                        const tile = this.add.image(x, y, color);
                        tile.setInteractive();
                        tile.setData('row', row);
                        tile.setData('col', col);
                        tile.setData('color', color);
                        
                        this.grid[row][col] = tile;
                    }
                }
                
                // 确保初始状态没有匹配
                this.removeInitialMatches();
            }

            getRandomColor() {
                return this.COLORS[Math.floor(Math.random() * this.COLORS.length)];
            }

            removeInitialMatches() {
                let hasMatches = true;
                while (hasMatches) {
                    hasMatches = false;
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        for (let col = 0; col < this.GRID_WIDTH; col++) {
                            if (this.hasMatchAt(row, col)) {
                                const newColor = this.getRandomColor();
                                this.grid[row][col].setTexture(newColor);
                                this.grid[row][col].setData('color', newColor);
                                hasMatches = true;
                            }
                        }
                    }
                }
            }

            onTileClick(pointer, tile) {
                if (this.isProcessing) return;

                if (!this.selectedTile) {
                    // 选择第一个方块
                    this.selectedTile = tile;
                    this.highlightSelectedTile(tile);
                } else if (this.selectedTile === tile) {
                    // 取消选择
                    this.clearTileHighlight(tile);
                    this.selectedTile = null;
                } else if (this.areAdjacent(this.selectedTile, tile)) {
                    // 交换相邻方块
                    this.clearTileHighlight(this.selectedTile);
                    this.swapTiles(this.selectedTile, tile);
                } else {
                    // 选择新方块
                    this.clearTileHighlight(this.selectedTile);
                    this.selectedTile = tile;
                    this.highlightSelectedTile(tile);
                }
            }

            highlightSelectedTile(tile) {
                // 添加发光效果
                tile.setTint(0xffffff);

                // 添加脉动动画
                this.tweens.add({
                    targets: tile,
                    scaleX: 1.1,
                    scaleY: 1.1,
                    duration: 300,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });

                // 保存动画引用以便后续清理
                tile.setData('isSelected', true);
            }

            clearTileHighlight(tile) {
                if (tile && tile.getData('isSelected')) {
                    tile.clearTint();
                    tile.setData('isSelected', false);

                    // 停止所有相关的动画
                    this.tweens.killTweensOf(tile);

                    // 重置缩放
                    tile.setScale(1, 1);
                }
            }

            areAdjacent(tile1, tile2) {
                const row1 = tile1.getData('row');
                const col1 = tile1.getData('col');
                const row2 = tile2.getData('row');
                const col2 = tile2.getData('col');
                
                const rowDiff = Math.abs(row1 - row2);
                const colDiff = Math.abs(col1 - col2);
                
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }

            swapTiles(tile1, tile2) {
                if (this.isProcessing) return;

                this.isProcessing = true;
                const color1 = tile1.getData('color');
                const color2 = tile2.getData('color');

                // 保存原始位置
                const pos1 = { x: tile1.x, y: tile1.y };
                const pos2 = { x: tile2.x, y: tile2.y };

                this.clearTileHighlight(tile1);
                this.selectedTile = null;

                // 创建交换动画
                const swapDuration = 250;

                // 第一个方块移动到第二个方块的位置
                this.tweens.add({
                    targets: tile1,
                    x: pos2.x,
                    y: pos2.y,
                    duration: swapDuration,
                    ease: 'Power2'
                });

                // 第二个方块移动到第一个方块的位置
                this.tweens.add({
                    targets: tile2,
                    x: pos1.x,
                    y: pos1.y,
                    duration: swapDuration,
                    ease: 'Power2',
                    onComplete: () => {
                        // 动画完成后交换数据
                        tile1.setTexture(color2);
                        tile1.setData('color', color2);
                        tile2.setTexture(color1);
                        tile2.setData('color', color1);

                        // 重置位置（因为我们只是视觉上移动了）
                        tile1.x = pos1.x;
                        tile1.y = pos1.y;
                        tile2.x = pos2.x;
                        tile2.y = pos2.y;

                        // 检查是否有匹配
                        const hasMatch = this.hasMatchAt(tile1.getData('row'), tile1.getData('col')) ||
                                        this.hasMatchAt(tile2.getData('row'), tile2.getData('col'));

                        if (hasMatch) {
                            // 有匹配，开始处理匹配
                            this.time.delayedCall(100, () => {
                                this.processMatches();
                            });
                        } else {
                            // 没有匹配，播放回退动画
                            this.playSwapBackAnimation(tile1, tile2, color1, color2, pos1, pos2);
                        }
                    }
                });
            }

            playSwapBackAnimation(tile1, tile2, originalColor1, originalColor2, pos1, pos2) {
                // 添加摇摆效果表示无效交换
                this.tweens.add({
                    targets: [tile1, tile2],
                    scaleX: 1.1,
                    scaleY: 1.1,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 延迟后开始回退动画
                this.time.delayedCall(200, () => {
                    // 第一个方块回到原位
                    this.tweens.add({
                        targets: tile1,
                        x: pos1.x,
                        y: pos1.y,
                        duration: 200,
                        ease: 'Power2'
                    });

                    // 第二个方块回到原位
                    this.tweens.add({
                        targets: tile2,
                        x: pos2.x,
                        y: pos2.y,
                        duration: 200,
                        ease: 'Power2',
                        onComplete: () => {
                            // 恢复原始颜色和数据
                            tile1.setTexture(originalColor1);
                            tile1.setData('color', originalColor1);
                            tile2.setTexture(originalColor2);
                            tile2.setData('color', originalColor2);

                            this.isProcessing = false;
                        }
                    });
                });
            }

            hasMatchAt(row, col) {
                if (!this.grid[row] || !this.grid[row][col]) return false;

                const color = this.grid[row][col].getData('color');

                // 检查水平匹配
                let horizontalCount = 1;
                // 向左检查
                for (let c = col - 1; c >= 0; c--) {
                    if (this.grid[row][c] && this.grid[row][c].getData('color') === color) {
                        horizontalCount++;
                    } else {
                        break;
                    }
                }
                // 向右检查
                for (let c = col + 1; c < this.GRID_WIDTH; c++) {
                    if (this.grid[row][c] && this.grid[row][c].getData('color') === color) {
                        horizontalCount++;
                    } else {
                        break;
                    }
                }

                // 检查垂直匹配
                let verticalCount = 1;
                // 向上检查
                for (let r = row - 1; r >= 0; r--) {
                    if (this.grid[r][col] && this.grid[r][col].getData('color') === color) {
                        verticalCount++;
                    } else {
                        break;
                    }
                }
                // 向下检查
                for (let r = row + 1; r < this.GRID_HEIGHT; r++) {
                    if (this.grid[r][col] && this.grid[r][col].getData('color') === color) {
                        verticalCount++;
                    } else {
                        break;
                    }
                }

                return horizontalCount >= 3 || verticalCount >= 3;
            }

            processMatches() {
                this.isProcessing = true;
                const matches = this.findAllMatches();

                if (matches.length > 0) {
                    this.removeMatches(matches);
                    this.updateScore(matches.length);

                    // 计算动画总时长（考虑延迟播放的波浪效果）
                    const totalAnimationTime = 250 + (matches.length * 50);

                    // 延迟后处理下落和填充
                    this.time.delayedCall(totalAnimationTime, () => {
                        this.dropTiles();
                        this.time.delayedCall(500, () => {
                            this.fillEmptySpaces();
                            this.time.delayedCall(300, () => {
                                // 检查是否有新的匹配
                                const newMatches = this.findAllMatches();
                                if (newMatches.length > 0) {
                                    // 连锁消除提示
                                    this.showComboEffect(newMatches.length);
                                    this.processMatches();
                                } else {
                                    this.isProcessing = false;
                                }
                            });
                        });
                    });
                } else {
                    this.isProcessing = false;
                }
            }

            findAllMatches() {
                const matches = [];
                const matchedPositions = new Set();

                // 检查水平匹配
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    let count = 1;
                    let currentColor = null;
                    let matchStart = 0;

                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const tile = this.grid[row][col];
                        if (tile && tile.active) {
                            const color = tile.getData('color');
                            if (color === currentColor) {
                                count++;
                            } else {
                                if (count >= 3) {
                                    for (let c = matchStart; c < col; c++) {
                                        matchedPositions.add(`${row}-${c}`);
                                    }
                                }
                                currentColor = color;
                                count = 1;
                                matchStart = col;
                            }
                        } else {
                            if (count >= 3) {
                                for (let c = matchStart; c < col; c++) {
                                    matchedPositions.add(`${row}-${c}`);
                                }
                            }
                            currentColor = null;
                            count = 1;
                        }
                    }
                    if (count >= 3) {
                        for (let c = matchStart; c < this.GRID_WIDTH; c++) {
                            matchedPositions.add(`${row}-${c}`);
                        }
                    }
                }

                // 检查垂直匹配
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let count = 1;
                    let currentColor = null;
                    let matchStart = 0;

                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        const tile = this.grid[row][col];
                        if (tile && tile.active) {
                            const color = tile.getData('color');
                            if (color === currentColor) {
                                count++;
                            } else {
                                if (count >= 3) {
                                    for (let r = matchStart; r < row; r++) {
                                        matchedPositions.add(`${r}-${col}`);
                                    }
                                }
                                currentColor = color;
                                count = 1;
                                matchStart = row;
                            }
                        } else {
                            if (count >= 3) {
                                for (let r = matchStart; r < row; r++) {
                                    matchedPositions.add(`${r}-${col}`);
                                }
                            }
                            currentColor = null;
                            count = 1;
                        }
                    }
                    if (count >= 3) {
                        for (let r = matchStart; r < this.GRID_HEIGHT; r++) {
                            matchedPositions.add(`${r}-${col}`);
                        }
                    }
                }

                // 转换为位置数组
                matchedPositions.forEach(pos => {
                    const [row, col] = pos.split('-').map(Number);
                    matches.push({ row, col });
                });

                return matches;
            }



            removeMatches(matches) {
                // 按顺序播放消除动画，创建连锁效果
                matches.forEach((pos, index) => {
                    const tile = this.grid[pos.row][pos.col];
                    if (tile && tile.active) {
                        // 延迟播放动画，创建波浪效果
                        this.time.delayedCall(index * 50, () => {
                            this.playDestroyAnimation(tile, pos);
                        });
                    }
                });
            }

            playDestroyAnimation(tile, pos) {
                if (!tile || !tile.active) return;

                // 创建爆炸粒子效果
                this.createExplosionEffect(tile.x, tile.y, tile.getData('color'));

                // 主要消除动画 - 旋转缩放消失
                this.tweens.add({
                    targets: tile,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    rotation: Math.PI * 2,
                    alpha: 0.8,
                    duration: 150,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段 - 快速缩小消失
                        this.tweens.add({
                            targets: tile,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 100,
                            ease: 'Power3',
                            onComplete: () => {
                                if (tile && tile.active) {
                                    tile.destroy();
                                }
                                this.grid[pos.row][pos.col] = null;
                            }
                        });
                    }
                });

                // 添加闪光效果
                this.tweens.add({
                    targets: tile,
                    tint: 0xffffff,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });
            }

            createExplosionEffect(x, y, color) {
                const particleCount = 8;
                const baseColor = this.getColorValue(color, 1.0);

                for (let i = 0; i < particleCount; i++) {
                    // 创建粒子
                    const particle = this.add.graphics();
                    particle.fillStyle(baseColor, 0.8);
                    particle.fillCircle(0, 0, 3);
                    particle.x = x;
                    particle.y = y;

                    // 随机方向和速度
                    const angle = (Math.PI * 2 * i) / particleCount + (Math.random() - 0.5) * 0.5;
                    const speed = 50 + Math.random() * 30;
                    const targetX = x + Math.cos(angle) * speed;
                    const targetY = y + Math.sin(angle) * speed;

                    // 粒子飞散动画
                    this.tweens.add({
                        targets: particle,
                        x: targetX,
                        y: targetY,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300 + Math.random() * 200,
                        ease: 'Power2',
                        onComplete: () => {
                            particle.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(0, 0, 5);
                ring.x = x;
                ring.y = y;

                // 光环扩散动画
                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });
            }

            dropTiles() {
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let writeRow = this.GRID_HEIGHT - 1;

                    for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                        if (this.grid[row][col] !== null) {
                            if (row !== writeRow) {
                                // 移动方块
                                this.grid[writeRow][col] = this.grid[row][col];
                                this.grid[row][col] = null;

                                // 更新方块数据
                                this.grid[writeRow][col].setData('row', writeRow);

                                // 添加下落动画
                                const boardStartY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;
                                const startY = boardStartY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                                const endY = boardStartY + writeRow * this.TILE_SIZE + this.TILE_SIZE / 2;

                                this.tweens.add({
                                    targets: this.grid[writeRow][col],
                                    y: endY,
                                    duration: 300,
                                    ease: 'Bounce.easeOut'
                                });
                            }
                            writeRow--;
                        }
                    }
                }
            }

            fillEmptySpaces() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;

                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                        if (this.grid[row][col] === null) {
                            const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                            const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;

                            const color = this.getRandomColor();
                            const tile = this.add.image(x, y - this.TILE_SIZE * 2, color);
                            tile.setInteractive();
                            tile.setData('row', row);
                            tile.setData('col', col);
                            tile.setData('color', color);

                            this.grid[row][col] = tile;

                            // 添加下落动画
                            this.tweens.add({
                                targets: tile,
                                y: y,
                                duration: 400,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }
                }
            }

            showComboEffect(matchCount) {
                // 创建连锁文字效果
                const comboText = this.add.text(
                    this.sys.game.config.width / 2,
                    this.sys.game.config.height / 2,
                    `COMBO x${matchCount}!`,
                    {
                        fontSize: '32px',
                        fontFamily: 'Arial',
                        color: '#ffff00',
                        stroke: '#ff0000',
                        strokeThickness: 4,
                        shadow: {
                            offsetX: 2,
                            offsetY: 2,
                            color: '#000000',
                            blur: 4,
                            fill: true
                        }
                    }
                );
                comboText.setOrigin(0.5);
                comboText.setScale(0);

                // 连锁文字动画
                this.tweens.add({
                    targets: comboText,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    duration: 200,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        this.tweens.add({
                            targets: comboText,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            y: comboText.y - 50,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                comboText.destroy();
                            }
                        });
                    }
                });

                // 屏幕震动效果
                this.cameras.main.shake(200, 0.01);
            }

            updateScore(matchCount) {
                this.score += matchCount * 10;
                document.getElementById('score').textContent = `分数: ${this.score}`;

                // 分数增加动画
                const scoreElement = document.getElementById('score');
                scoreElement.style.transform = 'scale(1.2)';
                scoreElement.style.color = '#ffff00';

                setTimeout(() => {
                    scoreElement.style.transform = 'scale(1)';
                    scoreElement.style.color = 'white';
                }, 200);
            }
        }

        // 游戏配置 - 横屏1280x720
        const config = {
            type: Phaser.AUTO,
            width: 1280,
            height: 720,
            parent: 'game',
            backgroundColor: '#2c3e50',
            scene: Match3Game,
            scale: {
                mode: Phaser.Scale.NONE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        const game = new Phaser.Game(config);
    </script>
</body>
</html>
