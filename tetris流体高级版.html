<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块流体模拟</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        #game-container {
            height: 100vh;
            width: 100vw;
        }
        .controls {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 14px;
            z-index: 1000;
            background: rgba(0,0,0,0.7);
            padding: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="controls">
        <div>F - 切换流体模式</div>
        <div>G - 调整重力</div>
        <div>V - 调整粘度</div>
        <div>空格 - 下落方块</div>
        <div>← → - 移动方块</div>
    </div>
    <div id="game-container"></div>
    
    <script>
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            const GAME_WIDTH = 800;
            const GAME_HEIGHT = 900;

            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000011',
                physics: {
                    default: 'matter',
                    matter: {
                        gravity: { y: 0 },
                        debug: false,
                        enableSleeping: false,
                        render: { visible: false }
                    }
                },
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let scene, graphics;
            let currentPiece = null;
            let nextPiece = null;
            let waitingForDrop = false;
            let gameOver = false;
            let isPaused = false;

            // 流体系统变量
            let fluidParticles = [];
            let fluidMode = false;
            let particleSize = 6;
            let gravityStrength = 0.8;
            let viscosity = 0.02;
            let surfaceTension = 0.0002;
            let maxParticles = 500;

            // 游戏区域配置
            const GRID_WIDTH = 12;
            const GRID_HEIGHT = 20;
            const CELL_SIZE = 30;
            const GRID_X = 50;
            const GRID_Y = 100;

            // 方块颜色（更鲜艳的流体色彩）
            const COLORS = [
                0x000000, // 空
                0xFF4444, // 红色 - 像岩浆
                0x44FF44, // 绿色 - 像酸液
                0x4444FF, // 蓝色 - 像水
                0xFFFF44, // 黄色 - 像油
                0xFF44FF, // 紫色 - 像毒液
                0x44FFFF, // 青色 - 像冰
                0xFFAA44  // 橙色 - 像蜂蜜
            ];

            // 方块形状定义（简化版）
            const PIECES = [
                { shape: [[1,1,1,1]], color: 1 }, // I
                { shape: [[2,2],[2,2]], color: 2 }, // O
                { shape: [[0,3,0],[3,3,3]], color: 3 }, // T
                { shape: [[0,4,4],[4,4,0]], color: 4 }, // S
                { shape: [[5,5,0],[0,5,5]], color: 5 }, // Z
                { shape: [[6,0,0],[6,6,6]], color: 6 }, // J
                { shape: [[0,0,7],[7,7,7]], color: 7 }  // L
            ];

            let walls = [];
            let statusText, fluidInfoText;

            function preload() {
                scene = this;
            }

            function create() {
                graphics = scene.add.graphics();
                
                // 创建墙壁
                createWalls();
                
                // 创建UI
                createUI();
                
                // 生成第一个方块
                spawnPiece();
                
                // 设置输入
                setupInput();
            }

            function createWalls() {
                const wallThickness = 20;
                
                // 左墙
                walls.push(scene.matter.add.rectangle(
                    GRID_X - wallThickness/2, GRID_Y + (GRID_HEIGHT * CELL_SIZE)/2,
                    wallThickness, GRID_HEIGHT * CELL_SIZE,
                    { isStatic: true, friction: 0.8, restitution: 0.2 }
                ));
                
                // 右墙
                walls.push(scene.matter.add.rectangle(
                    GRID_X + GRID_WIDTH * CELL_SIZE + wallThickness/2, GRID_Y + (GRID_HEIGHT * CELL_SIZE)/2,
                    wallThickness, GRID_HEIGHT * CELL_SIZE,
                    { isStatic: true, friction: 0.8, restitution: 0.2 }
                ));
                
                // 底墙
                walls.push(scene.matter.add.rectangle(
                    GRID_X + (GRID_WIDTH * CELL_SIZE)/2, GRID_Y + GRID_HEIGHT * CELL_SIZE + wallThickness,
                    GRID_WIDTH * CELL_SIZE + wallThickness * 2, wallThickness * 2,
                    { isStatic: true, friction: 0.8, restitution: 0.2 }
                ));
            }

            function createUI() {
                // 标题
                scene.add.text(GAME_WIDTH/2, 40, '俄罗斯方块流体模拟', {
                    fontSize: '32px', fill: '#ffffff', fontStyle: 'bold'
                }).setOrigin(0.5);

                // 状态显示
                statusText = scene.add.text(GRID_X + GRID_WIDTH * CELL_SIZE + 50, 150, '', {
                    fontSize: '16px', fill: '#ffffff'
                });

                // 流体信息显示
                fluidInfoText = scene.add.text(GRID_X + GRID_WIDTH * CELL_SIZE + 50, 250, '', {
                    fontSize: '14px', fill: '#00ffff'
                });

                updateUI();
            }

            function updateUI() {
                const mode = fluidMode ? '流体模式' : '方块模式';
                statusText.setText([
                    `模式: ${mode}`,
                    `粒子数: ${fluidParticles.length}`,
                    `重力: ${gravityStrength.toFixed(1)}`,
                    `粘度: ${viscosity.toFixed(3)}`
                ]);

                if (fluidMode) {
                    fluidInfoText.setText([
                        '流体特性:',
                        '• 表面张力',
                        '• 粘性阻尼',
                        '• 粒子碰撞',
                        '• 重力沉降',
                        '',
                        '不同颜色代表',
                        '不同流体类型'
                    ]);
                } else {
                    fluidInfoText.setText('');
                }
            }

            function setupInput() {
                scene.input.keyboard.on('keydown', (event) => {
                    if (gameOver || isPaused) return;

                    switch(event.code) {
                        case 'KeyF':
                            toggleFluidMode();
                            break;
                        case 'KeyG':
                            adjustGravity();
                            break;
                        case 'KeyV':
                            adjustViscosity();
                            break;
                        case 'Space':
                            if (waitingForDrop && currentPiece) {
                                dropPiece();
                            }
                            break;
                        case 'ArrowLeft':
                            if (waitingForDrop && currentPiece) {
                                movePiece(-1);
                            }
                            break;
                        case 'ArrowRight':
                            if (waitingForDrop && currentPiece) {
                                movePiece(1);
                            }
                            break;
                    }
                });
            }

            function toggleFluidMode() {
                fluidMode = !fluidMode;
                scene.matter.world.engine.world.gravity.y = fluidMode ? gravityStrength : 0;
                updateUI();
                
                // 显示切换提示
                const text = scene.add.text(GAME_WIDTH/2, GAME_HEIGHT/2, 
                    fluidMode ? '流体模式已开启' : '流体模式已关闭', {
                    fontSize: '28px', fill: fluidMode ? '#00ffff' : '#ffff00', fontStyle: 'bold'
                }).setOrigin(0.5);
                
                scene.time.delayedCall(1500, () => text.destroy());
            }

            function adjustGravity() {
                gravityStrength = (gravityStrength + 0.2) % 2.1;
                if (fluidMode) {
                    scene.matter.world.engine.world.gravity.y = gravityStrength;
                }
                updateUI();
            }

            function adjustViscosity() {
                viscosity = (viscosity + 0.01) % 0.06;
                updateUI();
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }

                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;

                nextPiece = createRandomPiece();
                waitingForDrop = true;
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                return {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0, y: 0
                };
            }

            function movePiece(direction) {
                if (!currentPiece || !waitingForDrop) return;
                
                const newX = currentPiece.x + direction;
                if (newX >= 0 && newX + currentPiece.shape[0].length <= GRID_WIDTH) {
                    currentPiece.x = newX;
                }
            }

            function dropPiece() {
                if (!currentPiece || !waitingForDrop) return;
                
                waitingForDrop = false;
                
                if (fluidMode) {
                    convertPieceToFluid(currentPiece);
                } else {
                    // 传统方块模式的处理
                    // 这里可以添加传统俄罗斯方块逻辑
                }
                
                currentPiece = null;
                spawnPiece();
            }

            function convertPieceToFluid(piece) {
                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const gridX = piece.x + x;
                            const gridY = piece.y + y;

                            if (gridY >= 0) {
                                // 每个方块生成多个粒子
                                const particlesPerBlock = 6;
                                for (let i = 0; i < particlesPerBlock; i++) {
                                    if (fluidParticles.length >= maxParticles) {
                                        // 移除最老的粒子
                                        const oldParticle = fluidParticles.shift();
                                        scene.matter.world.remove(scene.matter.world, oldParticle.body);
                                    }

                                    const offsetX = (Math.random() - 0.5) * CELL_SIZE * 0.8;
                                    const offsetY = (Math.random() - 0.5) * CELL_SIZE * 0.8;

                                    const particle = scene.matter.add.circle(
                                        GRID_X + gridX * CELL_SIZE + CELL_SIZE/2 + offsetX,
                                        GRID_Y + gridY * CELL_SIZE + CELL_SIZE/2 + offsetY,
                                        particleSize,
                                        {
                                            restitution: 0.4,
                                            friction: 0.1,
                                            frictionAir: viscosity,
                                            density: 0.8 + Math.random() * 0.4,
                                            render: { fillStyle: `#${COLORS[piece.color].toString(16).padStart(6, '0')}` }
                                        }
                                    );

                                    // 给粒子一个随机的初始速度
                                    const initialVelocity = {
                                        x: (Math.random() - 0.5) * 2,
                                        y: (Math.random() - 0.5) * 2
                                    };
                                    scene.matter.body.setVelocity(particle, initialVelocity);

                                    fluidParticles.push({
                                        body: particle,
                                        color: piece.color,
                                        age: 0,
                                        type: getFluidType(piece.color)
                                    });
                                }
                            }
                        }
                    }
                }
            }

            function getFluidType(color) {
                // 根据颜色定义不同的流体类型
                const types = {
                    1: 'lava',    // 红色 - 岩浆（高密度，低粘度）
                    2: 'acid',    // 绿色 - 酸液（中密度，中粘度）
                    3: 'water',   // 蓝色 - 水（低密度，低粘度）
                    4: 'oil',     // 黄色 - 油（低密度，高粘度）
                    5: 'poison',  // 紫色 - 毒液（中密度，高粘度）
                    6: 'ice',     // 青色 - 冰（高密度，极低粘度）
                    7: 'honey'    // 橙色 - 蜂蜜（高密度，极高粘度）
                };
                return types[color] || 'water';
            }

            function updateFluidPhysics() {
                if (!fluidMode || fluidParticles.length === 0) return;

                // 更新粒子年龄
                for (let i = fluidParticles.length - 1; i >= 0; i--) {
                    const particle = fluidParticles[i];
                    particle.age++;

                    // 移除超出边界的粒子
                    if (particle.body.position.y > GRID_Y + GRID_HEIGHT * CELL_SIZE + 100 ||
                        particle.body.position.x < GRID_X - 50 ||
                        particle.body.position.x > GRID_X + GRID_WIDTH * CELL_SIZE + 50) {
                        scene.matter.world.remove(scene.matter.world, particle.body);
                        fluidParticles.splice(i, 1);
                        continue;
                    }
                }

                // 应用流体力学
                applyFluidForces();

                // 更新UI
                updateUI();
            }

            function applyFluidForces() {
                const interactionRadius = particleSize * 4;

                for (let i = 0; i < fluidParticles.length; i++) {
                    const particle = fluidParticles[i];
                    const body = particle.body;

                    // 应用流体类型特定的属性
                    applyFluidTypeEffects(particle);

                    // 粒子间相互作用
                    for (let j = i + 1; j < fluidParticles.length; j++) {
                        const otherParticle = fluidParticles[j];
                        const otherBody = otherParticle.body;

                        const dx = otherBody.position.x - body.position.x;
                        const dy = otherBody.position.y - body.position.y;
                        const distance = Math.sqrt(dx * dx + dy * dy);

                        if (distance < interactionRadius && distance > 0) {
                            // 表面张力（吸引力）
                            const attraction = surfaceTension / (distance * distance);
                            const forceX = (dx / distance) * attraction;
                            const forceY = (dy / distance) * attraction;

                            // 同类型流体吸引力更强
                            const typeMultiplier = particle.type === otherParticle.type ? 2 : 1;

                            scene.matter.body.applyForce(body, body.position, {
                                x: forceX * typeMultiplier,
                                y: forceY * typeMultiplier
                            });
                            scene.matter.body.applyForce(otherBody, otherBody.position, {
                                x: -forceX * typeMultiplier,
                                y: -forceY * typeMultiplier
                            });

                            // 防止粒子重叠的排斥力
                            if (distance < particleSize * 2) {
                                const repulsion = 0.001 / distance;
                                const repelX = -(dx / distance) * repulsion;
                                const repelY = -(dy / distance) * repulsion;

                                scene.matter.body.applyForce(body, body.position, { x: repelX, y: repelY });
                                scene.matter.body.applyForce(otherBody, otherBody.position, { x: -repelX, y: -repelY });
                            }
                        }
                    }
                }
            }

            function applyFluidTypeEffects(particle) {
                const body = particle.body;
                const velocity = body.velocity;

                // 根据流体类型应用不同的物理效果
                switch(particle.type) {
                    case 'lava':
                        // 岩浆：高温，低粘度，会发光
                        body.render.fillStyle = `hsl(${Math.random() * 60}, 100%, ${50 + Math.random() * 30}%)`;
                        scene.matter.body.setVelocity(body, {
                            x: velocity.x * 0.95,
                            y: velocity.y * 0.95
                        });
                        break;

                    case 'water':
                        // 水：流动性好，透明度变化
                        body.render.fillStyle = `hsla(240, 100%, ${40 + Math.random() * 20}%, 0.8)`;
                        scene.matter.body.setVelocity(body, {
                            x: velocity.x * 0.98,
                            y: velocity.y * 0.98
                        });
                        break;

                    case 'oil':
                        // 油：粘稠，会形成薄膜
                        scene.matter.body.setVelocity(body, {
                            x: velocity.x * 0.85,
                            y: velocity.y * 0.90
                        });
                        break;

                    case 'honey':
                        // 蜂蜜：极其粘稠
                        scene.matter.body.setVelocity(body, {
                            x: velocity.x * 0.70,
                            y: velocity.y * 0.80
                        });
                        break;

                    case 'ice':
                        // 冰：滑溜，反弹性强
                        if (Math.random() < 0.01) {
                            body.render.fillStyle = `hsl(180, 100%, ${70 + Math.random() * 20}%)`;
                        }
                        break;
                }
            }

            function update(time, delta) {
                if (gameOver || isPaused) return;

                // 更新流体物理
                updateFluidPhysics();

                // 渲染
                render();
            }

            function render() {
                graphics.clear();

                // 绘制游戏区域边框
                graphics.lineStyle(3, 0x444444);
                graphics.strokeRect(GRID_X, GRID_Y, GRID_WIDTH * CELL_SIZE, GRID_HEIGHT * CELL_SIZE);

                // 绘制网格
                graphics.lineStyle(1, 0x222222, 0.3);
                for (let x = 0; x <= GRID_WIDTH; x++) {
                    graphics.moveTo(GRID_X + x * CELL_SIZE, GRID_Y);
                    graphics.lineTo(GRID_X + x * CELL_SIZE, GRID_Y + GRID_HEIGHT * CELL_SIZE);
                }
                for (let y = 0; y <= GRID_HEIGHT; y++) {
                    graphics.moveTo(GRID_X, GRID_Y + y * CELL_SIZE);
                    graphics.lineTo(GRID_X + GRID_WIDTH * CELL_SIZE, GRID_Y + y * CELL_SIZE);
                }
                graphics.strokePath();

                // 绘制当前方块
                if (currentPiece && waitingForDrop) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;

                                if (gridY >= 0 && gridX >= 0 && gridX < GRID_WIDTH && gridY < GRID_HEIGHT) {
                                    graphics.fillStyle(COLORS[currentPiece.color]);
                                    graphics.fillRect(
                                        GRID_X + gridX * CELL_SIZE + 2,
                                        GRID_Y + gridY * CELL_SIZE + 2,
                                        CELL_SIZE - 4,
                                        CELL_SIZE - 4
                                    );

                                    // 绘制发光边框
                                    graphics.lineStyle(2, 0xffffff, 0.8);
                                    graphics.strokeRect(
                                        GRID_X + gridX * CELL_SIZE + 2,
                                        GRID_Y + gridY * CELL_SIZE + 2,
                                        CELL_SIZE - 4,
                                        CELL_SIZE - 4
                                    );
                                }
                            }
                        }
                    }
                }

                // 绘制流体粒子
                if (fluidMode) {
                    for (const particle of fluidParticles) {
                        const body = particle.body;
                        const alpha = Math.max(0.4, 1 - particle.age / 2000);

                        // 主粒子
                        graphics.fillStyle(COLORS[particle.color]);
                        graphics.fillCircle(body.position.x, body.position.y, particleSize);

                        // 发光效果
                        graphics.fillStyle(0xffffff);
                        graphics.fillCircle(
                            body.position.x - particleSize/3,
                            body.position.y - particleSize/3,
                            particleSize/4
                        );

                        // 流体尾迹效果
                        const velocity = body.velocity;
                        const speed = Math.sqrt(velocity.x * velocity.x + velocity.y * velocity.y);
                        if (speed > 1) {
                            graphics.lineStyle(2, COLORS[particle.color], alpha * 0.5);
                            graphics.moveTo(body.position.x, body.position.y);
                            graphics.lineTo(
                                body.position.x - velocity.x * 3,
                                body.position.y - velocity.y * 3
                            );
                            graphics.strokePath();
                        }
                    }
                }

                // 绘制下一个方块预览
                if (nextPiece) {
                    const previewX = GRID_X + GRID_WIDTH * CELL_SIZE + 80;
                    const previewY = 120;

                    scene.add.text(previewX, previewY - 30, '下一个:', {
                        fontSize: '18px', fill: '#ffffff'
                    }).setOrigin(0);

                    for (let y = 0; y < nextPiece.shape.length; y++) {
                        for (let x = 0; x < nextPiece.shape[y].length; x++) {
                            if (nextPiece.shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * 20 + 2,
                                    previewY + y * 20 + 2,
                                    16, 16
                                );
                            }
                        }
                    }
                }
            }

            // 启动游戏
            const game = new Phaser.Game(config);
        });
    </script>
</body>
</html>
