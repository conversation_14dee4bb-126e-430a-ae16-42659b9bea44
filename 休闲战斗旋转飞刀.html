
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗 - 物理碰撞版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
let isPaused = false;
let roguelikePopup = null;
    let rotatingDisc;
    let stuckKnives = [];
    let monsters = [];
    let discHealth = 100, maxDiscHealth = 100;
    let currentLevel = 1;
    let currentWave = 1;
    let totalMonstersInWave = 3;
    let gamePhase = 'placing'; // 'placing' 或 'battle'
    let knivesToInsert = 10;
    let attemptsLeft = 10;
let pendingKnife = null; // 待发射飞刀对象（sprite+数据）

    let flyingKnife = null;
    let discRotationSpeed = 2;
    let knifeInserted = 0;
    let currentKnifeIndex = 1;
    let knifeOptions = [1,2,3]; // 当前备选飞刀编号
    let selectedKnifeIndex = 0; // 当前选中的备选飞刀索引（0-2）
    let knifeOptionSprites = []; // 备选飞刀sprite对象
let knifeShopSprites = [];
let knifeShopData = [
    { id: 1, price: 50, desc: '普通刀', effect: () => {} },
    { id: 2, price: 100, desc: '伤害+20', effect: () => { KNIFE_DAMAGE += 20; } },
    { id: 3, price: 150, desc: '插入时回血', effect: () => { discHealth = Math.min(maxDiscHealth, discHealth + 30); } }
];
let playerGold = 100000;
let selectedShopKnife = null;

    const DISC_RADIUS = 150; // 定义圆盘半径为常量
    let KNIFE_DAMAGE = 25; // 定义飞刀基础伤害
    const MONSTER_HIT_COOLDOWN = 500; // 怪物受伤冷却时间 (毫秒)

    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        this.load.image('background', 'images/rpg/background2.png');
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }
        for (let i = 1; i <= 7; i++) {
            this.load.image(`knife_${i}`, `images/knife/knife${i}.png`);
        }
    }

    // 创建游戏场景
    function create() {
        // 重置状态变量
        discHealth = 300; maxDiscHealth = 300;
        currentLevel = 1; currentWave = 1;
      totalMonstersInWave = 10;
        gamePhase = 'placing';
        // 前面关卡飞刀数量减少
        if(currentLevel<=2){
            knivesToInsert = 6;
            attemptsLeft = 6;
        }else if(currentLevel<=4){
            knivesToInsert = 8;
            attemptsLeft = 8;
        }else{
            knivesToInsert = 10;
            attemptsLeft = 10;
        }
        knifeInserted = 0;
        discRotationSpeed = 2; knifeInserted = 0; currentKnifeIndex = 1;
        stuckKnives = []; monsters = []; flyingKnife = null;

        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334);

        createUI.call(this);
        createKnifeGame.call(this);

        this.input.on('pointerdown', handleKnifeThrow, this);
    }

    // 创建波次怪物
    function createWave() {
        if (gamePhase !== 'battle') return;
        
        monsters = [];
        for (let i = 0; i < totalMonstersInWave; i++) {
            const monsterType = (i % 15) + 1;
            let startX, startY;
            const angle = (i / totalMonstersInWave) * 2 * Math.PI + Math.random() * 0.5;
            const distance = 450 + Math.random() * 100;
            
            startX = 375 + Math.cos(angle) * distance;
            startY = 667 + Math.sin(angle) * distance;
            
            startX = Math.max(50, Math.min(700, startX));
            startY = Math.max(100, Math.min(1200, startY));
            
            let monster = this.add.image(startX, startY, `monster_${monsterType}`).setScale(0.25);
            monster.health = 50 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isMoving = false;
            // 【核心修改】为怪物添加受伤时间戳，用于计算伤害冷却
            monster.lastHitTime = 0;
            monsters.push(monster);
        }
    }

    // 创建UI界面
    function createUI() {
        this.levelText = this.add.text(50, 20, `关卡: ${currentLevel}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.waveText = this.add.text(50, 60, `波次: ${currentWave}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.healthText = this.add.text(50, 100, `生命: ${discHealth}/${maxDiscHealth}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.gamePhaseText = this.add.text(50, 140, `阶段: ${gamePhase === 'placing' ? '摆放飞刀' : '战斗'}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.goldText = this.add.text(50, 180, `金币: ${playerGold}`, { fontSize: '24px', fill: '#ffffff', backgroundColor: '#00000080', padding: { x: 10, y: 5 } });
        this.monsterHealthBars = [];
    }

    // 创建旋转飞刀游戏
    function createKnifeGame() {
      
        rotatingDisc = this.add.graphics({ x: 375, y: 600 });
        rotatingDisc.fillStyle(0xf8bf48).fillCircle(0, 0, DISC_RADIUS);
        rotatingDisc.lineStyle(4, 0x654321).strokeCircle(0, 0, DISC_RADIUS);
        rotatingDisc.fillStyle(0x333333).fillCircle(0, 0, 15);
        // 飞刀商店区域
        knifeShopSprites = [];
        for(let i=0;i<3;i++){
            let x = 200 + i*180;
            let knife = knifeShopData[i];
            let sprite = this.add.image(x, 1200, `knife_${knife.id}`).setScale(0.7).setOrigin(0.5);
            sprite.setInteractive();
            // 价格和描述
            let priceText = this.add.text(x, 1240, `💰${knife.price}`, { fontSize: '20px', fill: '#ffd700' }).setOrigin(0.5);
            let descText = this.add.text(x, 1270, knife.desc, { fontSize: '18px', fill: '#fff' }).setOrigin(0.5);
            sprite.on('pointerdown', () => {
                if(gamePhase==='placing' && !pendingKnife && playerGold >= knife.price){
                    playerGold -= knife.price;
                    knife.effect();
                    // 创建新的飞刀精灵并移动到待发射区（圆盘下方）
                    let newKnifeSprite = this.add.image(sprite.x, sprite.y, `knife_${knife.id}`).setScale(0.7);
                    this.tweens.add({
                        targets: newKnifeSprite,
                        x: 375,
                        y: 1100,
                        duration: 400,
                        onComplete: () => {
                            pendingKnife = { sprite: newKnifeSprite, knife };
                        }
                    });
                    // 飞刀移动到目标位置后再刷新商店
            this.tweens.add({
                targets: newKnifeSprite,
                x: 375,
                y: 1100,
                duration: 400,
                onComplete: () => {
                    pendingKnife = { sprite: newKnifeSprite, knife };
                    // 飞刀到达目标位置后刷新商店
                    refreshKnifeShop.call(this, i);
                }
            });
                }
            });
            knifeShopSprites.push({ sprite, priceText, descText });
        }
// 刷新商店某个飞刀
function refreshKnifeShop(index) {
    // 随机新刀型（可扩展更多刀型）
    let newId = Phaser.Math.Between(1, 7);
    let newPrice = Phaser.Math.Between(50, 200);
    let newDesc = '新刀型';
    let spriteObj = knifeShopSprites[index];
    spriteObj.sprite.setTexture(`knife_${newId}`);
    spriteObj.sprite.x = 200 + index*180;
    spriteObj.sprite.y = 1200;
    spriteObj.sprite.setScale(0.7);
    spriteObj.priceText.setText(`💰${newPrice}`);
    spriteObj.priceText.x = 200 + index*180;
    spriteObj.priceText.y = 1240;
    spriteObj.descText.setText(newDesc);
    spriteObj.descText.x = 200 + index*180;
    spriteObj.descText.y = 1270;
    // 更新数据
    knifeShopData[index] = { id: newId, price: newPrice, desc: newDesc, effect: () => {} };
    // 重新绑定点击事件
    spriteObj.sprite.removeAllListeners();
    spriteObj.sprite.setInteractive();
    spriteObj.sprite.on('pointerdown', () => {
        // 获取当前最新的数据
        let currentKnife = knifeShopData[index];
        if(gamePhase==='placing' && !pendingKnife && playerGold >= currentKnife.price){
            playerGold -= currentKnife.price;
            currentKnife.effect();
            // 创建新的飞刀精灵并移动到待发射区（圆盘下方）
            let newKnifeSprite = this.add.image(spriteObj.sprite.x, spriteObj.sprite.y, `knife_${currentKnife.id}`).setScale(0.7);
            this.tweens.add({
                targets: newKnifeSprite,
                x: 375,
                y: 1050,
                duration: 400,
                onComplete: () => {
                    pendingKnife = { sprite: newKnifeSprite, knife: currentKnife };
                }
            });
            // 飞刀移动到目标位置后再刷新商店
            this.tweens.add({
                targets: newKnifeSprite,
                x: 375,
                y: 1050,
                duration: 400,
                onComplete: () => {
                    pendingKnife = { sprite: newKnifeSprite, knife: currentKnife };
                    // 飞刀到达目标位置后刷新商店
                    refreshKnifeShop.call(this, index);
                }
            });
        }
    });
}
        // 高亮和禁用逻辑可后续补充
        
    }

    // 处理飞刀发射
    function handleKnifeThrow(pointer) {
        // 只有购买后且点击上方刀才可插入
        // 只有待发射区有飞刀且点击上方才可发射
        if (pendingKnife && pointer.y < 1150 && !flyingKnife && gamePhase === 'placing' && attemptsLeft > 0) {
            flyingKnife = pendingKnife.sprite;
            attemptsLeft--;
            // 动画移动到圆盘插入点
            this.tweens.add({
                targets: flyingKnife,
                x: rotatingDisc.x,
                y: rotatingDisc.y + DISC_RADIUS,
                duration: 200,
                ease: 'Power2',
                onComplete: () => {
                    checkKnifeCollision.call(this);
                    pendingKnife = null;
                }
            });
        }
    }

    // 删除重复的throwKnife函数
    // 该功能已在handleKnifeThrow中实现
    
    // 【修复】检查飞刀碰撞，修正碰撞角度
    function checkKnifeCollision() {
        // 插入到container（如有）
        if (typeof discContainer !== 'undefined' && discContainer && !discContainer.list.includes(flyingKnife)) {
            discContainer.add(flyingKnife);
            // 转为相对坐标
            flyingKnife.x = Math.cos(Math.PI/2) * DISC_RADIUS;
            flyingKnife.y = Math.sin(Math.PI/2) * DISC_RADIUS;
        }
        if (!flyingKnife) return;
        // 插入到container（如有）
        if (typeof discContainer !== 'undefined' && discContainer && !discContainer.list.includes(flyingKnife)) {
            discContainer.add(flyingKnife);
            // 转为相对坐标
            flyingKnife.x = Math.cos(Math.PI/2) * DISC_RADIUS;
            flyingKnife.y = Math.sin(Math.PI/2) * DISC_RADIUS;
        }

        // 飞刀从下方插入，相对角度是 PI/2 (90度)
        const knifeAngle = Math.PI / 2;
        let collision = false;

        for (let stuckKnife of stuckKnives) {
            const currentStuckKnifeAngle = stuckKnife.angle + rotatingDisc.rotation;
            const angleDiff = Phaser.Math.Angle.Wrap(knifeAngle - currentStuckKnifeAngle);
            if (Math.abs(angleDiff) < 0.3) {
                collision = true;
                break;
            }
        }

        if (collision) {
            this.tweens.add({
                targets: flyingKnife, y: flyingKnife.y + 50, alpha: 0, duration: 300,
                onComplete: () => { if (flyingKnife) flyingKnife.destroy(); flyingKnife = null; }
            });
            discHealth -= 10;
        } else {
            // 保证插入后飞刀加入stuckKnives并跟随圆盘旋转
            const stuckKnife = {
                sprite: flyingKnife,
                angle: knifeAngle - rotatingDisc.rotation,
                distance: DISC_RADIUS
            };
            stuckKnives.push(stuckKnife);
            knifeInserted++;
            flyingKnife = null;
        }
        // pendingKnife = null; // 移除此行，因为已在handleKnifeThrow中处理

        if ((knifeInserted >= knivesToInsert || attemptsLeft <= 0) && gamePhase === 'placing') {
            if (stuckKnives.length === 0) { discHealth = 0; } else { startBattlePhase.call(this); }
        } else {
            // 发射后刷新备选飞刀
            refreshKnifeOptions.call(this);
        }
    }

    // 开始战斗阶段
    function startBattlePhase() {
        gamePhase = 'battle';
        this.gamePhaseText.setText('阶段: 战斗');
        if (this.launchKnifeSprite) this.launchKnifeSprite.setVisible(false);
        // 战斗模式隐藏备选飞刀
        if (typeof knifeOptionSprites !== 'undefined') {
            knifeOptionSprites.forEach(sprite => sprite.setVisible(false));
        }
        // 战斗模式隐藏商店
        knifeShopSprites.forEach(shopItem => {
            shopItem.sprite.setVisible(false);
            shopItem.priceText.setVisible(false);
            shopItem.descText.setVisible(false);
        });
        // 飞盘和飞刀一起缩小动画
     
        createWave.call(this);
    }

    // 重置飞刀游戏
    function resetKnifeGame() {
    stuckKnives.forEach(knife => { if (knife.sprite) knife.sprite.destroy(); });
    stuckKnives = [];
    knifeInserted = 0; attemptsLeft = 10;
    if (flyingKnife) { flyingKnife.destroy(); flyingKnife = null; }
    // 随机刷新备选飞刀
    refreshKnifeOptions.call(this);
    selectedKnifeIndex = 0;
    updateKnifeOptionHighlight();
    gamePhase = 'placing';
    this.gamePhaseText.setText('阶段: 摆放飞刀');
    // 摆放阶段显示备选飞刀
    if (typeof knifeOptionSprites !== 'undefined') {
        knifeOptionSprites.forEach(sprite => sprite.setVisible(true));
    }
    // 摆放阶段显示商店
    knifeShopSprites.forEach(shopItem => {
        shopItem.sprite.setVisible(true);
        shopItem.priceText.setVisible(true);
        shopItem.descText.setVisible(true);
    });
    totalMonstersInWave = Math.min(16, (3 + Math.floor(currentLevel / 2)) * 2);
    discRotationSpeed += 0.1;
    discHealth = Math.min(maxDiscHealth, discHealth + 20);
    // 摆放模式恢复圆盘和飞刀大小并加缩放动画
    if(rotatingDisc){
        this.tweens.add({
            targets: rotatingDisc,
            scaleX: 1.0,
            scaleY: 1.0,
            duration: 400,
            ease: 'Power2',
        });
    }
    stuckKnives.forEach(knife=>{
        if(knife.sprite){
            this.tweens.add({
                targets: knife.sprite,
                scaleX: 1.0,
                scaleY: 1.0,
                duration: 400,
                ease: 'Power2',
            });
        }
    });
    }
    
    // 游戏更新循环
    function update(time, delta) {
        if (gamePhase === 'gameOver' || isPaused) return;
        updateKnifeGame.call(this, delta);
        if (gamePhase === 'battle') {
            updateBattle.call(this, time);
        }
        updateUI.call(this);
        checkWaveComplete.call(this);
        if (discHealth <= 0 && gamePhase !== 'gameOver') {
            handleGameOver.call(this);
        }
    }
    
    function handleGameOver() {
        gamePhase = 'gameOver';
        monsters.forEach(m=>m.destroy());
        monsters = [];
        stuckKnives.forEach(k=>k.sprite.destroy());
        stuckKnives = [];
        if(rotatingDisc && rotatingDisc.active) rotatingDisc.destroy();
        
        this.add.text(375, 667, 'GAME OVER', { fontSize: '48px', fill: '#e74c3c', fontFamily: 'Arial' }).setOrigin(0.5);
        const restartButton = this.add.text(375, 750, '点击重新开始', { fontSize: '24px', fill: '#ffffff', backgroundColor: '#3498db', padding: { x: 20, y: 10 } }).setOrigin(0.5);
        restartButton.setInteractive().on('pointerdown', () => this.scene.restart());
    }

    // 更新旋转飞刀游戏
    function updateKnifeGame(delta) {
        if (!rotatingDisc || !rotatingDisc.active) return;
        rotatingDisc.rotation += discRotationSpeed * delta * 0.001;
        stuckKnives.forEach(knife => {
            const currentAngle = knife.angle + rotatingDisc.rotation;
            knife.sprite.x = rotatingDisc.x + Math.cos(currentAngle) * knife.distance;
            knife.sprite.y = rotatingDisc.y + Math.sin(currentAngle) * knife.distance;
            knife.sprite.rotation = currentAngle + Math.PI / 2;
        });
        // 血条位置始终和圆盘同步
        if (this.ringHealthBar) {
            this.ringHealthBar.x = rotatingDisc.x;
            this.ringHealthBar.y = rotatingDisc.y;
        }
        if (this.knifeProgressText) this.knifeProgressText.setText(`飞刀: ${knifeInserted}/${knivesToInsert}`);
        if (this.attemptsText) this.attemptsText.setText(`机会: ${attemptsLeft}/10`);
        if (this.currentKnifeText) this.currentKnifeText.setText(`当前刀: knife_${knifeOptions[selectedKnifeIndex]}`);
        // 在updateKnifeGame和updateUI中调用环形血条绘制
        if (typeof drawDiscRingHealthBar === 'function') {
            drawDiscRingHealthBar(this);
        }
    }

    // 【核心修改】更新战斗逻辑为物理碰撞检测
    function updateBattle(time) {
        // 示例：每波怪物击杀一半时弹出肉鸽3选1弹窗
        if (gamePhase === 'battle' && !isPaused && monsters.length === Math.floor(totalMonstersInWave/2)) {
            showRoguelikePopup.call(this);
        }
// 肉鸽3选1弹窗
function showRoguelikePopup() {
    isPaused = true;
    if (roguelikePopup) roguelikePopup.destroy();
    roguelikePopup = this.add.container(375, 667);
    // 全屏黑色半透明遮罩
    let mask = this.add.rectangle(0, 0, 750, 1334, 0x000000, 0.3).setOrigin(0.5);
    // 弹窗内容区域
    let bg = this.add.rectangle(0, 0, 750, 1334, 0x000000, 0.3).setOrigin(0.5);
    let title = this.add.text(0, -120, '选择一个强化', { fontSize: '32px', fill: '#fff' }).setOrigin(0.5);
    let options = [
        { text: '飞刀伤害+20', emoji: '🗡️', effect: () => { KNIFE_DAMAGE += 20; } },
        { text: '圆盘生命+100', emoji: '🛡️', effect: () => { discHealth += 100; maxDiscHealth += 100; } },
        { text: '飞刀数量+2', emoji: '➕', effect: () => { knivesToInsert += 2; attemptsLeft += 2; } }
    ];
    let btns = [];
    let btnWidth = 140, btnHeight = 180;
    let spacing = 180;
    let startX = -spacing;
    for(let i=0;i<3;i++){
        let x = startX + i*spacing;
        // 按钮底色
        let btn = this.add.rectangle(x, 40, btnWidth, btnHeight, 0x444444, 1).setStrokeStyle(4, 0xffff00).setInteractive();
        // emoji图标在按钮内
        let emoji = this.add.text(x, 10, options[i].emoji, { fontSize: '64px' }).setOrigin(0.5);
        // 选项文字在按钮内
        let txt = this.add.text(x, 80, options[i].text, { fontSize: '22px', fill: '#fff', wordWrap: { width: btnWidth-20 } }).setOrigin(0.5);
        btn.on('pointerdown', ()=>{
            options[i].effect();
            roguelikePopup.destroy();
            roguelikePopup = null;
            isPaused = false;
        });
        roguelikePopup.add(btn);
        roguelikePopup.add(emoji);
        roguelikePopup.add(txt);
        btns.push(btn);
    }
    roguelikePopup.add(bg);
    roguelikePopup.add(title);
    roguelikePopup.add(mask);
    roguelikePopup.sendToBack(mask);
    roguelikePopup.sendToBack(bg);
    roguelikePopup.sendToBack(title);
}
        // --- 飞刀对怪物的碰撞伤害 ---
        monsters.forEach(monster => {
            // 检查怪物是否可以被攻击 (冷却时间)
            if (time > monster.lastHitTime + MONSTER_HIT_COOLDOWN) {
                stuckKnives.forEach(knife => {
                    // 获取各自的边界框
                    const monsterBounds = monster.getBounds();
                    const knifeBounds = knife.sprite.getBounds();

                    // 检查是否重叠
                    if (Phaser.Geom.Intersects.RectangleToRectangle(monsterBounds, knifeBounds)) {
                        // 造成伤害并更新受伤时间
                        monster.health -= KNIFE_DAMAGE + currentLevel * 5;
                        monster.lastHitTime = time;

                        // 受伤特效
                        this.tweens.add({ targets: monster, alpha: 0.5, duration: 100, yoyo: true });
                        // 飞盘和血条一起抖动，防止累计偏移
                        if(rotatingDisc && rotatingDisc.active && this.ringHealthBar){
                            if(!this._discOriginX) this._discOriginX = rotatingDisc.x;
                            if(!this._discOriginY) this._discOriginY = rotatingDisc.y;
                            this.tweens.add({
                                targets: [rotatingDisc, this.ringHealthBar],
                                x: this._discOriginX + 10,
                                duration: 50,
                                yoyo: true,
                                repeat: 2,
                                onComplete: ()=>{
                                    rotatingDisc.x = this._discOriginX;
                                    this.ringHealthBar.x = this._discOriginX;
                                }
                            });
                        }
                        // 检查怪物是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                        }
                    }
                });
            }
        });
        // 从数组中移除已死亡的怪物
        monsters = monsters.filter(m => m.active);

        // --- 怪物移动与攻击圆盘的逻辑 (保持不变) ---
        monsters.forEach(monster => {
            const distanceToDisc = Phaser.Math.Distance.Between(monster.x, monster.y, rotatingDisc.x, rotatingDisc.y);
            // 怪物不能进入圆盘内部，只能在边缘很近的位置
            const MIN_MONSTER_DIST = DISC_RADIUS + 10; // 怪物距离圆盘中心最小距离
            if (distanceToDisc > MIN_MONSTER_DIST && !monster.isMoving) {
                monster.isMoving = true;
                const angle = Math.atan2(rotatingDisc.y - monster.y, rotatingDisc.x - monster.x);
                const targetX = rotatingDisc.x - Math.cos(angle) * MIN_MONSTER_DIST;
                const targetY = rotatingDisc.y - Math.sin(angle) * MIN_MONSTER_DIST;
                this.tweens.add({
                    targets: monster, x: targetX, y: targetY,
                    duration: Math.max(2000, Math.min(5000, distanceToDisc * 10)),
                    ease: 'Power2',
                    onComplete: () => monster.isMoving = false
                });
            }
            if (distanceToDisc <= 180 && time - monster.lastAttack > 2000) {
                discHealth -= 15 + currentLevel * 2;
                monster.lastAttack = time;
                if (rotatingDisc && rotatingDisc.active && this.ringHealthBar) {
                    if(!this._discOriginX) this._discOriginX = rotatingDisc.x;
                    if(!this._discOriginY) this._discOriginY = rotatingDisc.y;
                    this.tweens.add({
                        targets: [rotatingDisc, this.ringHealthBar],
                        x: this._discOriginX + 5,
                        duration: 50,
                        ease: 'Power2',
                        yoyo: true,
                        repeat: 2,
                        onComplete: ()=>{
                            rotatingDisc.x = this._discOriginX;
                            this.ringHealthBar.x = this._discOriginX;
                        }
                    });
                }
            }
        });
    }

    // 更新UI
    function updateUI() {
        this.levelText.setText(`关卡: ${currentLevel}`);
        this.waveText.setText(`波次: ${currentWave}`);
        this.healthText.setText(`生命: ${Math.max(0, Math.floor(discHealth))}/${maxDiscHealth}`);
        this.gamePhaseText.setText(`阶段: ${gamePhase === 'placing' ? '摆放飞刀' : '战斗'}`);
        this.goldText.setText(`金币: ${playerGold}`);

        this.monsterHealthBars.forEach(bar => bar.destroy());
        this.monsterHealthBars = [];
        monsters.forEach(monster => {
            const monsterHealthPercent = monster.health / monster.maxHealth;
            const barBg = this.add.rectangle(monster.x, monster.y - 60, 40, 6, 0x333333);
            this.monsterHealthBars.push(barBg);
            if (monsterHealthPercent > 0) {
                 const bar = this.add.rectangle(monster.x - 20 + (40 * monsterHealthPercent) / 2, monster.y - 60, 40 * monsterHealthPercent, 6, monsterHealthPercent > 0.5 ? 0x27ae60 : 0xe74c3c);
                 this.monsterHealthBars.push(bar);
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        if (monsters.length === 0 && gamePhase === 'battle') {
            gamePhase = 'wave_transition';
            currentWave++;
            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                const levelCompleteText = this.add.text(375, 607, `关卡 ${currentLevel - 1} 完成！`, { fontSize: '36px', fill: '#ffffff', fontFamily: 'Arial' }).setOrigin(0.5);
                this.time.delayedCall(2000, () => { levelCompleteText.destroy(); resetKnifeGame.call(this); });
            } else {
                const waveCompleteText = this.add.text(375, 667, `波次 ${currentWave - 1} 完成！`, { fontSize: '28px', fill: '#ffffff', fontFamily: 'Arial' }).setOrigin(0.5);
                this.time.delayedCall(1500, () => { waveCompleteText.destroy(); gamePhase = 'battle'; createWave.call(this); });
            }
        }
    }
    
    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };

    // 备选飞刀高亮
    function updateKnifeOptionHighlight(){
        knifeOptionSprites.forEach((sprite,i)=>{
            if(i===selectedKnifeIndex){
                sprite.setScale(0.8);
                sprite.setTint(0xffff00);
            }else{
                sprite.setScale(0.6);
                sprite.clearTint();
            }
        });
    }

    // 随机刷新备选飞刀
    function refreshKnifeOptions(){
        let arr = [];
        while(arr.length<3){
            let n = Phaser.Math.Between(1,7);
            if(!arr.includes(n)) arr.push(n);
        }
        knifeOptions = arr;
        knifeOptionSprites.forEach((sprite,i)=>{
            sprite.setTexture(`knife_${knifeOptions[i]}`);
        });
        selectedKnifeIndex = 0;
        updateKnifeOptionHighlight();
        if(game && game.scene && game.scene.scenes[0] && game.scene.scenes[0].currentKnifeText){
            game.scene.scenes[0].currentKnifeText.setText(`当前刀: knife_${knifeOptions[selectedKnifeIndex]}`);
        }
    }

    // 环形血条绘制函数
    function drawDiscRingHealthBar(scene) {
        if (!scene.ringHealthBar) {
            scene.ringHealthBar = scene.add.graphics({ x: rotatingDisc.x, y: rotatingDisc.y });
        }
        scene.ringHealthBar.clear();
        // 环形底色
        scene.ringHealthBar.lineStyle(10, 0x333333, 1);
        scene.ringHealthBar.beginPath();
        scene.ringHealthBar.arc(0, 0, DISC_RADIUS - 20, 0, 2 * Math.PI);
        scene.ringHealthBar.strokePath();
        // 环形进度
        let percent = Math.max(0, discHealth / maxDiscHealth);
        let color = percent > 0.5 ? 0x27ae60 : (percent > 0.25 ? 0xf39c12 : 0xe74c3c);
        scene.ringHealthBar.lineStyle(12, color, 1);
        scene.ringHealthBar.beginPath();
        scene.ringHealthBar.arc(0, 0, DISC_RADIUS - 20, -Math.PI/2, -Math.PI/2 + 2 * Math.PI * percent);
        scene.ringHealthBar.strokePath();
    }
</script>

</body>
</html>