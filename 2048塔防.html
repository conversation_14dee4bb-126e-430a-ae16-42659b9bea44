<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>2048射击融合游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #1a1a2e;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
        }
        #gameContainer {
            background: #16213e;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 0 20px rgba(0,0,0,0.5);
        }
        .controls {
            color: white;
            text-align: center;
            margin-bottom: 10px;
            font-size: 14px;
        }
        .score {
            color: #ffd700;
            font-weight: bold;
            font-size: 18px;
        }
        #gameOver {
            display: none;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            z-index: 100;
        }
        #gameOver h2 {
            margin-top: 0;
        }
        #restartButton {
            background: #ffd700;
            color: #1a1a2e;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-weight: bold;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div id="gameContainer">
        <div class="controls">
            <div class="score">得分: <span id="score">0</span></div>
            <div>使用方向键移动 | 空格键暂停</div>
        </div>
        <div id="game"></div>
    </div>
    <div id="gameOver">
        <h2>游戏完成!</h2>
        <p>您的得分: <span id="finalScore">0</span></p>
        <button id="restartButton">重新开始</button>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <script>
        class Game2048Shooter extends Phaser.Scene {
            constructor() {
                super({ key: 'Game2048Shooter' });
                this.score = 0;
                this.gameGrid = [];
                this.enemyGrid = [];
                this.enemyHP = [];  // 敌人血量数组
                this.bullets = [];
                this.enemies = [];
                this.damageTexts = []; // 存储伤害数字文本
                this.gameSize = 4;
                this.enemySize = 8;
                this.cellSize = 60;
                this.gameStartY = 370;
                this.enemyStartY = 50;
                this.lastShootTime = 0;
                this.shootInterval = 1000;
                this.isPaused = false;
                this.gameCompleted = false;
                
                // 颜色映射表
                this.colorMap = {
                    2: 0xffe4c4,     // 米色
                    4: 0xffd700,     // 金色
                    8: 0xffa500,     // 橙色
                    16: 0xff6347,    // 番茄红
                    32: 0xff1493,    // 深粉红
                    64: 0x9370db,    // 中紫色
                    128: 0x4169e1,   // 皇家蓝
                    256: 0x00ced1,   // 深青色
                    512: 0x32cd32,   // 石灰绿
                    1024: 0xffd700,  // 金色
                    2048: 0xff69b4   // 热粉红
                };
                
                // 颜色值到对应数字的映射（用于HP显示）
                this.valueMap = [2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048];
            }

            preload() {
                // 使用Phaser内置图形功能创建纹理
                this.createTextures();
            }

            createTextures() {
                // 创建不同颜色的方块纹理
                Object.entries(this.colorMap).forEach(([value, color]) => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(color);
                    graphics.fillRoundedRect(0, 0, this.cellSize - 2, this.cellSize - 2, 10);
                    graphics.lineStyle(2, 0x333333);
                    graphics.strokeRoundedRect(0, 0, this.cellSize - 2, this.cellSize - 2, 8);
                    graphics.generateTexture(`tile_${value}`, this.cellSize, this.cellSize);
                    graphics.destroy();
                });

                // 创建子弹纹理
                const bulletGraphics = this.add.graphics();
                bulletGraphics.fillStyle(0xffff00);
                bulletGraphics.fillCircle(0, 0, 4);
                bulletGraphics.generateTexture('bullet', 8, 8);
                bulletGraphics.destroy();

                // 创建敌人纹理
                Object.values(this.colorMap).forEach((color, index) => {
                    const enemyGraphics = this.add.graphics();
                    enemyGraphics.fillStyle(color);
                    enemyGraphics.fillRoundedRect(0, 0, this.cellSize - 4, this.cellSize - 4, 20);
                    enemyGraphics.lineStyle(1, 0x000000);
                    enemyGraphics.strokeRoundedRect(0, 0, this.cellSize - 4, this.cellSize - 4, 20);
                    enemyGraphics.generateTexture(`enemy_${index}`, this.cellSize, this.cellSize);
                    enemyGraphics.destroy();
                });
            }

            create() {
                // 初始化游戏网格
                this.initializeGameGrid();
                this.initializeEnemyGrid();
                
                // 添加初始方块
                this.addRandomTile();
                this.addRandomTile();
                
                // 创建键盘输入
                this.cursors = this.input.keyboard.createCursorKeys();
                this.spaceKey = this.input.keyboard.addKey(Phaser.Input.Keyboard.KeyCodes.SPACE);
                
                // 更新显示
                this.updateDisplay();
                this.updateEnemyDisplay();
                
                // 设置定时器
                this.time.addEvent({
                    delay: this.shootInterval,
                    callback: this.shootBullets,
                    callbackScope: this,
                    loop: true
                });
                
                // 初始化游戏完成界面
                this.gameOverElement = document.getElementById('gameOver');
                this.finalScoreElement = document.getElementById('finalScore');
                this.restartButton = document.getElementById('restartButton');
                
                this.restartButton.addEventListener('click', () => {
                    this.scene.restart();
                    this.gameOverElement.style.display = 'none';
                });
            }

            initializeGameGrid() {
                this.gameGrid = [];
                this.gameSprites = [];
                this.gameTexts = [];
                
                for (let row = 0; row < this.gameSize; row++) {
                    this.gameGrid[row] = [];
                    this.gameSprites[row] = [];
                    this.gameTexts[row] = [];
                    for (let col = 0; col < this.gameSize; col++) {
                        this.gameGrid[row][col] = 0;
                        
                        const x = 70 + col * this.cellSize;
                        const y = this.gameStartY + row * this.cellSize;
                        
                        // 创建背景
                        const bg = this.add.rectangle(x, y, this.cellSize - 2, this.cellSize - 2, 0x333333);
                        bg.setStrokeStyle(2, 0x555555);
                        
                        this.gameSprites[row][col] = null;
                        this.gameTexts[row][col] = null;
                    }
                }
            }

            initializeEnemyGrid() {
                this.enemyGrid = [];
                this.enemyHP = [];
                this.enemySprites = [];
                this.enemyTexts = [];
                
                for (let row = 0; row < this.enemySize; row++) {
                    this.enemyGrid[row] = [];
                    this.enemyHP[row] = [];
                    this.enemySprites[row] = [];
                    this.enemyTexts[row] = [];
                    for (let col = 0; col < this.enemySize; col++) {
                        // 默认填满8x8格子，随机分配颜色（使用颜色索引）
                        const colorIndex = Math.floor(Math.random() * Object.keys(this.colorMap).length);
                        this.enemyGrid[row][col] = colorIndex;
                        // 设置HP为对应颜色的数字值
                        this.enemyHP[row][col] = this.valueMap[colorIndex];
                        this.enemySprites[row][col] = null;
                        this.enemyTexts[row][col] = null;
                    }
                }
            }

            addRandomTile() {
                const emptyCells = [];
                for (let row = 0; row < this.gameSize; row++) {
                    for (let col = 0; col < this.gameSize; col++) {
                        if (this.gameGrid[row][col] === 0) {
                            emptyCells.push({ row, col });
                        }
                    }
                }
                
                if (emptyCells.length > 0) {
                    const randomCell = emptyCells[Math.floor(Math.random() * emptyCells.length)];
                    this.gameGrid[randomCell.row][randomCell.col] = Math.random() < 0.9 ? 2 : 4;
                }
            }

            updateDisplay() {
                for (let row = 0; row < this.gameSize; row++) {
                    for (let col = 0; col < this.gameSize; col++) {
                        const value = this.gameGrid[row][col];
                        const x = 70 + col * this.cellSize;
                        const y = this.gameStartY + row * this.cellSize;
                        
                        // 清除旧的精灵和文本
                        if (this.gameSprites[row][col]) {
                            this.gameSprites[row][col].destroy();
                            this.gameSprites[row][col] = null;
                        }
                        if (this.gameTexts[row][col]) {
                            this.gameTexts[row][col].destroy();
                            this.gameTexts[row][col] = null;
                        }
                        
                        if (value > 0) {
                            // 创建方块精灵
                            this.gameSprites[row][col] = this.add.image(x, y, `tile_${value}`);
                            this.gameSprites[row][col].setAlpha(0);
                            this.tweens.add({
                                targets: this.gameSprites[row][col],
                                alpha: 1,
                                duration: 300,
                                ease: 'Linear'
                            });
                            
                            // 添加数字文本
                            this.gameTexts[row][col] = this.add.text(x, y, value.toString(), {
                                fontSize: '16px',
                                fontFamily: 'Arial',
                                color: '#000000',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);
                        }
                    }
                }
            }

            // 物理下落效果 - 当敌人被消灭时触发
            applyGravityToEnemies(hitCol) {
                // 从被击中的列开始，让上方的敌人下落
                for (let row = this.enemySize - 1; row > 0; row--) {
                    if (this.enemyGrid[row][hitCol] === -1) { // -1 表示已死亡
                        // 找到空位，向上寻找可以下落的敌人
                        for (let upperRow = row - 1; upperRow >= 0; upperRow--) {
                            if (this.enemyGrid[upperRow][hitCol] >= 0) {
                                const oldY = this.enemyStartY + upperRow * (this.cellSize * 0.6);
                                const newY = this.enemyStartY + row * (this.cellSize * 0.6);
                                
                                // 先清除下方位置的旧数据
                                if (this.enemySprites[row][hitCol]) {
                                    this.enemySprites[row][hitCol].destroy();
                                    this.enemySprites[row][hitCol] = null;
                                }
                                if (this.enemyTexts[row][hitCol]) {
                                    this.enemyTexts[row][hitCol].destroy();
                                    this.enemyTexts[row][hitCol] = null;
                                }
                                
                                // 移动敌人到空位
                                this.enemyGrid[row][hitCol] = this.enemyGrid[upperRow][hitCol];
                                this.enemyHP[row][hitCol] = this.enemyHP[upperRow][hitCol];
                                
                                // 创建新的敌人精灵和文本
                                this.enemySprites[row][hitCol] = this.add.image(35 + hitCol * (this.cellSize * 0.6), oldY, `enemy_${this.enemyGrid[row][hitCol]}`);
                                this.enemySprites[row][hitCol].setScale(0.6);
                                this.enemyTexts[row][hitCol] = this.add.text(35 + hitCol * (this.cellSize * 0.6), oldY, this.enemyHP[row][hitCol].toString(), {
                                    fontSize: '12px',
                                    fontFamily: 'Arial',
                                    color: '#ffffff',
                                    fontWeight: 'bold',
                                    stroke: '#000000',
                                    strokeThickness: 2
                                }).setOrigin(0.5);

                                // 添加下落动画
                                this.tweens.add({
                                    targets: [this.enemySprites[row][hitCol], this.enemyTexts[row][hitCol]],
                                    y: newY,
                                    duration: 300,
                                    ease: 'Linear'
                                });
                                
                                // 完全清除原来位置的敌人数据
                                this.enemyGrid[upperRow][hitCol] = -1;
                                this.enemyHP[upperRow][hitCol] = 0;
                                if (this.enemySprites[upperRow][hitCol]) {
                                    this.enemySprites[upperRow][hitCol].destroy();
                                    this.enemySprites[upperRow][hitCol] = null;
                                }
                                if (this.enemyTexts[upperRow][hitCol]) {
                                    this.enemyTexts[upperRow][hitCol].destroy();
                                    this.enemyTexts[upperRow][hitCol] = null;
                                }
                                
                                break;
                            }
                        }
                    }
                }
                
                // 确保最上方的位置是空的
                if (this.enemyGrid[0][hitCol] >= 0) {
                    if (this.enemySprites[0][hitCol]) {
                        this.enemySprites[0][hitCol].destroy();
                        this.enemySprites[0][hitCol] = null;
                    }
                    if (this.enemyTexts[0][hitCol]) {
                        this.enemyTexts[0][hitCol].destroy();
                        this.enemyTexts[0][hitCol] = null;
                    }
                    this.enemyGrid[0][hitCol] = -1;
                    this.enemyHP[0][hitCol] = 0;
                }
                
                // 检查是否所有敌人都被消灭
                this.checkGameCompletion();
            }

            // 检查游戏是否完成（所有敌人被消灭）
            checkGameCompletion() {
                for (let row = 0; row < this.enemySize; row++) {
                    for (let col = 0; col < this.enemySize; col++) {
                        if (this.enemyGrid[row][col] >= 0) {
                            return false; // 还有敌人存在
                        }
                    }
                }
                
                // 所有敌人都被消灭，游戏完成
                this.gameCompleted = true;
                this.isPaused = true;
                this.finalScoreElement.textContent = this.score;
                this.gameOverElement.style.display = 'block';
                return true;
            }

            updateEnemyDisplay() {
                for (let row = 0; row < this.enemySize; row++) {
                    for (let col = 0; col < this.enemySize; col++) {
                        const value = this.enemyGrid[row][col];
                        const hp = this.enemyHP[row][col];
                        const x = 35 + col * (this.cellSize * 0.6);
                        const y = this.enemyStartY + row * (this.cellSize * 0.6);
                        
                        if (this.enemySprites[row][col]) {
                            this.enemySprites[row][col].destroy();
                            this.enemySprites[row][col] = null;
                        }
                        if (this.enemyTexts[row][col]) {
                            this.enemyTexts[row][col].destroy();
                            this.enemyTexts[row][col] = null;
                        }
                        
                        if (value >= 0 && hp > 0) {
                            this.enemySprites[row][col] = this.add.image(x, y, `enemy_${value}`);
                            this.enemySprites[row][col].setScale(0.6);
                            
                            // 添加HP文本，显示对应颜色的数字值
                            this.enemyTexts[row][col] = this.add.text(x, y, this.valueMap[value].toString(), {
                                fontSize: '12px',
                                fontFamily: 'Arial',
                                color: '#ffffff',
                                fontWeight: 'bold',
                                stroke: '#000000',
                                strokeThickness: 2
                            }).setOrigin(0.5);
                        }
                    }
                }
            }

            // 创建伤害数字显示
            createDamageText(x, y, damage) {
                const damageText = this.add.text(x, y, `-${damage}`, {
                    fontSize: '14px',
                    fontFamily: 'Arial',
                    color: '#ff0000',
                    fontWeight: 'bold',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);
                
                // 添加动画效果
                this.tweens.add({
                    targets: damageText,
                    y: y - 20,
                    alpha: 0,
                    duration: 800,
                    ease: 'Linear',
                    onComplete: () => {
                        damageText.destroy();
                        const index = this.damageTexts.indexOf(damageText);
                        if (index > -1) {
                            this.damageTexts.splice(index, 1);
                        }
                    }
                });
                
                this.damageTexts.push(damageText);
            }

            // 寻找前排敌人目标
            findFrontRowEnemies() {
                const targets = [];
                
                // 从前往后查找每列的第一个敌人
                for (let col = 0; col < this.enemySize; col++) {
                    for (let row = this.enemySize - 1; row >= 0; row--) {
                        if (this.enemyGrid[row][col] >= 0 && this.enemyHP[row][col] > 0) {
                            targets.push({ row, col });
                            break; // 找到该列的第一个敌人就跳出
                        }
                    }
                }
                
                return targets;
            }

            shootBullets() {
                if (this.isPaused || this.gameCompleted) return;
                
                const targets = this.findFrontRowEnemies();
                if (targets.length === 0) return;
                
                // 从最上排发射子弹
                for (let col = 0; col < this.gameSize; col++) {
                    if (this.gameGrid[0][col] > 0) {
                        const value = this.gameGrid[0][col];
                        const x = 70 + col * this.cellSize;
                        const y = this.gameStartY;
                        
                        // 随机选择一个前排敌人作为目标
                        const target = targets[Math.floor(Math.random() * targets.length)];
                        const targetX = 35 + target.col * (this.cellSize * 0.6);
                        const targetY = this.enemyStartY + target.row * (this.cellSize * 0.6);
                        
                        const bullet = this.add.image(x, y, 'bullet');
                        bullet.setTint(this.colorMap[value] || 0xffff00);
                        bullet.attackPower = value; // 攻击力等于方块数字
                        bullet.speed = 200;
                        bullet.targetX = targetX;
                        bullet.targetY = targetY;
                        bullet.targetRow = target.row;
                        bullet.targetCol = target.col;
                        
                        // 计算子弹朝向目标的方向
                        const angle = Phaser.Math.Angle.Between(x, y, targetX, targetY);
                        bullet.velocityX = Math.cos(angle) * bullet.speed;
                        bullet.velocityY = Math.sin(angle) * bullet.speed;
                        
                        this.bullets.push(bullet);
                    }
                }
            }

            update() {
                if (this.isPaused) return;
                
                // 更新子弹
                for (let i = this.bullets.length - 1; i >= 0; i--) {
                    const bullet = this.bullets[i];
                    bullet.x += bullet.velocityX * (1/60);
                    bullet.y += bullet.velocityY * (1/60);
                    
                    // 检查子弹是否超出边界
                    if (bullet.y < 0 || bullet.y > 600 || bullet.x < 0 || bullet.x > 320) {
                        bullet.destroy();
                        this.bullets.splice(i, 1);
                        continue;
                    }
                    
                    // 检查子弹碰撞
                    this.checkBulletCollision(bullet, i);
                }
                
                // 处理输入
                this.handleInput();
                
                // 更新分数显示
                document.getElementById('score').textContent = this.score;
            }

            checkBulletCollision(bullet, bulletIndex) {
                const bulletX = bullet.x;
                const bulletY = bullet.y;
                
                // 检查所有前排敌人
                const targets = this.findFrontRowEnemies();
                
                for (const target of targets) {
                    const row = target.row;
                    const col = target.col;
                    
                    if (this.enemyGrid[row][col] >= 0 && this.enemyHP[row][col] > 0) {
                        const enemyX = 35 + col * (this.cellSize * 0.6);
                        const enemyY = this.enemyStartY + row * (this.cellSize * 0.6);

                        const distance = Phaser.Math.Distance.Between(bulletX, bulletY, enemyX, enemyY);

                        if (distance < 25) {
                            // 击中敌人，根据攻击力减少HP
                            const damage = bullet.attackPower;
                            this.enemyHP[row][col] -= damage;
                            
                            // 创建伤害数字显示
                            this.createDamageText(enemyX, enemyY, damage);

                            // 抖动特效 - 使用绝对位置而不是相对位置
                            const originalX = this.enemySprites[row][col].x;
                            this.tweens.add({
                                targets: [this.enemySprites[row][col], this.enemyTexts[row][col]],
                                x: { from: originalX, to: originalX + 5, yoyo: true, repeat: 1 },
                                duration: 50,
                                ease: 'Linear'
                            });
                            
                            if (this.enemyHP[row][col] <= 0) {
                                // 敌人死亡
                                this.enemyGrid[row][col] = -1;
                                this.enemyHP[row][col] = 0;
                                this.score += damage * 10;
                                
                                // 触发该列的物理下落效果
                                this.applyGravityToEnemies(col);
                            } else {
                                // 敌人受伤但未死亡
                                this.score += damage;
                                // 更新HP显示
                                this.enemyTexts[row][col].setText(this.enemyHP[row][col].toString());
                            }

                            // 移除子弹
                            bullet.destroy();
                            this.bullets.splice(bulletIndex, 1);

                            return;
                        }
                    }
                }
            }

            handleInput() {
                if (Phaser.Input.Keyboard.JustDown(this.spaceKey)) {
                    this.isPaused = !this.isPaused;
                }
                
                if (this.isPaused) return;
                
                if (Phaser.Input.Keyboard.JustDown(this.cursors.left)) {
                    this.move('left');
                } else if (Phaser.Input.Keyboard.JustDown(this.cursors.right)) {
                    this.move('right');
                } else if (Phaser.Input.Keyboard.JustDown(this.cursors.up)) {
                    this.move('up');
                } else if (Phaser.Input.Keyboard.JustDown(this.cursors.down)) {
                    this.move('down');
                }
            }

            move(direction) {
                const previousGrid = this.gameGrid.map(row => [...row]);
                let moved = false;
                
                switch (direction) {
                    case 'left':
                        moved = this.moveLeft();
                        break;
                    case 'right':
                        moved = this.moveRight();
                        break;
                    case 'up':
                        moved = this.moveUp();
                        break;
                    case 'down':
                        moved = this.moveDown();
                        break;
                }
                
                if (moved) {
                    this.addRandomTile();
                    this.updateDisplay();
                }
            }

            moveLeft() {
                let moved = false;
                
                for (let row = 0; row < this.gameSize; row++) {
                    const oldRow = [...this.gameGrid[row]];
                    const newRow = this.processRow(oldRow);
                    this.gameGrid[row] = newRow;
                    
                    if (JSON.stringify(oldRow) !== JSON.stringify(newRow)) {
                        moved = true;
                    }
                }
                
                return moved;
            }

            moveRight() {
                let moved = false;
                
                for (let row = 0; row < this.gameSize; row++) {
                    const oldRow = [...this.gameGrid[row]];
                    const newRow = this.processRow(oldRow.reverse()).reverse();
                    this.gameGrid[row] = newRow;
                    
                    if (JSON.stringify(oldRow.reverse()) !== JSON.stringify(newRow)) {
                        moved = true;
                    }
                }
                
                return moved;
            }

            moveUp() {
                let moved = false;
                
                for (let col = 0; col < this.gameSize; col++) {
                    const oldCol = [];
                    for (let row = 0; row < this.gameSize; row++) {
                        oldCol.push(this.gameGrid[row][col]);
                    }
                    
                    const newCol = this.processRow(oldCol);
                    
                    for (let row = 0; row < this.gameSize; row++) {
                        if (this.gameGrid[row][col] !== newCol[row]) {
                            moved = true;
                        }
                        this.gameGrid[row][col] = newCol[row];
                    }
                }
                
                return moved;
            }

            moveDown() {
                let moved = false;
                
                for (let col = 0; col < this.gameSize; col++) {
                    const oldCol = [];
                    for (let row = 0; row < this.gameSize; row++) {
                        oldCol.push(this.gameGrid[row][col]);
                    }
                    
                    const newCol = this.processRow(oldCol.reverse()).reverse();
                    
                    for (let row = 0; row < this.gameSize; row++) {
                        if (this.gameGrid[row][col] !== newCol[row]) {
                            moved = true;
                        }
                        this.gameGrid[row][col] = newCol[row];
                    }
                }
                
                return moved;
            }

            processRow(row) {
                // 移除零
                let newRow = row.filter(cell => cell !== 0);
                
                // 合并相同的数字
                for (let i = 0; i < newRow.length - 1; i++) {
                    if (newRow[i] === newRow[i + 1]) {
                        newRow[i] *= 2;
                        this.score += newRow[i];
                        newRow.splice(i + 1, 1);
                    }
                }
                
                // 用零填充到原长度
                while (newRow.length < this.gameSize) {
                    newRow.push(0);
                }
                
                return newRow;
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 320,
            height: 600,
            parent: 'game',
            backgroundColor: '#1a1a2e',
            scene: Game2048Shooter,
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>
    