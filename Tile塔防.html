<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>塔防建造游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2d5016 0%, #1a3009 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .ui-text {
            font-family: 'Arial', sans-serif;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 60; // 建造格子尺寸
        // const TOWER_SLOT_COUNT = 5; // 防御塔槽位数量 - 已移除防御塔区域
        const GRID_SIZE = 7; // 建造区域大小 7x7

        // 建筑类型
        const BUILDING_TYPES = [
            { id: 0, color: 0x8B4513, emoji: '🏠️', name: '基地', level: 0, canMerge: false, isDefense: false },
            { id: 1, color: 0xFFD700, emoji: '⚡', name: '电厂', level: 1, canMerge: true, isDefense: false },
            { id: 2, color: 0x708090, emoji: '⛏️', name: '矿厂', level: 1, canMerge: true, isDefense: false },
            { id: 3, color: 0x8B4513, emoji: '🔫', name: '机枪塔', level: 1, canMerge: true, isDefense: true },
            { id: 4, color: 0xFF4500, emoji: '💣', name: '炮塔', level: 1, canMerge: true, isDefense: true },
            { id: 5, color: 0x9932CC, emoji: '🔬', name: '激光塔', level: 1, canMerge: true, isDefense: true }
        ];

        // 防御塔槽位显示的类型（只显示防御建筑） - 已移除防御塔区域
        // const DEFENSE_TOWER_TYPES = BUILDING_TYPES.filter(building => building.isDefense);

        // 敌人类型
        const ENEMY_TYPES = [
            { color: 0xFF0000, emoji: '👹', name: '小恶魔', hp: 30, speed: 1.5 },
            { color: 0x8B0000, emoji: '🧟', name: '僵尸', hp: 50, speed: 1.0 },
            { color: 0x006400, emoji: '🐲', name: '小龙', hp: 80, speed: 2.0 },
            { color: 0x4B0082, emoji: '👻', name: '幽灵', hp: 40, speed: 2.5 },
            { color: 0xFF8C00, emoji: '🦅', name: '飞鹰', hp: 25, speed: 3.0 }
        ];

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.buildGrid = []; // 7x7建造网格
                // this.towerSlots = []; // 防御塔槽位 - 已移除防御塔区域
                this.enemies = []; // 敌人数组
                this.buildings = []; // 已建造的建筑
                this.gold = 1000; // 初始金币
                this.power = 0; // 电量
                this.wave = 1; // 当前波次
                this.enemiesKilled = 0; // 击杀数
                this.isWaveActive = false;
                this.selectedBuilding = null; // 选中的建筑
                this.basePosition = { row: 3, col: 3 }; // 基地位置（中心）
                this.baseLevel = 1; // 基地等级
                this.activeEnemies = []; // 活跃的敌人（在战场上移动的）
                this.autoSpawnTimer = null; // 自动生成敌人的计时器
                this.bullets = []; // 子弹数组
                this.towerAttackTimers = []; // 防御塔攻击计时器
                this.mineralCells = []; // 有矿的格子
            }

            preload() {
                // 创建建筑和敌人纹理
                this.createBuildingTextures();
                this.createEnemyTextures();
            }

            create() {
                // 设置背景（草地色调）
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x228B22);

                // 创建设置按钮（左上角）
                this.createSettingsButton();

                // 创建金币显示（右上角）
                this.goldText = this.add.text(GAME_WIDTH - 30, 30, '🪙 1000', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);

                // 创建电量显示（右上角，金币下方）
                this.powerText = this.add.text(GAME_WIDTH - 30, 80, '⚡ 0/0', {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);

                // 创建波次显示（屏幕中央上方，大数字）
                this.waveText = this.add.text(GAME_WIDTH/2, 50, '第1波', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建击杀数显示
                this.killText = this.add.text(30, 100, '击杀: 0', {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0, 0);

                // 创建敌人区域（显示小怪）
                this.createEnemyArea();

                // 防御塔区域已移除 - 建筑可以直接在网格中拖动

                // 建造区域（7x7网格，向下移动）
                this.buildArea = {
                    x: 40,
                    y: 400,
                    width: GAME_WIDTH - 80,
                    height: 420
                };

                // 创建建造网格
                this.createBuildGrid();

                // 创建控制按钮区域
                this.createControlArea();

                // 初始化游戏
                this.initializeGame();

                // 设置输入事件（现在由各个格子自己处理点击事件）
            }

            createBuildingTextures() {
                BUILDING_TYPES.forEach((buildingType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆角矩形背景
                    graphics.fillStyle(buildingType.color);
                    graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);

                    // 添加边框
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeRoundedRect(2, 2, TILE_SIZE-4, TILE_SIZE-4, 6);

                    graphics.generateTexture(`building_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }

            createEnemyTextures() {
                ENEMY_TYPES.forEach((enemyType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆形背景
                    graphics.fillStyle(enemyType.color);
                    graphics.fillCircle(25, 25, 20);

                    // 添加边框
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeCircle(25, 25, 18);

                    graphics.generateTexture(`enemy_${index}`, 50, 50);
                    graphics.destroy();
                });
            }

            createEnemyArea() {
                // 清理之前的敌人
                if (this.enemies) {
                    this.enemies.forEach(enemy => {
                        if (enemy.sprite) enemy.sprite.destroy();
                    });
                }

                // 敌人显示区域（右侧）
                const enemyAreaX = GAME_WIDTH / 2 + 20;
                const enemyAreaY = 180;
                const enemyAreaWidth = GAME_WIDTH / 2 - 60;
                const enemyAreaHeight = 200;

                // 创建敌人区域背景
                const enemyBg = this.add.graphics();
                enemyBg.fillStyle(0x8B0000, 0.2);
                enemyBg.fillRoundedRect(enemyAreaX, enemyAreaY, enemyAreaWidth, enemyAreaHeight, 15);
                enemyBg.lineStyle(2, 0x8B0000, 0.5);
                enemyBg.strokeRoundedRect(enemyAreaX, enemyAreaY, enemyAreaWidth, enemyAreaHeight, 15);

                // 创建敌人标题
                this.add.text(enemyAreaX + enemyAreaWidth/2, enemyAreaY - 20, '即将到来的敌人', {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FF4444',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 重置敌人数组
                this.enemies = [];

                // 根据波次生成敌人预览
                this.generateEnemyPreview(enemyAreaX + 10, enemyAreaY + 20, enemyAreaWidth - 20, enemyAreaHeight - 40);
            }

            generateEnemyPreview(areaX, areaY, areaWidth, areaHeight) {
                // 根据当前波次确定敌人数量和类型
                const enemyCount = Math.min(5 + this.wave * 2, 20); // 最多20个敌人
                const rows = 4;
                const cols = Math.ceil(enemyCount / rows);
                const enemySize = 30;
                const spacing = 8;

                const totalWidth = cols * enemySize + (cols - 1) * spacing;
                const startX = areaX + (areaWidth - totalWidth) / 2;

                for (let i = 0; i < enemyCount; i++) {
                    const row = i % rows;
                    const col = Math.floor(i / rows);

                    const x = startX + col * (enemySize + spacing);
                    const y = areaY + row * (enemySize + spacing);

                    // 随机选择敌人类型，后期波次有更强的敌人
                    const enemyTypeIndex = this.getEnemyTypeForWave(this.wave);
                    const enemyType = ENEMY_TYPES[enemyTypeIndex];

                    // 创建敌人预览
                    const enemyContainer = this.add.container(x + enemySize/2, y + enemySize/2);

                    // 敌人背景
                    const enemyBg = this.add.image(0, 0, `enemy_${enemyTypeIndex}`);
                    enemyBg.setScale(0.8);

                    // 敌人表情
                    const enemyEmoji = this.add.text(0, 0, enemyType.emoji, {
                        fontSize: '24px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    enemyContainer.add([enemyBg, enemyEmoji]);

                    // 存储敌人信息，血量根据波次增加
                    const scaledHP = Math.floor(enemyType.hp * (1 + (this.wave - 1) * 0.3)); // 每波增加30%血量
                    this.enemies.push({
                        sprite: enemyContainer,
                        type: enemyTypeIndex,
                        hp: scaledHP,
                        maxHp: scaledHP,
                        speed: enemyType.speed,
                        x: x + enemySize/2,
                        y: y + enemySize/2,
                        isAlive: true
                    });
                }
            }

            getEnemyTypeForWave(wave) {
                // 根据波次返回敌人类型
                if (wave <= 2) {
                    return Phaser.Math.Between(0, 1); // 前两波只有小恶魔和僵尸
                } else if (wave <= 4) {
                    return Phaser.Math.Between(0, 2); // 3-4波加入小龙
                } else if (wave <= 6) {
                    return Phaser.Math.Between(0, 3); // 5-6波加入幽灵
                } else {
                    return Phaser.Math.Between(0, 4); // 7波以后所有敌人类型
                }
            }

            createSettingsButton() {
                // 设置按钮位置和大小
                const buttonSize = 50;
                const buttonX = 30;
                const buttonY = 30;

                // 创建设置按钮背景
                const settingsBg = this.add.graphics();
                settingsBg.fillStyle(0x34495E, 0.9);
                settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                settingsBg.lineStyle(2, 0x2C3E50, 1);
                settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);

                // 设置按钮可交互
                settingsBg.setInteractive(new Phaser.Geom.Rectangle(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize), Phaser.Geom.Rectangle.Contains);

                // 创建设置图标
                const settingsIcon = this.add.text(buttonX, buttonY, '⚙️', {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加悬停效果
                settingsBg.on('pointerover', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x5D6D7E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x34495E, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.1);
                });

                settingsBg.on('pointerout', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x34495E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x2C3E50, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.0);
                });

                // 添加点击事件（暂时只显示提示）
                settingsBg.on('pointerdown', () => {
                    this.showToolTip('设置', buttonX, buttonY - 35);
                });
            }

            createInstructionArea() {
                // 显示加载游戏文字，2秒后消失
                const loadingText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2, '加载游戏中...', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                loadingText.setDepth(1000);

                // 2秒后让加载文字消失
                this.time.delayedCall(2000, () => {
                    this.tweens.add({
                        targets: loadingText,
                        alpha: 0,
                        duration: 500,
                        ease: 'Power2',
                        onComplete: () => {
                            loadingText.destroy();
                        }
                    });
                });
            }

            // createTowerSlotArea() - 已移除防御塔区域功能
            // 建筑现在可以直接在网格中拖动移动

            // onTowerAreaClick() - 已移除防御塔区域功能
            // onTowerSlotClick() - 已移除防御塔区域功能
            // moveBuildingToTowerSlot() - 已移除防御塔区域功能



            createBuildGrid() {
                // 创建7x7建造网格
                this.buildGrid = [];
                this.mineralCells = [];
                const buildAreaY = 400;
                const buildAreaHeight = 720;
                const gridSpacing = (GAME_WIDTH - 80) / GRID_SIZE;
                const startX = 40 + gridSpacing / 2;
                const startY = buildAreaY + 20 + gridSpacing / 2;

                // 生成随机矿点位置（3-6个矿点，避开基地）
                const mineralPositions = this.generateMineralPositions();

                // 创建网格背景
                const gridBg = this.add.graphics();
                gridBg.fillStyle(0x90EE90, 0.3);
                gridBg.fillRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);
                gridBg.lineStyle(2, 0x228B22, 0.8);
                gridBg.strokeRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);

                for (let row = 0; row < GRID_SIZE; row++) {
                    this.buildGrid[row] = [];
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const x = startX + col * gridSpacing;
                        const y = startY + row * gridSpacing;
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);
                        const hasMineral = mineralPositions.some(pos => pos.row === row && pos.col === col);

                        // 创建网格格子
                        const gridCell = this.add.graphics();

                        if (isCenter) {
                            // 基地格子
                            gridCell.fillStyle(0x8B4513, 0.8);
                            gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            gridCell.lineStyle(2, 0x654321, 0.9);
                            gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        } else if (hasMineral) {
                            // 有矿的格子
                            gridCell.fillStyle(0xFFD700, 0.4);
                            gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            gridCell.lineStyle(2, 0xDAA520, 0.8);
                            gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        } else {
                            // 空格子
                            gridCell.fillStyle(0xFFFFFF, 0.1);
                            gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            gridCell.lineStyle(1, 0x228B22, 0.5);
                            gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        }

                        // 设置交互
                        gridCell.setInteractive(new Phaser.Geom.Rectangle(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE), Phaser.Geom.Rectangle.Contains);

                        const cell = {
                            x: x,
                            y: y,
                            row: row,
                            col: col,
                            background: gridCell,
                            building: null,
                            isEmpty: !isCenter,
                            isBase: isCenter,
                            hasMineral: hasMineral
                        };

                        // 如果有矿，添加到矿点列表
                        if (hasMineral) {
                            this.mineralCells.push(cell);
                            // 添加矿点图标
                            const mineralIcon = this.add.text(x, y, '💎', {
                                fontSize: '20px',
                                fontFamily: 'Arial, sans-serif',
                                resolution: 2
                            }).setOrigin(0.5);
                            cell.mineralIcon = mineralIcon;
                        }

                        // 如果是基地，创建基地建筑
                        if (isCenter) {
                            this.createBaseBuilding(cell); // 创建基地建筑
                        }

                        // 添加点击事件处理
                        gridCell.on('pointerdown', () => {
                            this.onCellClick(cell);
                        });

                        // 添加悬停效果（只对空格子）
                        if (!isCenter) {
                            gridCell.on('pointerover', () => {
                                if (cell.isEmpty) {
                                    gridCell.clear();
                                    gridCell.fillStyle(0x90EE90, 0.6);
                                    gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                    gridCell.lineStyle(2, 0x228B22, 0.8);
                                    gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                }
                            });

                            gridCell.on('pointerout', () => {
                                if (cell.isEmpty) {
                                    gridCell.clear();
                                    gridCell.fillStyle(0xFFFFFF, 0.1);
                                    gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                    gridCell.lineStyle(1, 0x228B22, 0.5);
                                    gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                }
                            });
                        }

                        this.buildGrid[row][col] = cell;
                    }
                }
            }

            generateMineralPositions() {
                const positions = [];
                const mineralCount = Phaser.Math.Between(3, 6); // 3-6个矿点

                while (positions.length < mineralCount) {
                    const row = Phaser.Math.Between(0, GRID_SIZE - 1);
                    const col = Phaser.Math.Between(0, GRID_SIZE - 1);

                    // 避开基地位置和已有矿点
                    const isBase = (row === this.basePosition.row && col === this.basePosition.col);
                    const alreadyExists = positions.some(pos => pos.row === row && pos.col === col);

                    if (!isBase && !alreadyExists) {
                        positions.push({ row, col });
                    }
                }

                return positions;
            }

            createBuilding(cell, buildingTypeId) {
                const buildingType = BUILDING_TYPES[buildingTypeId];

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑背景
                const buildingBg = this.add.image(0, 0, `building_${buildingTypeId}`);

                // 建筑表情
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加等级显示（右上角）
                let levelText = null;
                if (buildingType.level > 1) {
                    levelText = this.add.text(20, -20, buildingType.level.toString(), {
                        fontSize: '16px',
                        fontFamily: 'Arial, sans-serif',
                        color: '#FFD700',
                        fontStyle: 'bold',
                        backgroundColor: '#000000',
                        padding: { x: 4, y: 2 },
                        resolution: 2
                    }).setOrigin(0.5);
                }

                if (levelText) {
                    buildingContainer.add([buildingBg, buildingEmoji, levelText]);
                } else {
                    buildingContainer.add([buildingBg, buildingEmoji]);
                }

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: buildingTypeId,
                    level: buildingType.level,
                    canMerge: buildingType.canMerge,
                    isDefense: buildingType.isDefense,
                    cell: cell, // 存储格子引用
                    hp: this.getBuildingMaxHP(buildingTypeId, buildingType.level),
                    maxHp: this.getBuildingMaxHP(buildingTypeId, buildingType.level)
                };

                // 给所有建筑添加血条（除了基地）
                if (buildingTypeId !== 0) {
                    this.createBuildingHealthBar(cell.building);
                }

                // 如果不是基地，添加建造动画
                if (buildingTypeId !== 0) {
                    buildingContainer.setScale(0);
                    this.tweens.add({
                        targets: buildingContainer,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }

                // 如果是防御塔，启动攻击系统
                if (buildingType.isDefense) {
                    this.startTowerAttack(cell.building);
                }

                // 如果是矿厂，检查是否在矿点上并启动产金币系统
                if (buildingTypeId === 2 || buildingTypeId === 7) { // 矿厂或高级矿厂
                    if (cell.hasMineral) {
                        this.startMineProduction(cell.building);
                    }
                }

                // 更新电量显示
                this.updatePowerDisplay();

                return cell.building;
            }

            createBuildingWithLevel(cell, buildingTypeId, level) {
                const buildingType = BUILDING_TYPES[buildingTypeId];

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑背景
                const buildingBg = this.add.image(0, 0, `building_${buildingTypeId}`);

                // 建筑表情
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加等级显示（右上角）
                let levelText = null;
                if (level > 1) {
                    levelText = this.add.text(20, -20, level.toString(), {
                        fontSize: '16px',
                        fontFamily: 'Arial, sans-serif',
                        color: '#FFD700',
                        fontStyle: 'bold',
                        backgroundColor: '#000000',
                        padding: { x: 4, y: 2 },
                        resolution: 2
                    }).setOrigin(0.5);
                }

                if (levelText) {
                    buildingContainer.add([buildingBg, buildingEmoji, levelText]);
                } else {
                    buildingContainer.add([buildingBg, buildingEmoji]);
                }

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: buildingTypeId,
                    level: level,
                    canMerge: buildingType.canMerge,
                    isDefense: buildingType.isDefense,
                    cell: cell,
                    hp: this.getBuildingMaxHP(buildingTypeId, level),
                    maxHp: this.getBuildingMaxHP(buildingTypeId, level)
                };

                // 给所有建筑添加血条（除了基地）
                if (buildingTypeId !== 0) {
                    this.createBuildingHealthBar(cell.building);
                }

                // 如果不是基地，添加建造动画
                if (buildingTypeId !== 0) {
                    buildingContainer.setScale(0);
                    this.tweens.add({
                        targets: buildingContainer,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }

                // 如果是防御塔，启动攻击系统
                if (buildingType.isDefense) {
                    this.startTowerAttack(cell.building);
                }

                // 如果是矿厂，检查是否在矿点上并启动产金币系统
                if (buildingTypeId === 2) { // 矿厂
                    if (cell.hasMineral) {
                        this.startMineProduction(cell.building);
                    }
                }

                // 更新电量显示
                this.updatePowerDisplay();

                // 检查基地升级
                this.checkBaseUpgrade();

                return cell.building;
            }

            createBaseBuilding(cell) {
                const buildingType = BUILDING_TYPES[0]; // 基地

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑背景
                const buildingBg = this.add.image(0, 0, `building_0`);

                // 建筑表情
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加等级显示（右上角）
                let levelText = null;
                if (this.baseLevel > 1) {
                    levelText = this.add.text(20, -20, this.baseLevel.toString(), {
                        fontSize: '16px',
                        fontFamily: 'Arial, sans-serif',
                        color: '#FFD700',
                        fontStyle: 'bold',
                        backgroundColor: '#000000',
                        padding: { x: 4, y: 2 },
                        resolution: 2
                    }).setOrigin(0.5);
                }

                if (levelText) {
                    buildingContainer.add([buildingBg, buildingEmoji, levelText]);
                } else {
                    buildingContainer.add([buildingBg, buildingEmoji]);
                }

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: 0,
                    level: this.baseLevel,
                    canMerge: false,
                    isDefense: false,
                    cell: cell,
                    hp: this.getBuildingMaxHP(0, this.baseLevel),
                    maxHp: this.getBuildingMaxHP(0, this.baseLevel)
                };

                // 如果基地等级>=2，添加炮塔攻击
                if (this.baseLevel >= 2) {
                    this.startBaseAttack(cell.building, 'cannon');
                }

                // 如果基地等级>=3，添加激光塔攻击
                if (this.baseLevel >= 3) {
                    this.startBaseAttack(cell.building, 'laser');
                }

                return cell.building;
            }

            startBaseAttack(building, weaponType) {
                // 基地攻击系统
                const attackSpeed = weaponType === 'cannon' ? 1200 : 600; // 炮塔慢，激光快

                const attackInterval = this.time.addEvent({
                    delay: attackSpeed,
                    callback: () => {
                        this.baseAttack(building, weaponType);
                    },
                    callbackScope: this,
                    loop: true
                });

                // 存储攻击计时器
                if (!building.baseAttackTimers) {
                    building.baseAttackTimers = [];
                }
                building.baseAttackTimers.push(attackInterval);
                this.towerAttackTimers.push(attackInterval);
            }

            baseAttack(building, weaponType) {
                if (!building || !building.cell) return;

                // 基地攻击范围更大（5格范围）
                const target = this.findEnemyInRange(building.cell, 5);
                if (!target) return;

                // 根据武器类型发射攻击
                if (weaponType === 'cannon') {
                    this.fireBullet(building.cell, target, 'shell', this.baseLevel);
                } else if (weaponType === 'laser') {
                    this.fireLaser(building.cell, target, this.baseLevel);
                }
            }

            checkBaseUpgrade() {
                // 检查是否需要升级基地
                let maxBuildingLevel = 1;

                // 遍历所有建筑，找到最高等级
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row] && this.buildGrid[row][col];
                        if (cell && cell.building && cell.building.type !== 0) { // 不包括基地本身
                            maxBuildingLevel = Math.max(maxBuildingLevel, cell.building.level);
                        }
                    }
                }

                // 基地等级应该等于最高建筑等级
                if (maxBuildingLevel > this.baseLevel) {
                    this.upgradeBase(maxBuildingLevel);
                }
            }

            upgradeBase(newLevel) {
                const oldLevel = this.baseLevel;
                this.baseLevel = newLevel;

                // 找到基地格子
                const baseCell = this.buildGrid[this.basePosition.row][this.basePosition.col];
                if (!baseCell || !baseCell.building) return;

                // 停止旧的攻击计时器
                if (baseCell.building.baseAttackTimers) {
                    baseCell.building.baseAttackTimers.forEach(timer => {
                        timer.destroy();
                        const index = this.towerAttackTimers.indexOf(timer);
                        if (index > -1) {
                            this.towerAttackTimers.splice(index, 1);
                        }
                    });
                    baseCell.building.baseAttackTimers = [];
                }

                // 重新创建基地建筑
                baseCell.building.container.destroy();
                this.createBaseBuilding(baseCell);

                // 显示升级消息
                this.showMessage(`基地升级到${newLevel}级！`, '#FFD700');

                // 根据新等级添加攻击能力
                if (newLevel >= 2 && oldLevel < 2) {
                    this.showMessage('基地获得炮塔攻击能力！', '#FF4500');
                }
                if (newLevel >= 3 && oldLevel < 3) {
                    this.showMessage('基地获得激光攻击能力！', '#9932CC');
                }
            }

            getBuildingMaxHP(buildingTypeId, level = 1) {
                // 所有建筑的基础血量
                let baseHP;
                switch(buildingTypeId) {
                    case 0: return 500 * level; // 基地血量也乘以等级
                    case 1: baseHP = 80; break; // 电厂
                    case 2: baseHP = 60; break; // 矿厂
                    case 3: baseHP = 100; break; // 机枪塔
                    case 4: baseHP = 150; break; // 炮塔
                    case 5: baseHP = 120; break; // 激光塔
                    default: baseHP = 100;
                }

                // 血量直接乘以等级
                return baseHP * level;
            }

            createBuildingHealthBar(building) {
                // 创建防御塔血条
                const healthBarBg = this.add.graphics();
                healthBarBg.fillStyle(0x000000, 0.5);
                healthBarBg.fillRect(-25, -40, 50, 6);

                const healthBar = this.add.graphics();
                healthBar.fillStyle(0x00FF00);
                healthBar.fillRect(-25, -40, 50, 6);

                building.container.add([healthBarBg, healthBar]);
                building.healthBar = healthBar;
                building.healthBarBg = healthBarBg;
            }

            updateBuildingHealthBar(building) {
                if (!building.healthBar || !building.hp || !building.maxHp) return;

                const healthPercent = building.hp / building.maxHp;
                building.healthBar.clear();

                // 根据血量改变颜色
                if (healthPercent > 0.6) {
                    building.healthBar.fillStyle(0x00FF00);
                } else if (healthPercent > 0.3) {
                    building.healthBar.fillStyle(0xFFFF00);
                } else {
                    building.healthBar.fillStyle(0xFF0000);
                }

                building.healthBar.fillRect(-25, -40, 50 * healthPercent, 6);
            }

            startTowerAttack(building) {
                // 防御塔攻击系统
                const attackInterval = this.time.addEvent({
                    delay: this.getTowerAttackSpeed(building.type, building.level),
                    callback: () => {
                        this.towerAttack(building);
                    },
                    callbackScope: this,
                    loop: true
                });

                building.attackTimer = attackInterval;
                this.towerAttackTimers.push(attackInterval);
            }

            getTowerAttackSpeed(towerType, level = 1) {
                // 不同防御塔的基础攻击速度（毫秒）
                let baseSpeed;
                switch(towerType) {
                    case 3: baseSpeed = 500; break; // 机枪塔：快速攻击
                    case 4: baseSpeed = 1500; break; // 炮塔：慢速攻击
                    case 5: baseSpeed = 800; break; // 激光塔：中速攻击
                    default: baseSpeed = 1000;
                }

                // 等级越高攻击越快（最快不超过原速度的50%）
                const speedReduction = (level - 1) * 0.1; // 每级减少10%攻击间隔
                return Math.max(baseSpeed * 0.5, baseSpeed * (1 - speedReduction));
            }

            towerAttack(building) {
                if (!building || !building.cell) return;

                // 找到攻击范围内的敌人
                const target = this.findEnemyInRange(building.cell, 3); // 3x3范围
                if (!target) return;

                // 根据防御塔类型发射不同的攻击
                switch(building.type) {
                    case 3: // 机枪塔
                        this.fireBullet(building.cell, target, 'bullet', building.level);
                        break;
                    case 4: // 炮塔
                        this.fireBullet(building.cell, target, 'shell', building.level);
                        break;
                    case 5: // 激光塔
                        this.fireLaser(building.cell, target, building.level);
                        break;
                }
            }

            findEnemyInRange(towerCell, range) {
                // 在攻击范围内找到最近的敌人
                let closestEnemy = null;
                let closestDistance = Infinity;

                for (let enemy of this.activeEnemies) {
                    if (!enemy.isAlive) continue;

                    const distance = Phaser.Math.Distance.Between(
                        towerCell.x, towerCell.y,
                        enemy.container.x, enemy.container.y
                    );

                    // 检查是否在攻击范围内（range格子的距离）
                    if (distance <= range * TILE_SIZE && distance < closestDistance) {
                        closestDistance = distance;
                        closestEnemy = enemy;
                    }
                }

                return closestEnemy;
            }

            fireBullet(towerCell, target, bulletType, level = 1) {
                // 创建子弹
                const bullet = this.add.graphics();

                if (bulletType === 'bullet') {
                    // 机枪子弹：小黄点
                    bullet.fillStyle(0xFFFF00);
                    bullet.fillCircle(0, 0, 3);
                } else if (bulletType === 'shell') {
                    // 炮弹：大红球
                    bullet.fillStyle(0xFF4500);
                    bullet.fillCircle(0, 0, 6);
                }

                bullet.x = towerCell.x;
                bullet.y = towerCell.y;

                const bulletData = {
                    graphics: bullet,
                    target: target,
                    damage: this.getBulletDamage(bulletType, level),
                    speed: bulletType === 'bullet' ? 400 : 200
                };

                this.bullets.push(bulletData);

                // 子弹移动动画
                this.tweens.add({
                    targets: bullet,
                    x: target.container.x,
                    y: target.container.y,
                    duration: Phaser.Math.Distance.Between(towerCell.x, towerCell.y, target.container.x, target.container.y) / bulletData.speed * 1000,
                    ease: 'Linear',
                    onComplete: () => {
                        this.bulletHit(bulletData);
                    }
                });
            }

            fireLaser(towerCell, target, level = 1) {
                // 激光攻击：瞬间命中
                const laser = this.add.graphics();
                laser.lineStyle(4, 0xFF00FF, 0.8);
                laser.lineBetween(towerCell.x, towerCell.y, target.container.x, target.container.y);

                // 激光特效
                this.tweens.add({
                    targets: laser,
                    alpha: 0,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        laser.destroy();
                    }
                });

                // 立即造成伤害
                this.damageEnemy(target, this.getBulletDamage('laser', level));
            }

            getBulletDamage(weaponType, level = 1) {
                let baseDamage;
                switch(weaponType) {
                    case 'bullet': baseDamage = 15; break; // 机枪伤害
                    case 'shell': baseDamage = 40; break; // 炮弹伤害
                    case 'laser': baseDamage = 25; break; // 激光伤害
                    default: baseDamage = 10;
                }

                // 攻击力直接乘以等级
                return baseDamage * level;
            }

            bulletHit(bulletData) {
                // 子弹命中
                if (bulletData.target.isAlive) {
                    this.damageEnemy(bulletData.target, bulletData.damage);
                }

                // 移除子弹
                bulletData.graphics.destroy();
                const index = this.bullets.indexOf(bulletData);
                if (index > -1) {
                    this.bullets.splice(index, 1);
                }
            }

            damageEnemy(enemy, damage) {
                if (!enemy.isAlive) return;

                enemy.hp -= damage;
                this.updateEnemyHealthBar(enemy);

                // 显示伤害数字
                this.showDamageText(enemy.container.x, enemy.container.y, damage);

                // 敌人受击动画
                this.tweens.add({
                    targets: enemy.container,
                    scaleX: 0.8,
                    scaleY: 0.8,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });

                if (enemy.hp <= 0) {
                    // 敌人死亡
                    this.enemiesKilled++;
                    this.killText.setText(`击杀: ${this.enemiesKilled}`);

                    // 给予金币奖励
                    const reward = 10 + enemy.type * 5;
                    this.gold += reward;
                    this.goldText.setText(`🪙 ${this.gold}`);

                    this.removeEnemy(enemy);
                }
            }

            startMineProduction(building) {
                // 矿厂产金币系统，根据等级调整
                const baseRate = 5000; // 基础生产间隔
                const baseGold = 20; // 基础金币产出

                const productionRate = Math.max(1000, baseRate - (building.level - 1) * 800); // 等级越高越快
                const goldPerProduction = baseGold + (building.level - 1) * 15; // 等级越高产出越多

                const productionTimer = this.time.addEvent({
                    delay: productionRate,
                    callback: () => {
                        if (building.cell && building.cell.hasMineral) {
                            this.gold += goldPerProduction;
                            this.goldText.setText(`🪙 ${this.gold}`);

                            // 矿场工作动画
                            this.playMineWorkingAnimation(building);

                            // 显示金币产出特效
                            this.showGoldProduction(building.cell.x, building.cell.y, goldPerProduction);
                        }
                    },
                    callbackScope: this,
                    loop: true
                });

                building.productionTimer = productionTimer;
            }

            playMineWorkingAnimation(building) {
                // 只让emoji旋转，不动整个建筑
                const emojiChild = building.container.list.find(child => child.type === 'Text');
                if (emojiChild) {
                    this.tweens.add({
                        targets: emojiChild,
                        rotation: emojiChild.rotation + Math.PI * 2,
                        duration: 1000,
                        ease: 'Power2'
                    });
                }

                // 矿点图标闪烁动画
                if (building.cell && building.cell.mineralIcon) {
                    this.tweens.add({
                        targets: building.cell.mineralIcon,
                        scaleX: 1.5,
                        scaleY: 1.5,
                        duration: 300,
                        yoyo: true,
                        ease: 'Power2'
                    });
                }
            }

            showGoldProduction(x, y, amount) {
                const goldText = this.add.text(x, y - 30, `+${amount}🪙`, {
                    fontSize: '18px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 金币产出动画
                this.tweens.add({
                    targets: goldText,
                    y: y - 80,
                    alpha: 0,
                    duration: 1500,
                    ease: 'Power2',
                    onComplete: () => {
                        goldText.destroy();
                    }
                });
            }

            onCellClick(cell) {
                if (this.isWaveActive) return;

                if (cell.isEmpty) {
                    // 点击空格子 - 如果有选中的建筑，移动到这里
                    if (this.selectedBuilding && this.selectedBuilding !== cell) {
                        this.moveBuildingToCell(this.selectedBuilding, cell);
                    } else {
                        // 点击空格子不再生成建筑
                        this.deselectBuilding();
                    }
                } else if (cell.isBase) {
                    // 点击基地，在周围生成建筑，扣除100金币
                    this.generateBuildingAroundBase(cell);
                } else if (cell.building) {
                    // 点击已有建筑
                    if (this.selectedBuilding === cell) {
                        // 如果已经选中，取消选中
                        this.deselectBuilding();
                    } else if (this.selectedBuilding && this.selectedBuilding !== cell) {
                        // 有选中的建筑，尝试合成或移动
                        if (this.canMergeBuildings(this.selectedBuilding, cell)) {
                            this.mergeBuildings(this.selectedBuilding, cell);
                        } else {
                            this.showMessage('无法合成这两个建筑！', '#FF4444');
                        }
                    } else {
                        // 选中建筑（用于移动）
                        this.selectBuilding(cell);
                    }
                }
            }

            canMergeBuildings(cell1, cell2) {
                // 检查两个建筑是否可以合成
                if (!cell1.building || !cell2.building) return false;
                if (!cell1.building.canMerge || !cell2.building.canMerge) return false;
                if (cell1.building.type !== cell2.building.type) return false;

                // 检查等级是否相同且未达到最高级
                if (cell1.building.level !== cell2.building.level) return false;
                if (cell1.building.level >= 6) return false; // 最高6级

                return true;
            }

            generateBuildingAroundBase(baseCell) {
                // 检查金币是否足够
                if (this.gold < 100) {
                    this.showMessage('金币不足！需要100金币', '#FF4444');
                    return;
                }

                // 找到基地周围的空格子
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                const emptyAdjacentCells = [];
                for (let dir of directions) {
                    const newRow = baseCell.row + dir.row;
                    const newCol = baseCell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];
                        if (adjacentCell.isEmpty) {
                            emptyAdjacentCells.push(adjacentCell);
                        }
                    }
                }

                if (emptyAdjacentCells.length === 0) {
                    this.showMessage('基地周围没有空位！', '#FF4444');
                    return;
                }

                // 扣除金币
                this.gold -= 100;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 随机选择一个空格子生成建筑
                const randomCell = emptyAdjacentCells[Phaser.Math.Between(0, emptyAdjacentCells.length - 1)];
                this.generateRandomBuilding(randomCell);

                this.showMessage('花费100金币生成建筑！', '#FFD700');
            }

            selectBuilding(cell) {
                // 取消之前的选择
                this.deselectBuilding();

                // 选中当前建筑
                this.selectedBuilding = cell;

                // 添加选中效果 - 创建一个黄色边框
                const selectionBorder = this.add.graphics();
                selectionBorder.lineStyle(4, 0xFFFF00, 1);
                selectionBorder.strokeRoundedRect(cell.x - TILE_SIZE/2 - 2, cell.y - TILE_SIZE/2 - 2, TILE_SIZE + 4, TILE_SIZE + 4, 8);

                // 将边框存储到建筑对象中
                cell.building.selectionBorder = selectionBorder;

                this.showMessage(`选中了 ${BUILDING_TYPES[cell.building.type].name}，点击空格子移动建筑`, '#FFFF44');
            }

            deselectBuilding() {
                if (this.selectedBuilding && this.selectedBuilding.building) {
                    // 移除选中效果
                    if (this.selectedBuilding.building.selectionBorder) {
                        this.selectedBuilding.building.selectionBorder.destroy();
                        this.selectedBuilding.building.selectionBorder = null;
                    }
                    this.selectedBuilding = null;
                }
            }

            isAdjacentToBuilding(cell) {
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];
                        if (!adjacentCell.isEmpty) {
                            return true;
                        }
                    }
                }
                return false;
            }

            generateRandomBuilding(cell) {
                // 只在基地周围生成建筑时使用此函数
                // 根据基地等级决定可以建造的建筑类型
                let availableBuildings = [1, 2, 3]; // 基础建筑：电厂、矿厂、机枪塔

                // 基地等级2可以建造炮塔
                if (this.baseLevel >= 2) {
                    availableBuildings.push(4); // 炮塔
                }

                // 基地等级3可以建造激光塔
                if (this.baseLevel >= 3) {
                    availableBuildings.push(5); // 激光塔
                }

                const randomBuildingId = availableBuildings[Phaser.Math.Between(0, availableBuildings.length - 1)];

                this.createBuilding(cell, randomBuildingId);

                // 更新格子背景
                cell.background.clear();
                const buildingType = BUILDING_TYPES[randomBuildingId];
                cell.background.fillStyle(buildingType.color, 0.3);
                cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell.background.lineStyle(2, buildingType.color, 0.8);
                cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                this.showMessage(`生成了 ${buildingType.name}！`, '#44FF44');
            }

            moveBuildingToCell(fromCell, toCell) {
                if (!fromCell.building || !toCell.isEmpty) {
                    this.showMessage('无法移动到该位置！', '#FF4444');
                    return;
                }

                const building = fromCell.building;
                const buildingType = BUILDING_TYPES[building.type];
                const buildingLevel = building.level; // 保存等级信息

                // 清理选中边框
                if (building.selectionBorder) {
                    building.selectionBorder.destroy();
                    building.selectionBorder = null;
                }

                // 停止防御塔攻击计时器
                if (building.attackTimer) {
                    building.attackTimer.destroy();
                    const index = this.towerAttackTimers.indexOf(building.attackTimer);
                    if (index > -1) {
                        this.towerAttackTimers.splice(index, 1);
                    }
                }

                // 停止矿厂产金币计时器
                if (building.productionTimer) {
                    building.productionTimer.destroy();
                }

                // 销毁原位置的建筑显示
                building.container.destroy();

                // 在新位置创建建筑，保持等级
                this.createBuildingWithLevel(toCell, building.type, buildingLevel);

                // 更新新格子背景
                toCell.background.clear();
                toCell.background.fillStyle(buildingType.color, 0.3);
                toCell.background.fillRoundedRect(toCell.x - TILE_SIZE/2, toCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                toCell.background.lineStyle(2, buildingType.color, 0.8);
                toCell.background.strokeRoundedRect(toCell.x - TILE_SIZE/2, toCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 清空原格子
                fromCell.building = null;
                fromCell.isEmpty = true;
                fromCell.background.clear();
                fromCell.background.fillStyle(0xFFFFFF, 0.1);
                fromCell.background.fillRoundedRect(fromCell.x - TILE_SIZE/2, fromCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                fromCell.background.lineStyle(1, 0x228B22, 0.5);
                fromCell.background.strokeRoundedRect(fromCell.x - TILE_SIZE/2, fromCell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 取消选中
                this.selectedBuilding = null;

                // 更新电量显示
                this.updatePowerDisplay();

                this.showMessage(`${buildingLevel}级${buildingType.name} 已移动！`, '#44FF44');
            }

            tryMergeBuilding(cell) {
                const building = cell.building;
                if (!building.canMerge) return;

                // 寻找相邻的相同建筑
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];

                        if (!adjacentCell.isEmpty &&
                            adjacentCell.building &&
                            adjacentCell.building.type === building.type &&
                            adjacentCell.building.canMerge) {

                            // 执行合成
                            this.mergeBuildings(cell, adjacentCell);
                            return;
                        }
                    }
                }

                // 防御塔区域已移除，所有建筑都可以在网格中拖动
            }

            mergeBuildings(cell1, cell2) {
                const buildingType = cell1.building.type;
                const currentLevel = cell1.building.level;

                // 清理选中边框
                if (cell1.building.selectionBorder) {
                    cell1.building.selectionBorder.destroy();
                    cell1.building.selectionBorder = null;
                }
                if (cell2.building.selectionBorder) {
                    cell2.building.selectionBorder.destroy();
                    cell2.building.selectionBorder = null;
                }

                // 停止计时器
                [cell1.building, cell2.building].forEach(building => {
                    if (building.attackTimer) {
                        building.attackTimer.destroy();
                        const index = this.towerAttackTimers.indexOf(building.attackTimer);
                        if (index > -1) {
                            this.towerAttackTimers.splice(index, 1);
                        }
                    }
                    if (building.productionTimer) {
                        building.productionTimer.destroy();
                    }
                });

                // 销毁两个建筑
                cell1.building.container.destroy();
                cell2.building.container.destroy();

                // 清空第一个格子
                cell1.building = null;
                cell1.isEmpty = true;
                cell1.background.clear();
                cell1.background.fillStyle(0xFFFFFF, 0.1);
                cell1.background.fillRoundedRect(cell1.x - TILE_SIZE/2, cell1.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell1.background.lineStyle(1, 0x228B22, 0.5);
                cell1.background.strokeRoundedRect(cell1.x - TILE_SIZE/2, cell1.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 创建升级后的建筑（相同类型，等级+1）在第二个位置
                const newLevel = currentLevel + 1;
                this.createBuildingWithLevel(cell2, buildingType, newLevel);

                // 更新第二个格子背景
                const buildingTypeData = BUILDING_TYPES[buildingType];
                cell2.background.clear();
                cell2.background.fillStyle(buildingTypeData.color, 0.3);
                cell2.background.fillRoundedRect(cell2.x - TILE_SIZE/2, cell2.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell2.background.lineStyle(2, buildingTypeData.color, 0.8);
                cell2.background.strokeRoundedRect(cell2.x - TILE_SIZE/2, cell2.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 取消选中
                this.selectedBuilding = null;

                this.showMessage(`合成了 ${newLevel}级${buildingTypeData.name}！`, '#FFD700');

                // 检查基地升级
                this.checkBaseUpgrade();
            }

            createControlArea() {
                // 控制按钮区域（在屏幕最下方）- 只保留重置按钮
                const controlAreaY = GAME_HEIGHT - 80;
                const buttonWidth = 120;
                const buttonHeight = 60;
                const startButtonX = (GAME_WIDTH - buttonWidth) / 2;

                // 创建重置按钮（居中）
                const resetButton = this.createControlButton(
                    startButtonX, controlAreaY, buttonWidth, buttonHeight,
                    '重置', 0xFF5722, () => this.resetGame()
                );

                this.controlButtons = {
                    reset: resetButton
                };
            }

            createControlButton(x, y, width, height, text, color, callback) {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(color);
                buttonBg.fillRoundedRect(x, y, width, height, 10);
                buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x333333);
                buttonBg.strokeRoundedRect(x, y, width, height, 10);

                // 设置按钮可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(x, y, width, height), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(x + width/2, y + height/2, text, {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加点击事件
                buttonBg.on('pointerdown', callback);

                // 添加悬停效果
                buttonBg.on('pointerover', () => {
                    buttonBg.clear();
                    buttonBg.fillStyle(color + 0x222222);
                    buttonBg.fillRoundedRect(x, y, width, height, 10);
                    buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x111111);
                    buttonBg.strokeRoundedRect(x, y, width, height, 10);
                });

                buttonBg.on('pointerout', () => {
                    buttonBg.clear();
                    buttonBg.fillStyle(color);
                    buttonBg.fillRoundedRect(x, y, width, height, 10);
                    buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x333333);
                    buttonBg.strokeRoundedRect(x, y, width, height, 10);
                });

                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            showMessage(text, color = '#FFFFFF') {
                // 显示临时消息
                const message = this.add.text(GAME_WIDTH/2, 150, text, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: color,
                    fontStyle: 'bold',
                    backgroundColor: '#000000',
                    padding: { x: 15, y: 8 },
                    resolution: 2
                }).setOrigin(0.5);
                message.setDepth(1000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (message) {
                        this.tweens.add({
                            targets: message,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                message.destroy();
                            }
                        });
                    }
                });
            }

            getFruitTypeForPlate(plateIndex, fruitIndex) {
                // 根据关卡和水果盘索引生成水果类型
                const level = this.level;

                if (level === 1) {
                    // 第1关：1个水果盘，4个相同水果
                    return 7; // 使用樱桃（最后一个水果类型）
                }

                if (level === 2) {
                    // 第2关：2个水果盘，每个盘子4个相同水果，但两个盘子不同
                    return plateIndex === 0 ? 7 : 6; // 第一个盘子樱桃，第二个盘子猕猴桃
                }

                if (level === 3) {
                    // 第3关：3个水果盘，每个盘子4个相同水果
                    const fruitTypes = [7, 6, 5]; // 樱桃、猕猴桃、草莓
                    return fruitTypes[plateIndex];
                }

                // 第4关及以后：更复杂的组合
                if (level === 4) {
                    // 第4关：4个水果盘，前两个盘子相同水果，后两个盘子混合水果
                    if (plateIndex < 2) {
                        return plateIndex === 0 ? 7 : 6; // 樱桃和猕猴桃
                    } else {
                        // 后两个盘子：2个樱桃 + 2个草莓
                        return fruitIndex < 2 ? 7 : 5;
                    }
                }

                // 第5关及以后：完全随机但保证有解
                const availableTypes = [7, 6, 5, 4]; // 使用4种水果类型
                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getRandomFruitTypeForLevel() {
                // 根据关卡返回随机的可用水果类型（前5关只有1种干扰水果）
                let availableTypes = [];

                if (this.level === 1) {
                    // 第1关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6]; // 樱桃（目标） + 猕猴桃（干扰）
                } else if (this.level === 2) {
                    // 第2关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5]; // 樱桃、猕猴桃（目标） + 草莓（干扰）
                } else if (this.level === 3) {
                    // 第3关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4]; // 樱桃、猕猴桃、草莓（目标） + 葡萄（干扰）
                } else if (this.level === 4) {
                    // 第4关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 3]; // 4种目标水果 + 香蕉（干扰）
                } else if (this.level === 5) {
                    // 第5关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 2]; // 使用橙子作为干扰
                } else {
                    // 第6关及以后：使用更多干扰水果
                    availableTypes = [];
                    for (let i = 0; i < TILE_TYPES.length; i++) {
                        availableTypes.push(i);
                    }
                }

                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getGridSizeForLevel() {
                // 根据关卡返回网格大小
                if (this.level === 1) {
                    return { rows: 4, cols: 4 }; // 第1关：4x4
                } else if (this.level === 2) {
                    return { rows: 4, cols: 5 }; // 第2关：4x5
                } else if (this.level === 3) {
                    return { rows: 5, cols: 5 }; // 第3关：5x5
                } else if (this.level === 4) {
                    return { rows: 5, cols: 6 }; // 第4关：5x6
                } else {
                    return { rows: 6, cols: 6 }; // 第5关及以后：6x6
                }
            }

            getTargetFruitTypes() {
                // 获取当前关卡目标水果盘中需要的所有水果类型
                const targetTypes = new Set();

                if (this.fruitPlates) {
                    this.fruitPlates.forEach(plate => {
                        plate.fruits.forEach(fruit => {
                            targetTypes.add(fruit.getData('typeIndex'));
                        });
                    });
                }

                return Array.from(targetTypes);
            }

            createToolArea() {
                // 道具区域位置（在屏幕最下方）
                const toolAreaHeight = 100;
                const toolAreaY = GAME_HEIGHT - toolAreaHeight;
                const toolSize = 80;
                const toolSpacing = 60;
                const totalToolsWidth = 3 * toolSize + 2 * toolSpacing;
                const startToolX = (GAME_WIDTH - totalToolsWidth) / 2;

                // 创建道具背景区域
                const toolBgHeight = toolAreaHeight - 20; // 留10像素上下边距
                const toolBgY = toolAreaY + 10; // 从顶部留10像素开始

                const toolBg = this.add.graphics();
                toolBg.fillStyle(0x2C3E50, 0.8);
                toolBg.fillRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);
                toolBg.lineStyle(2, 0x34495E, 0.9);
                toolBg.strokeRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);

                // 道具信息
                const tools = [
                    { icon: '🔨', name: '锤子' },
                    { icon: '↶', name: '撤销' },
                    { icon: '🔄', name: '交换' }
                ];

                // 创建道具按钮（在背景框内上下居中）
                tools.forEach((tool, index) => {
                    const toolX = startToolX + index * (toolSize + toolSpacing);
                    const toolY = toolBgY + toolBgHeight / 2 - toolSize / 2; // 在背景框内垂直居中

                    // 创建道具按钮背景
                    const buttonBg = this.add.graphics();
                    buttonBg.fillStyle(0x3498DB, 0.9);
                    buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    buttonBg.lineStyle(3, 0x2980B9, 1);
                    buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);

                    // 设置按钮可交互
                    buttonBg.setInteractive(new Phaser.Geom.Rectangle(toolX, toolY, toolSize, toolSize), Phaser.Geom.Rectangle.Contains);

                    // 创建道具图标
                    const toolIcon = this.add.text(toolX + toolSize/2, toolY + toolSize/2, tool.icon, {
                        fontSize: '48px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    // 添加悬停效果
                    buttonBg.on('pointerover', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x2980B9, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x2471A3, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    });

                    buttonBg.on('pointerout', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x3498DB, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x2980B9, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    });

                    // 添加点击事件（暂时只显示提示）
                    buttonBg.on('pointerdown', () => {
                        this.showToolTip(tool.name, toolX + toolSize/2, toolY - 20);
                    });
                });
            }

            showToolTip(toolName, x, y) {
                // 显示道具提示
                const tooltip = this.add.text(x, y, `${toolName}功能开发中...`, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    backgroundColor: '#2C3E50',
                    padding: { x: 10, y: 5 },
                    resolution: 2
                }).setOrigin(0.5);
                tooltip.setDepth(2000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (tooltip) {
                        this.tweens.add({
                            targets: tooltip,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                tooltip.destroy();
                            }
                        });
                    }
                });
            }

            spawnTargetFruits() {
                // 重新创建水果盘区域（createEnemyArea会处理清理工作）
                this.createEnemyArea();
            }



            initializeGame() {
                // 重置游戏状态
                this.gold = 1000;
                this.power = 0;
                this.wave = 1;
                this.enemiesKilled = 0;
                this.isWaveActive = false;
                this.baseLevel = 1; // 重置基地等级

                // 清理选中状态
                this.deselectBuilding();
                this.selectedBuilding = null;
                this.activeEnemies = [];

                // 停止自动生成敌人的计时器
                if (this.autoSpawnTimer) {
                    this.autoSpawnTimer.destroy();
                    this.autoSpawnTimer = null;
                }

                // 清理所有防御塔攻击计时器
                this.towerAttackTimers.forEach(timer => {
                    if (timer) timer.destroy();
                });
                this.towerAttackTimers = [];

                // 清理所有子弹
                this.bullets.forEach(bullet => {
                    if (bullet.graphics) bullet.graphics.destroy();
                });
                this.bullets = [];

                // 更新UI（先不更新电量，等建造网格重置后再更新）
                this.goldText.setText(`🪙 ${this.gold}`);
                this.waveText.setText(`第${this.wave}波`);
                this.killText.setText(`击杀: ${this.enemiesKilled}`);

                // 清理已建造的建筑
                this.buildings.forEach(building => {
                    if (building.container) building.container.destroy();
                });
                this.buildings = [];

                // 重置建造网格
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);

                        if (cell.building && !isCenter) {
                            cell.building.container.destroy();
                            cell.building = null;
                        }

                        if (!isCenter) {
                            cell.isEmpty = true;
                            // 重置格子背景
                            cell.background.clear();
                            if (cell.hasMineral) {
                                cell.background.fillStyle(0xFFD700, 0.4);
                                cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                cell.background.lineStyle(2, 0xDAA520, 0.8);
                                cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            } else {
                                cell.background.fillStyle(0xFFFFFF, 0.1);
                                cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                cell.background.lineStyle(1, 0x228B22, 0.5);
                                cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            }
                        } else if (isCenter) {
                            // 重建基地
                            if (cell.building) {
                                cell.building.container.destroy();
                            }
                            this.createBaseBuilding(cell);
                        }
                    }
                }

                // 重置防御塔槽位 - 已移除防御塔区域功能
                // this.towerSlots.forEach(slot => { ... });

                // 现在建造网格已经重置完成，可以安全地更新电量显示
                this.updatePowerDisplay();

                // 开始自动生成敌人
                this.startAutoSpawn();

                // 重新生成敌人预览
                this.createEnemyArea();
            }

            calculateMaxPower() {
                // 计算最大电量：电厂数量 * 500
                if (!this.buildGrid || this.buildGrid.length === 0) {
                    return 0;
                }

                let totalPower = 0;
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row] && this.buildGrid[row][col];
                        if (cell && cell.building && cell.building.type === 1) {
                            // 电厂产电：基础500 + 等级加成
                            const basePower = 500;
                            const levelBonus = (cell.building.level - 1) * 250; // 每级增加250电
                            totalPower += basePower + levelBonus;
                        }
                    }
                }
                return totalPower;
            }

            calculatePowerConsumption() {
                // 计算电量消耗：每个建筑消耗100电（除了基地和电厂）
                if (!this.buildGrid || this.buildGrid.length === 0) {
                    return 0;
                }

                let consumption = 0;
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row] && this.buildGrid[row][col];
                        if (cell && cell.building && cell.building.type !== 0 && cell.building.type !== 1) {
                            // 建筑耗电：基础100 + 等级加成
                            const basePower = 100;
                            const levelBonus = (cell.building.level - 1) * 50; // 每级增加50电消耗
                            consumption += basePower + levelBonus;
                        }
                    }
                }
                return consumption;
            }

            updatePowerDisplay() {
                const maxPower = this.calculateMaxPower();
                const consumption = this.calculatePowerConsumption();
                this.power = Math.max(0, maxPower - consumption);
                this.powerText.setText(`⚡ ${this.power}/${maxPower}`);

                // 如果电力不足，显示警告
                if (this.power < 0) {
                    this.powerText.setColor('#FF4444');
                } else if (this.power < maxPower * 0.2) {
                    this.powerText.setColor('#FFA500');
                } else {
                    this.powerText.setColor('#FFD700');
                }
            }

            startAutoSpawn() {
                // 每10秒自动生成一波敌人（从5秒改为10秒）
                this.autoSpawnTimer = this.time.addEvent({
                    delay: 10000,
                    callback: this.spawnEnemyWave,
                    callbackScope: this,
                    loop: true
                });

                // 3秒后生成第一波（给玩家更多准备时间）
                this.time.delayedCall(3000, () => {
                    this.spawnEnemyWave();
                });
            }

            spawnEnemyWave() {
                // 根据波次生成敌人
                const enemyCount = Math.min(3 + Math.floor(this.wave / 2), 8);

                for (let i = 0; i < enemyCount; i++) {
                    this.time.delayedCall(i * 500, () => {
                        this.spawnSingleEnemy();
                    });
                }

                this.wave++;
                this.waveText.setText(`第${this.wave}波`);
            }

            spawnSingleEnemy() {
                // 从屏幕右侧生成敌人
                const enemyTypeIndex = this.getEnemyTypeForWave(this.wave);
                const enemyType = ENEMY_TYPES[enemyTypeIndex];

                // 创建敌人容器
                const enemyContainer = this.add.container(GAME_WIDTH + 50, 200 + Phaser.Math.Between(-100, 100));

                // 敌人背景
                const enemyBg = this.add.image(0, 0, `enemy_${enemyTypeIndex}`);
                enemyBg.setScale(0.8);

                // 敌人表情
                const enemyEmoji = this.add.text(0, 0, enemyType.emoji, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 血条背景
                const healthBarBg = this.add.graphics();
                healthBarBg.fillStyle(0x000000, 0.5);
                healthBarBg.fillRect(-20, -35, 40, 6);

                // 血条
                const healthBar = this.add.graphics();
                healthBar.fillStyle(0x00FF00);
                healthBar.fillRect(-20, -35, 40, 6);

                enemyContainer.add([enemyBg, enemyEmoji, healthBarBg, healthBar]);

                // 敌人数据，血量根据波次增加
                const scaledHP = Math.floor(enemyType.hp * (1 + (this.wave - 1) * 0.3)); // 每波增加30%血量
                const enemy = {
                    container: enemyContainer,
                    type: enemyTypeIndex,
                    hp: scaledHP,
                    maxHp: scaledHP,
                    speed: enemyType.speed,
                    healthBar: healthBar,
                    isAlive: true,
                    targetRow: -1,
                    targetCol: -1
                };

                this.activeEnemies.push(enemy);

                // 开始移动
                this.moveEnemyToTarget(enemy);
            }

            moveEnemyToTarget(enemy) {
                // 找到第一排的建筑作为目标
                const target = this.findFirstRowTarget();
                if (!target) {
                    // 没有目标，移除敌人
                    this.removeEnemy(enemy);
                    return;
                }

                enemy.targetRow = target.row;
                enemy.targetCol = target.col;

                const targetX = target.x;
                const targetY = target.y - 60; // 在建筑前方停下

                // 移动到目标位置
                this.tweens.add({
                    targets: enemy.container,
                    x: targetX,
                    y: targetY,
                    duration: 3000 / enemy.speed,
                    ease: 'Linear',
                    onComplete: () => {
                        if (enemy.isAlive) {
                            this.startEnemyAttack(enemy, target);
                        }
                    }
                });
            }

            findFirstRowTarget() {
                // 优先攻击防御塔，然后是其他建筑
                if (!this.buildGrid || this.buildGrid.length === 0) {
                    return null;
                }

                let defenseTargets = [];
                let otherTargets = [];

                // 从左到右扫描第一排（最右边的列）
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = GRID_SIZE - 1; col >= 0; col--) {
                        const cell = this.buildGrid[row] && this.buildGrid[row][col];
                        if (cell && cell.building && !cell.isBase) {
                            if (cell.building.isDefense) {
                                defenseTargets.push(cell);
                            } else {
                                otherTargets.push(cell);
                            }
                            break; // 找到这一行的第一个建筑就停止
                        }
                    }
                }

                // 优先选择防御塔
                if (defenseTargets.length > 0) {
                    return defenseTargets[Phaser.Math.Between(0, defenseTargets.length - 1)];
                } else if (otherTargets.length > 0) {
                    return otherTargets[Phaser.Math.Between(0, otherTargets.length - 1)];
                }

                return null;
            }

            startEnemyAttack(enemy, target) {
                // 敌人开始攻击建筑
                const attackInterval = this.time.addEvent({
                    delay: 1000, // 每秒攻击一次
                    callback: () => {
                        if (!enemy.isAlive || !target.building) {
                            attackInterval.destroy();
                            this.removeEnemy(enemy);
                            return;
                        }

                        // 攻击建筑
                        this.damageBuilding(target, 10);

                        // 攻击动画
                        this.showAttackAnimation(enemy.container.x, enemy.container.y, target.x, target.y);
                    },
                    callbackScope: this,
                    loop: true
                });

                // 将攻击计时器存储到敌人对象中
                enemy.attackTimer = attackInterval;
            }

            damageBuilding(target, damage) {
                if (!target.building) return;

                const building = target.building;

                // 所有建筑都有血量（除了基地）
                if (building.type !== 0 && building.hp !== null) {
                    building.hp -= damage;
                    this.updateBuildingHealthBar(building);

                    // 检查建筑是否被摧毁
                    if (building.hp <= 0) {
                        this.destroyBuilding(target);
                        return;
                    }
                }

                // 建筑受伤动画
                this.tweens.add({
                    targets: building.container,
                    scaleX: 0.9,
                    scaleY: 0.9,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 显示伤害数字
                this.showDamageText(target.x, target.y, damage);
            }

            destroyBuilding(target) {
                const building = target.building;

                // 停止防御塔攻击计时器
                if (building.attackTimer) {
                    building.attackTimer.destroy();
                    const index = this.towerAttackTimers.indexOf(building.attackTimer);
                    if (index > -1) {
                        this.towerAttackTimers.splice(index, 1);
                    }
                }

                // 停止矿厂产金币计时器
                if (building.productionTimer) {
                    building.productionTimer.destroy();
                }

                // 销毁建筑容器
                building.container.destroy();

                // 清空格子
                target.building = null;
                target.isEmpty = true;

                // 恢复格子背景
                target.background.clear();
                if (target.hasMineral) {
                    target.background.fillStyle(0xFFD700, 0.4);
                    target.background.fillRoundedRect(target.x - TILE_SIZE/2, target.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                    target.background.lineStyle(2, 0xDAA520, 0.8);
                    target.background.strokeRoundedRect(target.x - TILE_SIZE/2, target.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                } else {
                    target.background.fillStyle(0xFFFFFF, 0.1);
                    target.background.fillRoundedRect(target.x - TILE_SIZE/2, target.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                    target.background.lineStyle(1, 0x228B22, 0.5);
                    target.background.strokeRoundedRect(target.x - TILE_SIZE/2, target.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                }

                // 更新电量显示
                this.updatePowerDisplay();

                this.showMessage('建筑被摧毁！', '#FF4444');
            }

            showAttackAnimation(fromX, fromY, toX, toY) {
                // 创建攻击特效
                const attackEffect = this.add.graphics();
                attackEffect.lineStyle(3, 0xFF0000, 0.8);
                attackEffect.lineBetween(fromX, fromY, toX, toY);

                // 特效消失
                this.time.delayedCall(200, () => {
                    attackEffect.destroy();
                });
            }

            showDamageText(x, y, damage) {
                const damageText = this.add.text(x, y - 20, `-${damage}`, {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FF4444',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 伤害数字动画
                this.tweens.add({
                    targets: damageText,
                    y: y - 60,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        damageText.destroy();
                    }
                });
            }

            removeEnemy(enemy) {
                if (!enemy.isAlive) return;

                enemy.isAlive = false;

                // 停止攻击计时器
                if (enemy.attackTimer) {
                    enemy.attackTimer.destroy();
                }

                // 移除敌人容器
                if (enemy.container) {
                    enemy.container.destroy();
                }

                // 从活跃敌人列表中移除
                const index = this.activeEnemies.indexOf(enemy);
                if (index > -1) {
                    this.activeEnemies.splice(index, 1);
                }
            }

            updateEnemyHealthBar(enemy) {
                if (!enemy.isAlive || !enemy.healthBar) return;

                const healthPercent = enemy.hp / enemy.maxHp;
                enemy.healthBar.clear();

                // 根据血量改变颜色
                if (healthPercent > 0.6) {
                    enemy.healthBar.fillStyle(0x00FF00);
                } else if (healthPercent > 0.3) {
                    enemy.healthBar.fillStyle(0xFFFF00);
                } else {
                    enemy.healthBar.fillStyle(0xFF0000);
                }

                enemy.healthBar.fillRect(-20, -35, 40 * healthPercent, 6);
            }

            // 旧的波次系统已移除，现在使用自动生成敌人系统

            resetGame() {
                // 清理所有活跃敌人
                this.activeEnemies.forEach(enemy => {
                    this.removeEnemy(enemy);
                });
                this.activeEnemies = [];

                this.initializeGame();
                this.showMessage('游戏已重置', '#FFFF44');
            }





            selectGridTile(tile, row, col) {
                this.isAnimating = true;

                // 找到空的槽位（跳过锁住的槽位）
                const emptySlot = this.slots.find(slot => slot.tile === null && !slot.isLocked);

                if (!emptySlot) {
                    // 槽位已满，游戏结束
                    this.gameOver = true;
                    this.showGameOver();
                    this.isAnimating = false;
                    return;
                }

                // 从网格中移除方块
                this.tileGrid[row][col] = null;

                // 移动方块到槽位，同时缩小以匹配暂存区格子大小
                this.tweens.add({
                    targets: tile,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    scaleX: 0.83, // 从1.2缩小到1.0 (1.0/1.2 ≈ 0.83)
                    scaleY: 0.83,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 将方块放入槽位
                        emptySlot.tile = tile;

                        // 检查是否有三个相同的方块
                        this.checkForMatches();

                        // 执行列向上移动和补充新方块
                        this.moveColumnUp(col);

                        // 检查暂存区是否已满（游戏失败条件）
                        this.checkGameOverCondition();

                        this.isAnimating = false;

                        // 检查胜利条件（目标水果盘完成）
                        // 这里暂时不检查胜利条件，因为胜利逻辑在handlePlateComplete中处理
                    }
                });
            }

            moveColumnUp(col) {
                const gridSize = this.getGridSizeForLevel();
                const rows = gridSize.rows;
                const cols = gridSize.cols;
                const tileSpacing = 102; // 使用与createStackLayout一致的间距
                const startX = this.stackArea.x + (this.stackArea.width - (cols - 1) * tileSpacing) / 2;
                const startY = this.stackArea.y + 20; // 与createStackLayout保持一致

                // 将该列的所有方块向上移动一格
                for (let row = 0; row < rows - 1; row++) {
                    if (this.tileGrid[row + 1][col]) {
                        const tile = this.tileGrid[row + 1][col];
                        this.tileGrid[row][col] = tile;
                        this.tileGrid[row + 1][col] = null;

                        // 更新方块的网格位置数据
                        tile.setData('gridRow', row);

                        // 动画移动到新位置
                        const newY = startY + row * tileSpacing;
                        this.tweens.add({
                            targets: tile,
                            y: newY,
                            duration: 200,
                            ease: 'Power2'
                        });
                    }
                }

                // 在最底部补充新方块（根据关卡限制水果种类）
                const newTileType = this.getRandomFruitTypeForLevel();
                const newX = startX + col * tileSpacing;
                const newY = startY + (rows - 1) * tileSpacing;
                const newTile = this.createGridTile(newX, newY, newTileType, rows - 1, col);
                this.tileGrid[rows - 1][col] = newTile;

                // 新方块从下方滑入的动画
                newTile.y += 100;
                newTile.setAlpha(0);
                this.tweens.add({
                    targets: newTile,
                    y: newY,
                    alpha: 1,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 更新可点击状态
                        this.updateGridClickableStates();
                    }
                });
            }



            checkForMatches() {
                // 统计暂存区每种类型的水果数量
                const typeCounts = {};

                this.slots.forEach(slot => {
                    if (slot.tile) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        typeCounts[typeIndex] = (typeCounts[typeIndex] || 0) + 1;
                    }
                });

                // 更新水果盘中小水果的状态（变暗/变亮）
                this.updateFruitPlateStates(typeCounts);

                // 检查是否有完整的水果盘可以消除
                this.checkCompletePlates(typeCounts);
            }

            updateFruitPlateStates(typeCounts) {
                // 首先重置所有剩余水果盘中小水果的匹配状态
                this.fruitPlates.forEach(plate => {
                    plate.fruits.forEach(fruit => {
                        const wasMatched = fruit.getData('isMatched');
                        fruit.setData('isMatched', false);
                        fruit.setData('matchedSlotIndex', -1);

                        if (wasMatched) {
                            // 如果之前是亮的，现在变暗
                            fruit.setAlpha(0.6);
                            fruit.setData('isDark', true);
                        }
                    });
                });

                // 为每个暂存区的水果找到对应的小水果进行一对一匹配
                this.slots.forEach((slot, slotIndex) => {
                    if (!slot.tile) return;

                    const slotFruitType = slot.tile.getData('typeIndex');

                    // 找到第一个未匹配的、类型相同的小水果
                    let foundMatch = false;
                    for (let plate of this.fruitPlates) {
                        if (foundMatch) continue;

                        for (let fruit of plate.fruits) {
                            if (foundMatch) break;

                            const fruitType = fruit.getData('typeIndex');
                            const isMatched = fruit.getData('isMatched');

                            if (fruitType === slotFruitType && !isMatched) {
                                // 找到匹配，让小水果变亮
                                fruit.setAlpha(1.0);
                                fruit.setData('isDark', false);
                                fruit.setData('isMatched', true);
                                fruit.setData('matchedSlotIndex', slotIndex);
                                foundMatch = true;
                            }
                        }
                    }
                });
            }

            checkCompletePlates(typeCounts) {
                // 检查是否有水果盘的所有小水果都被匹配了（变亮了）
                // 需要从后往前遍历，因为完成的水果盘会从数组中移除
                for (let plateIndex = this.fruitPlates.length - 1; plateIndex >= 0; plateIndex--) {
                    const plate = this.fruitPlates[plateIndex];

                    // 检查这个水果盘是否所有小水果都被匹配了（都变亮了）
                    const allMatched = plate.fruits.every(fruit => fruit.getData('isMatched'));

                    if (allMatched) {
                        // 收集这个水果盘需要的暂存区水果
                        const tilesToRemove = [];
                        plate.fruits.forEach(fruit => {
                            const slotIndex = fruit.getData('matchedSlotIndex');
                            if (slotIndex >= 0 && this.slots[slotIndex].tile) {
                                tilesToRemove.push({
                                    tile: this.slots[slotIndex].tile,
                                    slot: this.slots[slotIndex],
                                    targetFruit: fruit
                                });
                                this.slots[slotIndex].tile = null;
                            }
                        });

                        if (tilesToRemove.length === 4) { // 确保有4个水果
                            this.completeFruitPlate(plateIndex, tilesToRemove);
                        }
                    }
                }
            }

            completeFruitPlate(plateIndex, tilesToRemove) {
                const plate = this.fruitPlates[plateIndex];

                // 播放消除动画
                this.playFruitPlateCompleteAnimation(tilesToRemove, plate);
            }

            playFruitPlateCompleteAnimation(tilesToRemove, plate) {
                // 让水果飞向盘子里对应的小水果位置
                let completedCount = 0;

                tilesToRemove.forEach((item, index) => {
                    const targetFruit = item.targetFruit;

                    // 延迟不同时间让水果飞向对应的小水果位置
                    this.time.delayedCall(index * 100, () => {
                        this.tweens.add({
                            targets: item.tile,
                            x: targetFruit.x,
                            y: targetFruit.y,
                            scaleX: 0.6,
                            scaleY: 0.6,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                // 显示完成特效在小水果位置
                                this.showCompleteAnimation(targetFruit.x, targetFruit.y);

                                // 暂存区水果消失
                                item.tile.destroy();

                                completedCount++;

                                // 如果是最后一个水果，处理完成逻辑
                                if (completedCount === tilesToRemove.length) {
                                    this.time.delayedCall(300, () => {
                                        this.handlePlateComplete(plate);
                                    });
                                }
                            }
                        });
                    });
                });
            }

            handlePlateComplete(plate) {
                // 计算分数
                const points = 100;
                this.score += points;
                this.scoreText.setText(`🪙 ${this.score}`);

                // 显示金币飞行动画
                this.showCoinFlyAnimation(plate.x, plate.y, points);

                this.showScoreAnimation(points);

                // 让完成的水果盘立即消失
                this.tweens.add({
                    targets: [plate.background, ...plate.fruits],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        // 销毁水果盘元素
                        plate.background.destroy();
                        plate.fruits.forEach(fruit => fruit.destroy());

                        // 从水果盘数组中移除这个盘子
                        const plateIndex = this.fruitPlates.indexOf(plate);
                        if (plateIndex > -1) {
                            this.fruitPlates.splice(plateIndex, 1);
                        }

                        // 重新排列剩余水果盘居中
                        this.time.delayedCall(300, () => {
                            this.reorganizeFruitPlates();
                        });
                    }
                });

                // 整理暂存区槽位
                this.reorganizeSlots();

                // 检查是否所有水果盘都完成了
                this.time.delayedCall(600, () => {
                    if (this.fruitPlates.length === 0) {
                        // 所有水果盘都消失了，进入下一关或显示胜利
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                    }
                });
            }

            completeLevel() {
                // 关卡完成处理
                this.level++;
                this.levelText.setText(`${this.level}`);

                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '🤣', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示关卡完成文字
                const levelCompleteText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, `第${this.level - 1}关完成！`, {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                levelCompleteText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建下一关按钮
                const nextButton = this.createNextLevelButton();

                // 启动散花特效
                this.startConfettiEffect();

                // 存储界面元素以便清理
                this.levelCompleteUI = {
                    overlay,
                    bigEmoji,
                    levelCompleteText,
                    scoreText,
                    nextButton
                };
            }

            createNextLevelButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0x4CAF50);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0x45A049);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100,
                    this.level > 10 ? '游戏完成' : '下一关', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onNextLevelClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            disableGameInput() {
                // 禁用游戏输入
                this.gameInputDisabled = true;

                // 禁用所有方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.disableInteractive();
                    });
                }
            }

            enableGameInput() {
                // 启用游戏输入
                this.gameInputDisabled = false;

                // 重新启用方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.setInteractive();
                    });
                }
            }

            onNextLevelClick() {
                // 清理关卡完成界面
                if (this.levelCompleteUI) {
                    Object.values(this.levelCompleteUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.levelCompleteUI = null;
                }

                // 停止散花特效
                this.stopConfettiEffect();

                // 重新启用游戏输入
                this.enableGameInput();

                // 检查是否达到最大关卡
                if (this.level > 10) {
                    this.showVictory();
                } else {
                    // 重新初始化游戏进入下一关
                    this.initializeGame();
                }
            }

            startConfettiEffect() {
                // 创建散花特效
                this.confettiParticles = [];
                this.confettiTimer = this.time.addEvent({
                    delay: 100,
                    callback: this.createConfettiParticle,
                    callbackScope: this,
                    loop: true
                });

                // 3秒后停止生成新的散花
                this.time.delayedCall(3000, () => {
                    if (this.confettiTimer) {
                        this.confettiTimer.destroy();
                        this.confettiTimer = null;
                    }
                });
            }

            createConfettiParticle() {
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0xFECA57, 0xFF9FF3, 0xA8E6CF];
                const shapes = ['●', '★', '♦', '▲'];

                // 随机选择颜色和形状
                const color = colors[Phaser.Math.Between(0, colors.length - 1)];
                const shape = shapes[Phaser.Math.Between(0, shapes.length - 1)];

                // 在屏幕顶部随机位置创建粒子
                const x = Phaser.Math.Between(0, GAME_WIDTH);
                const y = -20;

                const particle = this.add.text(x, y, shape, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: `#${color.toString(16).padStart(6, '0')}`,
                    resolution: 2
                }).setOrigin(0.5);
                particle.setDepth(1003);

                // 随机旋转和缩放
                particle.setRotation(Phaser.Math.Between(0, 360) * Math.PI / 180);
                particle.setScale(Phaser.Math.FloatBetween(0.5, 1.5));

                // 添加下落和旋转动画
                this.tweens.add({
                    targets: particle,
                    y: GAME_HEIGHT + 50,
                    x: x + Phaser.Math.Between(-100, 100),
                    rotation: particle.rotation + Phaser.Math.Between(2, 6) * Math.PI,
                    alpha: 0,
                    duration: Phaser.Math.Between(2000, 4000),
                    ease: 'Power2',
                    onComplete: () => {
                        particle.destroy();
                        // 从数组中移除
                        const index = this.confettiParticles.indexOf(particle);
                        if (index > -1) {
                            this.confettiParticles.splice(index, 1);
                        }
                    }
                });

                this.confettiParticles.push(particle);
            }

            stopConfettiEffect() {
                // 停止生成新的散花
                if (this.confettiTimer) {
                    this.confettiTimer.destroy();
                    this.confettiTimer = null;
                }

                // 清理现有的散花粒子
                if (this.confettiParticles) {
                    this.confettiParticles.forEach(particle => {
                        if (particle && particle.destroy) {
                            particle.destroy();
                        }
                    });
                    this.confettiParticles = [];
                }
            }

            showCoinFlyAnimation(startX, startY, points) {
                // 创建多个金币图标飞向右上角的金币显示位置
                const coinCount = Math.min(3, Math.ceil(points / 50)); // 减少金币数量，根据分数决定，最多3个

                // 获取金币显示文字的准确位置
                const scoreTextBounds = this.scoreText.getBounds();
                const targetX = scoreTextBounds.x + 18; // 金币图标在文字中的大概位置
                const targetY = scoreTextBounds.y + scoreTextBounds.height / 2; // 垂直居中

                for (let i = 0; i < coinCount; i++) {
                    // 减少延迟时间，让金币更快飞出
                    this.time.delayedCall(i * 50, () => {
                        // 创建金币图标
                        const coin = this.add.text(startX, startY, '🪙', {
                            fontSize: '32px',
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        coin.setDepth(1000);

                        // 添加初始的轻微随机偏移
                        const randomOffsetX = Phaser.Math.Between(-15, 15);
                        const randomOffsetY = Phaser.Math.Between(-15, 15);
                        coin.x += randomOffsetX;
                        coin.y += randomOffsetY;

                        // 金币飞向目标位置的动画
                        this.tweens.add({
                            targets: coin,
                            x: targetX,
                            y: targetY,
                            scaleX: 0.9,
                            scaleY: 0.9,
                            duration: 600, // 减少飞行时间
                            ease: 'Power2',
                            onComplete: () => {
                                // 到达目标位置后的闪烁效果
                                this.tweens.add({
                                    targets: coin,
                                    scaleX: 1.3,
                                    scaleY: 1.3,
                                    alpha: 0,
                                    duration: 150,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        coin.destroy();

                                        // 让金币显示文字闪烁一下
                                        if (i === coinCount - 1) { // 最后一个金币到达时
                                            this.tweens.add({
                                                targets: this.scoreText,
                                                scaleX: 1.15,
                                                scaleY: 1.15,
                                                duration: 120,
                                                ease: 'Power2',
                                                yoyo: true,
                                                repeat: 1
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        // 添加旋转动画
                        this.tweens.add({
                            targets: coin,
                            rotation: Math.PI * 1.5, // 减少旋转角度
                            duration: 600,
                            ease: 'Linear'
                        });
                    });
                }
            }

            showCompleteAnimation(x, y) {
                // 显示完成特效文字
                const completeText = this.add.text(x, y - 30, '完成!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#00FF00',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                completeText.setDepth(500);

                this.tweens.add({
                    targets: completeText,
                    y: completeText.y - 30,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        completeText.destroy();
                    }
                });

                // 添加光环特效
                const halo = this.add.graphics();
                halo.lineStyle(4, 0x00FF00, 0.8);
                halo.strokeCircle(x, y, 30);
                halo.setDepth(499);

                this.tweens.add({
                    targets: halo,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        halo.destroy();
                    }
                });
            }









            reorganizeFruitPlates() {
                // 重新排列剩余的水果盘，让它们居中显示
                if (this.fruitPlates.length === 0) return;

                const plateWidth = 140;
                const plateSpacing = 20;
                const remainingCount = this.fruitPlates.length;
                const totalPlatesWidth = remainingCount * plateWidth + (remainingCount - 1) * plateSpacing;
                const startPlateX = (GAME_WIDTH - totalPlatesWidth) / 2;
                const enemyAreaY = 220; // 与createEnemyArea中的位置保持一致

                // 为每个剩余的水果盘计算新位置并添加移动动画
                this.fruitPlates.forEach((plate, index) => {
                    const newPlateX = startPlateX + index * (plateWidth + plateSpacing);
                    const newCenterX = newPlateX + plateWidth / 2;

                    // 重新绘制水果盘背景到新位置
                    plate.background.clear();
                    plate.background.fillStyle(0x8B4513, 0.3);
                    plate.background.fillRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);
                    plate.background.lineStyle(2, 0x8B4513, 0.8);
                    plate.background.strokeRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);

                    // 更新水果盘中心位置
                    plate.x = newCenterX;

                    // 移动水果盘中的所有小水果
                    const smallFruitSize = 50;
                    const fruitSpacing = 10;
                    const startFruitX = newPlateX + (plateWidth - (2 * smallFruitSize + fruitSpacing)) / 2;
                    const startFruitY = enemyAreaY - (smallFruitSize + fruitSpacing / 2);

                    plate.fruits.forEach((fruit, fruitIndex) => {
                        const row = Math.floor(fruitIndex / 2);
                        const col = fruitIndex % 2;
                        const newFruitX = startFruitX + col * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;
                        const newFruitY = startFruitY + row * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;

                        this.tweens.add({
                            targets: fruit,
                            x: newFruitX,
                            y: newFruitY,
                            duration: 400,
                            ease: 'Power2'
                        });
                    });
                });
            }

            areAllTargetsFulfilled() {
                // 检查是否所有水果盘都已消失
                return this.fruitPlates.length === 0;
            }

            calculateStars() {
                // 根据分数计算星星数量
                const baseScore = this.level * 100; // 基础分数
                const scoreRatio = this.score / baseScore;

                if (scoreRatio >= 2.0) {
                    return 3; // 3星：分数达到基础分数的200%
                } else if (scoreRatio >= 1.5) {
                    return 2; // 2星：分数达到基础分数的150%
                } else {
                    return 1; // 1星：完成关卡即可
                }
            }



            checkGameOverCondition() {
                // 检查暂存区是否已满
                const isSlotsFull = this.slots.every(slot => slot.tile !== null);

                if (isSlotsFull) {
                    // 检查是否还有剩余的水果盘
                    if (this.fruitPlates.length === 0) {
                        // 没有水果盘了，关卡完成
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                        return;
                    }

                    // 在一对一匹配的情况下，检查是否有任何剩余水果盘可以完成
                    let canCompleteAnyPlate = false;

                    // 检查每个剩余的水果盘
                    this.fruitPlates.forEach(plate => {
                        // 统计这个水果盘需要的水果类型
                        const requiredTypes = [];
                        plate.fruits.forEach(fruit => {
                            requiredTypes.push(fruit.getData('typeIndex'));
                        });

                        // 统计暂存区的水果类型
                        const availableTypes = [];
                        this.slots.forEach(slot => {
                            if (slot.tile) {
                                availableTypes.push(slot.tile.getData('typeIndex'));
                            }
                        });

                        // 检查是否能一对一匹配完成这个水果盘
                        const tempAvailable = [...availableTypes];
                        let matchCount = 0;

                        requiredTypes.forEach(requiredType => {
                            const index = tempAvailable.indexOf(requiredType);
                            if (index >= 0) {
                                tempAvailable.splice(index, 1); // 移除已匹配的
                                matchCount++;
                            }
                        });

                        if (matchCount === 4) { // 如果能匹配完整个水果盘
                            canCompleteAnyPlate = true;
                        }
                    });

                    // 如果暂存区满了且无法完成任何剩余水果盘，游戏结束
                    if (!canCompleteAnyPlate) {
                        this.gameOver = true;
                        this.time.delayedCall(500, () => {
                            this.showGameOver();
                        });
                    }
                }
            }

            reorganizeSlots() {
                // 收集所有非空的方块
                const remainingTiles = [];
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        remainingTiles.push(slot.tile);
                        slot.tile = null;
                    }
                });

                // 重新排列到左侧
                remainingTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.tile = tile;

                    this.tweens.add({
                        targets: tile,
                        x: slot.x,
                        y: slot.y,
                        duration: 200,
                        ease: 'Power2'
                    });
                });
            }

            showScoreAnimation(points) {
                const scorePopup = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 100, `+${points}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scorePopup.setDepth(500); // 设置较高深度确保分数动画可见

                this.tweens.add({
                    targets: scorePopup,
                    y: scorePopup.y - 50,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scorePopup.destroy();
                    }
                });
            }

            showVictory() {
                // 立即清空暂存区，防止显示残留动物
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 计算关卡评星
                const stars = this.calculateStars();

                // 显示胜利信息
                const victoryBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                victoryBg.setDepth(1000); // 设置最高深度确保在最上层

                const victoryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 180, '恭喜过关！', {
                    fontSize: '90px', // 150% (48 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                victoryText.setDepth(1001); // 设置最高深度确保在最上层

                // 显示星星评级
                this.showStarRating(stars);

                const nextButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, 240, 72, 0x27AE60); // 120%按钮
                nextButton.setStrokeStyle(3, 0x2ECC71);
                nextButton.setInteractive();
                nextButton.setDepth(1001); // 设置最高深度确保在最上层

                const nextText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, '下一关', {
                    fontSize: '39px', // 150% (26 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                nextText.setDepth(1002); // 设置最高深度确保在最上层

                // 在UI界面上添加撒花庆祝特效
                this.showConfettiCelebration();

                nextButton.on('pointerdown', () => {
                    victoryBg.destroy();
                    victoryText.destroy();
                    nextButton.destroy();
                    nextText.destroy();
                    this.nextLevel();
                });
            }

            showConfettiCelebration() {
                // 创建撒花庆祝特效，2次爆炸效果，共2秒
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0xA8E6CF, 0xFD79A8];
                const centerX = GAME_WIDTH / 2;
                const centerY = GAME_HEIGHT / 2 - 80; // "恭喜过关"字上方位置
                const explosionCount = 2; // 2次爆炸
                const explosionInterval = 1000; // 1秒间隔（0秒和1秒时爆炸）

                for (let explosion = 0; explosion < explosionCount; explosion++) {
                    this.time.delayedCall(explosion * explosionInterval, () => {
                        // 每次爆炸创建多个花瓣
                        const confettiPerExplosion = 25; // 增加每次爆炸的花瓣数量
                        for (let i = 0; i < confettiPerExplosion; i++) {
                            this.createConfettiExplosion(centerX, centerY, colors);
                        }
                    });
                }
            }

            createConfettiExplosion(centerX, centerY, colors) {
                // 随机选择颜色
                const color = colors[Math.floor(Math.random() * colors.length)];

                // 创建花瓣形状（更大的尺寸）
                const confetti = this.add.graphics();
                confetti.fillStyle(color);

                // 随机选择花瓣形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形花瓣
                    confetti.fillCircle(0, 0, Math.random() * 8 + 6);
                } else if (shapeType === 1) {
                    // 方形花瓣
                    const size = Math.random() * 12 + 8;
                    confetti.fillRect(-size/2, -size/2, size, size);
                } else {
                    // 三角形花瓣
                    const size = Math.random() * 10 + 8;
                    confetti.fillTriangle(0, -size, -size/2, size/2, size/2, size/2);
                }

                // 从中心点开始
                confetti.x = centerX;
                confetti.y = centerY;
                confetti.setDepth(1003); // 确保在UI界面之上

                // 随机的爆炸方向和距离
                const angle = Math.random() * Math.PI * 2; // 随机角度
                const explosionRadius = Math.random() * 150 + 100; // 爆炸半径
                const explosionX = centerX + Math.cos(angle) * explosionRadius;
                const explosionY = centerY + Math.sin(angle) * explosionRadius;

                // 第一阶段：爆炸扩散
                this.tweens.add({
                    targets: confetti,
                    x: explosionX,
                    y: explosionY,
                    rotation: (Math.random() - 0.5) * 360 * Math.PI / 180,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段：重力下落
                        const fallDistance = GAME_HEIGHT - explosionY + 50;
                        const horizontalDrift = (Math.random() - 0.5) * 100;

                        this.tweens.add({
                            targets: confetti,
                            x: explosionX + horizontalDrift,
                            y: explosionY + fallDistance,
                            rotation: confetti.rotation + (Math.random() - 0.5) * 720 * Math.PI / 180,
                            alpha: 0,
                            duration: 1300, // 调整为1300ms，总时长1.5秒
                            ease: 'Power1',
                            onComplete: () => {
                                confetti.destroy();
                            }
                        });
                    }
                });

                // 添加轻微的摆动效果
                this.tweens.add({
                    targets: confetti,
                    scaleX: 0.8,
                    scaleY: 1.2,
                    duration: 300 + Math.random() * 100,
                    yoyo: true,
                    repeat: 2, // 重复2次，总共约1.2秒
                    ease: 'Sine.easeInOut'
                });
            }

            showStarRating(stars) {
                // 显示星星评级
                const starY = GAME_HEIGHT/2 - 80;
                const starSize = 60;
                const starSpacing = 80;
                const totalWidth = 3 * starSize + 2 * starSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2 + starSize / 2;

                // 创建3个星星位置
                for (let i = 0; i < 3; i++) {
                    const starX = startX + i * (starSize + starSpacing);
                    const isLit = i < stars; // 是否点亮这颗星星

                    // 延迟显示每颗星星
                    this.time.delayedCall(500 + i * 300, () => {
                        this.createAnimatedStar(starX, starY, starSize, isLit);
                    });
                }

                // 显示评级文字
                this.time.delayedCall(1400, () => {
                    let ratingText = '';
                    let ratingColor = '';

                    switch(stars) {
                        case 3:
                            ratingText = '完美通关！';
                            ratingColor = '#FFD700';
                            break;
                        case 2:
                            ratingText = '表现优秀！';
                            ratingColor = '#C0C0C0';
                            break;
                        case 1:
                            ratingText = '成功通关！';
                            ratingColor = '#CD7F32';
                            break;
                    }

                    const rating = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 10, ratingText, {
                        fontSize: '36px',
                        fontFamily: 'Arial, sans-serif',
                        color: ratingColor,
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);
                    rating.setDepth(1003);

                    // 评级文字出现动画
                    rating.setScale(0);
                    this.tweens.add({
                        targets: rating,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 400,
                        ease: 'Back.easeOut'
                    });
                });
            }

            createAnimatedStar(x, y, size, isLit) {
                // 创建星星图形
                const star = this.add.graphics();
                star.setDepth(1003);

                // 设置星星颜色
                const fillColor = isLit ? 0xFFD700 : 0x666666; // 金色或灰色
                const strokeColor = isLit ? 0xFFA500 : 0x444444; // 橙色或深灰色

                star.fillStyle(fillColor);
                star.lineStyle(3, strokeColor);

                // 绘制五角星
                const points = [];
                const outerRadius = size / 2;
                const innerRadius = outerRadius * 0.4;

                for (let i = 0; i < 10; i++) {
                    const angle = (i * Math.PI) / 5;
                    const radius = i % 2 === 0 ? outerRadius : innerRadius;
                    const px = x + Math.cos(angle - Math.PI / 2) * radius;
                    const py = y + Math.sin(angle - Math.PI / 2) * radius;
                    points.push(px, py);
                }

                star.fillPoints(points);
                star.strokePoints(points);

                // 星星出现动画
                star.setScale(0);
                star.setRotation(0);

                this.tweens.add({
                    targets: star,
                    scaleX: 1,
                    scaleY: 1,
                    rotation: Math.PI * 2,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 如果是点亮的星星，添加闪烁效果
                if (isLit) {
                    this.time.delayedCall(600, () => {
                        this.tweens.add({
                            targets: star,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 200,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 添加光芒效果
                        const glow = this.add.graphics();
                        glow.setDepth(1002);
                        glow.fillStyle(0xFFD700, 0.3);
                        glow.fillCircle(x, y, size);
                        glow.setScale(0);

                        this.tweens.add({
                            targets: glow,
                            scaleX: 1.5,
                            scaleY: 1.5,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });
                    });
                }
            }

            showGameOver() {
                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '😭', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示游戏结束文字
                const gameOverText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, '游戏结束！', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#E74C3C',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                gameOverText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建重新开始按钮
                const retryButton = this.createRetryButton();

                // 存储界面元素以便清理
                this.gameOverUI = {
                    overlay,
                    bigEmoji,
                    gameOverText,
                    scoreText,
                    retryButton
                };
            }

            createRetryButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0xE74C3C);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0xC0392B);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100, '重新开始', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onRetryClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            onRetryClick() {
                // 清理游戏结束界面
                if (this.gameOverUI) {
                    Object.values(this.gameOverUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.gameOverUI = null;
                }

                // 重新启用游戏输入
                this.enableGameInput();

                // 重新开始游戏
                this.restartGame();
            }

            nextLevel() {
                this.level++;
                this.levelText.setText(`${this.level}`);
                this.gameOver = false;
                this.isAnimating = false; // 重置动画状态

                // 强制清空暂存区（确保清空）
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }



            restartGame() {
                this.score = 0;
                this.level = 1;
                this.scoreText.setText('🪙 0');
                this.levelText.setText('1');
                this.gameOver = false;
                this.isAnimating = false;

                // 清空暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 清空敌人区域
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#2C3E50',
            scene: GameScene,
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false,
                transparent: false,
                clearBeforeRender: true,
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
                powerPreference: "high-performance"
            },
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>

