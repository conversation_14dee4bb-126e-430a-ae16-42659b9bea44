<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍉 合成大西瓜</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.85.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #2c3e50;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            touch-action: manipulation;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            max-width: 100vw;
            max-height: 100vh;
        }

        .loading {
            color: white;
            font-size: 24px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="gameCanvas">
        <div class="loading">游戏加载中...</div>
    </div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const CONTAINER_WIDTH = 600;
        const CONTAINER_HEIGHT = 700; // 减少水果区域高度
        const WALL_THICKNESS = 15;
        const UI_HEIGHT = 200;
        const STORAGE_HEIGHT = 100; // 暂存区高度
        const STORAGE_SLOTS = 7; // 暂存区格子数量

        // 水果配置
        const FRUITS = [
            { emoji: '🍒', size: 32, points: 1, color: 0xff6b6b },
            { emoji: '🍓', size: 40, points: 3, color: 0xff8e8e },
            { emoji: '🍇', size: 48, points: 6, color: 0x9b59b6 },
            { emoji: '🍊', size: 56, points: 10, color: 0xf39c12 },
            { emoji: '🍋', size: 64, points: 15, color: 0xf1c40f },
            { emoji: '🍎', size: 72, points: 21, color: 0xe74c3c },
            { emoji: '🍑', size: 80, points: 28, color: 0xfd79a8 },
            { emoji: '🥭', size: 88, points: 36, color: 0xfdcb6e },
            { emoji: '🍍', size: 96, points: 45, color: 0xe17055 },
            { emoji: '🥥', size: 104, points: 55, color: 0x8b4513 },
            { emoji: '🍉', size: 112, points: 66, color: 0x00b894 }
        ];

        let game;
        let score = 0;
        let highScore = 0;

        // 使用内存存储而不是sessionStorage
        try {
            highScore = parseInt(localStorage.getItem('watermelonHighScore') || '0');
        } catch (e) {
            highScore = 0;
        }

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.fruits = [];
                this.currentFruit = null;
                this.nextFruitType = 0;
                this.dropLine = null;
                this.gameOver = false;
                this.merging = new Set();
                this.containerBounds = null;
                this.storageSlots = []; // 暂存区格子
                this.autoDropTimer = null; // 自动下落计时器
                this.isFirstWave = true; // 标记是否是第一波
            }

            create() {
                this.createBackground();
                this.createUI();

                // 参考demo的简洁设置
                this.matter.world.setBounds();

                this.createContainer();
                this.createStorageArea();
                this.createDropLine();
                this.setupAutoDropSystem();
                this.setupClickControl();
                this.matter.world.on('collisionstart', this.handleCollision, this);
            }

            createBackground() {
                const graphics = this.add.graphics();
                graphics.fillGradientStyle(0x667eea, 0x667eea, 0x764ba2, 0x764ba2, 1);
                graphics.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

                graphics.fillStyle(0x2c3e50, 0.9);
                graphics.fillRoundedRect(20, 20, GAME_WIDTH - 40, UI_HEIGHT - 40, 25);

                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;
                graphics.fillStyle(0x87CEEB, 0.3);
                graphics.fillRoundedRect(containerX - 30, containerY - 30, CONTAINER_WIDTH + 60, CONTAINER_HEIGHT + 60, 30);
            }

            createUI() {
                this.add.text(GAME_WIDTH / 2, 70, '🍉 合成大西瓜 🍉', {
                    fontSize: '48px',
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.scoreText = this.add.text(60, 110, `得分: ${score}`, {
                    fontSize: '32px',
                    fill: '#4ecdc4',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                });

                this.highScoreText = this.add.text(GAME_WIDTH - 60, 110, `最高分: ${highScore}`, {
                    fontSize: '32px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(1, 0);

                this.add.text(GAME_WIDTH / 2, 150, '自动下落中...', {
                    fontSize: '24px',
                    fill: '#bdc3c7',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH / 2, GAME_HEIGHT - 40, '点击水果收集到暂存区 | 3个相同消除', {
                    fontSize: '24px',
                    fill: '#95a5a6',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }

            createContainer() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;

                this.containerBounds = {
                    left: containerX,
                    right: containerX + CONTAINER_WIDTH,
                    top: containerY,
                    bottom: containerY + CONTAINER_HEIGHT
                };

                const wallOptions = { 
                    isStatic: true, 
                    label: 'wall',
                    friction: 0.5,
                    restitution: 0.3
                };

                this.matter.add.rectangle(
                    containerX - WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH + WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH/2, 
                    containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2, 
                    CONTAINER_WIDTH + WALL_THICKNESS*2, 
                    WALL_THICKNESS, 
                    wallOptions
                );

                const graphics = this.add.graphics();
                graphics.lineStyle(6, 0x4ecdc4, 0.8);
                graphics.strokeRoundedRect(containerX - 3, containerY - 3, CONTAINER_WIDTH + 6, CONTAINER_HEIGHT + 6, 15);
            }

            createStorageArea() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const storageY = UI_HEIGHT + 50 + CONTAINER_HEIGHT + 50;
                const slotWidth = CONTAINER_WIDTH / STORAGE_SLOTS;

                // 初始化暂存区数组
                this.storageSlots = new Array(STORAGE_SLOTS).fill(null);

                // 绘制暂存区背景
                const graphics = this.add.graphics();
                graphics.fillStyle(0x34495e, 0.8);
                graphics.fillRoundedRect(containerX, storageY, CONTAINER_WIDTH, STORAGE_HEIGHT, 10);

                // 绘制格子分隔线
                graphics.lineStyle(2, 0x7f8c8d, 0.6);
                for (let i = 1; i < STORAGE_SLOTS; i++) {
                    const x = containerX + i * slotWidth;
                    graphics.moveTo(x, storageY);
                    graphics.lineTo(x, storageY + STORAGE_HEIGHT);
                }
                graphics.strokePath();

               
                // 存储暂存区位置信息
                this.storageArea = {
                    x: containerX,
                    y: storageY,
                    width: CONTAINER_WIDTH,
                    height: STORAGE_HEIGHT,
                    slotWidth: slotWidth
                };
            }

            createDropLine() {
                // 移除警戒线，因为不再判断失败
                // 游戏现在是无限模式，玩家通过收集管理游戏区域
            }

            setupAutoDropSystem() {
                // 第一波投放10个水果
                if (this.isFirstWave) {
                    this.dropFirstWave();
                } else {
                    // 启动自动下落系统
                    this.startAutoDropTimer();
                }
            }

            dropFirstWave() {
                // 第一波投放10个水果
                for (let i = 0; i < 10; i++) {
                    this.time.delayedCall(i * 200, () => {
                        this.dropRandomFruit();
                        
                        // 最后一个水果投放后，开始正常的自动下落
                        if (i === 9) {
                            this.isFirstWave = false;
                            this.time.delayedCall(2000, () => {
                                this.startAutoDropTimer();
                            });
                        }
                    });
                }
            }

            startAutoDropTimer() {
                // 清除现有计时器
                if (this.autoDropTimer) {
                    this.autoDropTimer.destroy();
                }

                // 创建2秒间隔的自动下落计时器
                this.autoDropTimer = this.time.addEvent({
                    delay: 2000, // 2秒
                    callback: this.dropRandomFruit,
                    callbackScope: this,
                    loop: true
                });
            }

            dropRandomFruit() {
                if (this.gameOver) return;

                const fruitType = Phaser.Math.Between(0, 4);
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;

                // 随机X位置
                const fruitConfig = FRUITS[fruitType];
                const randomX = Phaser.Math.Between(
                    containerX + fruitConfig.size,
                    containerX + CONTAINER_WIDTH - fruitConfig.size
                );
                const startY = UI_HEIGHT + 80;

                // 创建圆形水果
                const fruit = this.add.circle(randomX, startY, fruitConfig.size, fruitConfig.color);

                // 添加物理体（直接设为动态，立即下落）
                this.matter.add.gameObject(fruit, {
                    shape: 'circle',
                    radius: fruitConfig.size,
                    isStatic: false
                });

                // 添加emoji文本
                const emojiText = this.add.text(randomX, startY, fruitConfig.emoji, {
                    fontSize: `${fruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                fruit.emojiText = emojiText;
                fruit.fruitType = fruitType;

                // 设置水果为可点击 - 使用通用的水果点击设置方法
                this.setupFruitClick(fruit);

                this.fruits.push(fruit);
            }

            // 新增：统一的水果点击设置方法
            setupFruitClick(fruit) {
                fruit.setInteractive();
                fruit.on('pointerdown', () => {
                    this.collectFruit(fruit);
                });
            }

            collectFruit(fruit) {
                // 找到第一个空的暂存区位置
                const emptySlotIndex = this.storageSlots.findIndex(slot => slot === null);

                if (emptySlotIndex === -1) {
                    // 暂存区已满，游戏结束
                    this.gameOver = true;
                    this.time.delayedCall(500, () => {
                        this.showGameOver();
                    });
                    return;
                }

                // 计算暂存区位置
                const slotX = this.storageArea.x + (emptySlotIndex + 0.5) * this.storageArea.slotWidth;
                const slotY = this.storageArea.y + this.storageArea.height / 2;

                // 创建收集特效
                this.createCollectEffect(fruit.x, fruit.y, slotX, slotY);

                // 将水果信息存储到暂存区
                this.storageSlots[emptySlotIndex] = {
                    fruitType: fruit.fruitType,
                    emoji: FRUITS[fruit.fruitType].emoji,
                    displayObject: null
                };

                // 移除原水果
                this.removeFruit(fruit);

                // 在暂存区显示水果
                this.time.delayedCall(300, () => {
                    this.displayFruitInStorage(emptySlotIndex);
                    this.checkForMatches();
                });
            }

            createCollectEffect(fromX, fromY, toX, toY) {
                // 创建移动特效
                const effect = this.add.text(fromX, fromY, '✨', {
                    fontSize: '32px'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: effect,
                    x: toX,
                    y: toY,
                    scaleX: 0.5,
                    scaleY: 0.5,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        effect.destroy();
                    }
                });
            }

            displayFruitInStorage(slotIndex) {
                const slotData = this.storageSlots[slotIndex];
                if (!slotData) return;

                const slotX = this.storageArea.x + (slotIndex + 0.5) * this.storageArea.slotWidth;
                const slotY = this.storageArea.y + this.storageArea.height / 2;

                // 创建暂存区中的水果显示
                const displayObject = this.add.text(slotX, slotY, slotData.emoji, {
                    fontSize: '48px'
                }).setOrigin(0.5);

                slotData.displayObject = displayObject;
            }

            checkForMatches() {
                // 统计每种水果的数量和位置
                const fruitCounts = {};
                const fruitPositions = {};

                this.storageSlots.forEach((slot, index) => {
                    if (slot !== null) {
                        const type = slot.fruitType;
                        if (!fruitCounts[type]) {
                            fruitCounts[type] = 0;
                            fruitPositions[type] = [];
                        }
                        fruitCounts[type]++;
                        fruitPositions[type].push(index);
                    }
                });

                // 检查是否有3个或更多相同的水果
                for (const [fruitType, count] of Object.entries(fruitCounts)) {
                    if (count >= 3) {
                        this.eliminateFruits(parseInt(fruitType), fruitPositions[fruitType]);
                        break; // 一次只处理一种水果的消除
                    }
                }
            }

            eliminateFruits(fruitType, positions) {
                // 取前3个位置进行消除
                const eliminatePositions = positions.slice(0, 3);

                // 创建消除特效
                eliminatePositions.forEach(pos => {
                    const slot = this.storageSlots[pos];
                    if (slot && slot.displayObject) {
                        // 消除特效
                        this.tweens.add({
                            targets: slot.displayObject,
                            scaleX: 2,
                            scaleY: 2,
                            alpha: 0,
                            duration: 500,
                            ease: 'Power2',
                            onComplete: () => {
                                slot.displayObject.destroy();
                            }
                        });
                    }
                });

                // 延迟清空位置并重新排列
                this.time.delayedCall(500, () => {
                    // 清空消除的位置
                    eliminatePositions.forEach(pos => {
                        this.storageSlots[pos] = null;
                    });

                    // 重新排列暂存区（带动画）
                    this.rearrangeStorageWithAnimation();

                    // 计算得分 - 增加暂存区消除积分
                    const points = FRUITS[fruitType].points * 3 + 50; // 基础分数 + 消除奖励
                    this.updateScore(points);

                    // 显示得分特效
                    const scoreEffect = this.add.text(GAME_WIDTH / 2, this.storageArea.y - 50, `+${points}`, {
                        fontSize: '36px',
                        fill: '#f39c12',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);

                    // 添加得分特效动画
                    this.tweens.add({
                        targets: scoreEffect,
                        y: this.storageArea.y - 100,
                        alpha: 0,
                        scaleX: 1.5,
                        scaleY: 1.5,
                        duration: 1000,
                        ease: 'Power2',
                        onComplete: () => {
                            scoreEffect.destroy();
                        }
                    });
                });
            }

            rearrangeStorageWithAnimation() {
                // 收集所有非空的水果数据
                const remainingFruits = [];
                this.storageSlots.forEach((slot, index) => {
                    if (slot !== null) {
                        remainingFruits.push({
                            data: slot,
                            currentIndex: index
                        });
                    }
                });

                // 清除所有显示对象
                this.storageSlots.forEach(slot => {
                    if (slot && slot.displayObject) {
                        slot.displayObject.destroy();
                        slot.displayObject = null;
                    }
                });

                // 重置暂存区数组
                this.storageSlots = new Array(STORAGE_SLOTS).fill(null);

                // 重新排列并添加动画
                remainingFruits.forEach((fruitInfo, newIndex) => {
                    // 更新暂存区数据
                    this.storageSlots[newIndex] = fruitInfo.data;

                    // 计算新位置
                    const newSlotX = this.storageArea.x + (newIndex + 0.5) * this.storageArea.slotWidth;
                    const newSlotY = this.storageArea.y + this.storageArea.height / 2;

                    // 创建新的显示对象
                    const displayObject = this.add.text(newSlotX, newSlotY + 50, fruitInfo.data.emoji, {
                        fontSize: '48px',
                        alpha: 0
                    }).setOrigin(0.5);

                    // 添加进入动画
                    this.tweens.add({
                        targets: displayObject,
                        y: newSlotY,
                        alpha: 1,
                        duration: 300,
                        delay: newIndex * 50, // 错开动画时间
                        ease: 'Back.easeOut'
                    });

                    // 更新显示对象引用
                    this.storageSlots[newIndex].displayObject = displayObject;
                });
            }

            setupClickControl() {
                // 简化的点击控制，主要用于点击水果收集
                // 水果的点击事件在setupFruitClick中设置
            }

            handleCollision(event) {
                const pairs = event.pairs;

                for (let i = 0; i < pairs.length; i++) {
                    const pair = pairs[i];
                    const fruitA = pair.bodyA.gameObject;
                    const fruitB = pair.bodyB.gameObject;

                    // 检查是否是两个水果碰撞
                    if (fruitA && fruitB &&
                        typeof fruitA.fruitType === 'number' &&
                        typeof fruitB.fruitType === 'number') {

                        // 相同类型的水果才能合成
                        if (fruitA.fruitType === fruitB.fruitType &&
                            fruitA.fruitType < FRUITS.length - 1 &&
                            !this.merging.has(fruitA) &&
                            !this.merging.has(fruitB)) {

                            // 标记为正在合成，防止重复
                            this.merging.add(fruitA);
                            this.merging.add(fruitB);

                            // 延迟合成，避免物理引擎冲突
                            this.time.delayedCall(50, () => {
                                if (fruitA.active && fruitB.active) {
                                    this.mergeFruits(fruitA, fruitB, fruitA.fruitType);
                                }
                            });
                        }
                    }
                }
            }

            mergeFruits(fruitA, fruitB, fruitType) {
                // 计算合成位置
                const mergeX = (fruitA.x + fruitB.x) / 2;
                const mergeY = (fruitA.y + fruitB.y) / 2;

                // 移除旧水果
                this.removeFruit(fruitA);
                this.removeFruit(fruitB);

                // 创建新水果
                const newFruitType = fruitType + 1;
                if (newFruitType >= FRUITS.length) return; // 防止超出数组范围

                const newFruitConfig = FRUITS[newFruitType];
                const newFruit = this.add.circle(mergeX, mergeY, newFruitConfig.size, newFruitConfig.color);

                // 添加物理体
                this.matter.add.gameObject(newFruit, {
                    shape: 'circle',
                    radius: newFruitConfig.size,
                    isStatic: false
                });

                // 添加emoji文本
                const emojiText = this.add.text(mergeX, mergeY, newFruitConfig.emoji, {
                    fontSize: `${newFruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                newFruit.emojiText = emojiText;
                newFruit.fruitType = newFruitType;

                // 关键修复：为合成后的新水果设置点击事件
                this.setupFruitClick(newFruit);

                this.fruits.push(newFruit);

                // 计算并添加积分
                const points = newFruitConfig.points;
                this.updateScore(points);

                // 添加合成特效
                this.createMergeEffect(mergeX, mergeY, newFruitConfig.emoji, points);
            }

            createMergeEffect(x, y, emoji, points = 0) {
                // 创建合成特效文字
                const effect = this.add.text(x, y, emoji, {
                    fontSize: '60px'
                }).setOrigin(0.5);

                // 主要动画效果
                this.tweens.add({
                    targets: effect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    y: y - 80,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        effect.destroy();
                    }
                });

                // 创建积分显示
                if (points > 0) {
                    const scoreEffect = this.add.text(x, y + 60, `+${points}`, {
                        fontSize: '32px',
                        fill: '#f39c12',
                        fontStyle: 'bold',
                        stroke: '#ffffff',
                        strokeThickness: 2
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: scoreEffect,
                        y: y - 20,
                        alpha: 0,
                        scaleX: 1.5,
                        scaleY: 1.5,
                        duration: 1200,
                        ease: 'Power2',
                        onComplete: () => {
                            scoreEffect.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(x, y, 30);

                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });

                // 创建星星粒子效果
                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const star = this.add.text(x, y, '✨', {
                        fontSize: '24px'
                    }).setOrigin(0.5);

                    const targetX = x + Math.cos(angle) * 100;
                    const targetY = y + Math.sin(angle) * 100;

                    this.tweens.add({
                        targets: star,
                        x: targetX,
                        y: targetY,
                        alpha: 0,
                        duration: 800,
                        ease: 'Power2',
                        onComplete: () => {
                            star.destroy();
                        }
                    });
                }
            }

            removeFruit(fruit) {
                // 从合成标记中移除
                this.merging.delete(fruit);

                // 从水果数组中移除
                const index = this.fruits.indexOf(fruit);
                if (index > -1) {
                    this.fruits.splice(index, 1);
                }

                // 销毁emoji文本
                if (fruit.emojiText) {
                    fruit.emojiText.destroy();
                }

                // 销毁水果对象
                fruit.destroy();
            }

            updateScore(points) {
                score += points;
                this.scoreText.setText(`得分: ${score}`);

                if (score > highScore) {
                    highScore = score;
                    try {
                        localStorage.setItem('watermelonHighScore', highScore.toString());
                    } catch (e) {
                        console.log('无法保存最高分');
                    }
                    this.highScoreText.setText(`最高分: ${highScore}`);
                }
            }

            checkGameOver() {
                // 移除警戒线失败判断，游戏可以无限进行
                // 玩家可以通过收集水果到暂存区来管理游戏区域
            }

            showGameOver() {
                const overlay = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.8);
                overlay.setInteractive();

                const panel = this.add.graphics();
                panel.fillStyle(0x2c3e50, 0.95);
                panel.fillRoundedRect(GAME_WIDTH/2 - 250, GAME_HEIGHT/2 - 200, 500, 400, 30);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 120, '🎮 游戏结束 🎮', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 40, `最终得分: ${score}`, {
                    fontSize: '36px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const restartButton = this.add.graphics();
                restartButton.fillStyle(0x4ecdc4, 0.8);
                restartButton.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60, 30);

                const restartText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 110, '🔄 重新开始', {
                    fontSize: '28px',
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const buttonBounds = new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60);
                restartButton.setInteractive(buttonBounds, Phaser.Geom.Rectangle.Contains);

                restartButton.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });

                overlay.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });
            }

            update() {
                if (this.gameOver) return;

                // 过滤无效水果
                this.fruits = this.fruits.filter(fruit => fruit && fruit.active);

                // 同步emoji文本位置
                this.fruits.forEach(fruit => {
                    if (fruit && fruit.emojiText && fruit.body && fruit.body.position) {
                        fruit.emojiText.x = fruit.body.position.x;
                        fruit.emojiText.y = fruit.body.position.y;
                    }
                });

                // 检查游戏结束
                this.checkGameOver();
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'gameCanvas',
            backgroundColor: '#2c3e50',
            physics: {
                default: 'matter',
                matter: {
                    debug: false
                }
            },
            scene: GameScene,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            input: {
                touch: {
                    capture: true
                },
                activePointers: 1
            }
        };

        function startGame() {
            console.log('开始初始化游戏...');
            
            if (typeof Phaser === 'undefined') {
                console.error('Phaser没有加载成功');
                document.getElementById('gameCanvas').innerHTML = '<div class="loading">游戏加载失败，请刷新重试</div>';
                return;
            }
            
            console.log('Phaser版本:', Phaser.VERSION);
            
            document.getElementById('gameCanvas').innerHTML = '';
            game = new Phaser.Game(config);
            console.log('游戏已创建');
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startGame);
        } else {
            startGame();
        }

        window.addEventListener('load', () => {
            if (!game) {
                startGame();
            }
        });

    </script>
</body>
</html>
