<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sort Cubes - 方块排序</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 横屏优化
    const config = {
        type: Phaser.AUTO,
        width: 1400,
        height: 800,
        backgroundColor: '#1a237e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let columns = [];
    let selectedColumn = null;
    let timer = 60; // 增加时间适应横屏游戏
    let timerText;
    let titleText;
    let gameStarted = false;
    let gameWon = false;
    let gameScene = null;

    const COLUMN_CAPACITY = 8; // 每列最多8个方块
    const CUBE_SIZE = 70; // 稍微缩小方块
    const COLUMN_WIDTH = 120; // 增加列宽
    const COLUMN_HEIGHT = COLUMN_CAPACITY * CUBE_SIZE + 40;

    // 定义颜色
    const COLORS = {
        yellow: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
        purple: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
        blue: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
        green: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
        red: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
        orange: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 }
    };
    
    const COLOR_NAMES = Object.keys(COLORS);

    function preload() {
        // 创建方块纹理
        COLOR_NAMES.forEach(colorName => {
            const color = COLORS[colorName];
            const graphics = this.add.graphics();
            
            // 绘制3D效果的方块
            // 主体
            graphics.fillStyle(color.main);
            graphics.fillRoundedRect(0, 0, CUBE_SIZE, CUBE_SIZE, 8);
            
            // 阴影效果
            graphics.fillStyle(color.shadow);
            graphics.fillRoundedRect(4, 4, CUBE_SIZE-8, CUBE_SIZE-8, 6);
            
            // 高光效果
            graphics.fillStyle(color.highlight);
            graphics.fillRoundedRect(8, 8, CUBE_SIZE-16, CUBE_SIZE-16, 4);
            
            // 内部图案
            graphics.fillStyle(color.main);
            graphics.fillRoundedRect(12, 12, CUBE_SIZE-24, CUBE_SIZE-24, 3);
            
            graphics.generateTexture(`cube_${colorName}`, CUBE_SIZE, CUBE_SIZE);
            graphics.destroy();
        });
    }

    function create() {
        gameScene = this;

        // 背景渐变 - 横屏优化
        const bg = this.add.graphics();
        bg.fillGradientStyle(0x1a237e, 0x3949ab, 0x1a237e, 0x3949ab);
        bg.fillRect(0, 0, config.width, config.height);

        // 标题 - 左上角位置
        titleText = this.add.text(120, 60, 'SORT CUBES', {
            fontSize: '42px',
            fontWeight: 'bold',
            fill: '#ffffff',
            align: 'left',
            stroke: '#000000',
            strokeThickness: 4
        }).setOrigin(0, 0.5);

        // 游戏说明
        const instructionText = this.add.text(120, 100, 'Click columns to move cubes of the same color together', {
            fontSize: '18px',
            fontWeight: 'normal',
            fill: '#e3f2fd',
            alpha: 0.8
        }).setOrigin(0, 0.5);

        // 计时器背景 - 右上角位置
        const timerBg = this.add.graphics();
        timerBg.fillStyle(0xffffff, 0.9);
        timerBg.fillRoundedRect(config.width - 200, 30, 160, 60, 15);
        timerBg.lineStyle(3, 0x333333);
        timerBg.strokeRoundedRect(config.width - 200, 30, 160, 60, 15);

        // 计时器标签
        this.add.text(config.width - 120, 45, 'TIME', {
            fontSize: '14px',
            fontWeight: 'bold',
            fill: '#666666'
        }).setOrigin(0.5);

        // 计时器文本
        timerText = this.add.text(config.width - 120, 70, timer.toString(), {
            fontSize: '28px',
            fontWeight: 'bold',
            fill: '#333333'
        }).setOrigin(0.5);

        // 进度条背景
        const progressBg = this.add.graphics();
        progressBg.fillStyle(0x37474f, 0.8);
        progressBg.fillRoundedRect(config.width/2 - 200, config.height - 50, 400, 20, 10);

        // 进度条
        this.progressBar = this.add.graphics();

        // 初始化关卡
        initializeLevel();

        // 初始化进度条
        updateProgressBar();

        // 输入处理
        this.input.on('pointerdown', function(pointer) {
            handleInput(pointer);
        });

        // 启动计时器
        this.time.addEvent({
            delay: 1000,
            callback: function() {
                updateTimer();
            },
            loop: true
        });
    }

    function initializeLevel() {
        // 清除现有列
        columns.forEach(col => col.destroy());
        columns = [];

        const numColumns = 9; // 增加列数适应横屏
        const numColors = 5; // 增加颜色种类
        const cubesPerColor = 8; // 每种颜色8个方块
        const emptyColumns = 2;

        // 生成关卡数据
        const levelData = generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns);

        // 计算列的位置 - 横屏布局优化
        const totalWidth = numColumns * COLUMN_WIDTH;
        const startX = (config.width - totalWidth) / 2 + COLUMN_WIDTH/2;
        const columnY = config.height - 120; // 调整Y位置

        levelData.forEach((cubes, index) => {
            const column = new CubeColumn(gameScene, startX + (index * COLUMN_WIDTH), columnY, COLUMN_CAPACITY, cubes);
            columns.push(column);
        });
    }

    function generateRandomLevel(numColumns, numColors, cubesPerColor, emptyColumns) {
        let allCubes = [];
        const availableColors = Phaser.Utils.Array.Shuffle(COLOR_NAMES).slice(0, numColors);

        // 生成所有方块
        availableColors.forEach(color => {
            for (let i = 0; i < cubesPerColor; i++) {
                allCubes.push(color);
            }
        });

        // 确保参数合理性
        const filledColumns = numColumns - emptyColumns;
        const totalCubes = allCubes.length;

        // 检查是否能合理分配
        if (filledColumns <= 0) {
            console.warn("没有足够的非空列，调整为至少1个非空列");
            emptyColumns = numColumns - 1;
        }

        const maxCubesPerColumn = COLUMN_CAPACITY;
        if (totalCubes > filledColumns * maxCubesPerColumn) {
            console.warn("方块总数超过容量，减少方块数量");
            // 重新计算合理的方块数量
            const reasonableCubesPerColor = Math.floor((filledColumns * maxCubesPerColumn) / numColors);
            allCubes = [];
            availableColors.forEach(color => {
                for (let i = 0; i < reasonableCubesPerColor; i++) {
                    allCubes.push(color);
                }
            });
        }

        Phaser.Utils.Array.Shuffle(allCubes);

        const level = [];
        const actualFilledColumns = numColumns - emptyColumns;

        // 尽量均匀分配方块到各列
        let cubeIndex = 0;
        for (let i = 0; i < actualFilledColumns; i++) {
            const columnCubes = [];
            const cubesForThisColumn = Math.ceil((allCubes.length - cubeIndex) / (actualFilledColumns - i));

            for (let j = 0; j < cubesForThisColumn && cubeIndex < allCubes.length; j++) {
                columnCubes.push(allCubes[cubeIndex++]);
            }
            level.push(columnCubes);
        }

        // 添加空列
        for (let i = 0; i < emptyColumns; i++) {
            level.push([]);
        }

        return Phaser.Utils.Array.Shuffle(level);
    }

    function handleInput(pointer) {
        if (gameWon || timer <= 0) return;

        let clickedColumn = null;
        for (let column of columns) {
            if (column.getBounds().contains(pointer.x, pointer.y)) {
                clickedColumn = column;
                break;
            }
        }

        if (clickedColumn) {
            if (selectedColumn === null) {
                if (!clickedColumn.isEmpty()) {
                    selectedColumn = clickedColumn;
                    selectedColumn.highlight();
                }
            } else if (selectedColumn === clickedColumn) {
                selectedColumn.unhighlight();
                selectedColumn = null;
            } else {
                const moveSuccessful = tryMoveCubes(selectedColumn, clickedColumn);
                if (moveSuccessful) {
                    gameScene.time.delayedCall(300, function() {
                        checkWinCondition();
                    });
                }
                selectedColumn.unhighlight();
                selectedColumn = null;
            }
        }
    }

    function tryMoveCubes(sourceColumn, targetColumn) {
        if (sourceColumn.isEmpty()) return false;

        const { cubes: topCubesToMove, color: topColorToMove } = sourceColumn.getTopCubesInfo();
        const targetTopColor = targetColumn.getTopColor();
        const targetEmptySpace = targetColumn.getEmptySpace();

        // 检查移动规则
        if (!targetColumn.isEmpty() && targetTopColor !== topColorToMove) {
            return false;
        }

        if (targetEmptySpace < topCubesToMove.length) {
            return false;
        }

        // 执行移动
        sourceColumn.removeCubes(topCubesToMove.length);
        targetColumn.addCubes(topCubesToMove);

        return true;
    }

    function checkWinCondition() {
        let allSorted = true;
        for (let column of columns) {
            if (!column.isSortedAndFull() && !column.isEmpty()) {
                allSorted = false;
                break;
            }
        }

        if (allSorted) {
            gameWon = true;
            // 添加胜利粒子效果
            createWinParticles();
            showWinMessage();
        }
    }

    function createWinParticles() {
        // 创建庆祝粒子效果
        for (let i = 0; i < 50; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 8 + 4);

            particle.x = Math.random() * config.width;
            particle.y = Math.random() * config.height;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y + Math.random() * 200 + 100,
                x: particle.x + (Math.random() - 0.5) * 200,
                alpha: 0,
                rotation: Math.random() * Math.PI * 2,
                duration: Math.random() * 2000 + 1000,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function showWinMessage() {
        // 创建胜利背景
        const winBg = gameScene.add.graphics();
        winBg.fillStyle(0x000000, 0.7);
        winBg.fillRect(0, 0, config.width, config.height);

        const winText = gameScene.add.text(config.width/2, config.height/2 - 50, 'CONGRATULATIONS!', {
            fontSize: '72px',
            fontWeight: 'bold',
            fill: '#FFD700',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const subText = gameScene.add.text(config.width/2, config.height/2 + 30, 'All cubes sorted perfectly!', {
            fontSize: '32px',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5);

        gameScene.tweens.add({
            targets: winText,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    function updateTimer() {
        if (gameWon || timer <= 0) return;
        
        timer--;
        timerText.setText(timer.toString());
        
        if (timer <= 0) {
            showGameOverMessage();
        }
    }

    function showGameOverMessage() {
        // 创建游戏结束背景
        const gameOverBg = gameScene.add.graphics();
        gameOverBg.fillStyle(0x000000, 0.8);
        gameOverBg.fillRect(0, 0, config.width, config.height);

        const gameOverText = gameScene.add.text(config.width/2, config.height/2 - 40, 'TIME UP!', {
            fontSize: '68px',
            fontWeight: 'bold',
            fill: '#FF4444',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const retryText = gameScene.add.text(config.width/2, config.height/2 + 40, 'Click to restart', {
            fontSize: '28px',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 添加重启功能
        gameScene.input.once('pointerdown', function() {
            location.reload();
        });
    }

    function updateProgressBar() {
        if (!gameScene || !gameScene.progressBar) return;

        gameScene.progressBar.clear();

        // 计算完成进度
        let totalColumns = 0;
        let completedColumns = 0;

        columns.forEach(column => {
            if (!column.isEmpty()) {
                totalColumns++;
                if (column.isSortedAndFull()) {
                    completedColumns++;
                }
            }
        });

        const progress = totalColumns > 0 ? completedColumns / totalColumns : 0;
        const progressWidth = 396 * progress; // 400 - 4 for padding

        // 绘制进度条
        if (progressWidth > 0) {
            const gradient = progress < 0.5 ? 0xff6b6b : progress < 0.8 ? 0xffd93d : 0x6bcf7f;
            gameScene.progressBar.fillStyle(gradient, 0.9);
            gameScene.progressBar.fillRoundedRect(config.width/2 - 198, config.height - 48, progressWidth, 16, 8);
        }
    }

    function update() {
        // 游戏主循环
        updateProgressBar();
    }

    // CubeColumn 类 - 方块列容器
    class CubeColumn extends Phaser.GameObjects.Container {
        constructor(scene, x, y, capacity, initialCubes) {
            super(scene, x, y);
            scene.add.existing(this);

            this.capacity = capacity;
            this.cubes = initialCubes || [];
            this.cubeSprites = [];

            // 绘制列的底座
            this.drawBase();

            // 创建方块精灵
            this.updateCubeVisuals();

            // 设置交互区域
            this.setSize(COLUMN_WIDTH, COLUMN_HEIGHT);
            this.setInteractive();
        }

        drawBase() {
            // 绘制列的底座和边框 - 横屏优化
            const graphics = this.scene.add.graphics();

            // 底座 - 更宽更稳定的设计
            graphics.fillStyle(0x37474f, 0.9);
            graphics.fillRoundedRect(-COLUMN_WIDTH/2, 0, COLUMN_WIDTH, 40, 12);

            // 底座阴影效果
            graphics.fillStyle(0x263238, 0.6);
            graphics.fillRoundedRect(-COLUMN_WIDTH/2 + 3, 3, COLUMN_WIDTH - 6, 37, 10);

            // 边框指示器 - 更明显的视觉效果
            graphics.lineStyle(4, 0x607d8b, 0.8);
            graphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 8, -COLUMN_HEIGHT + 40, COLUMN_WIDTH - 16, COLUMN_HEIGHT - 40, 8);

            // 内部虚线指示器
            graphics.lineStyle(2, 0x90a4ae, 0.4);
            for (let i = 1; i < this.capacity; i++) {
                const y = -i * CUBE_SIZE;
                graphics.lineBetween(-COLUMN_WIDTH/2 + 12, y, COLUMN_WIDTH/2 - 12, y);
            }

            this.add(graphics);
        }

        updateCubeVisuals() {
            // 清除现有方块精灵
            this.cubeSprites.forEach(sprite => sprite.destroy());
            this.cubeSprites = [];

            // 从底部向上创建方块精灵
            for (let i = 0; i < this.cubes.length; i++) {
                const colorName = this.cubes[i];
                const cubeSprite = this.scene.add.image(0, -i * CUBE_SIZE - CUBE_SIZE/2, `cube_${colorName}`);
                cubeSprite.setOrigin(0.5);

                // 添加轻微的随机旋转和缩放效果 - 横屏优化
                const randomRotation = (Math.random() - 0.5) * 0.08;
                const randomScale = 0.96 + Math.random() * 0.08;
                cubeSprite.setRotation(randomRotation);
                cubeSprite.setScale(randomScale);

                // 添加深度效果
                const depthOffset = (Math.random() - 0.5) * 4;
                cubeSprite.x = depthOffset;

                this.add(cubeSprite);
                this.cubeSprites.push(cubeSprite);
            }
        }

        getTopColor() {
            if (this.cubes.length === 0) return null;
            return this.cubes[this.cubes.length - 1];
        }

        getTopCubesInfo() {
            if (this.cubes.length === 0) return { cubes: [], color: null };

            const topColor = this.getTopColor();
            let count = 0;
            for (let i = this.cubes.length - 1; i >= 0; i--) {
                if (this.cubes[i] === topColor) {
                    count++;
                } else {
                    break;
                }
            }
            return {
                cubes: this.cubes.slice(this.cubes.length - count),
                color: topColor
            };
        }

        getEmptySpace() {
            return this.capacity - this.cubes.length;
        }

        isEmpty() {
            return this.cubes.length === 0;
        }

        addCubes(cubesToAdd) {
            this.cubes = this.cubes.concat(cubesToAdd);
            this.updateCubeVisuals();

            // 添加方块掉落动画 - 横屏优化
            const newCubes = this.cubeSprites.slice(-cubesToAdd.length);
            newCubes.forEach((sprite, index) => {
                sprite.y -= 120; // 从更高处开始
                sprite.alpha = 0.7;
                sprite.scaleX = 0.8;
                sprite.scaleY = 0.8;

                this.scene.tweens.add({
                    targets: sprite,
                    y: -(this.cubes.length - cubesToAdd.length + index) * CUBE_SIZE - CUBE_SIZE/2,
                    alpha: 1,
                    scaleX: sprite.scaleX / 0.8,
                    scaleY: sprite.scaleY / 0.8,
                    duration: 300,
                    ease: 'Back.easeOut',
                    delay: index * 50 // 错开动画时间
                });
            });
        }

        removeCubes(count) {
            // 添加移除动画 - 横屏优化
            const cubesToRemove = this.cubeSprites.slice(-count);
            cubesToRemove.forEach((sprite, index) => {
                this.scene.tweens.add({
                    targets: sprite,
                    alpha: 0,
                    y: sprite.y - 80,
                    scaleX: 0.6,
                    scaleY: 0.6,
                    rotation: (Math.random() - 0.5) * 0.5,
                    duration: 250,
                    delay: index * 30,
                    ease: 'Power2.easeIn',
                    onComplete: () => sprite.destroy()
                });
            });

            this.cubes.splice(this.cubes.length - count, count);
            this.cubeSprites.splice(-count, count);
        }

        isSortedAndFull() {
            if (this.cubes.length === 0) return false;
            if (this.cubes.length < this.capacity) return false;

            const firstColor = this.cubes[0];
            return this.cubes.every(color => color === firstColor);
        }

        highlight() {
            // 添加高亮效果 - 横屏优化
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
            }

            this.highlightGraphics = this.scene.add.graphics();

            // 外层高亮边框
            this.highlightGraphics.lineStyle(8, 0xFFD700, 1);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 4, -COLUMN_HEIGHT + 40, COLUMN_WIDTH - 8, COLUMN_HEIGHT - 40, 8);

            // 内层发光效果
            this.highlightGraphics.lineStyle(4, 0xFFFFFF, 0.6);
            this.highlightGraphics.strokeRoundedRect(-COLUMN_WIDTH/2 + 8, -COLUMN_HEIGHT + 44, COLUMN_WIDTH - 16, COLUMN_HEIGHT - 44, 6);

            this.add(this.highlightGraphics);

            // 添加脉冲效果
            this.scene.tweens.add({
                targets: this.highlightGraphics,
                alpha: 0.4,
                scaleX: 1.05,
                scaleY: 1.02,
                duration: 400,
                yoyo: true,
                repeat: -1,
                ease: 'Sine.easeInOut'
            });
        }

        unhighlight() {
            if (this.highlightGraphics) {
                this.highlightGraphics.destroy();
                this.highlightGraphics = null;
            }
        }

        getBounds() {
            return new Phaser.Geom.Rectangle(
                this.x - COLUMN_WIDTH/2,
                this.y - COLUMN_HEIGHT,
                COLUMN_WIDTH,
                COLUMN_HEIGHT
            );
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
