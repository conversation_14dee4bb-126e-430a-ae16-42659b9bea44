<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗基础版 - 合成大西瓜融合版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'playing';
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;
    let isCreatingWave = false; // 防止重复创建波次的标志位

    // 水果收集相关变量
    let fruits = [];
    let containerBounds = null;
    let storageSlots = [];
    let storageArea = null;
    let score = 0;
    let highScore = 0;

    // 装备配置 - 对应images/other下的图片
    const EQUIPMENT = [
        { name: '血瓶', image: 'blood_bottle', size: 80,points:1, color: 0xff6b6b, effect: 'heal' },
        { name: '手雷', image: 'grenade', size: 80, points: 6, color: 0x9b59b6, effect: 'explosive' },
        { name: '盾牌', image: 'shield', size: 80, points: 10, color: 0xf39c12, effect: 'shield' },
        { name: '武器', image: 'weapon', size: 80, points: 15, color: 0xf1c40f, effect: 'weapon' },
        { name: '能量瓶', image: 'energy_bottle', size: 80, points: 28, color: 0xfd79a8, effect: 'energy' },
        { name: '导弹', image: 'missile', size: 80, points: 45, color: 0xe17055, effect: 'rocket' },
        { name: '金币', image: 'gold_coin', size: 80, points: 21, color: 0xe67e22, effect: 'gem' },
        { name: '宝箱', image: 'treasure_box', size: 80, points: 66, color: 0x00b894, effect: 'treasure' }
    ];

    // 水果收集游戏配置
    const CONTAINER_WIDTH = 500;  // 增大容器
    const CONTAINER_HEIGHT =700; // 进一步增加高度
    const WALL_THICKNESS = 8;
    const STORAGE_HEIGHT = 80;    // 增大暂存区
    const STORAGE_SLOTS = 7;

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600, // 从2000减少到800，攻击更快
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        physics: {
            default: 'matter',
            matter: {
                debug: false,  // 确保调试模式关闭，去掉蓝色线
                gravity: { y: 0.8 }
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/ak.png');

        // 加载装备图片
        this.load.image('blood_bottle', 'images/other/血瓶.png');
        this.load.image('grenade', 'images/other/手雷.png');
        this.load.image('shield', 'images/other/盾牌.png');
        this.load.image('weapon', 'images/other/武器.png');
        this.load.image('energy_bottle', 'images/other/能量瓶.png');
        this.load.image('missile', 'images/other/导弹.png');
        this.load.image('gold_coin', 'images/other/金币.png');
        this.load.image('treasure_box', 'images/other/宝箱.png');

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

      

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6); // 缩小到0.5
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        // 根据Y轴位置设置深度，确保与怪物有正确的层级关系
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8); // 武器缩放
        playerWeapon.setOrigin(0.5, 1); // 设置旋转中心在下中
        // 去掉武器旋转，保持水平
        playerWeapon.setDepth(100 + player.y * 0.1 + 1); // 武器深度比角色稍高

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建水果收集游戏区域
        createFruitCollectionGame.call(this);
    }

    // 创建波次怪物
    function createWave() {
        // 确保标志位已设置，防止并发创建
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 清除现有血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80; // 怪物间距
        const startX = 450; // 起始X位置
        const startY = player.y; // 与角色底部对齐

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部
            // 根据Y轴位置设置深度，Y轴越大（越靠下）深度越大，显示在前面
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
        }

        monstersKilled = 0;

        // 延迟初始化血条，确保怪物完全渲染后再创建血条
        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            // 血条创建完成后重置标志位
            isCreatingWave = false;
        }, 50);
    }

    // 创建水果收集游戏区域
    function createFruitCollectionGame() {
        // 初始化变量
        fruits = [];
        storageSlots = [];
        score = 0;

        // 设置物理世界边界
        this.matter.world.setBounds();

        // 创建容器区域
        createFruitContainer.call(this);

        // 创建暂存区
        createFruitStorageArea.call(this);

        // 移除自动合成功能，不设置碰撞检测
    }

    // 创建水果容器
    function createFruitContainer() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const containerY = 400;//调整位置适配更高的容器

        containerBounds = {
            left: containerX,
            right: containerX + CONTAINER_WIDTH,
            top: containerY,
            bottom: containerY + CONTAINER_HEIGHT
        };

        const wallOptions = {
            isStatic: true,
            label: 'wall',
            friction: 0.8,
            restitution: 0.1  // 降低弹性
        };

        // 左墙
        this.matter.add.rectangle(
            containerX - WALL_THICKNESS/2,
            containerY + CONTAINER_HEIGHT/2,
            WALL_THICKNESS,
            CONTAINER_HEIGHT,
            wallOptions
        );

        // 右墙
        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH + WALL_THICKNESS/2,
            containerY + CONTAINER_HEIGHT/2,
            WALL_THICKNESS,
            CONTAINER_HEIGHT,
            wallOptions
        );

        // 底墙
        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH/2,
            containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2,
            CONTAINER_WIDTH + WALL_THICKNESS*2,
            WALL_THICKNESS,
            wallOptions
        );

        // 绘制容器边框
        const graphics = this.add.graphics();
        graphics.lineStyle(3, 0x4ecdc4, 0.8);
        graphics.strokeRoundedRect(containerX - 2, containerY - 2, CONTAINER_WIDTH + 4, CONTAINER_HEIGHT + 4, 8);

    
    }

    // 创建暂存区
    function createFruitStorageArea() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const storageY = 400+ CONTAINER_HEIGHT + 20;
        const slotWidth = CONTAINER_WIDTH / STORAGE_SLOTS;

        // 初始化暂存区数组
        storageSlots = new Array(STORAGE_SLOTS).fill(null);

        // 绘制暂存区背景
        const graphics = this.add.graphics();
        graphics.fillStyle(0x34495e, 0.8);
        graphics.fillRoundedRect(containerX, storageY, CONTAINER_WIDTH, STORAGE_HEIGHT, 5);

        // 绘制格子分隔线
        graphics.lineStyle(1, 0x7f8c8d, 0.6);
        for (let i = 1; i < STORAGE_SLOTS; i++) {
            const x = containerX + i * slotWidth;
            graphics.moveTo(x, storageY);
            graphics.lineTo(x, storageY + STORAGE_HEIGHT);
        }
        graphics.strokePath();

        // 存储暂存区位置信息
        storageArea = {
            x: containerX,
            y: storageY,
            width: CONTAINER_WIDTH,
            height: STORAGE_HEIGHT,
            slotWidth: slotWidth
        };

      
    
    }

    // 怪物死亡掉落水果
    function dropFruitFromMonster(monster) {
        // 确保容器边界已设置
        if (!containerBounds) {
            console.warn('Container bounds not set, skipping fruit drop');
            return;
        }

        // 随机选择装备类型（前5种装备）
        const equipmentType = Phaser.Math.Between(0, 4);
        const equipmentConfig = EQUIPMENT[equipmentType];

        // 从怪物位置开始掉落
        const dropX = monster.x;
        const dropY = monster.y;

        // 创建装备图片在怪物位置
        const equipment = this.add.image(dropX, dropY, equipmentConfig.image);
        //equipment.setScale(equipmentConfig.size / Math.max(equipment.width, equipment.height)); // 等比缩放

        equipment.equipmentType = equipmentType;

        // 设置装备为可点击
        setupEquipmentClick.call(this, equipment);

        // 检查怪物位置是否在容器X范围内，如果不在则调整到容器边界
        const scaledSize = equipmentConfig.size;
        let targetX = dropX;
        if (dropX < containerBounds.left + scaledSize/2 + 10) {
            targetX = containerBounds.left + scaledSize/2 + 10;
        } else if (dropX > containerBounds.right - scaledSize/2 - 10) {
            targetX = containerBounds.right - scaledSize/2 - 10;
        }

        // 使用动画让装备垂直下落到容器内
        this.tweens.add({
            targets: equipment,
            x: targetX, // 保持X位置不变或调整到容器边界内
            y: containerBounds.top + 50, // 掉落到容器内
            duration: 600,
            ease: 'Power2.easeIn', // 重力加速效果
            onComplete: () => {
                // 动画完成后添加物理体，让装备继续在容器内物理下落
                // 使用矩形碰撞体，更符合装备的实际形状
                this.matter.add.gameObject(equipment, {
                    shape: 'rectangle',
                    isStatic: false,
                    restitution: 0.1, // 极低弹性，几乎无回弹
                    friction: 0.9,    // 高摩擦力
                    frictionAir: 0.01 // 空气阻力
                });

                fruits.push(equipment);
            }
        });

        // 掉落特效
        const dropEffect = this.add.text(monster.x, monster.y - 30, '💎', {
            fontSize: '20px'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: dropEffect,
            alpha: 0,
            y: dropEffect.y - 30,
            duration: 1000,
            onComplete: () => dropEffect.destroy()
        });
    }

    // 设置装备点击事件
    function setupEquipmentClick(equipment) {
        equipment.setInteractive();
        equipment.on('pointerdown', () => {
            collectFruit.call(this, equipment);
        });
    }

    // 收集水果到暂存区
    function collectFruit(fruit) {
        // 找到第一个空的暂存区位置
        const emptySlotIndex = storageSlots.findIndex(slot => slot === null);

        if (emptySlotIndex === -1) {
            // 暂存区已满，显示提示
            showMessage.call(this, '暂存区已满！', fruit.x, fruit.y - 50);
            return;
        }

        // 计算暂存区位置
        const slotX = storageArea.x + (emptySlotIndex + 0.5) * storageArea.slotWidth;
        const slotY = storageArea.y + storageArea.height / 2;

        // 创建收集特效
        createCollectEffect.call(this, fruit.x, fruit.y, slotX, slotY);

        // 将装备信息存储到暂存区
        storageSlots[emptySlotIndex] = {
            equipmentType: fruit.equipmentType,
            name: EQUIPMENT[fruit.equipmentType].name,
            image: EQUIPMENT[fruit.equipmentType].image,
            displayObject: null
        };

        // 移除原装备
        removeEquipment.call(this, fruit);

        // 在暂存区显示装备
        this.time.delayedCall(300, () => {
            displayFruitInStorage.call(this, emptySlotIndex);
            checkForMatches.call(this);
        });
    }

    // 创建收集特效
    function createCollectEffect(fromX, fromY, toX, toY) {
        const effect = this.add.text(fromX, fromY, '✨', {
            fontSize: '24px'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: effect,
            x: toX,
            y: toY,
            scaleX: 0.5,
            scaleY: 0.5,
            duration: 300,
            ease: 'Power2',
            onComplete: () => {
                effect.destroy();
            }
        });
    }

    // 在暂存区显示水果
    function displayFruitInStorage(slotIndex) {
        const slotData = storageSlots[slotIndex];
        if (!slotData) return;

        const slotX = storageArea.x + (slotIndex + 0.5) * storageArea.slotWidth;
        const slotY = storageArea.y + storageArea.height / 2;

        // 创建暂存区中的装备显示
        const displayObject = this.add.image(slotX, slotY, slotData.image);
        // 等比缩放到40像素，保持原始比例
        const targetSize = 40;
        const scale = targetSize / Math.max(displayObject.width, displayObject.height);
        displayObject.setScale(scale);
        displayObject.setOrigin(0.5);

        slotData.displayObject = displayObject;
    }

    // 检查匹配并消除
    function checkForMatches() {
        // 统计每种水果的数量和位置
        const fruitCounts = {};
        const fruitPositions = {};

        storageSlots.forEach((slot, index) => {
            if (slot !== null) {
                const type = slot.equipmentType;
                if (!fruitCounts[type]) {
                    fruitCounts[type] = 0;
                    fruitPositions[type] = [];
                }
                fruitCounts[type]++;
                fruitPositions[type].push(index);
            }
        });

        // 检查是否有3个或更多相同的水果
        for (const [equipmentType, count] of Object.entries(fruitCounts)) {
            if (count >= 3) {
                eliminateEquipments.call(this, parseInt(equipmentType), fruitPositions[equipmentType]);
                break; // 一次只处理一种水果的消除
            }
        }
    }

    // 消除装备并使用装备效果
    function eliminateEquipments(equipmentType, positions) {
        // 取前3个位置进行消除
        const eliminatePositions = positions.slice(0, 3);
        const equipmentConfig = EQUIPMENT[equipmentType];

        // 创建消除特效
        eliminatePositions.forEach(pos => {
            const slot = storageSlots[pos];
            if (slot && slot.displayObject) {
                // 消除特效
                this.tweens.add({
                    targets: slot.displayObject,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        slot.displayObject.destroy();
                    }
                });
            }
        });

        // 延迟清空位置并重新排列
        this.time.delayedCall(500, () => {
            // 清空消除的位置
            eliminatePositions.forEach(pos => {
                storageSlots[pos] = null;
            });

            // 重新排列暂存区
            rearrangeStorage.call(this);

            // 使用装备效果
            useEquipmentEffect.call(this, equipmentConfig);

            // 计算得分
            const points = equipmentConfig.points * 3 + 50;
            updateScore.call(this, points);

            // 显示得分特效
            showScoreEffect.call(this, points);
        });
    }

    // 使用装备效果
    function useEquipmentEffect(equipmentConfig) {
        const effect = equipmentConfig.effect;
        const name = equipmentConfig.name;

        switch(effect) {
            case 'heal': // 血瓶 - 恢复生命值
                const healAmount = 50;
                playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);
                showEffectMessage.call(this, `使用${name}！恢复${healAmount}生命值`, '#27ae60');
                break;

            case 'ammo': // 弹夹 - 增加攻击速度
                playerStats.attackSpeed = Math.max(200, playerStats.attackSpeed - 50);
                showEffectMessage.call(this, `使用${name}！攻击速度提升`, '#3498db');
                break;

            case 'explosive': // 手雷 - 增加攻击力
                playerStats.attackDamage += 5;
                showEffectMessage.call(this, `使用${name}！攻击力+5`, '#e74c3c');
                break;

            case 'shield': // 盾牌 - 增加最大生命值
                maxPlayerHealth += 20;
                playerHealth += 20;
                showEffectMessage.call(this, `使用${name}！最大生命值+20`, '#f39c12');
                break;

            case 'weapon': // 武器 - 增加暴击率
                playerStats.critChance += 0.1;
                showEffectMessage.call(this, `使用${name}！暴击率+10%`, '#9b59b6');
                break;

            case 'gem': // 宝石 - 增加得分倍率
                score += 100;
                showEffectMessage.call(this, `使用${name}！获得100分奖励`, '#e67e22');
                break;

            case 'energy': // 能量 - 恢复生命值并增加攻击力
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 30);
                playerStats.attackDamage += 3;
                showEffectMessage.call(this, `使用${name}！恢复生命值+攻击力`, '#f1c40f');
                break;

            case 'aim': // 瞄准器 - 大幅增加暴击率
                playerStats.critChance += 0.15;
                showEffectMessage.call(this, `使用${name}！暴击率+15%`, '#fd79a8');
                break;

            case 'rocket': // 火箭 - 大幅增加攻击力
                playerStats.attackDamage += 10;
                showEffectMessage.call(this, `使用${name}！攻击力+10`, '#34495e');
                break;

            case 'crown': // 王冠 - 全属性提升
                playerStats.attackDamage += 5;
                playerStats.critChance += 0.1;
                maxPlayerHealth += 30;
                playerHealth += 30;
                showEffectMessage.call(this, `使用${name}！全属性提升`, '#f39c12');
                break;

            case 'treasure': // 宝箱 - 大量分数奖励
                score += 500;
                showEffectMessage.call(this, `使用${name}！获得500分大奖`, '#27ae60');
                break;
        }
    }

    // 显示装备效果消息
    function showEffectMessage(message, color) {
        const effectText = this.add.text(375, 520, message, {
            fontSize: '24px',
            fill: color,
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: effectText,
            alpha: 0,
            y: effectText.y - 50,
            duration: 2000,
            ease: 'Power2',
            onComplete: () => {
                effectText.destroy();
            }
        });
    }

    // 重新排列暂存区
    function rearrangeStorage() {
        // 收集所有非空的水果数据
        const remainingFruits = [];
        storageSlots.forEach((slot, index) => {
            if (slot !== null) {
                remainingFruits.push(slot);
            }
        });

        // 清除所有显示对象
        storageSlots.forEach(slot => {
            if (slot && slot.displayObject) {
                slot.displayObject.destroy();
                slot.displayObject = null;
            }
        });

        // 重置暂存区数组
        storageSlots = new Array(STORAGE_SLOTS).fill(null);

        // 重新排列
        remainingFruits.forEach((equipmentData, newIndex) => {
            storageSlots[newIndex] = equipmentData;
            displayFruitInStorage.call(this, newIndex);
        });
    }

    // 移除装备
    function removeEquipment(equipment) {
        // 从装备数组中移除
        const index = fruits.indexOf(equipment);
        if (index > -1) {
            fruits.splice(index, 1);
        }

        // 销毁装备对象
        equipment.destroy();
    }

    // 显示消息
    function showMessage(message, x, y) {
        const messageText = this.add.text(x, y, message, {
            fontSize: '20px',
            fill: '#ff6b6b',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: messageText,
            alpha: 0,
            y: y - 30,
            duration: 1500,
            onComplete: () => messageText.destroy()
        });
    }

    // 更新得分
    function updateScore(points) {
        score += points;
        // 更新得分显示
        if (game && game.scene && game.scene.scenes[0] && game.scene.scenes[0].scoreText) {
            game.scene.scenes[0].scoreText.setText(`装备得分: ${score}`);
        }
    }

    // 显示得分特效
    function showScoreEffect(points) {
        const scoreEffect = this.add.text(375, storageArea.y - 30, `+${points}`, {
            fontSize: '28px',
            fill: '#f39c12',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: scoreEffect,
            y: storageArea.y - 60,
            alpha: 0,
            scaleX: 1.5,
            scaleY: 1.5,
            duration: 1000,
            ease: 'Power2',
            onComplete: () => {
                scoreEffect.destroy();
            }
        });
    }

    // 移除自动合成功能 - 水果只能通过收集到暂存区进行装备合成

    // 移除战术棋盘创建函数

    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 多重射击：攻击多个目标
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i];

                // 创建攻击特效 - 从武器最右边中间位置发射
                const weaponRightX = playerWeapon ? playerWeapon.x + 40 : player.x + 60; // 武器最右边
                const weaponCenterY = playerWeapon ? playerWeapon.y - 15 : player.y - 30; // 武器中间高度
                const projectile = this.add.circle(weaponRightX, weaponCenterY, 5, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 30, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 狂战士效果：生命越低攻击越高
                        if (playerStats.berserker) {
                            const healthPercent = playerHealth / maxPlayerHealth;
                            const berserkerBonus = (1 - healthPercent) * 2; // 最多200%加成
                            damage *= (1 + berserkerBonus);
                        }

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 冰冻效果
                        if (playerStats.freeze && Math.random() < playerStats.freeze) {
                            target.frozen = true;
                            target.frozenTime = 2000; // 冰冻2秒
                            target.setTint(0x87ceeb); // 浅蓝色

                            const freezeText = this.add.text(target.x, target.y - 100, 'FROZEN!', {
                                fontSize: '16px',
                                fill: '#87ceeb',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: freezeText,
                                alpha: 0,
                                y: freezeText.y - 20,
                                duration: 1000,
                                onComplete: () => freezeText.destroy()
                            });
                        }

                        // 毒性效果
                        if (playerStats.poison) {
                            if (!target.poisoned) {
                                target.poisoned = true;
                                target.poisonDamage = Math.floor(damage * 0.3);
                                target.poisonDuration = 5000; // 持续5秒
                                target.setTint(0x9acd32); // 绿色
                            }
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 爆炸攻击效果
                        if (playerStats.explosive) {
                            const explosionRadius = 80;
                            const explosionDamage = Math.floor(damage * 0.5);

                            // 爆炸特效
                            const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6b35, 0.3);
                            this.tweens.add({
                                targets: explosion,
                                scaleX: 1.5,
                                scaleY: 1.5,
                                alpha: 0,
                                duration: 300,
                                onComplete: () => explosion.destroy()
                            });

                            // 对范围内的其他怪物造成伤害
                            monsters.forEach(otherMonster => {
                                if (otherMonster !== target) {
                                    const distance = Phaser.Math.Distance.Between(
                                        target.x, target.y, otherMonster.x, otherMonster.y
                                    );
                                    if (distance <= explosionRadius) {
                                        otherMonster.health -= explosionDamage;

                                        // 爆炸伤害特效
                                        this.tweens.add({
                                            targets: otherMonster,
                                            scaleX: 0.3,
                                            scaleY: 0.3,
                                            duration: 100,
                                            yoyo: true
                                        });
                                    }
                                }
                            });
                        }

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            // 怪物死亡时掉落水果
                            dropFruitFromMonster.call(this, target);

                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 武器开火抖动 - 更明显的后坐力
            if (playerWeapon) {
                // 记录武器原始位置
                const originalWeaponX = playerWeapon.x;
                const originalWeaponY = playerWeapon.y;

                // 水平后坐力
                this.tweens.add({
                    targets: playerWeapon,
                    x: originalWeaponX - 12, // 向后抖动12像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            x: originalWeaponX,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });

                // 垂直抖动
                this.tweens.add({
                    targets: playerWeapon,
                    y: originalWeaponY - 6, // 向上抖动6像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            y: originalWeaponY,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });
            }
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        if (gameState === 'playing') {
            // 更新战斗逻辑
            updateBattle.call(this, time);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }

        // 更新装备位置同步
        updateEquipments.call(this);
    }

    // 更新装备位置同步
    function updateEquipments() {
        // 过滤无效装备
        fruits = fruits.filter(equipment => equipment && equipment.active);
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理毒性伤害
            if (monster.poisoned) {
                monster.poisonDuration -= delta;
                if (monster.poisonDuration <= 0) {
                    monster.poisoned = false;
                    monster.clearTint();
                } else {
                    // 每秒造成毒性伤害
                    if (!monster.lastPoisonDamage) monster.lastPoisonDamage = 0;
                    if (time - monster.lastPoisonDamage > 1000) {
                        monster.health -= monster.poisonDamage;
                        monster.lastPoisonDamage = time;

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 毒性伤害特效
                        const poisonText = this.add.text(monster.x, monster.y - 60, `-${monster.poisonDamage}`, {
                            fontSize: '14px',
                            fill: '#9acd32',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: poisonText,
                            alpha: 0,
                            y: poisonText.y - 20,
                            duration: 800,
                            onComplete: () => poisonText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            // 怪物死亡时掉落水果
                            dropFruitFromMonster.call(this, monster);

                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 15000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.2, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;

                // 护盾系统
                if (playerStats.shield && playerStats.shield > 0) {
                    const shieldAbsorb = Math.min(damage, playerStats.shield);
                    playerStats.shield -= shieldAbsorb;
                    damage -= shieldAbsorb;

                    // 护盾特效
                    const shieldText = this.add.text(player.x, player.y - 80, `SHIELD -${shieldAbsorb}`, {
                        fontSize: '16px',
                        fill: '#3498db',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: shieldText,
                        alpha: 0,
                        y: shieldText.y - 20,
                        duration: 1000,
                        onComplete: () => shieldText.destroy()
                    });
                }

                // 剩余伤害作用于生命值
                if (damage > 0) {
                    playerHealth -= damage;
                }

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 80,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 80,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 80;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true; // 设置标志位，防止重复触发

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
                isCreatingWave = false; // 创建完成后重置标志位
            }, 1000);
        }
    }

    // 移除棋子类型定义

    // 移除商店相关函数

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 添加装备得分显示
        this.scoreText = this.add.text(375, 600, `装备得分: ${score}`, {
            fontSize: '24px',
            fill: '#4ecdc4',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加游戏说明
        this.add.text(375, 1300, '击杀怪物掉落水果 | 点击收集到暂存区 | 3个相同合成装备', {
            fontSize: '18px',
            fill: '#95a5a6',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }

    // 下棋函数
    function placeTacticalPiece(row, col, player, pieceType) {
        if (gameBoard[row][col] !== null) return false;

        const piece = pieceTypes[pieceType];

        // 创建棋子数据
        const pieceData = {
            type: pieceType,
            player: player,
            attack: piece.attack,
            health: piece.health,
            maxHealth: piece.health,
            skill: piece.skill,
            row: row,
            col: col
        };

        // 更新棋盘数据
        gameBoard[row][col] = pieceData;

        // 创建棋子图形
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const pieceX = startX + col * cellSize;
        const pieceY = startY + row * cellSize;

        if (player === 'player') {
            // 玩家棋子：白色背景 + emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0xffffff);
            pieceCircle.setStrokeStyle(2, 0x000000);

            const pieceIcon = this.add.text(pieceX, pieceY, piece.emoji, {
                fontSize: '20px'
            }).setOrigin(0.5);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: pieceIcon, data: pieceData };
        } else {
            // 敌方棋子：纯黑色，无emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0x000000);
            pieceCircle.setStrokeStyle(2, 0xffffff);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: null, data: pieceData };
        }

        // 更新计数
        if (player === 'player') {
            playerPieces++;
        } else {
            enemyPieces++;
        }

        // 更新UI
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);

        // 检查是否棋盘已满，开始战斗
        if (playerPieces + enemyPieces >= totalMaxPieces) {
            startBattle.call(this);
            return true;
        }

        // 检查是否需要切换回合
        if (player === 'player') {
            // 玩家下棋后，如果还有空位且敌方还能下棋，切换到敌方回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = false;
                this.tacticalStatusText.setText('敌方下棋中...');
                setTimeout(() => {
                    enemyMove.call(this);
                }, 1000);
            }
        } else if (player === 'enemy') {
            // 敌方下棋后，如果还有空位且玩家还能下棋，切换到玩家回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = true;
                this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            }
        }

        return true;
    }

    // 敌方AI下棋
    function enemyMove() {
        if (tacticalGameState !== 'placing') return;

        // 随机选择棋子类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedEnemyPieceType = pieceTypeKeys[Math.floor(Math.random() * pieceTypeKeys.length)];

        // 寻找空位
        const emptySpaces = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] === null) {
                    emptySpaces.push({ row, col });
                }
            }
        }

        if (emptySpaces.length > 0) {
            const randomSpace = emptySpaces[Math.floor(Math.random() * emptySpaces.length)];
            placeTacticalPiece.call(this, randomSpace.row, randomSpace.col, 'enemy', selectedEnemyPieceType);
        }
    }



    // 开始战斗
    function startBattle() {
        tacticalGameState = 'battle';
        gameEnded = false;
        currentBattleIndex = 0;
        this.tacticalStatusText.setText('棋盘已满！战斗开始！');

        // 隐藏商店
        shop.forEach(shopItem => {
            shopItem.playerPiece.setVisible(false);
            shopItem.playerIcon.setVisible(false);
            shopItem.pieceName.setVisible(false);
            shopItem.pieceDesc.setVisible(false);
        });
        // 隐藏商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(false);
        }

        // 创建战斗序列（从左上角到右下角）
        battleSequence = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] !== null) {
                    battleSequence.push(gameBoard[row][col]);
                }
            }
        }

        // 开始战斗循环
        setTimeout(() => {
            battleLoop.call(this);
        }, 2000);
    }

    // 战斗循环 - 按格子顺序执行
    function battleLoop() {
        if (tacticalGameState !== 'battle' || gameEnded) return;

        // 检查游戏结束条件
        if (playerHealth <= 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('玩家失败！');
            this.tacticalStatusText.setFill('#ff0000');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        if (monsters.length === 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('所有怪物被击败！玩家获胜！');
            this.tacticalStatusText.setFill('#00ff00');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        // 按格子顺序执行，每个格子一个回合
        if (currentBattleIndex < battleSequence.length) {
            const currentUnit = battleSequence[currentBattleIndex];
            if (currentUnit && currentUnit.health > 0) {
                executeCurrentTurn.call(this, currentUnit);
            }
            currentBattleIndex++;

            // 继续下一个格子
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1500);
        } else {
            // 一轮结束，重新开始
            currentBattleIndex = 0;
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1000);
        }
    }

    // 执行当前回合（按格子顺序）
    function executeCurrentTurn(currentUnit) {
        const pieceType = pieceTypes[currentUnit.type];

        // 高亮当前格子
        highlightCurrentPiece.call(this, currentUnit);

        if (currentUnit.player === 'player') {
            // 玩家棋子（白棋）
            if (pieceType.isAttackSkill) {
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 发动攻击！`);
            } else {
                // 非攻击技能执行辅助效果
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 使用技能！`);
            }
        } else {
            // 敌方棋子（黑棋）
            if (pieceType.isAttackSkill) {
                // 敌方棋子触发怪物攻击
                monsterAttackTriggeredByPiece.call(this, currentUnit);
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 指挥怪物攻击！`);
            } else {
                // 敌方辅助技能（如果有的话）
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 使用技能！`);
            }
        }
    }

    // 执行技能攻击怪物
    function executeSkillOnMonsters(unit) {
        if (monsters.length === 0) return;

        switch (unit.skill) {
            case 'charge': // 战士冲锋 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    performMeleeAttack.call(this, unit, target, unit.attack, '⚔️');
                }
                break;

            case 'multishot': // 弓箭手多重射击 - 远程，攻击多个
                const targets = monsters.slice(0, 2);
                targets.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.7);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🏹');
                });
                break;

            case 'fireball': // 法师火球术 - 远程，群攻
                monsters.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.5);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🔥');
                });
                break;

            case 'heal': // 治疗师治疗玩家
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 30);
                const healText = this.add.text(player.x, player.y - 60, '💚 +30', {
                    fontSize: '16px',
                    fill: '#00ff00',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: healText,
                    alpha: 0,
                    y: healText.y - 30,
                    duration: 1500,
                    onComplete: () => healText.destroy()
                });
                break;

            case 'shield': // 坦克护盾
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 20);
                const shieldText = this.add.text(player.x, player.y - 60, '🛡️ +20', {
                    fontSize: '16px',
                    fill: '#3498db',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: shieldText,
                    alpha: 0,
                    y: shieldText.y - 30,
                    duration: 1500,
                    onComplete: () => shieldText.destroy()
                });
                break;

            case 'critical': // 刺客暴击 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    const damage = unit.attack * 2;
                    performMeleeAttack.call(this, unit, target, damage, '💥');
                }
                break;
        }

        // 移除死亡的怪物并重新排列
        const deadMonsters = [];
        monsters = monsters.filter((monster, index) => {
            if (monster.health <= 0) {
                monster.destroy();
                monstersKilled++;
                deadMonsters.push(index);
                return false;
            }
            return true;
        });

        // 如果有怪物死亡，重新排列剩余怪物
        if (deadMonsters.length > 0) {
            repositionMonsters.call(this);
            updateEnemyHealthBar.call(this); // 更新进度条
            updateMonsterHealthBars.call(this); // 更新怪物血条
        }
    }

    // 执行近战攻击（角色移动）
    function performMeleeAttack(unit, target, damage, icon) {
        // 记录角色原始位置
        const originalX = player.x;
        const originalY = player.y;

        // 移动角色到目标附近
        const targetX = target.x - 80;
        const targetY = target.y;

        this.tweens.add({
            targets: player,
            x: targetX,
            y: targetY,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                // 造成伤害
                target.health -= damage;
                showMonsterDamage.call(this, target, damage, icon);

                // 角色攻击动画
                this.tweens.add({
                    targets: player,
                    rotation: 0.3,
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 返回原位
                setTimeout(() => {
                    this.tweens.add({
                        targets: player,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }, 300);
            }
        });
    }

    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }

    // 显示怪物伤害
    function showMonsterDamage(monster, damage, icon) {
        const damageText = this.add.text(monster.x, monster.y - 100, `${icon} -${damage}`, {
            fontSize: '16px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        damageText.setDepth(200); // 确保伤害数字显示在血条之上

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: damageText.y - 30,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });

        // 怪物受击效果
        this.tweens.add({
            targets: monster,
            tint: 0xff0000,
            duration: 200,
            yoyo: true,
            onComplete: () => {
                monster.clearTint();
                // 受击后更新血条
                updateMonsterHealthBars.call(this);
            }
        });
    }

    // 敌方棋子触发怪物攻击
    function monsterAttackTriggeredByPiece(unit) {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击
            const damage = unit.attack; // 使用棋子的攻击力

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 150, // 移动到玩家附近
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });
        }
    }

    // 怪物攻击玩家
    function monsterAttack() {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 100,
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    const damage = 15 + currentLevel * 2;
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });

            this.tacticalStatusText.setText('怪物攻击玩家！');
        }
    }

    // 高亮当前攻击的棋子
    function highlightCurrentPiece(unit) {
        // 清除之前的高亮
        clearAllHighlights.call(this);

        // 找到对应的棋盘格子
        const cell = tacticalCells[unit.row][unit.col];
        if (cell && cell.piece) {
            // 创建高亮效果
            const cellSize = 100;
            const gridSize = 4;
            const gridWidth = gridSize * cellSize;
            const startX = (750 - gridWidth) / 2 + cellSize / 2;
            const startY = 550;

            const highlightX = startX + unit.col * cellSize;
            const highlightY = startY + unit.row * cellSize;

            // 创建高亮圆圈
            this.currentHighlight = this.add.circle(highlightX, highlightY, 45, 0xffff00, 0.3);
            this.currentHighlight.setStrokeStyle(3, 0xffff00);

            // 添加脉动效果
            this.tweens.add({
                targets: this.currentHighlight,
                scaleX: 1.2,
                scaleY: 1.2,
                duration: 500,
                yoyo: true,
                repeat: -1
            });
        }
    }

    // 清除所有高亮
    function clearAllHighlights() {
        if (this.currentHighlight) {
            this.currentHighlight.destroy();
            this.currentHighlight = null;
        }
    }

    // 游戏结束后清空棋盘
    function clearBoardAfterGame() {
        // 延迟3秒后清空棋盘，让玩家看到结果
        setTimeout(() => {
            // 清空棋盘数据和图形
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < 4; col++) {
                    if (gameBoard[row][col] !== null) {
                        // 移除棋子图形
                        const cell = tacticalCells[row][col];
                        if (cell && cell.piece) {
                            if (cell.piece.circle) cell.piece.circle.destroy();
                            if (cell.piece.icon) cell.piece.icon.destroy();
                            cell.piece = null;
                        }
                        gameBoard[row][col] = null;
                    }
                }
            }

            // 重置计数
            playerPieces = 0;
            enemyPieces = 0;

            // 重置游戏状态
            tacticalGameState = 'placing';
            isPlayerTurn = true;
            gameEnded = false;
            currentBattleIndex = 0;
            battleSequence = [];

            // 更新UI
            this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
            this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            this.tacticalStatusText.setFill('#ffffff');

            // 重新刷新商店
            refreshShop.call(this);
        }, 3000);
    }

    // 显示伤害
    function showDamage(unit, damage, icon) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const damageText = this.add.text(x, y - 40, `${icon} -${damage}`, {
            fontSize: '14px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });
    }

    // 显示治疗
    function showHeal(unit, heal) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const healText = this.add.text(x, y - 40, `💚 +${heal}`, {
            fontSize: '14px',
            fill: '#00ff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: healText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => healText.destroy()
        });
    }

    // 更新棋子显示
    function updatePieceDisplay() {
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                const piece = gameBoard[row][col];
                const cell = tacticalCells[row][col];

                if (piece && cell.piece) {
                    // 更新棋子透明度表示血量
                    const healthPercent = piece.health / piece.maxHealth;
                    cell.piece.circle.setAlpha(Math.max(0.3, healthPercent));

                    // 只有玩家棋子才有icon
                    if (cell.piece.icon) {
                        cell.piece.icon.setAlpha(Math.max(0.3, healthPercent));
                    }

                    // 如果血量为0，移除棋子
                    if (piece.health <= 0) {
                        cell.piece.circle.destroy();
                        if (cell.piece.icon) {
                            cell.piece.icon.destroy();
                        }
                        cell.piece = null;
                        gameBoard[row][col] = null;
                    }
                }
            }
        }
    }

    // 重新开始游戏
    function restartGame() {
        // 重置游戏状态
        tacticalGameState = 'placing';
        isPlayerTurn = true;
        currentPlayer = 'player';
        playerPieces = 0;
        enemyPieces = 0;
        selectedPieceType = null;
        currentBattleIndex = 0;
        battleSequence = [];
        gameEnded = false;

        // 清除高亮
        clearAllHighlights.call(this);

        // 清空棋盘数据
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                gameBoard[row][col] = null;
                // 移除棋子
                if (tacticalCells[row][col] && tacticalCells[row][col].piece) {
                    tacticalCells[row][col].piece.circle.destroy();
                    if (tacticalCells[row][col].piece.icon) {
                        tacticalCells[row][col].piece.icon.destroy();
                    }
                    tacticalCells[row][col].piece = null;
                }
            }
        }

        // 重新刷新商店
        refreshShop.call(this);

        // 重置状态文本
        this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
        this.tacticalStatusText.setFill('#ffff00');
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
    }

    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
