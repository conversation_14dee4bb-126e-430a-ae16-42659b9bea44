<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗五子棋</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'playing';
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;

    // 战术棋盘游戏变量
    let tacticalGrid = []; // 6x6的战术棋盘
    let currentPlayer = 'player'; // 当前玩家：'player' 或 'enemy'
    let gameBoard = []; // 棋盘状态：存储棋子信息
    let tacticalGameState = 'placing'; // 'placing'(下棋阶段), 'battle'(战斗阶段), 'finished'
    let isPlayerTurn = true; // 是否轮到玩家
    let tacticalCells = []; // 存储棋盘格子的图形对象
    let selectedPieceType = null; // 当前选中的棋子类型
    let shop = []; // 商店界面
    let playerPieces = 0; // 玩家已下棋子数
    let enemyPieces = 0; // 敌方已下棋子数
    let maxPieces = 8; // 每方最多8个棋子(4x4的一半)
    let totalMaxPieces = 16; // 棋盘总共16个位置
    let battleSequence = []; // 战斗序列
    let currentBattleIndex = 0; // 当前战斗索引
    let isPlayerBattleTurn = true; // 是否轮到玩家战斗回合
    let battlePhase = 'player'; // 'player' 或 'enemy'
    let gameEnded = false; // 游戏是否结束

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 2000,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character2.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

      

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6); // 缩小到0.5
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        player.setDepth(200); // 设置中等层级

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建6x6格子
        createGrid.call(this);
    }

    // 创建波次怪物
    function createWave() {
        // 清除现有怪物
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80; // 怪物间距
        const startX = 450; // 起始X位置
        const startY = player.y; // 与角色底部对齐

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部
            monster.setDepth(100); // 设置较低层级，确保弹窗能显示在上方

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
        }

        monstersKilled = 0;
    }



    // 创建4x4战术棋盘
    function createGrid() {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        // 初始化棋盘数据
        gameBoard = [];
        tacticalCells = [];
        for (let row = 0; row < gridSize; row++) {
            gameBoard[row] = [];
            tacticalCells[row] = [];
            for (let col = 0; col < gridSize; col++) {
                gameBoard[row][col] = null; // null=空位

                // 创建棋盘格子
                const graphics = this.add.graphics();
                graphics.fillStyle(0x34495e); // 深色背景
                graphics.lineStyle(2, 0x3498db); // 蓝色边框
                graphics.fillRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 10);
                graphics.strokeRoundedRect(-cellSize/2, -cellSize/2, cellSize, cellSize, 10);

                // 设置位置
                graphics.x = startX + col * cellSize;
                graphics.y = startY + row * cellSize;

                // 添加属性
                graphics.row = row;
                graphics.col = col;
                graphics.piece = null; // 存储棋子对象

                // 设置为可交互（用于拖放检测）
                graphics.setInteractive(new Phaser.Geom.Rectangle(-cellSize/2, -cellSize/2, cellSize, cellSize), Phaser.Geom.Rectangle.Contains);

                tacticalCells[row][col] = graphics;
            }
        }

        // 创建商店
        createShop.call(this);

        // 创建UI元素
        createUI.call(this);

      

        // 添加游戏状态显示
        this.tacticalStatusText = this.add.text(375, 930, '拖动棋子到棋盘下棋', {
            fontSize: '20px',
            fill: '#ffff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 添加棋子计数显示
        this.pieceCountText = this.add.text(375, 480, `玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`, {
            fontSize: '14px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 添加重新开始按钮
        this.restartButton = this.add.rectangle(375, 1300, 150, 40, 0x3498db);
        this.restartButton.setStrokeStyle(2, 0x2980b9);
        this.restartButton.setInteractive();

        this.restartButtonText = this.add.text(375, 1300, '重新开始', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        this.restartButton.on('pointerdown', () => {
            restartGame.call(this);
        });

        // 按钮悬停效果
        this.restartButton.on('pointerover', () => {
            this.restartButton.setFillStyle(0x2980b9);
        });
        this.restartButton.on('pointerout', () => {
            this.restartButton.setFillStyle(0x3498db);
        });
    }

    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 多重射击：攻击多个目标
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i];

                // 创建攻击特效
                const projectile = this.add.circle(player.x + 30, player.y - 20, 5, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y,
                    duration: 500,
                    delay: i * 100, // 多重射击时添加延迟
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 狂战士效果：生命越低攻击越高
                        if (playerStats.berserker) {
                            const healthPercent = playerHealth / maxPlayerHealth;
                            const berserkerBonus = (1 - healthPercent) * 2; // 最多200%加成
                            damage *= (1 + berserkerBonus);
                        }

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 冰冻效果
                        if (playerStats.freeze && Math.random() < playerStats.freeze) {
                            target.frozen = true;
                            target.frozenTime = 2000; // 冰冻2秒
                            target.setTint(0x87ceeb); // 浅蓝色

                            const freezeText = this.add.text(target.x, target.y - 100, 'FROZEN!', {
                                fontSize: '16px',
                                fill: '#87ceeb',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: freezeText,
                                alpha: 0,
                                y: freezeText.y - 20,
                                duration: 1000,
                                onComplete: () => freezeText.destroy()
                            });
                        }

                        // 毒性效果
                        if (playerStats.poison) {
                            if (!target.poisoned) {
                                target.poisoned = true;
                                target.poisonDamage = Math.floor(damage * 0.3);
                                target.poisonDuration = 5000; // 持续5秒
                                target.setTint(0x9acd32); // 绿色
                            }
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 爆炸攻击效果
                        if (playerStats.explosive) {
                            const explosionRadius = 80;
                            const explosionDamage = Math.floor(damage * 0.5);

                            // 爆炸特效
                            const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6b35, 0.3);
                            this.tweens.add({
                                targets: explosion,
                                scaleX: 1.5,
                                scaleY: 1.5,
                                alpha: 0,
                                duration: 300,
                                onComplete: () => explosion.destroy()
                            });

                            // 对范围内的其他怪物造成伤害
                            monsters.forEach(otherMonster => {
                                if (otherMonster !== target) {
                                    const distance = Phaser.Math.Distance.Between(
                                        target.x, target.y, otherMonster.x, otherMonster.y
                                    );
                                    if (distance <= explosionRadius) {
                                        otherMonster.health -= explosionDamage;

                                        // 爆炸伤害特效
                                        this.tweens.add({
                                            targets: otherMonster,
                                            scaleX: 0.3,
                                            scaleY: 0.3,
                                            duration: 100,
                                            yoyo: true
                                        });
                                    }
                                }
                            });
                        }

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 向前倾斜
            this.tweens.add({
                targets: player,
                rotation: 0.3, // 向前倾斜约17度
                duration: 150,
                yoyo: true,
                ease: 'Power2'
            });

            // 同时添加轻微的缩放效果
            this.tweens.add({
                targets: player,
                scaleX: 0.55,
                scaleY: 0.55,
                duration: 150,
                yoyo: true
            });
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        if (gameState === 'playing') {
            // 在下棋阶段，怪物不攻击
            // 在战斗阶段，由回合制系统控制

            // 只在下棋阶段检查波次完成
            if (tacticalGameState === 'placing') {
                checkWaveComplete.call(this);
            }

            // 检查游戏结束
            if (playerHealth <= 0 && !gameEnded) {
                gameState = 'gameOver';
                gameEnded = true;
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新战术游戏状态显示
        if (this.tacticalStatusText) {
            if (tacticalGameState === 'finished') {
                // 状态已在battleLoop中设置
            } else if (tacticalGameState === 'battle') {
                // 战斗状态的更新在battleLoop中处理
            } else if (tacticalGameState === 'placing') {
                if (isPlayerTurn) {
                    this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
                    this.tacticalStatusText.setFill('#ffffff');
                } else {
                    this.tacticalStatusText.setText('敌方选择棋子中...');
                    this.tacticalStatusText.setFill('#ffff00');
                }
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理毒性伤害
            if (monster.poisoned) {
                monster.poisonDuration -= delta;
                if (monster.poisonDuration <= 0) {
                    monster.poisoned = false;
                    monster.clearTint();
                } else {
                    // 每秒造成毒性伤害
                    if (!monster.lastPoisonDamage) monster.lastPoisonDamage = 0;
                    if (time - monster.lastPoisonDamage > 1000) {
                        monster.health -= monster.poisonDamage;
                        monster.lastPoisonDamage = time;

                        // 毒性伤害特效
                        const poisonText = this.add.text(monster.x, monster.y - 60, `-${monster.poisonDamage}`, {
                            fontSize: '14px',
                            fill: '#9acd32',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: poisonText,
                            alpha: 0,
                            y: poisonText.y - 20,
                            duration: 800,
                            onComplete: () => poisonText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 10000, // 从2500增加到10000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.2, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;

                // 护盾系统
                if (playerStats.shield && playerStats.shield > 0) {
                    const shieldAbsorb = Math.min(damage, playerStats.shield);
                    playerStats.shield -= shieldAbsorb;
                    damage -= shieldAbsorb;

                    // 护盾特效
                    const shieldText = this.add.text(player.x, player.y - 80, `SHIELD -${shieldAbsorb}`, {
                        fontSize: '16px',
                        fill: '#3498db',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: shieldText,
                        alpha: 0,
                        y: shieldText.y - 20,
                        duration: 1000,
                        onComplete: () => shieldText.destroy()
                    });
                }

                // 剩余伤害作用于生命值
                if (damage > 0) {
                    playerHealth -= damage;
                }

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新怪物头顶血条
        updateMonsterHealthBars.call(this);
    }

    // 更新怪物头顶血条
    function updateMonsterHealthBars() {
        // 清除现有血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => bar.destroy());
        }
        this.monsterHealthBars = [];

        // 为每个怪物创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;

                // 血条背景
                const barBg = this.add.rectangle(
                    monster.x,
                    monster.y - 80, // 在怪物头顶上方
                    50,
                    8,
                    0x2c3e50 // 深色背景
                );
                barBg.setStrokeStyle(1, 0x000000); // 黑色边框
                barBg.setDepth(150); // 设置层级，在怪物之上但在弹窗之下
                this.monsterHealthBars.push(barBg);

                // 血条前景
                const currentWidth = 48 * healthPercent; // 减去边框的宽度
                if (currentWidth > 0) {
                    // 血条颜色根据血量变化
                    let barColor = 0x27ae60; // 绿色
                    if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
                    if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

                    const bar = this.add.rectangle(
                        monster.x - 24 + currentWidth / 2, // 从左边开始填充
                        monster.y - 80,
                        currentWidth,
                        6,
                        barColor
                    );
                    bar.setDepth(151); // 比背景稍高
                    this.monsterHealthBars.push(bar);
                }

                // 血量数字（可选，较小字体）
                const healthText = this.add.text(
                    monster.x,
                    monster.y - 95,
                    `${Math.ceil(monster.health)}`,
                    {
                        fontSize: '10px',
                        fill: '#ffffff',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }
                ).setOrigin(0.5);
                healthText.setDepth(152);
                this.monsterHealthBars.push(healthText);
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        if (monsters.length === 0) {
            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
            }, 1000);
        }
    }

    // 棋子类型定义
    const pieceTypes = {
        WARRIOR: {
            name: '强力攻击',
            emoji: '⚔️',
            color: 0xff6b6b,
            attack: 25,
            health: 100,
            skill: 'charge', // 冲锋攻击
            description: '给敌人强力的精准一击',
            isAttackSkill: true
        },
        ARCHER: {
            name: '精准射击',
            emoji: '🏹',
            color: 0x4ecdc4,
            attack: 20,
            health: 70,
            skill: 'multishot', // 多重射击
            description: '发射远程弓箭，可攻击多个目标',
            isAttackSkill: true
        },
        MAGE: {
            name: '魔法弹',
            emoji: '🔮',
            color: 0x9b59b6,
            attack: 30,
            health: 60,
            skill: 'fireball', // 火球术
            description: '发射一个魔法球，范围伤害',
            isAttackSkill: true
        },
        HEALER: {
            name: '治疗',
            emoji: '💚',
            color: 0x2ecc71,
            attack: 10,
            health: 80,
            skill: 'heal', // 治疗
            description: '治疗友军单位80',
            isAttackSkill: false
        },
        TANK: {
            name: '坦克',
            emoji: '🛡️',
            color: 0x95a5a6,
            attack: 15,
            health: 150,
            skill: 'shield', // 护盾
            description: '高血量，提供护盾，抵挡下个回合的攻击的50%',
            isAttackSkill: false
        },
        ASSASSIN: {
            name: '刺客',
            emoji: '🗡️',
            color: 0x34495e,
            attack: 35,
            health: 50,
            skill: 'critical', // 暴击
            description: '隐身后，背刺攻击',
            isAttackSkill: false
        }
    };

    // 创建商店
    function createShop() {
        const shopY = 1050;
        const shopStartX = 175; // 调整起始位置，3个棋子居中
        const shopSpacing = 150; // 增加间距

        // 玩家商店背景
        this.shopBackground = this.add.rectangle(375, shopY+30, 600, 250, 0x2c3e50, 0.8);

        // 初始化商店选项
        refreshShop.call(this);
    }

    // 刷新商店
    function refreshShop() {
        // 显示商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(true);
        }

        // 清除现有商店物品
        shop.forEach(shopItem => {
            if (shopItem.playerPiece) shopItem.playerPiece.destroy();
            if (shopItem.playerIcon) shopItem.playerIcon.destroy();
            if (shopItem.pieceName) shopItem.pieceName.destroy();
            if (shopItem.pieceDesc) shopItem.pieceDesc.destroy();
        });
        shop = [];

        const shopY = 1030;
        const shopSpacing = 180;
        // 计算居中位置：屏幕中心 - (总宽度/2) + 第一个位置偏移
        const totalWidth = (3 - 1) * shopSpacing; // 2个间距的总宽度
        const shopStartX = 375 - totalWidth / 2; // 375是屏幕中心

        // 随机选择3个棋子类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedTypes = [];
        while (selectedTypes.length < 3) {
            const randomKey = pieceTypeKeys[Math.floor(Math.random() * pieceTypeKeys.length)];
            if (!selectedTypes.includes(randomKey)) {
                selectedTypes.push(randomKey);
            }
        }

        selectedTypes.forEach((key, index) => {
            const piece = pieceTypes[key];
            const x = shopStartX + index * shopSpacing;

            // 玩家棋子 - 白色背景，更大尺寸
            const playerPiece = this.add.circle(x, shopY - 10, 35, 0xffffff);
            playerPiece.setStrokeStyle(3, 0x000000);
            playerPiece.setInteractive();
            this.input.setDraggable(playerPiece);

            const playerIcon = this.add.text(x, shopY - 10, piece.emoji, {
                fontSize: '38px'
            }).setOrigin(0.5);

            // 添加棋子名称 - 更大字体
            const pieceName = this.add.text(x, shopY + 35, piece.name, {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 添加技能描述 - 调整字体和换行
            const pieceDesc = this.add.text(x-60, shopY + 65, piece.description, {
                fontSize: '20px',
                fill: '#ffdc35',
                fontFamily: 'Arial',
                wordWrap: { width: 140, useAdvancedWrap: true },
                align: 'center'
            })

            // 拖动事件
            playerPiece.on('dragstart', () => {
                playerPiece.setScale(1.2);
                playerIcon.setScale(1.2);
                selectedPieceType = key;
            });

            playerPiece.on('drag', (pointer, dragX, dragY) => {
                playerPiece.x = dragX;
                playerPiece.y = dragY;
                playerIcon.x = dragX;
                playerIcon.y = dragY;
            });

            playerPiece.on('dragend', () => {
                // 检查是否拖动到棋盘上
                const cellSize = 100;
                const gridSize = 4;
                const gridWidth = gridSize * cellSize;
                const startX = (750 - gridWidth) / 2 + cellSize / 2;
                const startY = 550;

                let placed = false;
                for (let row = 0; row < 4; row++) {
                    for (let col = 0; col < 4; col++) {
                        const cellX = startX + col * cellSize;
                        const cellY = startY + row * cellSize;
                        const distance = Phaser.Math.Distance.Between(playerPiece.x, playerPiece.y, cellX, cellY);

                        if (distance < cellSize/2 && gameBoard[row][col] === null && isPlayerTurn) {
                            placeTacticalPiece.call(this, row, col, 'player', key);
                            placed = true;
                            // 下棋成功后刷新商店
                            refreshShop.call(this);
                            break;
                        }
                    }
                    if (placed) break;
                }

                // 重置位置
                playerPiece.x = x;
                playerPiece.y = shopY - 10;
                playerPiece.setScale(1);
                playerIcon.x = x;
                playerIcon.y = shopY - 10;
                playerIcon.setScale(1);
            });

            shop.push({
                playerPiece,
                playerIcon,
                pieceName,
                pieceDesc,
                type: key
            });
        });
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }

    // 下棋函数
    function placeTacticalPiece(row, col, player, pieceType) {
        if (gameBoard[row][col] !== null) return false;

        const piece = pieceTypes[pieceType];

        // 创建棋子数据
        const pieceData = {
            type: pieceType,
            player: player,
            attack: piece.attack,
            health: piece.health,
            maxHealth: piece.health,
            skill: piece.skill,
            row: row,
            col: col
        };

        // 更新棋盘数据
        gameBoard[row][col] = pieceData;

        // 创建棋子图形
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const pieceX = startX + col * cellSize;
        const pieceY = startY + row * cellSize;

        if (player === 'player') {
            // 玩家棋子：白色背景 + emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0xffffff);
            pieceCircle.setStrokeStyle(2, 0x000000);

            const pieceIcon = this.add.text(pieceX, pieceY, piece.emoji, {
                fontSize: '20px'
            }).setOrigin(0.5);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: pieceIcon, data: pieceData };
        } else {
            // 敌方棋子：纯黑色，无emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0x000000);
            pieceCircle.setStrokeStyle(2, 0xffffff);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: null, data: pieceData };
        }

        // 更新计数
        if (player === 'player') {
            playerPieces++;
        } else {
            enemyPieces++;
        }

        // 更新UI
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);

        // 检查是否棋盘已满，开始战斗
        if (playerPieces + enemyPieces >= totalMaxPieces) {
            startBattle.call(this);
            return true;
        }

        // 检查是否需要切换回合
        if (player === 'player') {
            // 玩家下棋后，如果还有空位且敌方还能下棋，切换到敌方回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = false;
                this.tacticalStatusText.setText('敌方下棋中...');
                setTimeout(() => {
                    enemyMove.call(this);
                }, 1000);
            }
        } else if (player === 'enemy') {
            // 敌方下棋后，如果还有空位且玩家还能下棋，切换到玩家回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = true;
                this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            }
        }

        return true;
    }

    // 敌方AI下棋
    function enemyMove() {
        if (tacticalGameState !== 'placing') return;

        // 随机选择棋子类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedEnemyPieceType = pieceTypeKeys[Math.floor(Math.random() * pieceTypeKeys.length)];

        // 寻找空位
        const emptySpaces = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] === null) {
                    emptySpaces.push({ row, col });
                }
            }
        }

        if (emptySpaces.length > 0) {
            const randomSpace = emptySpaces[Math.floor(Math.random() * emptySpaces.length)];
            placeTacticalPiece.call(this, randomSpace.row, randomSpace.col, 'enemy', selectedEnemyPieceType);
        }
    }



    // 开始战斗
    function startBattle() {
        tacticalGameState = 'battle';
        gameEnded = false;
        currentBattleIndex = 0;
        this.tacticalStatusText.setText('棋盘已满！战斗开始！');

        // 隐藏商店
        shop.forEach(shopItem => {
            shopItem.playerPiece.setVisible(false);
            shopItem.playerIcon.setVisible(false);
            shopItem.pieceName.setVisible(false);
            shopItem.pieceDesc.setVisible(false);
        });
        // 隐藏商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(false);
        }

        // 创建战斗序列（从左上角到右下角）
        battleSequence = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] !== null) {
                    battleSequence.push(gameBoard[row][col]);
                }
            }
        }

        // 开始战斗循环
        setTimeout(() => {
            battleLoop.call(this);
        }, 2000);
    }

    // 战斗循环 - 按格子顺序执行
    function battleLoop() {
        if (tacticalGameState !== 'battle' || gameEnded) return;

        // 检查游戏结束条件
        if (playerHealth <= 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('玩家失败！');
            this.tacticalStatusText.setFill('#ff0000');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        if (monsters.length === 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('所有怪物被击败！玩家获胜！');
            this.tacticalStatusText.setFill('#00ff00');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        // 按格子顺序执行，每个格子一个回合
        if (currentBattleIndex < battleSequence.length) {
            const currentUnit = battleSequence[currentBattleIndex];
            if (currentUnit && currentUnit.health > 0) {
                executeCurrentTurn.call(this, currentUnit);
            }
            currentBattleIndex++;

            // 继续下一个格子
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1500);
        } else {
            // 一轮结束，重新开始
            currentBattleIndex = 0;
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1000);
        }
    }

    // 执行当前回合（按格子顺序）
    function executeCurrentTurn(currentUnit) {
        const pieceType = pieceTypes[currentUnit.type];

        // 高亮当前格子
        highlightCurrentPiece.call(this, currentUnit);

        if (currentUnit.player === 'player') {
            // 玩家棋子（白棋）
            if (pieceType.isAttackSkill) {
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 发动攻击！`);
            } else {
                // 非攻击技能执行辅助效果
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 使用技能！`);
            }
        } else {
            // 敌方棋子（黑棋）
            if (pieceType.isAttackSkill) {
                // 敌方棋子触发怪物攻击
                monsterAttackTriggeredByPiece.call(this, currentUnit);
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 指挥怪物攻击！`);
            } else {
                // 敌方辅助技能（如果有的话）
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 使用技能！`);
            }
        }
    }

    // 执行技能攻击怪物
    function executeSkillOnMonsters(unit) {
        if (monsters.length === 0) return;

        switch (unit.skill) {
            case 'charge': // 战士冲锋 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    performMeleeAttack.call(this, unit, target, unit.attack, '⚔️');
                }
                break;

            case 'multishot': // 弓箭手多重射击 - 远程，攻击多个
                const targets = monsters.slice(0, 2);
                targets.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.7);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🏹');
                });
                break;

            case 'fireball': // 法师火球术 - 远程，群攻
                monsters.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.5);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🔥');
                });
                break;

            case 'heal': // 治疗师治疗玩家
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 30);
                const healText = this.add.text(player.x, player.y - 60, '💚 +30', {
                    fontSize: '16px',
                    fill: '#00ff00',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: healText,
                    alpha: 0,
                    y: healText.y - 30,
                    duration: 1500,
                    onComplete: () => healText.destroy()
                });
                break;

            case 'shield': // 坦克护盾
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 20);
                const shieldText = this.add.text(player.x, player.y - 60, '🛡️ +20', {
                    fontSize: '16px',
                    fill: '#3498db',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: shieldText,
                    alpha: 0,
                    y: shieldText.y - 30,
                    duration: 1500,
                    onComplete: () => shieldText.destroy()
                });
                break;

            case 'critical': // 刺客暴击 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    const damage = unit.attack * 2;
                    performMeleeAttack.call(this, unit, target, damage, '💥');
                }
                break;
        }

        // 移除死亡的怪物并重新排列
        const deadMonsters = [];
        monsters = monsters.filter((monster, index) => {
            if (monster.health <= 0) {
                monster.destroy();
                monstersKilled++;
                deadMonsters.push(index);
                return false;
            }
            return true;
        });

        // 如果有怪物死亡，重新排列剩余怪物
        if (deadMonsters.length > 0) {
            repositionMonsters.call(this);
            updateEnemyHealthBar.call(this); // 更新进度条
            updateMonsterHealthBars.call(this); // 更新怪物血条
        }
    }

    // 执行近战攻击（角色移动）
    function performMeleeAttack(unit, target, damage, icon) {
        // 记录角色原始位置
        const originalX = player.x;
        const originalY = player.y;

        // 移动角色到目标附近
        const targetX = target.x - 80;
        const targetY = target.y;

        this.tweens.add({
            targets: player,
            x: targetX,
            y: targetY,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                // 造成伤害
                target.health -= damage;
                showMonsterDamage.call(this, target, damage, icon);

                // 角色攻击动画
                this.tweens.add({
                    targets: player,
                    rotation: 0.3,
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 返回原位
                setTimeout(() => {
                    this.tweens.add({
                        targets: player,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }, 300);
            }
        });
    }

    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2'
            });
        });
    }

    // 显示怪物伤害
    function showMonsterDamage(monster, damage, icon) {
        const damageText = this.add.text(monster.x, monster.y - 100, `${icon} -${damage}`, {
            fontSize: '16px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        damageText.setDepth(200); // 确保伤害数字显示在血条之上

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: damageText.y - 30,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });

        // 怪物受击效果
        this.tweens.add({
            targets: monster,
            tint: 0xff0000,
            duration: 200,
            yoyo: true,
            onComplete: () => {
                monster.clearTint();
                // 受击后更新血条
                updateMonsterHealthBars.call(this);
            }
        });
    }

    // 敌方棋子触发怪物攻击
    function monsterAttackTriggeredByPiece(unit) {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击
            const damage = unit.attack; // 使用棋子的攻击力

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 150, // 移动到玩家附近
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });
        }
    }

    // 怪物攻击玩家
    function monsterAttack() {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 100,
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    const damage = 15 + currentLevel * 2;
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });

            this.tacticalStatusText.setText('怪物攻击玩家！');
        }
    }

    // 高亮当前攻击的棋子
    function highlightCurrentPiece(unit) {
        // 清除之前的高亮
        clearAllHighlights.call(this);

        // 找到对应的棋盘格子
        const cell = tacticalCells[unit.row][unit.col];
        if (cell && cell.piece) {
            // 创建高亮效果
            const cellSize = 100;
            const gridSize = 4;
            const gridWidth = gridSize * cellSize;
            const startX = (750 - gridWidth) / 2 + cellSize / 2;
            const startY = 550;

            const highlightX = startX + unit.col * cellSize;
            const highlightY = startY + unit.row * cellSize;

            // 创建高亮圆圈
            this.currentHighlight = this.add.circle(highlightX, highlightY, 45, 0xffff00, 0.3);
            this.currentHighlight.setStrokeStyle(3, 0xffff00);

            // 添加脉动效果
            this.tweens.add({
                targets: this.currentHighlight,
                scaleX: 1.2,
                scaleY: 1.2,
                duration: 500,
                yoyo: true,
                repeat: -1
            });
        }
    }

    // 清除所有高亮
    function clearAllHighlights() {
        if (this.currentHighlight) {
            this.currentHighlight.destroy();
            this.currentHighlight = null;
        }
    }

    // 游戏结束后清空棋盘
    function clearBoardAfterGame() {
        // 延迟3秒后清空棋盘，让玩家看到结果
        setTimeout(() => {
            // 清空棋盘数据和图形
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < 4; col++) {
                    if (gameBoard[row][col] !== null) {
                        // 移除棋子图形
                        const cell = tacticalCells[row][col];
                        if (cell && cell.piece) {
                            if (cell.piece.circle) cell.piece.circle.destroy();
                            if (cell.piece.icon) cell.piece.icon.destroy();
                            cell.piece = null;
                        }
                        gameBoard[row][col] = null;
                    }
                }
            }

            // 重置计数
            playerPieces = 0;
            enemyPieces = 0;

            // 重置游戏状态
            tacticalGameState = 'placing';
            isPlayerTurn = true;
            gameEnded = false;
            currentBattleIndex = 0;
            battleSequence = [];

            // 更新UI
            this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
            this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            this.tacticalStatusText.setFill('#ffffff');

            // 重新刷新商店
            refreshShop.call(this);
        }, 3000);
    }

    // 显示伤害
    function showDamage(unit, damage, icon) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const damageText = this.add.text(x, y - 40, `${icon} -${damage}`, {
            fontSize: '14px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });
    }

    // 显示治疗
    function showHeal(unit, heal) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const healText = this.add.text(x, y - 40, `💚 +${heal}`, {
            fontSize: '14px',
            fill: '#00ff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: healText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => healText.destroy()
        });
    }

    // 更新棋子显示
    function updatePieceDisplay() {
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                const piece = gameBoard[row][col];
                const cell = tacticalCells[row][col];

                if (piece && cell.piece) {
                    // 更新棋子透明度表示血量
                    const healthPercent = piece.health / piece.maxHealth;
                    cell.piece.circle.setAlpha(Math.max(0.3, healthPercent));

                    // 只有玩家棋子才有icon
                    if (cell.piece.icon) {
                        cell.piece.icon.setAlpha(Math.max(0.3, healthPercent));
                    }

                    // 如果血量为0，移除棋子
                    if (piece.health <= 0) {
                        cell.piece.circle.destroy();
                        if (cell.piece.icon) {
                            cell.piece.icon.destroy();
                        }
                        cell.piece = null;
                        gameBoard[row][col] = null;
                    }
                }
            }
        }
    }

    // 重新开始游戏
    function restartGame() {
        // 重置游戏状态
        tacticalGameState = 'placing';
        isPlayerTurn = true;
        currentPlayer = 'player';
        playerPieces = 0;
        enemyPieces = 0;
        selectedPieceType = null;
        currentBattleIndex = 0;
        battleSequence = [];
        gameEnded = false;

        // 清除高亮
        clearAllHighlights.call(this);

        // 清空棋盘数据
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                gameBoard[row][col] = null;
                // 移除棋子
                if (tacticalCells[row][col] && tacticalCells[row][col].piece) {
                    tacticalCells[row][col].piece.circle.destroy();
                    if (tacticalCells[row][col].piece.icon) {
                        tacticalCells[row][col].piece.icon.destroy();
                    }
                    tacticalCells[row][col].piece = null;
                }
            }
        }

        // 重新刷新商店
        refreshShop.call(this);

        // 重置状态文本
        this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
        this.tacticalStatusText.setFill('#ffff00');
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
