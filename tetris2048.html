<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>俄罗斯方块弹球</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        #game-container {
            /* border: 3px solid #333; */
            border-radius: 10px;
            /* box-shadow: 0 0 20px rgba(0,0,0,0.5); */
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 防止多次初始化
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            // 逻辑分辨率（放大游戏区域）
            const GAME_WIDTH = 600;
            const GAME_HEIGHT = 900;

            // Phaser配置，直接用逻辑分辨率
            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000000',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let gameGrid = [];
            let numberGrid = []; // 存储每个格子的数字
            let currentPiece = null;
            let nextPiece = null;
            let score = 0;
            let level = 1;
            // 记录每种方块被弹球消除的数量
            let blockCollectCount = [0, 0, 0, 0, 0, 0, 0, 0]; // 0号不用
            let dropTime = 0;
            let dropInterval = 1000;
            let gameOver = false;
            let isPaused = false;
            let fastDrop = false; // 快速下落状态
            let isMerging = false; // 是否正在合成
            let mergeAnimationStartTime = 0; // 合成动画开始时间
            let mergeAnimations = []; // 合成动画数组
            let mergeParticles = []; // 合成粒子效果
            let isGravityAnimating = false; // 是否正在重力动画
            let gravityAnimationStartTime = 0; // 重力动画开始时间
            let gravityAnimations = []; // 重力动画数组

            // 动画参数
            const MERGE_ANIMATION_DURATION = 300; // 合成动画时长
            const GRAVITY_ANIMATION_DURATION = 400; // 重力下落动画时长

            // 游戏区域配置（放大格子并居中）
            const GRID_WIDTH = 10;
            const GRID_HEIGHT = 19;
            const CELL_SIZE = 40;
            const GRID_X = Math.floor((GAME_WIDTH - GRID_WIDTH * CELL_SIZE) / 2) - 80;
            const GRID_Y = 120;

            // 方块颜色
            const COLORS = [
                0x000000, // 空
                0xFF0000, // 红色 - I
                0x00FF00, // 绿色 - O  
                0x0000FF, // 蓝色 - T
                0xFFFF00, // 黄色 - S
                0xFF00FF, // 紫色 - Z
                0x00FFFF, // 青色 - J
                0xFFA500  // 橙色 - L
            ];

            // 方块形状定义
            const PIECES = [
                // I 形状
                {
                    shape: [
                        [0,0,0,0],
                        [1,1,1,1],
                        [0,0,0,0],
                        [0,0,0,0]
                    ],
                    color: 1
                },
                // O 形状
                {
                    shape: [
                        [2,2],
                        [2,2]
                    ],
                    color: 2
                },
                // T 形状
                {
                    shape: [
                        [0,3,0],
                        [3,3,3],
                        [0,0,0]
                    ],
                    color: 3
                },
                // S 形状
                {
                    shape: [
                        [0,4,4],
                        [4,4,0],
                        [0,0,0]
                    ],
                    color: 4
                },
                // Z 形状
                {
                    shape: [
                        [5,5,0],
                        [0,5,5],
                        [0,0,0]
                    ],
                    color: 5
                },
                // J 形状
                {
                    shape: [
                        [6,0,0],
                        [6,6,6],
                        [0,0,0]
                    ],
                    color: 6
                },
                // L 形状
                {
                    shape: [
                        [0,0,7],
                        [7,7,7],
                        [0,0,0]
                    ],
                    color: 7
                }
            ];

            let scene;
            let graphics;
            // let scoreText, levelText, linesText;
            // let leftButton, rightButton, rotateButton, downButton, pauseButton;
            let parentSizer, logicSizer;
            let numberTexts = []; // 数字文本对象池

            // 多弹球相关变量
            // let balls = [];
            // function createBall() {
            //     // 随机初始方向
            //     let angle = Math.random() * Math.PI * 2;
            //     let speed = 4;
            //     return {
            //         x: GRID_X + (GRID_WIDTH * CELL_SIZE) / 2,
            //         y: GRID_Y + (GRID_HEIGHT * CELL_SIZE) / 2,
            //         vx: Math.cos(angle) * speed,
            //         vy: Math.sin(angle) * speed,
            //         radius: 12,
            //         color: 0xffffff
            //     };
            // }

            // 粒子特效数组
            // let effectParticles = [];

            // 音效相关
            // let sound_bgm, sound_hit, sound_drop, sound_clear;

            function preload() {
                scene = this;
                // 加载音效
                // this.load.audio('bgm', ['sound/俄罗斯方块/背景音乐.mp3']);
                // this.load.audio('hit', ['sound/俄罗斯方块/碰撞.mp3']);
                // this.load.audio('drop', ['sound/俄罗斯方块/下落.mp3']);
                // this.load.audio('clear', ['sound/俄罗斯方块/消除一行.mp3']);
            }

            function create() {
                graphics = scene.add.graphics();
                // 音效对象
                // sound_bgm = scene.sound.add('bgm', { loop: true, volume: 0.5 });
                // sound_hit = scene.sound.add('hit', { volume: 1 });
                // sound_drop = scene.sound.add('drop', { volume: 1 });
                // sound_clear = scene.sound.add('clear', { volume: 1 });
                // 播放背景音乐
                // if (!sound_bgm.isPlaying) sound_bgm.play();

                // 逻辑区域和父容器的 Size 适配
                parentSizer = new Phaser.Structs.Size(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer = new Phaser.Structs.Size(GAME_WIDTH, GAME_HEIGHT, Phaser.Structs.Size.FIT, parentSizer);
                parentSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);

                // 监听resize事件
                this.scale.on('resize', resizeGame, this);
                
                // 初始化游戏网格
                initGrid();
                
                // 创建UI
                createUI();
                
                // 生成第一个方块
                spawnPiece();
                
                // 设置输入
                setupInput();

                // 初始化弹球数组
                // balls = [createBall()];
            }

            function resizeGame(gameSize) {
                if (!parentSizer || !logicSizer) return;
                const width = gameSize.width;
                const height = gameSize.height;
                parentSizer.setSize(width, height);
                logicSizer.setSize(width, height);
                // 这里可以统一调整 camera、container、layer 的缩放和位置（如有）
                // 目前所有元素都用逻辑坐标，Phaser会自动缩放
            }

            function initGrid() {
                gameGrid = [];
                numberGrid = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    gameGrid[y] = [];
                    numberGrid[y] = [];
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        gameGrid[y][x] = 0;
                        numberGrid[y][x] = 0;
                    }
                }
            }

            // 右侧面板X坐标，提升为全局变量，供createUI和render使用
            const rightPanelX = GAME_WIDTH - 160;
            function createUI() {
                // 标题
                scene.add.text(GAME_WIDTH * 0.5, 40, '俄罗斯方块2048', {
                    fontSize: Math.floor(40) + 'px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    resolution: window.devicePixelRatio
                }).setOrigin(0.5);

                // 每种方块收集数量统计
                // 传统俄罗斯方块顶部信息
                scene.scoreText = scene.add.text(60, 80, `积分: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                scene.levelText = scene.add.text(220, 80, `等级: 1`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ff00ff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                // 下一个方块预览区域（放在右上角）
                scene.add.text(rightPanelX, 130, '下一个:', {
                    fontSize: Math.floor(20) + 'px',
                    fill: '#ffffff'
                });
                // 玩法说明
                const ruleY = 220;
                const ruleX = rightPanelX;
                const ruleText = [
                    '玩法说明:',
                    '1. 键盘←→移动',
                    '2. ↑旋转，↓加速下落',
                    '3. 空格硬降落',
                    '4. 方块带数字(2,4,8)',
                    '5. 相邻相同数字合成',
                    '6. 合成后自动重力下落',
                    '7. 目标：合成2048!'
                ];
                for (let i = 0; i < ruleText.length; i++) {
                    scene.add.text(ruleX, ruleY + i * 26, ruleText[i], {
                        fontSize: Math.floor(i === 0 ? 18 : 16) + 'px',
                        fill: i === 0 ? '#ffff00' : '#ffffff'
                    });
                }
            }

            // 已移除 createControls 函数

            function setupInput() {
                // 键盘控制
                const cursors = scene.input.keyboard.createCursorKeys();
                const wasd = scene.input.keyboard.addKeys('W,S,A,D,SPACE,P');

                scene.input.keyboard.on('keydown', (event) => {
                    if (gameOver || isPaused || isMerging || isGravityAnimating) return;

                    switch(event.code) {
                        case 'ArrowLeft':
                        case 'KeyA':
                            movePiece(-1, 0);
                            break;
                        case 'ArrowRight':
                        case 'KeyD':
                            movePiece(1, 0);
                            break;
                        case 'ArrowDown':
                        case 'KeyS':
                            startFastDrop();
                            break;
                        case 'ArrowUp':
                        case 'KeyW':
                            rotatePiece();
                            break;
                        case 'Space':
                            hardDrop();
                            break;
                        case 'KeyP':
                            togglePause();
                            break;
                    }
                });

                scene.input.keyboard.on('keyup', (event) => {
                    switch(event.code) {
                        case 'ArrowDown':
                        case 'KeyS':
                            stopFastDrop();
                            break;
                    }
                });
            }

            function startFastDrop() {
                if (gameOver || isPaused || isMerging || isGravityAnimating) return;
                fastDrop = true;
            }

            function stopFastDrop() {
                fastDrop = false;
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }
                
                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;
                
                nextPiece = createRandomPiece();
                
                // 检查游戏结束
                if (checkCollision(currentPiece, 0, 0)) {
                    gameOver = true;
                    showGameOver();
                }
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                const piece = {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0,
                    y: 0,
                    numbers: [] // 存储每个方块的数字
                };

                // 为每个方块分配数字（2的幂次）
                for (let y = 0; y < piece.shape.length; y++) {
                    piece.numbers[y] = [];
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            // 随机生成2, 4, 8中的一个数字
                            const numbers = [2, 4, 8];
                            piece.numbers[y][x] = numbers[Math.floor(Math.random() * numbers.length)];
                        } else {
                            piece.numbers[y][x] = 0;
                        }
                    }
                }

                return piece;
            }

            function movePiece(dx, dy) {
                if (gameOver || isPaused || !currentPiece || isMerging || isGravityAnimating) return;

                if (!checkCollision(currentPiece, dx, dy)) {
                    currentPiece.x += dx;
                    currentPiece.y += dy;
                    return true;
                }
                return false;
            }

            function rotatePiece() {
                if (gameOver || isPaused || !currentPiece || isMerging || isGravityAnimating) return;

                const rotatedShape = rotateMatrix(currentPiece.shape);
                const rotatedNumbers = rotateMatrix(currentPiece.numbers);
                const originalShape = currentPiece.shape;
                const originalNumbers = currentPiece.numbers;

                currentPiece.shape = rotatedShape;
                currentPiece.numbers = rotatedNumbers;

                // 检查旋转后是否有碰撞
                if (checkCollision(currentPiece, 0, 0)) {
                    // 尝试向左或向右移动来避免碰撞
                    if (!checkCollision(currentPiece, -1, 0)) {
                        currentPiece.x -= 1;
                    } else if (!checkCollision(currentPiece, 1, 0)) {
                        currentPiece.x += 1;
                    } else {
                        // 无法旋转，恢复原状
                        currentPiece.shape = originalShape;
                        currentPiece.numbers = originalNumbers;
                    }
                }
            }

            function rotateMatrix(matrix) {
                const rows = matrix.length;
                const cols = matrix[0].length;
                const rotated = [];

                for (let i = 0; i < cols; i++) {
                    rotated[i] = [];
                    for (let j = 0; j < rows; j++) {
                        rotated[i][j] = matrix[rows - 1 - j][i];
                    }
                }
                return rotated;
            }

            function checkCollision(piece, dx, dy) {
                const newX = piece.x + dx;
                const newY = piece.y + dy;

                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const gridX = newX + x;
                            const gridY = newY + y;

                            // 检查边界
                            if (gridX < 0 || gridX >= GRID_WIDTH || gridY >= GRID_HEIGHT) {
                                return true;
                            }

                            // 检查与已有方块的碰撞
                            if (gridY >= 0 && gameGrid[gridY][gridX] !== 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            function placePiece() {
                if (!currentPiece) return;

                // 停止快速下落
                fastDrop = false;

                // 将当前方块放置到网格中
                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x] !== 0) {
                            const gridX = currentPiece.x + x;
                            const gridY = currentPiece.y + y;
                            if (gridY >= 0) {
                                gameGrid[gridY][gridX] = currentPiece.color;
                                numberGrid[gridY][gridX] = currentPiece.numbers[y][x];
                            }
                        }
                    }
                }

                // 关键修复：消除行动画期间不再渲染currentPiece，避免覆盖闪烁
                currentPiece = null;

                // 进行数字合成，合成后自动重力填补
                performMerge(() => {
                    // 合成完成后生成新方块
                    spawnPiece();
                });
            }

            // 数字合成功能 - 支持连续合成
            function performMerge(callback) {
                performSingleMergeRound(callback, 1);
            }

            // 执行单轮合成
            function performSingleMergeRound(callback, roundNumber) {
                let totalMerged = false;
                let currentRoundAnimations = []; // 当前轮次的动画



                let mergedThisRound = new Set(); // 记录本轮已经合成过的位置

                // 检查所有相邻的相同数字
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (numberGrid[y][x] === 0) continue;

                        const currentNumber = numberGrid[y][x];
                        const posKey = `${x},${y}`;

                        if (mergedThisRound.has(posKey)) continue;



                        // 检查右边是否有相同数字
                        if (x < GRID_WIDTH - 1 &&
                            numberGrid[y][x + 1] === currentNumber &&
                            !mergedThisRound.has(`${x+1},${y}`)) {



                            // 合成到左边的格子（优先左）
                            const targetX = x;
                            const targetY = y;
                            const sourceX = x + 1;
                            const sourceY = y;

                            // 创建合成动画
                            currentRoundAnimations.push({
                                sourceX: sourceX,
                                sourceY: sourceY,
                                targetX: targetX,
                                targetY: targetY,
                                number: currentNumber,
                                newNumber: currentNumber * 2,
                                startTime: Date.now(),
                                round: roundNumber
                            });

                            // 创建粒子爆炸效果
                            createMergeParticles(sourceX, sourceY, targetX, targetY, currentNumber);

                            // 设置目标位置的新数字
                            numberGrid[targetY][targetX] = currentNumber * 2;
                            numberGrid[sourceY][sourceX] = 0;
                            gameGrid[sourceY][sourceX] = 0;

                            mergedThisRound.add(posKey);
                            mergedThisRound.add(`${sourceX},${sourceY}`);
                            totalMerged = true;

                            // 更新分数 - 连续合成有额外奖励
                            const bonus = roundNumber > 1 ? roundNumber * 10 : 0;
                            score += currentNumber * 2 + bonus;

                            // 检查是否达到2048
                            if (currentNumber * 2 >= 2048) {
                                showWinMessage();
                            }
                        }
                        // 检查下面是否有相同数字
                        else if (y < GRID_HEIGHT - 1 &&
                                 numberGrid[y + 1][x] === currentNumber &&
                                 !mergedThisRound.has(`${x},${y+1}`)) {



                            // 合成到下面的格子（优先下）
                            const targetX = x;
                            const targetY = y + 1;
                            const sourceX = x;
                            const sourceY = y;

                            // 创建合成动画
                            currentRoundAnimations.push({
                                sourceX: sourceX,
                                sourceY: sourceY,
                                targetX: targetX,
                                targetY: targetY,
                                number: currentNumber,
                                newNumber: currentNumber * 2,
                                startTime: Date.now(),
                                round: roundNumber
                            });

                            // 创建粒子爆炸效果
                            createMergeParticles(sourceX, sourceY, targetX, targetY, currentNumber);

                            // 设置目标位置的新数字
                            numberGrid[targetY][targetX] = currentNumber * 2;
                            numberGrid[sourceY][sourceX] = 0;
                            gameGrid[sourceY][sourceX] = 0;

                            mergedThisRound.add(posKey);
                            mergedThisRound.add(`${targetX},${targetY}`);
                            totalMerged = true;

                            // 更新分数 - 连续合成有额外奖励
                            const bonus = roundNumber > 1 ? roundNumber * 10 : 0;
                            score += currentNumber * 2 + bonus;

                            // 检查是否达到2048
                            if (currentNumber * 2 >= 2048) {
                                showWinMessage();
                            }
                        }
                    }
                }



                if (totalMerged) {
                    // 将当前轮次的动画添加到总动画列表
                    mergeAnimations = mergeAnimations.concat(currentRoundAnimations);

                    // 更新UI分数
                    if (scene && scene.scoreText) {
                        scene.scoreText.setText(`积分: ${score}`);
                    }

                    // 显示连击提示
                    if (roundNumber > 1) {
                        showComboMessage(roundNumber, currentRoundAnimations.length);
                    }


                    // 开始合成动画
                    isMerging = true;
                    mergeAnimationStartTime = Date.now();

                    // 动画结束后检查是否还有可合成的
                    setTimeout(() => {
                        isMerging = false;

                        // 短暂延迟后应用重力并检查下一轮合成
                        setTimeout(() => {
                            // 先应用重力填补空格
                            applyGravity(() => {
                                // 重力动画完成后，检查是否还有可以合成的数字
                                if (hasMoreMerges()) {
                                    // 继续下一轮合成
                                    performSingleMergeRound(callback, roundNumber + 1);
                                } else {
                                    // 所有合成完成
                                    mergeAnimations = [];
                                    if (callback) callback();
                                }
                            });
                        }, 100); // 100ms延迟确保渲染稳定
                    }, MERGE_ANIMATION_DURATION);
                } else {
                    // 没有合成，结束
                    if (callback) callback();
                }
            }

            // 应用重力，让方块下落填补空格（带动画）
            function applyGravity(callback) {
                gravityAnimations = [];
                let hasMovement = false;

                // 从下往上，从左往右处理每一列
                for (let x = 0; x < GRID_WIDTH; x++) {
                    // 收集当前列的所有非空方块及其位置
                    let columnData = [];

                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        if (gameGrid[y][x] !== 0) {
                            columnData.push({
                                color: gameGrid[y][x],
                                number: numberGrid[y][x],
                                originalY: y
                            });
                        }
                    }

                    // 计算新位置并创建动画
                    for (let i = 0; i < columnData.length; i++) {
                        const targetY = GRID_HEIGHT - 1 - i;
                        const block = columnData[columnData.length - 1 - i];

                        if (block.originalY !== targetY) {
                            // 需要移动，创建动画
                            gravityAnimations.push({
                                x: x,
                                sourceY: block.originalY,
                                targetY: targetY,
                                color: block.color,
                                number: block.number
                            });
                            hasMovement = true;
                        }
                    }

                    // 清空当前列
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        gameGrid[y][x] = 0;
                        numberGrid[y][x] = 0;
                    }

                    // 设置最终位置（动画结束后的状态）
                    for (let i = 0; i < columnData.length; i++) {
                        const targetY = GRID_HEIGHT - 1 - i;
                        const block = columnData[columnData.length - 1 - i];
                        gameGrid[targetY][x] = block.color;
                        numberGrid[targetY][x] = block.number;
                    }
                }

                if (hasMovement) {
                    // 开始重力动画
                    isGravityAnimating = true;
                    gravityAnimationStartTime = Date.now();

                    setTimeout(() => {
                        isGravityAnimating = false;
                        gravityAnimations = [];
                        if (callback) callback();
                    }, GRAVITY_ANIMATION_DURATION);
                } else {
                    // 无需动画，直接回调
                    if (callback) callback();
                }
            }

            // 检查是否还有可以合成的数字
            function hasMoreMerges() {
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (numberGrid[y][x] === 0) continue;

                        const currentNumber = numberGrid[y][x];

                        // 检查右边
                        if (x < GRID_WIDTH - 1 && numberGrid[y][x + 1] === currentNumber) {
                            return true;
                        }
                        // 检查下面
                        if (y < GRID_HEIGHT - 1 && numberGrid[y + 1][x] === currentNumber) {
                            return true;
                        }
                    }
                }
                return false;
            }

            // 显示连击消息
            function showComboMessage(comboCount, mergeCount) {
                const comboText = scene.add.text(
                    GAME_WIDTH * 0.5,
                    GAME_HEIGHT * 0.3,
                    `${comboCount}连击! +${mergeCount}合成!`,
                    {
                        fontSize: '32px',
                        fill: '#FFD700',
                        fontStyle: 'bold',
                        stroke: '#FF6B6B',
                        strokeThickness: 3
                    }
                ).setOrigin(0.5);

                // 添加缩放和淡出动画
                scene.tweens.add({
                    targets: comboText,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    alpha: 0,
                    duration: 1500,
                    ease: 'Power2',
                    onComplete: () => {
                        comboText.destroy();
                    }
                });
            }

            // 处理空行下落
            function dropEmptyRows(callback) {
                let hasEmptyRows = false;

                // 检查是否有完全空的行
                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    let isEmpty = true;
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] !== 0) {
                            isEmpty = false;
                            break;
                        }
                    }

                    if (isEmpty) {
                        // 移除空行
                        gameGrid.splice(y, 1);
                        numberGrid.splice(y, 1);
                        // 在顶部添加新的空行
                        gameGrid.unshift(new Array(GRID_WIDTH).fill(0));
                        numberGrid.unshift(new Array(GRID_WIDTH).fill(0));
                        hasEmptyRows = true;
                        y++; // 重新检查当前行
                    }
                }

                if (hasEmptyRows) {
                    // 播放下落动画
                    setTimeout(() => {
                        if (callback) callback();
                    }, FALL_ANIMATION_DURATION);
                } else {
                    if (callback) callback();
                }
            }

            // 创建合成粒子效果 - 增强版
            function createMergeParticles(sourceX, sourceY, targetX, targetY, number) {
                const centerX = GRID_X + targetX * CELL_SIZE + CELL_SIZE / 2;
                const centerY = GRID_Y + targetY * CELL_SIZE + CELL_SIZE / 2;

                // 根据数字大小决定粒子颜色和效果强度
                let particleColor = 0xFFD700; // 金色
                let particleCount = 20;
                let shockwaveSize = CELL_SIZE * 2;

                if (number >= 128) {
                    particleColor = 0xFF1493; // 深粉色
                    particleCount = 40;
                    shockwaveSize = CELL_SIZE * 3;
                } else if (number >= 64) {
                    particleColor = 0xFF6B6B; // 红色
                    particleCount = 35;
                    shockwaveSize = CELL_SIZE * 2.5;
                } else if (number >= 32) {
                    particleColor = 0xFF8C00; // 橙色
                    particleCount = 30;
                    shockwaveSize = CELL_SIZE * 2.2;
                } else if (number >= 16) {
                    particleColor = 0x4ECDC4; // 青色
                    particleCount = 25;
                } else if (number >= 8) {
                    particleColor = 0x45B7D1; // 蓝色
                } else if (number >= 4) {
                    particleColor = 0x98FB98; // 浅绿色
                }

                // 创建多个粒子
                for (let i = 0; i < particleCount; i++) {
                    const angle = (Math.PI * 2 * i) / particleCount + Math.random() * 0.5;
                    const speed = 3 + Math.random() * 5;
                    const size = 3 + Math.random() * 8;
                    const life = 25 + Math.random() * 25;

                    mergeParticles.push({
                        x: centerX,
                        y: centerY,
                        vx: Math.cos(angle) * speed,
                        vy: Math.sin(angle) * speed,
                        size: size,
                        life: life,
                        maxLife: life,
                        color: particleColor,
                        gravity: 0.08 + Math.random() * 0.04
                    });
                }

                // 创建多层冲击波效果
                for (let wave = 0; wave < 3; wave++) {
                    mergeParticles.push({
                        x: centerX,
                        y: centerY,
                        vx: 0,
                        vy: 0,
                        size: 0,
                        life: 25 - wave * 5,
                        maxLife: 25 - wave * 5,
                        color: wave === 0 ? 0xFFFFFF : particleColor,
                        isShockwave: true,
                        maxSize: shockwaveSize * (1 + wave * 0.3),
                        delay: wave * 3
                    });
                }

                // 为高数字添加星星效果
                if (number >= 32) {
                    for (let i = 0; i < 8; i++) {
                        const angle = (Math.PI * 2 * i) / 8;
                        const distance = CELL_SIZE * 0.8;

                        mergeParticles.push({
                            x: centerX + Math.cos(angle) * distance,
                            y: centerY + Math.sin(angle) * distance,
                            vx: Math.cos(angle) * 2,
                            vy: Math.sin(angle) * 2,
                            size: 6,
                            life: 40,
                            maxLife: 40,
                            color: 0xFFFFFF,
                            gravity: 0,
                            isStar: true
                        });
                    }
                }
            }

            function showWinMessage() {
                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 - 100, '恭喜！达到2048！', {
                    fontSize: Math.floor(36) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('winText');
            }

            // 移除了整行消除功能，改为重力填补

            function hardDrop() {
                if (gameOver || isPaused || !currentPiece || isMerging || isGravityAnimating) return;

                let dropDistance = 0;
                while (!checkCollision(currentPiece, 0, 1)) {
                    currentPiece.y++;
                    dropDistance++;
                }

                if (dropDistance > 0) {
                    // 硬降落得分奖励
                    score += dropDistance * 2;
                    // 播放下落音效
                    // if (sound_drop) sound_drop.play();
                }

                placePiece();
            }

            function togglePause() {
                isPaused = !isPaused;
                if (isPaused) {
                    scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏暂停', {
                        fontSize: Math.floor(48) + 'px',
                        fill: '#ffff00',
                        fontStyle: 'bold'
                    }).setOrigin(0.5).setName('pauseText');
                    // 游戏暂停时停止背景音乐
                    // stopBgm();
                } else {
                    const pauseText = scene.children.getByName('pauseText');
                    if (pauseText) pauseText.destroy();
                    // 游戏继续时重新播放背景音乐
                    // if (sound_bgm) sound_bgm.play();
                }
            }

            function showGameOver() {
                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏结束', {
                    fontSize: Math.floor(48) + 'px',
                    fill: '#ff0000',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('gameOverText');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 60, '点击重新开始', {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5).setName('restartText');

                scene.input.once('pointerdown', restartGame);
                // 游戏结束时停止背景音乐
                // stopBgm();
            }

            function restartGame() {
                score = 0;
                level = 1;
                dropTime = 0;
                dropInterval = 1000;
                gameOver = false;
                isPaused = false;
                isMerging = false;
                isGravityAnimating = false;
                mergeAnimations = [];
                mergeParticles = [];
                gravityAnimations = [];

                // 重置顶部统计
                if (scene && scene.scoreText && scene.levelText) {
                    scene.scoreText.setText('积分: 0');
                    scene.levelText.setText('等级: 1');
                }

                initGrid();
                spawnPiece();

                // 清除游戏结束和重新开始文本
                const gameOverText = scene.children.getByName('gameOverText');
                if (gameOverText) gameOverText.destroy();
                const restartText = scene.children.getByName('restartText');
                if (restartText) restartText.destroy();
                const winText = scene.children.getByName('winText');
                if (winText) winText.destroy();
                // 重新开始游戏时重新播放背景音乐
                // if (sound_bgm) sound_bgm.play();
            }

            function update(time, delta) {
                if (gameOver || isPaused || isMerging || isGravityAnimating) return;

                dropTime += delta;

                // 快速下落时使用更短的间隔
                const currentDropInterval = fastDrop ? Math.max(50, dropInterval / 10) : dropInterval;

                if (dropTime >= currentDropInterval) {
                    if (currentPiece) {
                        if (!movePiece(0, 1)) {
                            placePiece();
                            // 快速下落结束时给予额外分数
                            if (fastDrop) {
                                score += 1;
                                // scoreText.setText('分数: ' + score);
                            }
                        } else if (fastDrop) {
                            // 快速下落时给予分数
                            score += 1;
                            // scoreText.setText('分数: ' + score);
                        }
                    }
                    dropTime = 0;
                }

                // 多弹球运动与碰撞检测
                // for (let ball of balls) {
                //     // 运动
                //     ball.x += ball.vx;
                //     ball.y += ball.vy;

                //     // 边界反弹（只在格子区域内）
                //     if (ball.x - ball.radius < GRID_X) {
                //         ball.x = GRID_X + ball.radius;
                //         ball.vx *= -1;
                //     }
                //     if (ball.x + ball.radius > GRID_X + GRID_WIDTH * CELL_SIZE) {
                //         ball.x = GRID_X + GRID_WIDTH * CELL_SIZE - ball.radius;
                //         ball.vx *= -1;
                //     }
                //     if (ball.y - ball.radius < GRID_Y) {
                //         ball.y = GRID_Y + ball.radius;
                //         ball.vy *= -1;
                //     }
                //     if (ball.y + ball.radius > GRID_Y + GRID_HEIGHT * CELL_SIZE) {
                //         ball.y = GRID_Y + GRID_HEIGHT * CELL_SIZE - ball.radius;
                //         ball.vy *= -1;
                //     }

                //     // 与已停止方块的碰撞检测
                //     // 只检测 gameGrid，不检测 currentPiece
                //     let gridX = Math.floor((ball.x - GRID_X) / CELL_SIZE);
                //     let gridY = Math.floor((ball.y - GRID_Y) / CELL_SIZE);
                //     if (
                //         gridX >= 0 && gridX < GRID_WIDTH &&
                //         gridY >= 0 && gridY < GRID_HEIGHT &&
                //         gameGrid[gridY][gridX] !== 0
                //     ) {
                //         // 播放碰撞音效
                //         // if (sound_hit) sound_hit.play();
                //         // 消除方块
                //         let colorIdx = gameGrid[gridY][gridX];
                //         let color = COLORS[colorIdx];
                //         gameGrid[gridY][gridX] = 0;
                //         // 统计收集数量
                //         blockCollectCount[colorIdx]++;
                //         // 更新UI
                //         if (scene.blockStatTexts && scene.blockStatTexts[colorIdx]) {
                //             scene.blockStatTexts[colorIdx].setText(`${blockCollectCount[colorIdx]}`);
                //             // 抖动特效
                //             if (scene.blockStatRects && scene.blockStatRects[colorIdx]) {
                //                 let rect = scene.blockStatRects[colorIdx];
                //                 let text = scene.blockStatTexts[colorIdx];
                //                 scene.tweens.add({
                //                     targets: [rect, text],
                //                     scaleX: 1.3,
                //                     scaleY: 1.3,
                //                     duration: 80,
                //                     yoyo: true,
                //                     ease: 'Quad.easeInOut',
                //                     onComplete: () => {
                //                         rect.setScale(1,1);
                //                         text.setScale(1,1);
                //                     }
                //                 });
                //             }
                //         }
                //         // 反弹（简单处理：反转y速度）
                //         ball.vy *= -1;
                //         // 生成爆炸粒子特效
                //         spawnBlockParticles(gridX, gridY, color);
                //     }
                // }

                // 更新粒子特效
                // for (let i = effectParticles.length - 1; i >= 0; i--) {
                //     let p = effectParticles[i];
                //     p.x += p.vx;
                //     p.y += p.vy;
                //     p.life--;
                //     if (p.life <= 0) {
                //         effectParticles.splice(i, 1);
                //     }
                // }

                render();
            }

            function render() {
                graphics.clear();

                // 清理之前的数字文本
                numberTexts.forEach(text => {
                    if (text && text.active) {
                        text.destroy();
                    }
                });
                numberTexts = [];

                // 快速下落时的背景效果
                if (fastDrop) {
                    graphics.fillStyle(0x444444, 0.3);
                    graphics.fillRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);
                }

                // 绘制游戏区域边框
                graphics.lineStyle(Math.floor(3), fastDrop ? 0x00ff00 : 0xffffff);
                graphics.strokeRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);

                // 绘制网格背景
                graphics.lineStyle(Math.max(1, Math.floor(1)), 0x333333);
                for (let x = 0; x <= GRID_WIDTH; x++) {
                    graphics.moveTo(GRID_X + x * CELL_SIZE, GRID_Y);
                    graphics.lineTo(GRID_X + x * CELL_SIZE, GRID_Y + GRID_HEIGHT * CELL_SIZE);
                }
                for (let y = 0; y <= GRID_HEIGHT; y++) {
                    graphics.moveTo(GRID_X, GRID_Y + y * CELL_SIZE);
                    graphics.lineTo(GRID_X + GRID_WIDTH * CELL_SIZE, GRID_Y + y * CELL_SIZE);
                }
                graphics.strokePath();

                // 绘制重力下落动画
                if (isGravityAnimating && gravityAnimations.length > 0) {
                    const currentTime = Date.now();
                    const elapsed = currentTime - gravityAnimationStartTime;
                    const progress = Math.min(elapsed / GRAVITY_ANIMATION_DURATION, 1);
                    const easeProgress = 1 - Math.pow(1 - progress, 3); // 缓动效果

                    // 先绘制静态方块（不在动画中的）
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[y][x] !== 0) {
                                // 检查是否在重力动画中
                                let isAnimating = false;
                                for (let anim of gravityAnimations) {
                                    if (anim.x === x && anim.targetY === y) {
                                        isAnimating = true;
                                        break;
                                    }
                                }

                                if (!isAnimating) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]], 1, numberGrid[y][x]);
                                }
                            }
                        }
                    }

                    // 绘制正在下落的方块
                    gravityAnimations.forEach(anim => {
                        const currentY = anim.sourceY + (anim.targetY - anim.sourceY) * easeProgress;
                        drawCell(anim.x, currentY, COLORS[anim.color], 1, anim.number);
                    });
                } else {
                    // 正常绘制所有方块
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[y][x] !== 0) {
                                // 如果正在合成动画，跳过动画目标位置的绘制
                                let skipDraw = false;
                                if (isMerging && mergeAnimations.length > 0) {
                                    for (let anim of mergeAnimations) {
                                        if (anim.targetX === x && anim.targetY === y) {
                                            skipDraw = true;
                                            break;
                                        }
                                    }
                                }

                                if (!skipDraw) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]], 1, numberGrid[y][x]);
                                }
                            }
                        }
                    }
                }

                // 绘制幽灵方块（预览位置）
                if (currentPiece) {
                    const ghostPiece = {
                        ...currentPiece,
                        y: currentPiece.y
                    };

                    // 计算幽灵方块位置
                    while (!checkCollision(ghostPiece, 0, 1)) {
                        ghostPiece.y++;
                    }

                    // 绘制幽灵方块
                    for (let y = 0; y < ghostPiece.shape.length; y++) {
                        for (let x = 0; x < ghostPiece.shape[y].length; x++) {
                            if (ghostPiece.shape[y][x] !== 0) {
                                const gridX = ghostPiece.x + x;
                                const gridY = ghostPiece.y + y;
                                if (gridY >= 0 && gridY !== currentPiece.y + y) {
                                    drawGhostCell(gridX, gridY, COLORS[ghostPiece.color], currentPiece.numbers[y][x]);
                                }
                            }
                        }
                    }
                }

                // 绘制当前方块
                if (currentPiece) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;
                                if (gridY >= 0) {
                                    drawCell(gridX, gridY, COLORS[currentPiece.color], 1, currentPiece.numbers[y][x]);
                                }
                            }
                        }
                    }
                }

                // 绘制下一个方块预览（右上角）
                if (nextPiece) {
                    const previewX = rightPanelX;
                    const previewY = 160;
                    const previewCellSize = Math.floor(20);
                    for (let y = 0; y < nextPiece.shape.length; y++) {
                        for (let x = 0; x < nextPiece.shape[y].length; x++) {
                            if (nextPiece.shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * previewCellSize,
                                    previewY + y * previewCellSize,
                                    previewCellSize - 1,
                                    previewCellSize - 1
                                );

                                // 绘制预览方块的数字
                                if (nextPiece.numbers && nextPiece.numbers[y] && nextPiece.numbers[y][x] > 0) {
                                    const numberText = scene.add.text(
                                        previewX + x * previewCellSize + previewCellSize / 2,
                                        previewY + y * previewCellSize + previewCellSize / 2,
                                        nextPiece.numbers[y][x].toString(),
                                        {
                                            fontSize: '14px',
                                            fill: '#ffffff',
                                            fontStyle: 'bold',
                                            stroke: '#000000',
                                            strokeThickness: 1
                                        }
                                    ).setOrigin(0.5);

                                    numberTexts.push(numberText);
                                }
                            }
                        }
                    }
                }

                // 绘制粒子特效
                // for (let p of effectParticles) {
                //     graphics.fillStyle(p.color, Math.max(0, p.life / 24));
                //     graphics.fillRect(p.x - p.size / 2, p.y - p.size / 2, p.size, p.size);
                // }

                // 绘制合成粒子效果
                if (mergeParticles.length > 0) {

                    // 更新和绘制粒子
                    for (let i = mergeParticles.length - 1; i >= 0; i--) {
                        const particle = mergeParticles[i];

                        if (particle.isShockwave) {
                            // 冲击波效果 - 支持延迟
                            if (particle.delay && particle.delay > 0) {
                                particle.delay--;
                                continue;
                            }

                            const progress = 1 - (particle.life / particle.maxLife);
                            const currentSize = particle.maxSize * progress;
                            const alpha = particle.life / particle.maxLife;

                            graphics.lineStyle(4, particle.color, alpha * 0.8);
                            graphics.strokeCircle(particle.x, particle.y, currentSize);

                            graphics.lineStyle(2, particle.color, alpha * 0.4);
                            graphics.strokeCircle(particle.x, particle.y, currentSize * 1.2);

                            particle.life--;
                        } else if (particle.isStar) {
                            // 星星效果
                            particle.x += particle.vx;
                            particle.y += particle.vy;
                            particle.vx *= 0.95;
                            particle.vy *= 0.95;

                            const alpha = particle.life / particle.maxLife;
                            const size = particle.size * alpha;

                            // 绘制星星形状 - 简化为闪烁的十字
                            graphics.fillStyle(particle.color, alpha * 0.9);

                            const halfSize = size / 2;
                            // 垂直线
                            graphics.fillRect(particle.x - 1, particle.y - halfSize, 2, size);
                            // 水平线
                            graphics.fillRect(particle.x - halfSize, particle.y - 1, size, 2);

                            // 中心亮点
                            graphics.fillStyle(0xFFFFFF, alpha);
                            graphics.fillCircle(particle.x, particle.y, size * 0.4);

                            particle.life--;
                        } else {
                            // 普通粒子
                            particle.x += particle.vx;
                            particle.y += particle.vy;
                            particle.vy += particle.gravity; // 重力效果
                            particle.vx *= 0.98; // 空气阻力
                            particle.vy *= 0.98;

                            const alpha = particle.life / particle.maxLife;
                            const size = particle.size * alpha;

                            // 绘制粒子主体
                            graphics.fillStyle(particle.color, alpha * 0.8);
                            graphics.fillCircle(particle.x, particle.y, size);

                            // 绘制粒子光晕
                            graphics.fillStyle(0xFFFFFF, alpha * 0.3);
                            graphics.fillCircle(particle.x, particle.y, size * 0.5);

                            particle.life--;
                        }

                        // 移除生命值耗尽的粒子
                        if (particle.life <= 0) {
                            mergeParticles.splice(i, 1);
                        }
                    }
                }

                // 绘制合成后的方块闪烁效果
                if (isMerging && mergeAnimations.length > 0) {
                    const currentTime = Date.now();
                    const elapsed = currentTime - mergeAnimationStartTime;
                    const progress = Math.min(elapsed / MERGE_ANIMATION_DURATION, 1);

                    mergeAnimations.forEach(anim => {
                        const pixelX = GRID_X + anim.targetX * CELL_SIZE;
                        const pixelY = GRID_Y + anim.targetY * CELL_SIZE;

                        // 闪烁效果
                        const flashIntensity = Math.sin(elapsed * 0.02) * 0.5 + 0.5;

                        // 绘制发光边框
                        graphics.lineStyle(4, 0xFFD700, flashIntensity * 0.8);
                        graphics.strokeRect(pixelX, pixelY, CELL_SIZE, CELL_SIZE);

                        // 绘制内部光效
                        graphics.fillStyle(0xFFFFFF, flashIntensity * 0.2);
                        graphics.fillRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);
                    });
                }

                // 绘制所有弹球
                // for (let ball of balls) {
                //     graphics.fillStyle(ball.color);
                //     graphics.fillCircle(ball.x, ball.y, ball.radius);
                //     graphics.lineStyle(2, 0x00ffff);
                //     graphics.strokeCircle(ball.x, ball.y, ball.radius);
                // }
            }

            function drawCell(x, y, color, alpha = 1, number = 0) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 绘制方块主体
                graphics.fillStyle(color, alpha);
                graphics.fillRect(pixelX + 1, pixelY + 1, CELL_SIZE - 2, CELL_SIZE - 2);

                // 简化的3D效果 - 使用更简单的颜色计算
                const rgb = Phaser.Display.Color.IntegerToRGB(color);

                // 绘制高光效果
                const highlightColor = Phaser.Display.Color.GetColor32(
                    Math.min(255, rgb.r + 60),
                    Math.min(255, rgb.g + 60),
                    Math.min(255, rgb.b + 60),
                    255
                );
                graphics.fillStyle(highlightColor, alpha);
                graphics.fillRect(pixelX + 1, pixelY + 1, CELL_SIZE - 2, 4);
                graphics.fillRect(pixelX + 1, pixelY + 1, 4, CELL_SIZE - 2);

                // 绘制阴影效果
                const shadowColor = Phaser.Display.Color.GetColor32(
                    Math.max(0, rgb.r - 60),
                    Math.max(0, rgb.g - 60),
                    Math.max(0, rgb.b - 60),
                    255
                );
                graphics.fillStyle(shadowColor, alpha);
                graphics.fillRect(pixelX + CELL_SIZE - 5, pixelY + 1, 4, CELL_SIZE - 2);
                graphics.fillRect(pixelX + 1, pixelY + CELL_SIZE - 5, CELL_SIZE - 2, 4);

                // 绘制数字
                if (number > 0) {
                    // 根据数字大小调整字体大小 - 增大字体
                    let fontSize = 24;
                    if (number >= 1000) fontSize = 18;
                    else if (number >= 100) fontSize = 20;
                    else if (number >= 10) fontSize = 22;
                    else fontSize = 24;

                    // 创建文本对象并添加到池中
                    const numberText = scene.add.text(
                        pixelX + CELL_SIZE / 2,
                        pixelY + CELL_SIZE / 2,
                        number.toString(),
                        {
                            fontSize: fontSize + 'px',
                            fill: '#ffffff',
                            fontStyle: 'bold',
                            stroke: '#000000',
                            strokeThickness: 2,
                            alpha: alpha
                        }
                    ).setOrigin(0.5);

                    // 确保文本对象有效
                    if (numberText && numberText.active) {
                        numberTexts.push(numberText);
                    }
                }
            }

            function drawGhostCell(x, y, color, number = 0) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 绘制更低透明度的幽灵方块
                graphics.fillStyle(color, 0.3);
                graphics.fillRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);

                // 绘制边框
                graphics.lineStyle(2, color, 0.3);
                graphics.strokeRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);

                // 绘制数字（半透明）
                if (number > 0) {
                    let fontSize = 24;
                    if (number >= 1000) fontSize = 18;
                    else if (number >= 100) fontSize = 20;
                    else if (number >= 10) fontSize = 22;
                    else fontSize = 24;

                    const numberText = scene.add.text(
                        pixelX + CELL_SIZE / 2,
                        pixelY + CELL_SIZE / 2,
                        number.toString(),
                        {
                            fontSize: fontSize + 'px',
                            fill: '#ffffff',
                            fontStyle: 'bold',
                            stroke: '#000000',
                            strokeThickness: 2,
                            alpha: 0.5
                        }
                    ).setOrigin(0.5);

                    numberTexts.push(numberText);
                }
            }

            // 生成爆炸粒子特效
            // function spawnBlockParticles(gridX, gridY, color) {
            //     const centerX = GRID_X + gridX * CELL_SIZE + CELL_SIZE / 2;
            //     const centerY = GRID_Y + gridY * CELL_SIZE + CELL_SIZE / 2;
            //     for (let i = 0; i < 10; i++) {
            //         const angle = Math.random() * Math.PI * 2;
            //         const speed = 2 + Math.random() * 2;
            //         effectParticles.push({
            //             x: centerX,
            //             y: centerY,
            //             vx: Math.cos(angle) * speed,
            //             vy: Math.sin(angle) * speed,
            //             color: color,
            //             size: 6 + Math.random() * 4,
            //             life: 18 + Math.floor(Math.random() * 8)
            //         });
            //     }
            // }

            // 游戏暂停/结束时停止背景音乐
            // function stopBgm() {
            //     if (sound_bgm && sound_bgm.isPlaying) sound_bgm.stop();
            // }
            // 在showGameOver和togglePause里可调用stopBgm()，如需暂停音乐

            // 启动游戏
            const game = new Phaser.Game(config);
        });
    </script>
</body>
</html>
