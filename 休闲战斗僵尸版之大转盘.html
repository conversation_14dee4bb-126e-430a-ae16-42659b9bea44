<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大转盘升级战斗</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'preparation'; // 新增状态：preparation（准备阶段）, battle（战斗阶段）
    let monstersKilled = 0;
    let totalMonstersInWave = 8;
    let isCreatingWave = false; // 防止重复创建波次的标志位

    // 转盘相关变量
    let wheelRotating = false; // 转盘是否正在旋转
    let wheelRotationTimer = null; // 转盘旋转定时器
    let currentWheelRotation = 0; // 当前转盘旋转角度
    let wheelBackground = null; // 转盘背景对象
    let wheelContainer = null; // 转盘容器，包含转盘和所有子弹

    // 4x4格子和零件商店变量
    let tacticalGrid = []; // 4x4的战术棋盘
    let gameBoard = []; // 棋盘状态：存储棋子信息
    let tacticalCells = []; // 存储棋盘格子的图形对象
    let selectedPieceType = null; // 当前选中的棋子类型
    let shop = []; // 商店界面

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 霰弹枪属性系统
    let shotgunStats = {
        fireRate: 600,        // 射击速度 (毫秒间隔)
        stability: 0,         // 稳定性 (减少抖动)
        reloadSpeed: 1000,    // 装填速度
        specialEffect: null,  // 特殊效果 (freeze, burn, explosive)
        effectValue: 0        // 特效强度
    };

    // 已安装的零件
    let installedParts = {
        stock: null,
        magazine: null,
        barrel: null,
        bullet: null
    };

    // 玩家拥有的枪支数量
    let playerGuns = 0;
    // 存储所有玩家武器的数组
    let playerWeapons = [];

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background8.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character3.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/js (${i}).png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/kong.png');

        // 加载霰弹枪零件图片
        this.load.image('gun_stock', 'images/gun/枪托.png');
        this.load.image('gun_magazine', 'images/gun/弹夹.png');
        this.load.image('gun_barrel', 'images/gun/枪管.png');

        // 加载子弹图片
        this.load.image('ice_bullet', 'images/gun/冰冻子弹.png');
        this.load.image('fire_bullet', 'images/gun/火焰子弹.png');
        this.load.image('explosive_bullet', 'images/gun/爆炸子弹.png');

        // 加载转盘UI图片
        this.load.image('wheel_background', 'images/ui/大转盘.png');
        this.load.image('wheel_base', 'images/ui/大转盘底座背景.png');
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 500, 'player');
        player.setScale(1);
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建4x4格子
        createGrid.call(this);
    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 只有在战斗状态才创建怪物
        if (gameState === 'battle') {
            // 创建新怪物 - 排成一行，底部与角色对齐
            const monsterSpacing = 80;
            const startX = 450;
            const startY = player.y;

            for (let i = 0; i < totalMonstersInWave; i++) {
                const xPos = startX + i * monsterSpacing;
                const yPos = startY;

                // 随机选择怪物图片 (1-15)
                const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

                let monster = this.add.image(xPos, yPos, `monster_${monsterImageIndex}`);
                monster.setScale(0.5);
                monster.setOrigin(0.5, 1);
                monster.setDepth(100 + yPos * 0.1);

                monster.health = 30 + currentLevel * 10;
                monster.maxHealth = monster.health;
                monster.lastAttack = 0;
                monster.isMoving = false;
                monster.originalX = xPos;
                monster.originalY = yPos;
                monster.jumpTween = null;
                monsters.push(monster);
            }

            setTimeout(() => {
                updateMonsterHealthBars.call(this);
                isCreatingWave = false;
            }, 50);
        } else {
            // 准备阶段，设置游戏状态为准备中
            gameState = 'preparation';
            monstersKilled = 0;
            isCreatingWave = false;
        }
    }


    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 计算总攻击次数：基础攻击 + 额外武器攻击
            const totalWeapons = Math.max(1, playerGuns); // 至少有1把武器（原始武器）
            const targetsToAttack = Math.min(playerStats.multiShot * totalWeapons, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i % monsters.length]; // 如果武器多于怪物，循环攻击

                // 选择发射武器（轮流使用不同武器）
                let weaponX, weaponY;
                if (i === 0 && playerWeapon) {
                    // 第一发使用原始武器
                    weaponX = playerWeapon.x + 40;
                    weaponY = playerWeapon.y - 50;
                } else if (playerWeapons.length > 0) {
                    // 后续使用额外武器
                    const weaponIndex = (i - 1) % playerWeapons.length;
                    const weapon = playerWeapons[weaponIndex];
                    weaponX = weapon.x + 20;
                    weaponY = weapon.y - 30;
                } else {
                    // 默认位置
                    weaponX = player.x + 60;
                    weaponY = player.y - 30;
                }

                const projectile = this.add.circle(weaponX, weaponY, 4, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 100, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 应用特殊子弹效果
                        applyBulletEffect.call(this, target, damage);

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 所有武器开火抖动 - 更明显的后坐力
            // 原始武器动画
            if (playerWeapon) {
                animateWeaponRecoil.call(this, playerWeapon);
            }

            // 额外武器动画
            playerWeapons.forEach(weapon => {
                if (weapon) {
                    animateWeaponRecoil.call(this, weapon);
                }
            });
        }
    }

    // 武器后坐力动画
    function animateWeaponRecoil(weapon) {
        const originalWeaponX = weapon.x;
        const originalWeaponY = weapon.y;

        // 水平后坐力
        this.tweens.add({
            targets: weapon,
            x: originalWeaponX - 8, // 向后抖动8像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    x: originalWeaponX,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });

        // 垂直抖动
        this.tweens.add({
            targets: weapon,
            y: originalWeaponY - 4, // 向上抖动4像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    y: originalWeaponY,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        // 更新玩家武器位置
        updatePlayerWeapons.call(this);

        if (gameState === 'battle') {
            // 更新战斗逻辑
            updateBattle.call(this, time, delta);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                stopWheelRotation.call(this);
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time, delta) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理燃烧状态
            if (monster.burning) {
                monster.burnDuration -= delta;
                if (monster.burnDuration <= 0) {
                    monster.burning = false;
                    monster.clearTint();
                } else {
                    // 每秒造成燃烧伤害
                    if (!monster.lastBurnDamage) monster.lastBurnDamage = 0;
                    if (time - monster.lastBurnDamage > 1000) {
                        monster.health -= monster.burnDamage;
                        monster.lastBurnDamage = time;

                        // 燃烧伤害特效
                        const burnText = this.add.text(monster.x, monster.y - 60, `🔥 -${monster.burnDamage}`, {
                            fontSize: '14px',
                            fill: '#ff4500',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: burnText,
                            alpha: 0,
                            y: burnText.y - 20,
                            duration: 800,
                            onComplete: () => burnText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 25000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.52, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;
                playerHealth -= damage;

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 140,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 140,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 140;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave && gameState === 'battle') {
            isCreatingWave = true; // 设置标志位，防止重复触发

            // 停止转盘旋转
            stopWheelRotation.call(this);

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 回到准备状态
            gameState = 'preparation';

            // 显示开始战斗按钮
            this.startBattleButton.setVisible(true);
            this.battleButtonBg.setVisible(true);
            this.battleButtonArea.setVisible(true);

            // 显示商店
            showShop.call(this);

            // 显示波次完成提示
            const completeText = this.add.text(375, 350, `波次 ${currentWave - 1} 完成！`, {
                fontSize: '24px',
                fill: '#00ff00',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: completeText,
                alpha: 0,
                duration: 3000,
                onComplete: () => completeText.destroy()
            });

            isCreatingWave = false; // 重置标志位
        }
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0xffffff); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0xffffff); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🤴', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '🧟', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 创建开始战斗按钮
        const buttonBg = this.add.graphics();
        buttonBg.fillStyle(0x27ae60, 0.9); // 绿色背景
        buttonBg.lineStyle(3, 0x2ecc71); // 浅绿色边框
        buttonBg.fillRoundedRect(300, 400, 150, 50, 10);
        buttonBg.strokeRoundedRect(300, 400, 150, 50, 10);

        this.startBattleButton = this.add.text(375, 425, '开始战斗', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 设置按钮交互
        const buttonArea = this.add.rectangle(375, 425, 150, 50, 0x000000, 0);
        buttonArea.setInteractive();
        buttonArea.on('pointerdown', () => {
            startBattle.call(this);
        });

        // 按钮悬停效果
        buttonArea.on('pointerover', () => {
            buttonBg.clear();
            buttonBg.fillStyle(0x2ecc71, 0.9); // 悬停时更亮的绿色
            buttonBg.lineStyle(3, 0x27ae60);
            buttonBg.fillRoundedRect(300, 400, 150, 50, 10);
            buttonBg.strokeRoundedRect(300, 400, 150, 50, 10);
        });

        buttonArea.on('pointerout', () => {
            if (gameState === 'preparation') {
                buttonBg.clear();
                buttonBg.fillStyle(0x27ae60, 0.9); // 恢复原色
                buttonBg.lineStyle(3, 0x2ecc71);
                buttonBg.fillRoundedRect(300, 400, 150, 50, 10);
                buttonBg.strokeRoundedRect(300, 400, 150, 50, 10);
            }
        });

        // 存储按钮相关对象
        this.battleButtonBg = buttonBg;
        this.battleButtonArea = buttonArea;



        // 子弹安装提示
        this.add.text(275, 1030, '拖拽子弹到转盘任意位置', {
            fontSize: '16px',
            fill: '#ffdc35',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 添加转盘说明
        this.add.text(475, 1030, '不同子弹有不同的特殊效果', {
            fontSize: '14px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }
    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }





    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

    // 创建大转盘抽奖格子
    function createGrid() {
        const centerX = 375; // 屏幕中心X
        const centerY = 750; // 转盘中心Y (调整位置)
        const radius = 120; // 转盘半径 (缩小一些)
        const slotCount = 12; // 转盘格子数量

        // 初始化棋盘数据 - 改为一维数组存储转盘格子
        gameBoard = [];
        tacticalCells = [];

        // 添加转盘底座背景（不在容器中，保持固定）
        const wheelBase = this.add.image(centerX, centerY+15, 'wheel_base');
        wheelBase.setScale(0.7); // 调整大小
        wheelBase.setDepth(1); // 设置在最底层

        // 创建转盘容器
        wheelContainer = this.add.container(centerX, centerY);
        wheelContainer.setDepth(2);

        // 添加转盘背景到容器中
        wheelBackground = this.add.image(0, 0, 'wheel_background'); // 相对于容器的位置
        wheelBackground.setScale(0.7); // 调整大小
        wheelContainer.add(wheelBackground);

        // 创建转盘格子
        for (let i = 0; i < slotCount; i++) {
            const angle = (i * 2 * Math.PI) / slotCount - Math.PI / 2; // 从顶部开始
            const slotX = Math.cos(angle) * radius; // 相对于容器中心的位置
            const slotY = Math.sin(angle) * radius;

            // 创建透明的交互区域（不绘制可见图形）
            const graphics = this.add.graphics();
            graphics.fillStyle(0x000000, 0); // 完全透明
            graphics.fillCircle(0, 0, 35); // 创建交互区域

            // 设置位置（相对于容器）
            graphics.x = slotX;
            graphics.y = slotY;

            // 添加属性
            graphics.slotIndex = i; // 转盘格子索引
            graphics.angle = angle; // 角度
            graphics.piece = null; // 存储棋子对象

            // 设置为可交互（用于拖放检测）
            graphics.setInteractive(new Phaser.Geom.Circle(0, 0, 35), Phaser.Geom.Circle.Contains);

            // 将交互区域添加到容器中
            wheelContainer.add(graphics);

            // 添加格子编号，位置在内侧靠近圆心
            const labelRadius = radius * 0.6; // 编号距离圆心的距离，比格子更靠近圆心
            const labelX = Math.cos(angle) * labelRadius;
            const labelY = Math.sin(angle) * labelRadius;
            const slotLabel = this.add.text(labelX, labelY, `${i + 1}`, {
                fontSize: '12px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);

            // 将编号也添加到容器中
            wheelContainer.add(slotLabel);

            gameBoard[i] = null; // null=空位
            tacticalCells[i] = graphics;
        }

        // 创建商店
        createShop.call(this);
    }



    // 子弹类型定义
    const pieceTypes = {
        ICE_BULLET: {
            name: '冰冻子弹',
            emoji: '❄️',
            image: 'ice_bullet',
            color: 0x87ceeb,
            effect: 'freeze',
            value: 20,
            description: '冰冻特效子弹，减缓敌人速度',
            category: 'bullet'
        },
        FIRE_BULLET: {
            name: '火焰子弹',
            emoji: '🔥',
            image: 'fire_bullet',
            color: 0xff4500,
            effect: 'burn',
            value: 25,
            description: '火焰特效子弹，持续伤害',
            category: 'bullet'
        },
        EXPLOSIVE_BULLET: {
            name: '爆炸子弹',
            emoji: '💥',
            image: 'explosive_bullet',
            color: 0xff6347,
            effect: 'explosive',
            value: 30,
            description: '爆炸特效子弹，范围伤害',
            category: 'bullet'
        }
    };

    // 创建商店
    function createShop() {
        const shopY = 1150;
        const shopStartX = 175; // 调整起始位置，3个棋子居中
        const shopSpacing = 150; // 增加间距

        // 玩家商店背景
        this.shopBackground = this.add.rectangle(375, shopY+30, 600, 250, 0x2c3e50, 0.8);

        // 初始化商店选项
        refreshShop.call(this);
    }

    // 刷新商店
    function refreshShop() {
        // 显示商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(true);
        }

        // 清除现有商店物品
        shop.forEach(shopItem => {
            if (shopItem.playerPiece) shopItem.playerPiece.destroy();
            if (shopItem.playerIcon) shopItem.playerIcon.destroy();
            if (shopItem.pieceName) shopItem.pieceName.destroy();
            if (shopItem.pieceDesc) shopItem.pieceDesc.destroy();
        });
        shop = [];

        const shopY = 1150;
        const shopSpacing = 180;

        // 显示所有子弹类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedTypes = pieceTypeKeys; // 显示所有子弹类型

        // 计算居中位置：屏幕中心 - (总宽度/2) + 第一个位置偏移
        const totalWidth = (selectedTypes.length - 1) * shopSpacing; // 间距的总宽度
        const shopStartX = 375 - totalWidth / 2; // 375是屏幕中心

        selectedTypes.forEach((key, index) => {
            const piece = pieceTypes[key];
            const x = shopStartX + index * shopSpacing;

            // 使用图片或emoji，不需要背景
            let playerIcon;
            if (piece.image) {
                playerIcon = this.add.image(x, shopY - 10, piece.image);
                playerIcon.setScale(0.6); // 调整图片大小，稍微大一点
                playerIcon.setOrigin(0.5);
                playerIcon.setInteractive();
                this.input.setDraggable(playerIcon);
            } else {
                playerIcon = this.add.text(x, shopY - 10, piece.emoji, {
                    fontSize: '38px'
                }).setOrigin(0.5);
                playerIcon.setInteractive();
                this.input.setDraggable(playerIcon);
            }

            // 将playerIcon作为主要的可拖拽对象
            const playerPiece = playerIcon;

            // 添加棋子名称 - 更大字体
            const pieceName = this.add.text(x, shopY + 35, piece.name, {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 添加技能描述 - 调整字体和换行
            const pieceDesc = this.add.text(x-60, shopY + 65, piece.description, {
                fontSize: '20px',
                fill: '#ffdc35',
                fontFamily: 'Arial',
                wordWrap: { width: 140, useAdvancedWrap: true },
                align: 'center'
            })

            // 拖动事件
            playerPiece.on('dragstart', () => {
                // 战斗状态下禁止拖拽
                if (gameState === 'battle') {
                    return;
                }
                playerPiece.setScale(1.2);
                playerIcon.setScale(1.2);
                playerPiece.setDepth(100); // 拖动时设置为最高层级
                playerIcon.setDepth(100);
                selectedPieceType = key;
            });

            playerPiece.on('drag', (pointer, dragX, dragY) => {
                playerPiece.x = dragX;
                playerPiece.y = dragY;
                playerIcon.x = dragX;
                playerIcon.y = dragY;
            });

            playerPiece.on('dragend', () => {
                // 战斗状态下禁止拖拽
                if (gameState === 'battle') {
                    return;
                }

                // 检查是否拖动到转盘上
                const centerX = 375;
                const centerY = 750; // 与createGrid中的centerY保持一致
                const radius = 120;
                const slotCount = 12;

                let placed = false;
                for (let i = 0; i < slotCount; i++) {
                    // 计算格子的世界坐标（考虑容器旋转）
                    const angle = (i * 2 * Math.PI) / slotCount - Math.PI / 2;
                    const localX = Math.cos(angle) * radius;
                    const localY = Math.sin(angle) * radius;

                    // 应用容器的旋转变换
                    const containerRotation = wheelContainer ? wheelContainer.rotation : 0;
                    const rotatedX = localX * Math.cos(containerRotation) - localY * Math.sin(containerRotation);
                    const rotatedY = localX * Math.sin(containerRotation) + localY * Math.cos(containerRotation);

                    const slotX = centerX + rotatedX;
                    const slotY = centerY + rotatedY;
                    const distance = Phaser.Math.Distance.Between(playerPiece.x, playerPiece.y, slotX, slotY);

                    if (distance < 35) {
                        placeTacticalPiece.call(this, i, 'player', key);
                        placed = true;
                        // 下棋成功后刷新商店
                        refreshShop.call(this);
                        break;
                    }
                }

                // 重置位置和层级
                playerPiece.x = x;
                playerPiece.y = shopY - 10;
                playerPiece.setScale(1);
                playerPiece.setDepth(0); // 恢复正常层级
                playerIcon.x = x;
                playerIcon.y = shopY - 10;
                playerIcon.setScale(1);
                playerIcon.setDepth(0); // 恢复正常层级
            });

            shop.push({
                playerPiece,
                playerIcon,
                pieceName,
                pieceDesc,
                type: key
            });
        });
    }

    // 隐藏商店
    function hideShop() {
        if (this.shopBackground) {
            this.shopBackground.setVisible(false);
        }

        shop.forEach(shopItem => {
            if (shopItem.playerPiece) shopItem.playerPiece.setVisible(false);
            if (shopItem.playerIcon) shopItem.playerIcon.setVisible(false);
            if (shopItem.pieceName) shopItem.pieceName.setVisible(false);
            if (shopItem.pieceDesc) shopItem.pieceDesc.setVisible(false);
        });
    }

    // 显示商店
    function showShop() {
        if (this.shopBackground) {
            this.shopBackground.setVisible(true);
        }

        shop.forEach(shopItem => {
            if (shopItem.playerPiece) shopItem.playerPiece.setVisible(true);
            if (shopItem.playerIcon) shopItem.playerIcon.setVisible(true);
            if (shopItem.pieceName) shopItem.pieceName.setVisible(true);
            if (shopItem.pieceDesc) shopItem.pieceDesc.setVisible(true);
        });
    }

    // 安装子弹函数 - 适配转盘系统，支持合成
    function placeTacticalPiece(slotIndex, player, pieceType) {
        const piece = pieceTypes[pieceType];

        // 检查是否可以合成
        if (gameBoard[slotIndex] !== null) {
            const existingPiece = gameBoard[slotIndex];

            // 如果是相同类型的子弹，进行合成
            if (existingPiece.type === pieceType) {
                const newLevel = (existingPiece.level || 1) + 1;
                const newValue = piece.value + existingPiece.value * 0.5; // 合成后效果增强

                // 更新子弹数据
                existingPiece.level = newLevel;
                existingPiece.value = newValue;

                // 更新等级显示
                updateBulletLevelDisplay.call(this, slotIndex, newLevel);

                // 显示合成提示
                const mergeText = this.add.text(375, 520, `${piece.name} 合成！等级 ${newLevel}`, {
                    fontSize: '16px',
                    fill: '#ffff00',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: mergeText,
                    alpha: 0,
                    duration: 1500,
                    onComplete: () => mergeText.destroy()
                });

                return true;
            } else {
                // 不同类型不能合成
                const errorText = this.add.text(375, 520, '不同类型子弹无法合成！', {
                    fontSize: '16px',
                    fill: '#ff0000',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: errorText,
                    alpha: 0,
                    duration: 1500,
                    onComplete: () => errorText.destroy()
                });

                return false;
            }
        }

        // 创建新子弹数据
        const pieceData = {
            type: pieceType,
            player: player,
            effect: piece.effect,
            value: piece.value,
            category: piece.category,
            slotIndex: slotIndex,
            level: 1 // 初始等级为1
        };

        // 更新棋盘数据
        gameBoard[slotIndex] = pieceData;

        // 应用子弹效果到霰弹枪
        applyPartEffect(pieceType, piece);

        // 创建子弹图形 - 计算转盘位置（相对于容器）
        const radius = 120;
        const angle = (slotIndex * 2 * Math.PI) / 12 - Math.PI / 2;
        const pieceX = Math.cos(angle) * radius; // 相对于容器中心的位置
        const pieceY = Math.sin(angle) * radius;

        if (player === 'player') {
            // 子弹：直接显示图片或emoji
            let pieceIcon;
            if (piece.image) {
                pieceIcon = this.add.image(pieceX, pieceY, piece.image);
                pieceIcon.setScale(0.4); // 调整图片大小适合转盘格子
                pieceIcon.setOrigin(0.5);
                // 根据格子角度旋转子弹，使子弹头指向转盘外面
                pieceIcon.setRotation(angle); // 子弹朝向指向转盘外侧
            } else {
                pieceIcon = this.add.text(pieceX, pieceY, piece.emoji, {
                    fontSize: '18px'
                }).setOrigin(0.5);
                // emoji也指向转盘外侧
                pieceIcon.setRotation(angle);
            }

            // 创建等级显示文字
            const levelText = this.add.text(pieceX + 15, pieceY - 15, '1', {
                fontSize: '12px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);

            // 将子弹和等级文字添加到容器中
            wheelContainer.add(pieceIcon);
            wheelContainer.add(levelText);

            tacticalCells[slotIndex].piece = {
                icon: pieceIcon,
                data: pieceData,
                levelText: levelText
            };
        }

        // 显示放置成功提示
        const successText = this.add.text(375, 520, `放置了${piece.name}！`, {
            fontSize: '16px',
            fill: '#00ff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: successText,
            alpha: 0,
            duration: 1500,
            onComplete: () => successText.destroy()
        });

        return true;
    }

    // 更新子弹等级显示
    function updateBulletLevelDisplay(slotIndex, level) {
        if (tacticalCells[slotIndex] && tacticalCells[slotIndex].piece && tacticalCells[slotIndex].piece.levelText) {
            tacticalCells[slotIndex].piece.levelText.setText(level.toString());

            // 等级提升特效
            this.tweens.add({
                targets: tacticalCells[slotIndex].piece.levelText,
                scaleX: 1.5,
                scaleY: 1.5,
                duration: 200,
                yoyo: true,
                ease: 'Power2'
            });
        }
    }

    // 开始战斗函数
    function startBattle() {
        if (gameState !== 'preparation') return;

        gameState = 'battle';

        // 隐藏开始战斗按钮
        this.startBattleButton.setVisible(false);
        this.battleButtonBg.setVisible(false);
        this.battleButtonArea.setVisible(false);

        // 隐藏商店
        hideShop.call(this);

        // 创建怪物
        createWave.call(this);

        // 开始转盘旋转
        startWheelRotation.call(this);
    }

    // 开始转盘旋转
    function startWheelRotation() {
        if (wheelRotating) return;

        wheelRotating = true;

        // 立即开始第一次旋转
        rotateWheel.call(this);

        // 每3秒旋转一次（给足够时间完成旋转和释放子弹）
        wheelRotationTimer = setInterval(() => {
            rotateWheel.call(this);
        }, 3000);
    }

    // 停止转盘旋转
    function stopWheelRotation() {
        wheelRotating = false;
        if (wheelRotationTimer) {
            clearInterval(wheelRotationTimer);
            wheelRotationTimer = null;
        }
    }

    // 转盘旋转函数
    function rotateWheel() {
        if (!wheelContainer || gameState !== 'battle') return;

        // 计算旋转角度：0.5-1.5圈 + 一个格子的位置
        const baseRotations = 0.5; // 基础0.5圈
        const extraRotation = Math.random() * 1.0; // 额外0-1圈
        const finalSlotRotation = (2 * Math.PI) / 12; // 最终停在下一个格子
        const totalRotation = (baseRotations + extraRotation) * 2 * Math.PI + finalSlotRotation;

        currentWheelRotation += totalRotation;

        // 根据旋转圈数计算持续时间：一整圈1秒，最多2秒
        const totalCircles = (baseRotations + extraRotation);
        const duration = Math.min(totalCircles * 1000, 2000); // 一圈1秒，最多2秒

        // 旋转整个容器，包含转盘背景、子弹、等级数字等所有元素
        this.tweens.add({
            targets: wheelContainer,
            rotation: currentWheelRotation,
            duration: duration,
            ease: 'Power3.easeOut', // 使用Power3.easeOut，开始快，慢慢停下来
            onComplete: () => {
                // 旋转完成后，检查最上方的格子并释放子弹
                checkTopSlotAndFireBullet.call(this);
            }
        });
    }

    // 检查最上方格子并释放对应子弹
    function checkTopSlotAndFireBullet() {
        // 计算当前最上方的格子索引
        const normalizedRotation = ((currentWheelRotation % (2 * Math.PI)) + (2 * Math.PI)) % (2 * Math.PI);
        const topSlotIndex = Math.round(normalizedRotation / (2 * Math.PI / 12)) % 12;

        // 检查该格子是否有子弹
        if (gameBoard[topSlotIndex] && gameBoard[topSlotIndex] !== null) {
            const bulletData = gameBoard[topSlotIndex];

            // 应用子弹效果（根据等级增强）
            shotgunStats.specialEffect = bulletData.effect;
            shotgunStats.effectValue = bulletData.value;

            // 释放大特效子弹
            fireSpecialBullet.call(this, bulletData);

            // 显示释放子弹的提示
            const piece = pieceTypes[bulletData.type];
            const level = bulletData.level || 1;
            const fireText = this.add.text(375, 300, `🔥 释放${piece.name} Lv.${level}！`, {
                fontSize: '18px',
                fill: '#ffff00',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }).setOrigin(0.5);

            this.tweens.add({
                targets: fireText,
                alpha: 0,
                y: fireText.y - 30,
                duration: 1500,
                onComplete: () => fireText.destroy()
            });

            // 高亮对应的子弹
            if (tacticalCells[topSlotIndex] && tacticalCells[topSlotIndex].piece) {
                this.tweens.add({
                    targets: tacticalCells[topSlotIndex].piece.icon,
                    scaleX: 0.6,
                    scaleY: 0.6,
                    duration: 200,
                    yoyo: true,
                    repeat: 1
                });
            }
        }
    }

    // 释放特效子弹
    function fireSpecialBullet(bulletData) {
        if (monsters.length === 0) return;

        const piece = pieceTypes[bulletData.type];
        const level = bulletData.level || 1;

        // 从主角武器位置发射大特效子弹
        const startX = playerWeapon ? playerWeapon.x + 40 : player.x + 60;
        const startY = playerWeapon ? playerWeapon.y - 50 : player.y - 30;

        // 创建大特效子弹
        let specialBullet;
        if (piece.image) {
            specialBullet = this.add.image(startX, startY, piece.image);
            specialBullet.setScale(0.8 + level * 0.2); // 根据等级调整大小
        } else {
            specialBullet = this.add.text(startX, startY, piece.emoji, {
                fontSize: (24 + level * 4) + 'px'
            }).setOrigin(0.5);
        }

        specialBullet.setDepth(50); // 确保特效子弹在最上层

        // 添加特效光环
        const effectRing = this.add.graphics();
        effectRing.lineStyle(3, piece.color, 0.8);
        effectRing.strokeCircle(startX, startY, 30 + level * 10);
        effectRing.setDepth(49);

        // 光环脉动效果
        this.tweens.add({
            targets: effectRing,
            scaleX: 1.5,
            scaleY: 1.5,
            alpha: 0,
            duration: 800,
            ease: 'Power2',
            onComplete: () => effectRing.destroy()
        });

        // 选择目标（最近的怪物）
        const target = monsters[0];

        // 特效子弹飞行动画
        this.tweens.add({
            targets: specialBullet,
            x: target.x - 30,
            y: target.y - 100,
            duration: 800, // 从300增加到800，让子弹飞行更慢
            ease: 'Power2',
            onComplete: () => {
                // 爆炸特效
                createBulletExplosion.call(this, target.x - 30, target.y - 100, piece, level);

                // 应用特殊效果到所有怪物
                applySpecialBulletEffect.call(this, bulletData, target);

                specialBullet.destroy();
            }
        });
    }

    // 创建子弹爆炸特效
    function createBulletExplosion(x, y, piece, level) {
        // 爆炸圆圈
        const explosion = this.add.graphics();
        explosion.fillStyle(piece.color, 0.6);
        explosion.fillCircle(x, y, 10);
        explosion.setDepth(60);

        // 爆炸扩散动画
        this.tweens.add({
            targets: explosion,
            scaleX: 3 + level * 0.5,
            scaleY: 3 + level * 0.5,
            alpha: 0,
            duration: 500,
            ease: 'Power2',
            onComplete: () => explosion.destroy()
        });

        // 粒子效果
        for (let i = 0; i < 8 + level * 2; i++) {
            const particle = this.add.graphics();
            particle.fillStyle(piece.color);
            particle.fillCircle(x, y, 3);
            particle.setDepth(61);

            const angle = (i * Math.PI * 2) / (8 + level * 2);
            const distance = 50 + level * 20;

            this.tweens.add({
                targets: particle,
                x: x + Math.cos(angle) * distance,
                y: y + Math.sin(angle) * distance,
                alpha: 0,
                duration: 600,
                ease: 'Power2',
                onComplete: () => particle.destroy()
            });
        }
    }

    // 应用特殊子弹效果
    function applySpecialBulletEffect(bulletData, target) {
        const level = bulletData.level || 1;
        const enhancedValue = bulletData.value * level; // 等级越高效果越强

        switch (bulletData.effect) {
            case 'freeze':
                // 冰冻效果 - 影响所有怪物
                monsters.forEach(monster => {
                    monster.frozen = true;
                    monster.frozenTime = enhancedValue * 100;
                    monster.setTint(0x87ceeb);
                });
                break;

            case 'burn':
                // 燃烧效果 - 影响所有怪物
                monsters.forEach(monster => {
                    monster.burning = true;
                    monster.burnDamage = Math.floor(enhancedValue * 0.5);
                    monster.burnDuration = enhancedValue * 100;
                    monster.setTint(0xff4500);
                });
                break;

            case 'explosive':
                // 爆炸效果 - 对所有怪物造成伤害
                monsters.forEach(monster => {
                    monster.health -= enhancedValue;
                    showDamageText.call(this, monster, enhancedValue);
                    monsterHitEffect.call(this, monster);
                });
                break;
        }
    }



    // 应用零件效果
    function applyPartEffect(pieceType, piece) {
        switch (piece.effect) {
            case 'fireRate':
                // 减少射击间隔，提高射速
                shotgunStats.fireRate = Math.max(200, shotgunStats.fireRate - piece.value);
                playerStats.attackSpeed = shotgunStats.fireRate;
                break;
            case 'stability':
                // 增加稳定性
                shotgunStats.stability += piece.value;
                break;
            case 'reloadSpeed':
                // 减少装填时间
                shotgunStats.reloadSpeed = Math.max(300, shotgunStats.reloadSpeed - piece.value);
                break;
            case 'freeze':
                shotgunStats.specialEffect = 'freeze';
                shotgunStats.effectValue = piece.value;
                break;
            case 'burn':
                shotgunStats.specialEffect = 'burn';
                shotgunStats.effectValue = piece.value;
                break;
            case 'explosive':
                shotgunStats.specialEffect = 'explosive';
                shotgunStats.effectValue = piece.value;
                break;
        }

        // 记录已安装的零件
        switch (piece.category) {
            case 'stock':
                installedParts.stock = pieceType;
                break;
            case 'magazine':
                installedParts.magazine = pieceType;
                break;
            case 'barrel':
                installedParts.barrel = pieceType;
                break;
            case 'bullet':
                installedParts.bullet = pieceType;
                break;
        }
    }



    // 应用特殊子弹效果
    function applyBulletEffect(target, damage) {
        if (!shotgunStats.specialEffect) return;

        switch (shotgunStats.specialEffect) {
            case 'freeze':
                // 冰冻效果
                target.frozen = true;
                target.frozenTime = shotgunStats.effectValue * 100; // 转换为毫秒
                target.setTint(0x87ceeb); // 浅蓝色

                const freezeText = this.add.text(target.x, target.y - 100, '❄️ 冰冻!', {
                    fontSize: '16px',
                    fill: '#87ceeb',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: freezeText,
                    alpha: 0,
                    y: freezeText.y - 20,
                    duration: 1000,
                    onComplete: () => freezeText.destroy()
                });
                break;

            case 'burn':
                // 火焰效果
                if (!target.burning) {
                    target.burning = true;
                    target.burnDamage = Math.floor(damage * 0.3);
                    target.burnDuration = shotgunStats.effectValue * 100;
                    target.setTint(0xff4500); // 橙红色

                    const burnText = this.add.text(target.x, target.y - 100, '🔥 燃烧!', {
                        fontSize: '16px',
                        fill: '#ff4500',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: burnText,
                        alpha: 0,
                        y: burnText.y - 20,
                        duration: 1000,
                        onComplete: () => burnText.destroy()
                    });
                }
                break;

            case 'explosive':
                // 爆炸效果
                const explosionRadius = shotgunStats.effectValue * 3;
                const explosionDamage = Math.floor(damage * 0.5);

                // 爆炸特效
                const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6347, 0.3);
                this.tweens.add({
                    targets: explosion,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => explosion.destroy()
                });

                // 对范围内的其他怪物造成伤害
                monsters.forEach(otherMonster => {
                    if (otherMonster !== target) {
                        const distance = Phaser.Math.Distance.Between(
                            target.x, target.y, otherMonster.x, otherMonster.y
                        );
                        if (distance <= explosionRadius) {
                            otherMonster.health -= explosionDamage;

                            // 爆炸伤害特效
                            this.tweens.add({
                                targets: otherMonster,
                                scaleX: 0.3,
                                scaleY: 0.3,
                                duration: 100,
                                yoyo: true
                            });
                        }
                    }
                });

                const explosiveText = this.add.text(target.x, target.y - 100, '💥 爆炸!', {
                    fontSize: '16px',
                    fill: '#ff6347',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: explosiveText,
                    alpha: 0,
                    y: explosiveText.y - 20,
                    duration: 1000,
                    onComplete: () => explosiveText.destroy()
                });
                break;
        }
    }

    // 创建玩家武器
    function createPlayerWeapon(weaponNumber) {
        // 计算武器位置，围绕主角排列
        const angle = (weaponNumber - 1) * (Math.PI * 2 / 8); // 最多8把武器围成圆圈
        const radius = 40; // 武器距离主角的半径
        const weaponX = player.x + Math.cos(angle) * radius;
        const weaponY = player.y + Math.sin(angle) * radius;

        // 创建武器图像
        const weapon = this.add.image(weaponX, weaponY, 'knife');
        weapon.setScale(0.6);
        weapon.setOrigin(0.5, 1);
        weapon.setDepth(100 + player.y * 0.1 + weaponNumber);

        // 存储武器
        playerWeapons.push(weapon);

        // 武器跟随主角的逻辑会在updatePlayerWeapons中处理
    }

    // 更新所有玩家武器位置
    function updatePlayerWeapons() {
        playerWeapons.forEach((weapon, index) => {
            if (weapon && player) {
                const angle = index * (Math.PI * 2 / Math.max(8, playerWeapons.length));
                const radius = 40;
                weapon.x = player.x + Math.cos(angle) * radius;
                weapon.y = player.y + Math.sin(angle) * radius;
                weapon.setDepth(100 + player.y * 0.1 + index + 1);
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
