<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>俄罗斯方块消灭星星模式</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        #game-container {
            /* border: 3px solid #333; */
            border-radius: 10px;
            /* box-shadow: 0 0 20px rgba(0,0,0,0.5); */
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 防止多次初始化
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            // 逻辑分辨率
            const GAME_WIDTH = 720;
            const GAME_HEIGHT = 1280;

            // Phaser配置，直接用逻辑分辨率
            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000000',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let gameGrid = [];
            let currentPiece = null;
            let nextPiece = null;
            let score = 0;
            let level = 1;
            let lines = 0;
            let dropTime = 0;
            let dropInterval = 1000;
            let gameOver = false;
            let isPaused = false;
            let fastDrop = false; // 快速下落状态

            // 游戏区域配置（使用逻辑分辨率）
            const GRID_WIDTH = 13;
            const GRID_HEIGHT = 23;
            const CELL_SIZE = 32;
            // 让主区域更居中
            const GRID_X = Math.max(20, Math.floor((GAME_WIDTH - GRID_WIDTH * CELL_SIZE) / 2) - 120);
            const GRID_Y = 120;

            // 方块颜色
            const COLORS = [
                0x000000, // 空
                0xFF0000, // 红色 - I
                0x00FF00, // 绿色 - O  
                0x0000FF, // 蓝色 - T
                0xFFFF00, // 黄色 - S
                0xFF00FF, // 紫色 - Z
                0x00FFFF, // 青色 - J
                0xFFA500  // 橙色 - L
            ];

            // 方块形状定义
            const PIECES = [
                // I 形状
                {
                    shape: [
                        [0,0,0,0],
                        [1,1,1,1],
                        [0,0,0,0],
                        [0,0,0,0]
                    ],
                    color: 1
                },
                // O 形状
                {
                    shape: [
                        [2,2],
                        [2,2]
                    ],
                    color: 2
                },
                // T 形状
                {
                    shape: [
                        [0,3,0],
                        [3,3,3],
                        [0,0,0]
                    ],
                    color: 3
                },
                // S 形状
                {
                    shape: [
                        [0,4,4],
                        [4,4,0],
                        [0,0,0]
                    ],
                    color: 4
                },
                // Z 形状
                {
                    shape: [
                        [5,5,0],
                        [0,5,5],
                        [0,0,0]
                    ],
                    color: 5
                },
                // J 形状
                {
                    shape: [
                        [6,0,0],
                        [6,6,6],
                        [0,0,0]
                    ],
                    color: 6
                },
                // L 形状
                {
                    shape: [
                        [0,0,7],
                        [7,7,7],
                        [0,0,0]
                    ],
                    color: 7
                }
            ];

            let scene;
            let graphics;
            let scoreText, levelText, linesText;
            let leftButton, rightButton, rotateButton, downButton, pauseButton;
            let parentSizer, logicSizer;

            function preload() {
                scene = this;
            }

            function create() {
                graphics = scene.add.graphics();

                // 逻辑区域和父容器的 Size 适配
                parentSizer = new Phaser.Structs.Size(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer = new Phaser.Structs.Size(GAME_WIDTH, GAME_HEIGHT, Phaser.Structs.Size.FIT, parentSizer);
                parentSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);

                // 监听resize事件
                this.scale.on('resize', resizeGame, this);

                // 初始化游戏网格
                initGrid();
                // 创建UI
                createUI();
                // 创建控制按钮
                createControls();
                // 生成第一个方块
                spawnPiece();
                // 设置输入
                setupInput();
            }

            function resizeGame(gameSize) {
                if (!parentSizer || !logicSizer) return;
                const width = gameSize.width;
                const height = gameSize.height;
                parentSizer.setSize(width, height);
                logicSizer.setSize(width, height);
                // 这里可以统一调整 camera、container、layer 的缩放和位置（如有）
                // 目前所有元素都用逻辑坐标，Phaser会自动缩放
            }

            function initGrid() {
                gameGrid = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    gameGrid[y] = [];
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        gameGrid[y][x] = 0;
                    }
                }
            }

            function createUI() {
                // 标题
                scene.add.text(GAME_WIDTH * 0.5, 50, '俄罗斯方块+消灭星星模式', {
                    fontSize: '44px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 分数显示
                scoreText = scene.add.text(GAME_WIDTH * 0.7, 250, '分数: 0', {
                    fontSize: '32px',
                    fill: '#ffffff'
                });

                levelText = scene.add.text(GAME_WIDTH * 0.7, 300, '等级: 1', {
                    fontSize: '32px',
                    fill: '#ffffff'
                });

                linesText = scene.add.text(GAME_WIDTH * 0.7, 350, '行数: 0', {
                    fontSize: '32px',
                    fill: '#ffffff'
                });

                // 下一个方块预览区域
                scene.add.text(GAME_WIDTH * 0.7, 420, '下一个:', {
                    fontSize: '26px',
                    fill: '#ffffff'
                });

                // 控制说明
                const controlsY = 550;
                const controlsFontSize = '22px';
                scene.add.text(GAME_WIDTH * 0.7, controlsY, '控制说明:', {
                    fontSize: '26px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                });
                scene.add.text(GAME_WIDTH * 0.7, controlsY + 35, '← → 移动', {
                    fontSize: controlsFontSize,
                    fill: '#cccccc'
                });
                scene.add.text(GAME_WIDTH * 0.7, controlsY + 65, '上 旋转', {
                    fontSize: controlsFontSize,
                    fill: '#cccccc'
                });
                scene.add.text(GAME_WIDTH * 0.7, controlsY + 95, '↓ 快速下落', {
                    fontSize: controlsFontSize,
                    fill: '#cccccc'
                });
              
                scene.add.text(GAME_WIDTH * 0.7, controlsY + 155, 'Space 硬降落', {
                    fontSize: controlsFontSize,
                    fill: '#cccccc'
                });
            }

            function createControls() {
                const buttonStyle = {
                    fontSize: '48px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                };
                // 右上角
                const pauseX = GAME_WIDTH - 60;
                const pauseY = 20;
                pauseButton = scene.add.text(pauseX, pauseY, '⏸', buttonStyle)
                    .setInteractive()
                    .on('pointerdown', togglePause);
            }

            function setupInput() {
                // 键盘控制
                const cursors = scene.input.keyboard.createCursorKeys();
                const wasd = scene.input.keyboard.addKeys('W,S,A,D,SPACE,P');

                scene.input.keyboard.on('keydown', (event) => {
                    if (gameOver || isPaused) return;

                    switch(event.code) {
                        case 'ArrowLeft':
                        case 'KeyA':
                            movePiece(-1, 0);
                            break;
                        case 'ArrowRight':
                        case 'KeyD':
                            movePiece(1, 0);
                            break;
                        case 'ArrowDown':
                        case 'KeyS':
                            startFastDrop();
                            break;
                        case 'ArrowUp':
                        case 'KeyW':
                            rotatePiece();
                            break;
                        case 'Space':
                            hardDrop();
                            break;
                        case 'KeyP':
                            togglePause();
                            break;
                    }
                });

                scene.input.keyboard.on('keyup', (event) => {
                    switch(event.code) {
                        case 'ArrowDown':
                        case 'KeyS':
                            stopFastDrop();
                            break;
                    }
                });
            }

            function startFastDrop() {
                if (gameOver || isPaused) return;
                fastDrop = true;
            }

            function stopFastDrop() {
                fastDrop = false;
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }
                
                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;
                
                nextPiece = createRandomPiece();
                
                // 检查游戏结束
                if (checkCollision(currentPiece, 0, 0)) {
                    gameOver = true;
                    showGameOver();
                }
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                return {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0,
                    y: 0
                };
            }

            function movePiece(dx, dy) {
                if (gameOver || isPaused || !currentPiece) return;

                if (!checkCollision(currentPiece, dx, dy)) {
                    currentPiece.x += dx;
                    currentPiece.y += dy;
                    return true;
                }
                return false;
            }

            function rotatePiece() {
                if (gameOver || isPaused || !currentPiece) return;

                const rotated = rotateMatrix(currentPiece.shape);
                const originalShape = currentPiece.shape;
                currentPiece.shape = rotated;

                // 检查旋转后是否有碰撞
                if (checkCollision(currentPiece, 0, 0)) {
                    // 尝试向左或向右移动来避免碰撞
                    if (!checkCollision(currentPiece, -1, 0)) {
                        currentPiece.x -= 1;
                    } else if (!checkCollision(currentPiece, 1, 0)) {
                        currentPiece.x += 1;
                    } else {
                        // 无法旋转，恢复原状
                        currentPiece.shape = originalShape;
                    }
                }
            }

            function rotateMatrix(matrix) {
                const rows = matrix.length;
                const cols = matrix[0].length;
                const rotated = [];

                for (let i = 0; i < cols; i++) {
                    rotated[i] = [];
                    for (let j = 0; j < rows; j++) {
                        rotated[i][j] = matrix[rows - 1 - j][i];
                    }
                }
                return rotated;
            }

            function checkCollision(piece, dx, dy) {
                const newX = piece.x + dx;
                const newY = piece.y + dy;

                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const gridX = newX + x;
                            const gridY = newY + y;

                            // 检查边界
                            if (gridX < 0 || gridX >= GRID_WIDTH || gridY >= GRID_HEIGHT) {
                                return true;
                            }

                            // 检查与已有方块的碰撞
                            if (gridY >= 0 && gameGrid[gridY][gridX] !== 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            // 动画参数参考 match3-game.html
            const MATCH_ANIMATION_BASE = 150; // 每格消除主动画时长
            const MATCH_ANIMATION_EXTRA = 100; // 每格消除后缩小消失
            const MATCH_ANIMATION_WAVE = 50; // 波浪延迟
            const GRAVITY_ANIMATION = 300; // 下落动画时长

            function clearMatches(callback) {
                // callback: 动画全部结束后回调（如需继续流程）
                let totalCleared = 0;
                // 标记所有可消除格子
                let toClear = Array.from({length: GRID_HEIGHT}, () => Array(GRID_WIDTH).fill(false));
                let found = false;
                // 相邻4连及以上（横竖相邻，L型T型都可）
                let visited = Array.from({length: GRID_HEIGHT}, () => Array(GRID_WIDTH).fill(false));
                function floodFill(sy, sx, color) {
                    let stack = [[sy, sx]];
                    let group = [];
                    visited[sy][sx] = true;
                    while (stack.length) {
                        let [y, x] = stack.pop();
                        group.push([y, x]);
                        for (const [dy, dx] of [[0,1],[1,0],[0,-1],[-1,0]]) {
                            let ny = y + dy, nx = x + dx;
                            if (ny >= 0 && ny < GRID_HEIGHT && nx >= 0 && nx < GRID_WIDTH && !visited[ny][nx] && gameGrid[ny][nx] === color) {
                                visited[ny][nx] = true;
                                stack.push([ny, nx]);
                            }
                        }
                    }
                    return group;
                }
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (!visited[y][x] && gameGrid[y][x] !== 0) {
                            let group = floodFill(y, x, gameGrid[y][x]);
                            if (group.length >= 6) {
                                for (const [gy, gx] of group) {
                                    toClear[gy][gx] = true;
                                }
                                found = true;
                            }
                        }
                    }
                }
                // 执行消除动画
                let cleared = 0;
                let clearWave = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (toClear[y][x]) {
                            cleared++;
                            clearWave.push({x, y});
                        }
                    }
                }
                if (cleared > 0) {
                    // 动画：消除波浪
                    clearWave.forEach((pos, idx) => {
                        setTimeout(() => {
                            animateCellClear(pos.x, pos.y);
                        }, idx * MATCH_ANIMATION_WAVE);
                    });
                    // 等待动画后重力补全（中间加延迟）
                    const totalAnim = MATCH_ANIMATION_BASE + MATCH_ANIMATION_EXTRA + clearWave.length * MATCH_ANIMATION_WAVE;
                    const DELAY_BETWEEN_CLEAR_AND_FALL = 150;
                    setTimeout(() => {
                        setTimeout(() => {
                            // 先清除格子
                            for (let y = 0; y < GRID_HEIGHT; y++) {
                                for (let x = 0; x < GRID_WIDTH; x++) {
                                    if (toClear[y][x]) gameGrid[y][x] = 0;
                                }
                            }
                            applyGravityAnimated(() => {
                                // 重力补全后再判断是否有新消除
                                clearMatches(() => {
                                    if (callback) callback();
                                });
                            });
                        }, DELAY_BETWEEN_CLEAR_AND_FALL);
                    }, totalAnim);
                    // 计分
                    score += cleared * 10;
                    scoreText.setText('分数: ' + score);
                } else {
                    // 没有可消除，动画全部结束，回调
                    if (callback) callback();
                }
            }

            // 消除动画：缩放+透明，动画后再设为0
            function animateCellClear(x, y) {
                // 只做动画效果，不直接清除
                // 这里用Phaser的Tween做动画，实际用canvas只能做简单效果
                // 这里用一个动画队列记录动画状态
                if (!scene || !graphics) {
                    gameGrid[y][x] = 0;
                    return;
                }
                // 记录动画帧数
                let anim = { x, y, t: 0, done: false };
                if (!window._cellAnims) window._cellAnims = [];
                window._cellAnims.push(anim);
                // 动画帧推进
                let steps = 20;
                let timer = setInterval(() => {
                    anim.t++;
                    if (anim.t >= steps) {
                        clearInterval(timer);
                        anim.done = true;
                        gameGrid[y][x] = 0;
                    }
                }, (MATCH_ANIMATION_BASE + MATCH_ANIMATION_EXTRA) / steps);
            }

            // 重力补全动画（带下落动画）
            function applyGravityAnimated(callback) {
                if (!window._fallAnims) window._fallAnims = [];
                let moved = false;
                let anims = [];
                // 先记录所有下落动画
                for (let x = 0; x < GRID_WIDTH; x++) {
                    let pointer = GRID_HEIGHT - 1;
                    for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                        if (gameGrid[y][x] !== 0) {
                            if (y !== pointer) {
                                // 记录动画
                                anims.push({
                                    fromY: y,
                                    toY: pointer,
                                    x: x,
                                    color: gameGrid[y][x],
                                    t: 0,
                                    done: false
                                });
                                moved = true;
                            }
                            pointer--;
                        }
                    }
                }
                // 先清理数据（不直接移动，等动画结束后再移动）
                for (const anim of anims) {
                    gameGrid[anim.fromY][anim.x] = 0;
                }
                if (anims.length === 0) {
                    if (callback) callback();
                    return;
                }
                window._fallAnims = anims;
                // 动画帧推进
                let steps = 20;
                let timer = setInterval(() => {
                    let allDone = true;
                    for (const anim of window._fallAnims) {
                        if (!anim.done) {
                            anim.t++;
                            if (anim.t >= steps) {
                                anim.done = true;
                                // 动画结束，真正落到底
                                gameGrid[anim.toY][anim.x] = anim.color;
                            } else {
                                allDone = false;
                            }
                        }
                    }
                    if (allDone) {
                        clearInterval(timer);
                        window._fallAnims = [];
                        if (callback) callback();
                    }
                }, GRAVITY_ANIMATION / steps);
            }

            function updateScore(linesCleared) {
                const linePoints = [0, 100, 300, 500, 800];
                score += linePoints[linesCleared] * level;
                lines += linesCleared;

                // 每10行提升一个等级
                const newLevel = Math.floor(lines / 10) + 1;
                if (newLevel > level) {
                    level = newLevel;
                    dropInterval = Math.max(100, 1000 - (level - 1) * 100);
                }

                // 更新UI
                scoreText.setText('分数: ' + score);
                levelText.setText('等级: ' + level);
                linesText.setText('行数: ' + lines);
            }

            function hardDrop() {
                if (gameOver || isPaused || !currentPiece) return;

                let dropDistance = 0;
                while (!checkCollision(currentPiece, 0, 1)) {
                    currentPiece.y++;
                    dropDistance++;
                }

                // 硬降落得分奖励
                score += dropDistance * 2;
                scoreText.setText('分数: ' + score);

                placePiece();
            }

            function togglePause() {
                isPaused = !isPaused;
                if (isPaused) {
                    pauseButton.setText('▶');
                    scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏暂停', {
                        fontSize: Math.floor(48) + 'px',
                        fill: '#ffff00',
                        fontStyle: 'bold'
                    }).setOrigin(0.5).setName('pauseText');
                } else {
                    pauseButton.setText('⏸');
                    const pauseText = scene.children.getByName('pauseText');
                    if (pauseText) pauseText.destroy();
                }
            }

            function showGameOver() {
                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏结束', {
                    fontSize: Math.floor(48) + 'px',
                    fill: '#ff0000',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 60, '点击重新开始', {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5);

                scene.input.once('pointerdown', restartGame);
            }

            function restartGame() {
                score = 0;
                level = 1;
                lines = 0;
                dropTime = 0;
                dropInterval = 1000;
                gameOver = false;
                isPaused = false;

                initGrid();
                spawnPiece();

                scoreText.setText('分数: 0');
                levelText.setText('等级: 1');
                linesText.setText('行数: 0');

                // 清除游戏结束文本
                scene.children.list.forEach(child => {
                    if (child.type === 'Text' && (child.text === '游戏结束' || child.text === '点击重新开始')) {
                        child.destroy();
                    }
                });
            }

            function update(time, delta) {
                if (gameOver || isPaused) return;

                dropTime += delta;

                // 快速下落时使用更短的间隔（更快）
                const currentDropInterval = fastDrop ? Math.max(15, dropInterval / 40) : dropInterval;

                if (dropTime >= currentDropInterval) {
                    if (currentPiece) {
                        if (!movePiece(0, 1)) {
                            placePiece();
                            // 快速下落结束时给予额外分数
                            if (fastDrop) {
                                score += 1;
                                scoreText.setText('分数: ' + score);
                            }
                        } else if (fastDrop) {
                            // 快速下落时给予分数
                            score += 1;
                            scoreText.setText('分数: ' + score);
                        }
                    }
                    dropTime = 0;
                }

                render();
            }

            function render() {
                graphics.clear();

                // 快速下落时的背景效果
                if (fastDrop) {
                    graphics.fillStyle(0x444444, 0.3);
                    graphics.fillRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);
                }

                // 绘制游戏区域边框
                graphics.lineStyle(Math.floor(3), fastDrop ? 0x00ff00 : 0xffffff);
                graphics.strokeRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);

                // 绘制网格背景
                graphics.lineStyle(Math.max(1, Math.floor(1)), 0x333333);
                for (let x = 0; x <= GRID_WIDTH; x++) {
                    graphics.moveTo(GRID_X + x * CELL_SIZE, GRID_Y);
                    graphics.lineTo(GRID_X + x * CELL_SIZE, GRID_Y + GRID_HEIGHT * CELL_SIZE);
                }
                for (let y = 0; y <= GRID_HEIGHT; y++) {
                    graphics.moveTo(GRID_X, GRID_Y + y * CELL_SIZE);
                    graphics.lineTo(GRID_X + GRID_WIDTH * CELL_SIZE, GRID_Y + y * CELL_SIZE);
                }
                graphics.strokePath();

                // 消除动画帧渲染
                if (window._cellAnims) {
                    window._cellAnims = window._cellAnims.filter(anim => !anim.done);
                    for (const anim of window._cellAnims) {
                        const alpha = 1 - anim.t / 20;
                        const scale = 1 + 0.3 * (anim.t / 20);
                        drawCell(anim.x, anim.y, COLORS[gameGrid[anim.y][anim.x] || 1], alpha, scale);
                    }
                }

                // 绘制下落动画中的格子
                if (window._fallAnims) {
                    for (const anim of window._fallAnims) {
                        if (!anim.done) {
                            // 插值位置
                            const frac = anim.t / 20;
                            const drawY = anim.fromY + (anim.toY - anim.fromY) * frac;
                            drawCell(anim.x, drawY, COLORS[anim.color]);
                        }
                    }
                }
                // 绘制已放置的方块
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        // 跳过动画中的格子
                        if (window._cellAnims && window._cellAnims.some(a => a.x === x && a.y === y && !a.done)) continue;
                        if (window._fallAnims && window._fallAnims.some(a => a.x === x && a.toY === y && !a.done)) continue;
                        if (gameGrid[y][x] !== 0) {
                            drawCell(x, y, COLORS[gameGrid[y][x]]);
                        }
                    }
                }

                // 绘制落点提示方块（半透明）
                if (currentPiece) {
                    const ghostPiece = {
                        ...currentPiece,
                        y: currentPiece.y
                    };
                    // 计算幽灵方块位置
                    while (!checkCollision(ghostPiece, 0, 1)) {
                        ghostPiece.y++;
                    }
                    // 绘制落点提示方块（半透明）
                    for (let y = 0; y < ghostPiece.shape.length; y++) {
                        for (let x = 0; x < ghostPiece.shape[y].length; x++) {
                            if (ghostPiece.shape[y][x] !== 0) {
                                const gridX = ghostPiece.x + x;
                                const gridY = ghostPiece.y + y;
                                if (gridY >= 0 && gridY !== currentPiece.y + y) {
                                    drawCell(gridX, gridY, COLORS[ghostPiece.color], 0.5);
                                }
                            }
                        }
                    }
                }

                // 绘制当前方块
                if (currentPiece) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;
                                if (gridY >= 0) {
                                    drawCell(gridX, gridY, COLORS[currentPiece.color]);
                                }
                            }
                        }
                    }
                }

                // 绘制下一个方块预览
                if (nextPiece) {
                    const previewX = GAME_WIDTH * 0.7;
                    const previewY = 450;
                    const previewCellSize = Math.floor(20);

                    for (let y = 0; y < nextPiece.shape.length; y++) {
                        for (let x = 0; x < nextPiece.shape[y].length; x++) {
                            if (nextPiece.shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * previewCellSize,
                                    previewY + y * previewCellSize,
                                    previewCellSize - 1,
                                    previewCellSize - 1
                                );
                            }
                        }
                    }
                }
            }

            function drawCell(x, y, color, alpha = 1, scale = 1) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;
                const size = CELL_SIZE * scale;
                const offset = (CELL_SIZE - size) / 2;

                // 绘制方块主体
                graphics.fillStyle(color, alpha);
                graphics.fillRect(pixelX + 1 + offset, pixelY + 1 + offset, size - 2, size - 2);

                // 简化的3D效果 - 使用更简单的颜色计算
                const rgb = Phaser.Display.Color.IntegerToRGB(color);

                // 绘制高光效果
                const highlightColor = Phaser.Display.Color.GetColor32(
                    Math.min(255, rgb.r + 60),
                    Math.min(255, rgb.g + 60),
                    Math.min(255, rgb.b + 60),
                    255
                );
                graphics.fillStyle(highlightColor, alpha);
                graphics.fillRect(pixelX + 1 + offset, pixelY + 1 + offset, size - 2, Math.max(4, size / 8));
                graphics.fillRect(pixelX + 1 + offset, pixelY + 1 + offset, Math.max(4, size / 8), size - 2);

                // 绘制阴影效果
                const shadowColor = Phaser.Display.Color.GetColor32(
                    Math.max(0, rgb.r - 60),
                    Math.max(0, rgb.g - 60),
                    Math.max(0, rgb.b - 60),
                    255
                );
                graphics.fillStyle(shadowColor, alpha);
                graphics.fillRect(pixelX + size - 5 - offset, pixelY + 1 + offset, 4, size - 2);
                graphics.fillRect(pixelX + 1 + offset, pixelY + size - 5 - offset, size - 2, 4);
            }

            function drawGhostCell(x, y, color) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 绘制半透明的幽灵方块
                const rgb = Phaser.Display.Color.IntegerToRGB(color);
                const ghostColor = Phaser.Display.Color.GetColor32(rgb.r, rgb.g, rgb.b, 80);

                graphics.fillStyle(ghostColor);
                graphics.fillRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);

                // 绘制边框
                graphics.lineStyle(2, color, 0.5);
                graphics.strokeRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);
            }

            function placePiece() {
                if (!currentPiece) return;

                // 停止快速下落
                fastDrop = false;

                // 将当前方块放置到网格中
                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x] !== 0) {
                            const gridX = currentPiece.x + x;
                            const gridY = currentPiece.y + y;
                            if (gridY >= 0) {
                                gameGrid[gridY][gridX] = currentPiece.color;
                            }
                        }
                    }
                }

                // 直接消除（不再重力补全）
                clearMatches(() => {
                    spawnPiece();
                });
            }

            // 启动游戏
            const game = new Phaser.Game(config);
        });
    </script>
</body>
</html>
