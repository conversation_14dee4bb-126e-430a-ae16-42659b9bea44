<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>经典数字华容道</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }
        #game-container {
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        class NumberPuzzleGame extends Phaser.Scene {
            constructor() {
                super({ key: 'NumberPuzzleGame' });
                this.gridSize = 3;
                this.tileSize = 100;
                this.gap = 5;
                this.grid = [];
                this.emptyPos = { x: 2, y: 2 };
                this.moves = 0;
                this.isAnimating = false;
            }

            preload() {
                // 创建数字方块纹理
                this.createTileTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(200, 400, 400, 800, 0xFFFFFF);

                // this.add.rectangle(200, 700, 400, 800, 0x421c0f);
               

                // 标题
                this.add.text(200, 80, '数字华容道', {
                    fontSize: '32px',
                    fontFamily: 'Microsoft YaHei',
                    fill: '#000000',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 目标提示
                this.add.text(200, 120, '目标:12345678排列 ', {
                    fontSize: '16px',
                    fontFamily: 'Microsoft YaHei',
                    fill: '#000000',
                    align: 'center'
                }).setOrigin(0.5);

                // 移动计数
                this.movesText = this.add.text(200, 180, '移动次数: 0', {
                    fontSize: '18px',
                    fontFamily: 'Microsoft YaHei',
                    fill: '#000000'
                }).setOrigin(0.5);

                // 初始化游戏网格
                this.initializeGrid();
                this.shuffleGrid();
                this.createGridDisplay();

                // 重新开始按钮
                const restartBtn = this.add.rectangle(200, 720, 150, 50, 0x3498db)
                    .setInteractive()
                    .on('pointerdown', () => this.restartGame());

                this.add.text(200, 720, '重新开始', {
                    fontSize: '18px',
                    fontFamily: 'Microsoft YaHei',
                    fill: '#ffffff'
                }).setOrigin(0.5);

                // 胜利文本（初始隐藏）
                this.winText = this.add.text(200, 650, '恭喜通关！', {
                    fontSize: '24px',
                    fontFamily: 'Microsoft YaHei',
                    fill: '#f1c40f',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setVisible(false);
            }

            createTileTextures() {
                const graphics = this.add.graphics();

                // 创建数字1-8的纹理
                for (let i = 1; i <= 8; i++) {
                    graphics.clear();

                    // 绘制方块背景
                    graphics.fillStyle(0xe4b77c);
                    graphics.fillRoundedRect(0, 0, this.tileSize, this.tileSize, 10);

                    // 绘制边框
                    graphics.lineStyle(3, 0x6d543a);
                    graphics.strokeRoundedRect(0, 0, this.tileSize, this.tileSize, 10);

                    graphics.generateTexture(`tile${i}`, this.tileSize, this.tileSize);
                }

                // 创建空格纹理
                graphics.clear();
                graphics.fillStyle(0x421c0f);
                graphics.fillRoundedRect(0, 0, this.tileSize, this.tileSize, 10);
                graphics.lineStyle(2, 0x2c3e50);
                graphics.strokeRoundedRect(0, 0, this.tileSize, this.tileSize, 10);
                graphics.generateTexture('empty', this.tileSize, this.tileSize);

                graphics.destroy();
            }

            initializeGrid() {
                // 初始化为目标状态
                this.grid = [
                    [1, 2, 3],
                    [4, 5, 6],
                    [7, 8, 0]
                ];
                this.emptyPos = { x: 2, y: 2 };
            }

            shuffleGrid() {
                // 通过随机移动来打乱，确保可解
                for (let i = 0; i < 1000; i++) {
                    const neighbors = this.getEmptyNeighbors();
                    if (neighbors.length > 0) {
                        const randomNeighbor = neighbors[Math.floor(Math.random() * neighbors.length)];
                        this.swapWithEmpty(randomNeighbor.x, randomNeighbor.y, false);
                    }
                }
                this.moves = 0;
            }

            createGridDisplay() {
                // 清除现有显示
                if (this.gridGroup) {
                    this.gridGroup.destroy();
                }
                
                this.gridGroup = this.add.group();
                const startX = 200 - (this.gridSize * (this.tileSize + this.gap) - this.gap) / 2 + this.tileSize / 2;
                const startY = 350;

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const x = startX + col * (this.tileSize + this.gap);
                        const y = startY + row * (this.tileSize + this.gap);
                        const value = this.grid[row][col];

                        let tile;
                        if (value === 0) {
                            tile = this.add.image(x, y, 'empty');
                        } else {
                            tile = this.add.image(x, y, `tile${value}`);

                            // 添加数字文本
                            const numberText = this.add.text(x, y, value.toString(), {
                                fontSize: '36px',
                                fontFamily: 'Microsoft YaHei',
                                fill: '#6d543a',
                                fontStyle: 'bold'
                            }).setOrigin(0.5);

                            this.gridGroup.add(numberText);
                        }

                        tile.setInteractive();
                        tile.gridX = col;
                        tile.gridY = row;
                        tile.on('pointerdown', () => this.onTileClick(col, row));

                        this.gridGroup.add(tile);
                    }
                }
            }

            onTileClick(x, y) {
                if (this.isAnimating) return;

                const value = this.grid[y][x];
                if (value === 0) return; // 点击空格无效

                // 检查是否与空格相邻
                if (this.isAdjacentToEmpty(x, y)) {
                    this.swapWithEmpty(x, y, true);
                }
            }

            isAdjacentToEmpty(x, y) {
                const dx = Math.abs(x - this.emptyPos.x);
                const dy = Math.abs(y - this.emptyPos.y);
                return (dx === 1 && dy === 0) || (dx === 0 && dy === 1);
            }

            swapWithEmpty(x, y, countMove = true) {
                // 交换位置
                this.grid[this.emptyPos.y][this.emptyPos.x] = this.grid[y][x];
                this.grid[y][x] = 0;
                this.emptyPos = { x, y };

                if (countMove) {
                    this.moves++;
                    this.movesText.setText(`移动次数: ${this.moves}`);
                    
                    // 重新创建显示
                    this.createGridDisplay();
                    
                    // 检查胜利条件
                    if (this.checkWin()) {
                        this.showWin();
                    }
                }
            }

            getEmptyNeighbors() {
                const neighbors = [];
                const directions = [
                    { x: 0, y: -1 }, { x: 0, y: 1 },
                    { x: -1, y: 0 }, { x: 1, y: 0 }
                ];

                for (const dir of directions) {
                    const newX = this.emptyPos.x + dir.x;
                    const newY = this.emptyPos.y + dir.y;
                    
                    if (newX >= 0 && newX < this.gridSize && newY >= 0 && newY < this.gridSize) {
                        neighbors.push({ x: newX, y: newY });
                    }
                }
                
                return neighbors;
            }

            checkWin() {
                const target = [
                    [1, 2, 3],
                    [4, 5, 6],
                    [7, 8, 0]
                ];

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (this.grid[row][col] !== target[row][col]) {
                            return false;
                        }
                    }
                }
                return true;
            }

            showWin() {
                this.winText.setVisible(true);

                // 添加庆祝动画
                this.tweens.add({
                    targets: this.winText,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    duration: 500,
                    yoyo: true,
                    repeat: 2
                });
            }

            restartGame() {
                this.winText.setVisible(false);
                this.initializeGrid();
                this.shuffleGrid();
                this.createGridDisplay();
                this.movesText.setText('移动次数: 0');
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 400,
            height: 800,
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: NumberPuzzleGame,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>
