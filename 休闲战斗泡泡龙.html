<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗基础版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'playing';
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;
    let isCreatingWave = false; // 防止重复创建波次的标志位

    // 泡泡龙游戏变量
    const BUBBLE_RADIUS = 20; // 增大泡泡半径
    const GRID_WIDTH = 13;
    const GRID_HEIGHT = 12;
    const BUBBLE_COLORS = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']; // 减少到4种颜色

    let bubbleGrid = [];
    let bubbleGridSprites = [];
    let currentBubble = null;
    let nextBubble = null;
    let currentBubbleSprite = null;
    let nextBubbleSprite = null;
    let movingBubbleSprite = null;
    let shooter = { x: 375, y: 1050, angle: 0 }; // 移到泡泡区域最下方
    let shooterGraphics = null;
    let bubbleScore = 0;
    let bubbleScoreText = null;
    let bubbleGameRunning = true;

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600, // 从2000减少到800，攻击更快
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background2.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/ak.png');

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

        // 创建泡泡龙游戏区域背景 (扩大高度)
        const bubbleAreaBg = this.add.graphics();
        bubbleAreaBg.fillStyle(0x87CEEB, 0.8);
        bubbleAreaBg.fillRect(0, 0, 750, 890); // 保持高度890
        bubbleAreaBg.setDepth(-5);

        // 创建战斗游戏区域背景 (下方区域)
        const battleAreaBg = this.add.graphics();
        battleAreaBg.fillStyle(0x1a1a2e, 0.8);
        battleAreaBg.fillRect(0, 890, 750, 444); // 下方区域
        battleAreaBg.setDepth(-5);

        // 初始化泡泡龙游戏
        initBubbleGame.call(this);

        // 创建主角 - 在战斗区域 (下方1/3)
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6); // 缩小到0.5
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        // 根据Y轴位置设置深度，确保与怪物有正确的层级关系
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8); // 武器缩放
        playerWeapon.setOrigin(0.5, 1); // 设置旋转中心在下中
        // 去掉武器旋转，保持水平
        playerWeapon.setDepth(100 + player.y * 0.1 + 1); // 武器深度比角色稍高

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 设置鼠标事件
        setupBubbleInput.call(this);
    }

    // ==================== 泡泡龙游戏函数 ====================

    // 初始化泡泡龙游戏
    function initBubbleGame() {
        // 初始化网格
        bubbleGrid = [];
        bubbleGridSprites = [];

        for (let row = 0; row < GRID_HEIGHT; row++) {
            bubbleGrid[row] = [];
            bubbleGridSprites[row] = [];
            for (let col = 0; col < GRID_WIDTH; col++) {
                if (row < 6) { // 只在前6行放置泡泡
                    // 六边形网格布局
                    if ((row % 2 === 0 && col < GRID_WIDTH) || (row % 2 === 1 && col < GRID_WIDTH - 1)) {
                        const color = BUBBLE_COLORS[Math.floor(Math.random() * BUBBLE_COLORS.length)];
                        bubbleGrid[row][col] = {
                            color: color,
                            x: getBubbleGridX(row, col),
                            y: getBubbleGridY(row),
                            exists: true
                        };

                        // 创建泡泡精灵
                        const bubbleSprite = this.add.circle(
                            getBubbleGridX(row, col),
                            getBubbleGridY(row),
                            BUBBLE_RADIUS,
                            Phaser.Display.Color.HexStringToColor(color).color
                        );
                        bubbleSprite.setStrokeStyle(2, 0xffffff);
                        bubbleSprite.setDepth(10);
                        bubbleGridSprites[row][col] = bubbleSprite;
                    } else {
                        bubbleGrid[row][col] = null;
                        bubbleGridSprites[row][col] = null;
                    }
                } else {
                    bubbleGrid[row][col] = null;
                    bubbleGridSprites[row][col] = null;
                }
            }
        }

        // 创建当前和下一个泡泡
        currentBubble = createRandomBubbleData();
        nextBubble = createRandomBubbleData();

        // 创建发射器
        shooterGraphics = this.add.graphics();
        shooterGraphics.setDepth(15);
        updateShooterGraphics();

        // 创建当前泡泡精灵
        currentBubbleSprite = this.add.circle(
            shooter.x,
            shooter.y,
            BUBBLE_RADIUS,
            Phaser.Display.Color.HexStringToColor(currentBubble.color).color
        );
        currentBubbleSprite.setStrokeStyle(2, 0xffffff);
        currentBubbleSprite.setDepth(20);

        // 创建下一个泡泡预览
        nextBubbleSprite = this.add.circle(
            650,
            120,
            BUBBLE_RADIUS,
            Phaser.Display.Color.HexStringToColor(nextBubble.color).color
        );
        nextBubbleSprite.setStrokeStyle(2, 0xffffff);
        nextBubbleSprite.setDepth(20);

        // 创建分数显示
        bubbleScoreText = this.add.text(50, 100, '泡泡龙分数: 0', {
            fontSize: '20px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });
        bubbleScoreText.setDepth(25);

        // 创建下一个泡泡标签
        this.add.text(600, 90, '下一个:', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setDepth(25);

        // 创建倾斜地板
        createSlopedFloor.call(this);

        // 创建水管
        createPipe.call(this);
    }

    // 创建向左倾斜45度的地板
    function createSlopedFloor() {
        const floorGraphics = this.add.graphics();
        floorGraphics.setDepth(5); // 在泡泡下方但在背景上方

        // 地板参数 - 全宽度，向左倾斜45度
        const gameWidth = 750;
        const floorThickness = 20;
        const floorCenterY = 1150; // 地板中心高度
        const tiltDistance = gameWidth / 2; // 45度倾斜时的高度差

        // 计算地板四个角的坐标 - 向左倾斜（左侧高，右侧低）
        const topLeft = { x: 0, y: floorCenterY + tiltDistance / 2 };
        const topRight = { x: gameWidth, y: floorCenterY - tiltDistance / 2 };
        const bottomLeft = { x: 0, y: topLeft.y + floorThickness };
        const bottomRight = { x: gameWidth, y: topRight.y + floorThickness };

        // 绘制地板主体
        floorGraphics.fillStyle(0x8B4513); // 棕色
        floorGraphics.beginPath();
        floorGraphics.moveTo(topLeft.x, topLeft.y);
        floorGraphics.lineTo(topRight.x, topRight.y);
        floorGraphics.lineTo(bottomRight.x, bottomRight.y);
        floorGraphics.lineTo(bottomLeft.x, bottomLeft.y);
        floorGraphics.closePath();
        floorGraphics.fillPath();

        // 绘制地板边框
        floorGraphics.lineStyle(3, 0x654321); // 深棕色边框
        floorGraphics.strokePath();

        // 添加地板表面高光
        floorGraphics.lineStyle(2, 0xD2B48C); // 浅棕色高光
        floorGraphics.beginPath();
        floorGraphics.moveTo(topLeft.x, topLeft.y);
        floorGraphics.lineTo(topRight.x, topRight.y);
        floorGraphics.strokePath();

        // 添加纹理线条
        for (let i = 1; i < 8; i++) {
            const progress = i / 8;
            const lineStartX = topLeft.x + (topRight.x - topLeft.x) * progress;
            const lineStartY = topLeft.y + (topRight.y - topLeft.y) * progress;
            const lineEndX = bottomLeft.x + (bottomRight.x - bottomLeft.x) * progress;
            const lineEndY = bottomLeft.y + (bottomRight.y - bottomLeft.y) * progress;

            floorGraphics.lineStyle(1, 0x654321, 0.3);
            floorGraphics.beginPath();
            floorGraphics.moveTo(lineStartX, lineStartY);
            floorGraphics.lineTo(lineEndX, lineEndY);
            floorGraphics.strokePath();
        }

        // 存储地板信息供物理计算使用
        this.slopedFloor = {
            startX: 0,
            endX: gameWidth,
            startY: topLeft.y,
            endY: topRight.y,
            thickness: floorThickness,
            angle: -Math.PI / 4 // 向左倾斜45度（负角度）
        };
    }

    // 创建水管连接到角色
    function createPipe() {
        const pipeGraphics = this.add.graphics();
        pipeGraphics.setDepth(6); // 在地板上方

        // 水管参数 - 修正到左侧
        const pipeWidth = 60;
        const pipeStartX = 50; // 地板左侧低点附近
        const pipeStartY = 1285; // 地板底部
        const pipeEndX = 50;   // 角色位置
        const pipeEndY = 400;   // 角色上方

        // 绘制直线水管外壁
        pipeGraphics.lineStyle(pipeWidth + 4, 0x2F4F2F); // 深绿色外壁
        pipeGraphics.beginPath();
        pipeGraphics.moveTo(pipeStartX, pipeStartY);
        pipeGraphics.lineTo(pipeEndX, pipeEndY);
        pipeGraphics.strokePath();

        // 绘制直线水管内壁
        pipeGraphics.lineStyle(pipeWidth, 0x228B22); // 绿色内壁
        pipeGraphics.beginPath();
        pipeGraphics.moveTo(pipeStartX, pipeStartY);
        pipeGraphics.lineTo(pipeEndX, pipeEndY);
        pipeGraphics.strokePath();

        // 绘制直线水管高光
        pipeGraphics.lineStyle(12, 0x90EE90, 0.6); // 浅绿色高光
        pipeGraphics.beginPath();
        pipeGraphics.moveTo(pipeStartX, pipeStartY);
        pipeGraphics.lineTo(pipeEndX, pipeEndY);
        pipeGraphics.strokePath();

        // 在水管入口处添加收集区域标识
        const collectorGraphics = this.add.graphics();
        collectorGraphics.setDepth(7);

        // 绘制收集器开口
        collectorGraphics.fillStyle(0x2F4F2F);
        collectorGraphics.fillCircle(pipeStartX, pipeStartY - 10, 25);

        // 绘制收集器边框
        collectorGraphics.lineStyle(3, 0x1F3F1F);
        collectorGraphics.strokeCircle(pipeStartX, pipeStartY - 10, 25);

        // 绘制收集器内部
        collectorGraphics.fillStyle(0x000000, 0.8);
        collectorGraphics.fillCircle(pipeStartX, pipeStartY - 10, 20);

        // 存储水管信息
        this.pipe = {
            collectorX: pipeStartX,
            collectorY: pipeStartY - 10,
            collectorRadius: 35, // 增大收集范围
            targetX: pipeEndX,
            targetY: pipeEndY
        };
    }

    // 获取网格X坐标
    function getBubbleGridX(row, col) {
        const bubbleSpacing = BUBBLE_RADIUS * 2 + 2; // 泡泡间距
        const offsetX = (row % 2) * (BUBBLE_RADIUS + 1); // 六边形偏移
        const gridWidth = (GRID_WIDTH - 1) * bubbleSpacing + BUBBLE_RADIUS * 2; // 网格总宽度
        const startX = (750 - gridWidth) / 2 + 50; // 向右移动80像素，避开水管
        return startX + col * bubbleSpacing + BUBBLE_RADIUS + offsetX;
    }

    // 获取网格Y坐标
    function getBubbleGridY(row) {
        return row * (BUBBLE_RADIUS * 1.8) + BUBBLE_RADIUS + 400; // 向上移动40像素
    }

    // 创建随机泡泡数据
    function createRandomBubbleData() {
        return {
            color: BUBBLE_COLORS[Math.floor(Math.random() * BUBBLE_COLORS.length)],
            x: shooter.x,
            y: shooter.y,
            vx: 0,
            vy: 0,
            moving: false
        };
    }

    // 更新发射器图形
    function updateShooterGraphics() {
        if (!shooterGraphics) return;

        shooterGraphics.clear();

        const shooterLength = 50;
        // 使用正确的角度计算，向上为0度
        const endX = shooter.x + Math.sin(shooter.angle) * shooterLength;
        const endY = shooter.y - Math.cos(shooter.angle) * shooterLength; // 向上为负Y

        // 发射器线条
        shooterGraphics.lineStyle(6, 0x333333);
        shooterGraphics.beginPath();
        shooterGraphics.moveTo(shooter.x, shooter.y);
        shooterGraphics.lineTo(endX, endY);
        shooterGraphics.strokePath();

        // 发射器底座
        shooterGraphics.fillStyle(0x666666);
        shooterGraphics.fillCircle(shooter.x, shooter.y, 12);
    }

    // 设置泡泡龙输入
    function setupBubbleInput() {
        // 鼠标移动事件
        this.input.on('pointermove', (pointer) => {
            if (pointer.y < 890) { // 只在泡泡龙区域响应
                const mouseX = pointer.x;
                const mouseY = pointer.y;

                // 计算角度，向上为负Y方向
                const deltaX = mouseX - shooter.x;
                const deltaY = mouseY - shooter.y;
                shooter.angle = Math.atan2(deltaX, -deltaY); // 注意这里用-deltaY，使向上为0度

                // 限制角度范围（向上为中心，左右各60度）
                const maxAngle = Math.PI / 3; // 60度
                if (shooter.angle > maxAngle) shooter.angle = maxAngle;
                if (shooter.angle < -maxAngle) shooter.angle = -maxAngle;

                updateShooterGraphics();
            }
        });

        // 鼠标点击事件
        this.input.on('pointerdown', (pointer) => {
            if (pointer.y < 890 && currentBubble && !currentBubble.moving) { // 只在泡泡龙区域响应
                shootBubble.call(this);
            }
        });
    }

    // 发射泡泡
    function shootBubble() {
        if (!currentBubble || currentBubble.moving) return;

        const speed = 8;
        // 使用正确的速度计算，向上为0度
        currentBubble.vx = Math.sin(shooter.angle) * speed;
        currentBubble.vy = -Math.cos(shooter.angle) * speed; // 向上为负Y
        currentBubble.moving = true;

        // 创建移动的泡泡精灵
        movingBubbleSprite = this.add.circle(
            currentBubble.x,
            currentBubble.y,
            BUBBLE_RADIUS,
            Phaser.Display.Color.HexStringToColor(currentBubble.color).color
        );
        movingBubbleSprite.setStrokeStyle(2, 0xffffff);
        movingBubbleSprite.setDepth(20);

        // 隐藏发射器上的泡泡
        currentBubbleSprite.setVisible(false);
    }

    // 更新移动的泡泡
    function updateMovingBubble() {
        if (!movingBubbleSprite || !currentBubble.moving) return;

        currentBubble.x += currentBubble.vx;
        currentBubble.y += currentBubble.vy;

        // 更新精灵位置
        movingBubbleSprite.x = currentBubble.x;
        movingBubbleSprite.y = currentBubble.y;

        // 边界反弹
        if (currentBubble.x <= BUBBLE_RADIUS || currentBubble.x >= 750 - BUBBLE_RADIUS) {
            currentBubble.vx = -currentBubble.vx;
        }

        // 检查碰撞
        if (checkBubbleCollision()) {
            placeBubble.call(this);
        }

        // 检查是否到达顶部
        if (currentBubble.y <= BUBBLE_RADIUS + 460) {
            placeBubble.call(this);
        }
    }

    // 检查泡泡碰撞
    function checkBubbleCollision() {
        for (let row = 0; row < GRID_HEIGHT; row++) {
            for (let col = 0; col < GRID_WIDTH; col++) {
                const gridBubble = bubbleGrid[row][col];
                if (gridBubble && gridBubble.exists) {
                    const distance = Math.sqrt(
                        Math.pow(currentBubble.x - gridBubble.x, 2) +
                        Math.pow(currentBubble.y - gridBubble.y, 2)
                    );
                    if (distance < BUBBLE_RADIUS * 2) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    // 放置泡泡到网格
    function placeBubble() {
        const gridPos = getGridPosition(currentBubble.x, currentBubble.y);

        if (gridPos && gridPos.row >= 0 && gridPos.row < GRID_HEIGHT &&
            gridPos.col >= 0 && gridPos.col < GRID_WIDTH && !bubbleGrid[gridPos.row][gridPos.col]) {

            bubbleGrid[gridPos.row][gridPos.col] = {
                color: currentBubble.color,
                x: getBubbleGridX(gridPos.row, gridPos.col),
                y: getBubbleGridY(gridPos.row),
                exists: true
            };

            // 创建新的网格泡泡精灵
            const newBubbleSprite = this.add.circle(
                getBubbleGridX(gridPos.row, gridPos.col),
                getBubbleGridY(gridPos.row),
                BUBBLE_RADIUS,
                Phaser.Display.Color.HexStringToColor(currentBubble.color).color
            );
            newBubbleSprite.setStrokeStyle(2, 0xffffff);
            newBubbleSprite.setDepth(10);
            bubbleGridSprites[gridPos.row][gridPos.col] = newBubbleSprite;

            // 检查相同颜色泡泡并使其下落
            const connectedBubbles = findConnectedBubbles(gridPos.row, gridPos.col, currentBubble.color);
            if (connectedBubbles.length >= 3) {
                // 让相同颜色的泡泡下落
                dropConnectedBubbles.call(this, connectedBubbles);
            }

            // 准备下一个泡泡
            prepareNextBubble.call(this);
        } else {
            // 如果无法放置，也准备下一个泡泡
            prepareNextBubble.call(this);
        }

        // 清理移动的泡泡
        if (movingBubbleSprite) {
            movingBubbleSprite.destroy();
            movingBubbleSprite = null;
        }
        currentBubble.moving = false;
    }

    // 获取网格位置
    function getGridPosition(x, y) {
        let bestRow = -1, bestCol = -1;
        let minDistance = Infinity;

        for (let row = 0; row < GRID_HEIGHT; row++) {
            for (let col = 0; col < GRID_WIDTH; col++) {
                if ((row % 2 === 0 && col < GRID_WIDTH) || (row % 2 === 1 && col < GRID_WIDTH - 1)) {
                    const gridX = getBubbleGridX(row, col);
                    const gridY = getBubbleGridY(row);
                    const distance = Math.sqrt(Math.pow(x - gridX, 2) + Math.pow(y - gridY, 2));

                    if (distance < minDistance) {
                        minDistance = distance;
                        bestRow = row;
                        bestCol = col;
                    }
                }
            }
        }

        return minDistance < BUBBLE_RADIUS * 2 ? { row: bestRow, col: bestCol } : null;
    }

    // 查找连接的同色泡泡
    function findConnectedBubbles(startRow, startCol, color) {
        const visited = new Set();
        const connected = [];
        const stack = [{ row: startRow, col: startCol }];

        while (stack.length > 0) {
            const { row, col } = stack.pop();
            const key = `${row},${col}`;

            if (visited.has(key)) continue;
            visited.add(key);

            const bubble = bubbleGrid[row][col];
            if (bubble && bubble.exists && bubble.color === color) {
                connected.push({ row, col });

                // 检查相邻位置
                const neighbors = getNeighbors(row, col);
                neighbors.forEach(neighbor => {
                    if (!visited.has(`${neighbor.row},${neighbor.col}`)) {
                        stack.push(neighbor);
                    }
                });
            }
        }

        return connected;
    }

    // 获取相邻位置
    function getNeighbors(row, col) {
        const neighbors = [];
        const isEvenRow = row % 2 === 0;

        // 六边形网格的相邻位置
        const directions = isEvenRow ?
            [[-1, -1], [-1, 0], [0, -1], [0, 1], [1, -1], [1, 0]] :
            [[-1, 0], [-1, 1], [0, -1], [0, 1], [1, 0], [1, 1]];

        directions.forEach(([dRow, dCol]) => {
            const newRow = row + dRow;
            const newCol = col + dCol;

            if (newRow >= 0 && newRow < GRID_HEIGHT && newCol >= 0 && newCol < GRID_WIDTH) {
                neighbors.push({ row: newRow, col: newCol });
            }
        });

        return neighbors;
    }

    // 移除悬空泡泡（让它们下落）
    function removeFloatingBubbles() {
        const connected = new Set();

        // 从顶行开始标记所有连接的泡泡
        for (let col = 0; col < GRID_WIDTH; col++) {
            if (bubbleGrid[0][col] && bubbleGrid[0][col].exists) {
                markConnectedBubbles(0, col, connected);
            }
        }

        // 让未连接的泡泡下落
        const floatingBubbles = [];
        for (let row = 0; row < GRID_HEIGHT; row++) {
            for (let col = 0; col < GRID_WIDTH; col++) {
                const bubble = bubbleGrid[row][col];
                if (bubble && bubble.exists && !connected.has(`${row},${col}`)) {
                    floatingBubbles.push({row, col});
                }
            }
        }

        // 让悬空泡泡下落
        floatingBubbles.forEach((pos, index) => {
            const sprite = bubbleGridSprites[pos.row][pos.col];
            if (sprite) {
                // 从网格中移除
                bubbleGrid[pos.row][pos.col] = null;
                bubbleGridSprites[pos.row][pos.col] = null;

                // 添加物理属性，延迟启动让泡泡依次下落
                setTimeout(() => {
                    sprite.velocityY = 0; // 初始垂直速度
                    sprite.velocityX = (Math.random() - 0.5) * 150; // 随机水平速度，比匹配的更大
                    sprite.gravity = 350; // 稍大的重力加速度
                    sprite.bounce = 0.7; // 更高的弹性
                    sprite.isDropping = true;
                    sprite.groundY = 1334; // 地面位置

                    // 立即得分
                    bubbleScore += 5;

                    // 更新分数显示
                    if (bubbleScoreText) {
                        bubbleScoreText.setText('泡泡龙分数: ' + bubbleScore);
                    }
                }, index * 100); // 每个泡泡延迟100ms
            }
        });
    }

    // 标记连接的泡泡
    function markConnectedBubbles(row, col, connected) {
        const key = `${row},${col}`;
        if (connected.has(key)) return;

        const bubble = bubbleGrid[row][col];
        if (!bubble || !bubble.exists) return;

        connected.add(key);

        const neighbors = getNeighbors(row, col);
        neighbors.forEach(neighbor => {
            markConnectedBubbles(neighbor.row, neighbor.col, connected);
        });
    }

    // 让连接的泡泡下落
    function dropConnectedBubbles(connectedBubbles) {
        connectedBubbles.forEach(pos => {
            const bubble = bubbleGrid[pos.row][pos.col];
            const sprite = bubbleGridSprites[pos.row][pos.col];

            if (bubble && sprite) {
                // 从网格中移除
                bubbleGrid[pos.row][pos.col] = null;
                bubbleGridSprites[pos.row][pos.col] = null;

                // 添加物理属性
                sprite.velocityY = 0; // 初始垂直速度
                sprite.velocityX = (Math.random() - 0.5) * 100; // 随机水平速度
                sprite.gravity = 300; // 重力加速度
                sprite.bounce = 0.6; // 弹性系数
                sprite.isDropping = true;
                sprite.groundY = 1334; // 地面位置

                // 立即得分
                bubbleScore += 10;

                // 更新分数显示
                if (bubbleScoreText) {
                    bubbleScoreText.setText('泡泡龙分数: ' + bubbleScore);
                }
            }
        });

        // 检查并移除悬空的泡泡
        setTimeout(() => {
            removeFloatingBubbles.call(this);
        }, 100);
    }

    // 准备下一个泡泡
    function prepareNextBubble() {
        currentBubble = nextBubble;
        currentBubble.x = shooter.x;
        currentBubble.y = shooter.y;
        currentBubble.moving = false;
        nextBubble = createRandomBubbleData();

        // 更新精灵
        currentBubbleSprite.setVisible(true);
        currentBubbleSprite.setFillStyle(Phaser.Display.Color.HexStringToColor(currentBubble.color).color);
        nextBubbleSprite.setFillStyle(Phaser.Display.Color.HexStringToColor(nextBubble.color).color);

        // 更新分数显示
        if (bubbleScoreText) {
            bubbleScoreText.setText('泡泡龙分数: ' + bubbleScore);
        }
    }

    // 更新下落泡泡的物理效果
    function updateDroppingBubbles(delta) {
        const deltaSeconds = delta / 1000; // 转换为秒

        // 遍历所有网格精灵，查找正在下落的泡泡
        for (let row = 0; row < GRID_HEIGHT; row++) {
            for (let col = 0; col < GRID_WIDTH; col++) {
                const sprite = bubbleGridSprites[row][col];
                if (sprite && sprite.isDropping) {
                    updateBubblePhysics(sprite, deltaSeconds);
                }
            }
        }

        // 检查场景中的其他下落泡泡（已从网格移除的）
        this.children.list.forEach(child => {
            if (child.isDropping) {
                updateBubblePhysics(child, deltaSeconds);
            }
        });
    }

    // 更新单个泡泡的物理效果
    function updateBubblePhysics(sprite, deltaSeconds) {
        // 应用重力
        sprite.velocityY += sprite.gravity * deltaSeconds;

        // 更新位置
        sprite.x += sprite.velocityX * deltaSeconds;
        sprite.y += sprite.velocityY * deltaSeconds;

        // 边界碰撞检测
        const gameWidth = 750;

        // 左右边界反弹
        if (sprite.x <= BUBBLE_RADIUS) {
            sprite.x = BUBBLE_RADIUS;
            sprite.velocityX = -sprite.velocityX * sprite.bounce;
        } else if (sprite.x >= gameWidth - BUBBLE_RADIUS) {
            sprite.x = gameWidth - BUBBLE_RADIUS;
            sprite.velocityX = -sprite.velocityX * sprite.bounce;
        }

        // 检查与倾斜地板的碰撞
        const scene = sprite.scene;
        if (scene && scene.slopedFloor && checkSlopedFloorCollision(sprite, scene.slopedFloor)) {
            handleSlopedFloorCollision(sprite, scene.slopedFloor, deltaSeconds);
        }

        // 检查是否被水管收集
        if (scene && scene.pipe && checkPipeCollection(sprite, scene.pipe)) {
            handlePipeCollection.call(scene, sprite);
        }

        // 普通地面碰撞
        if (sprite.y >= sprite.groundY - BUBBLE_RADIUS) {
            sprite.y = sprite.groundY - BUBBLE_RADIUS;
            sprite.velocityY = -sprite.velocityY * sprite.bounce;
            sprite.velocityX *= 0.8; // 地面摩擦

            // 如果速度很小，停止弹跳
            if (Math.abs(sprite.velocityY) < 50) {
                sprite.velocityY = 0;
            }

            // 如果泡泡基本静止，开始淡出
            if (Math.abs(sprite.velocityY) < 10 && Math.abs(sprite.velocityX) < 10) {
                if (!sprite.fadingOut) {
                    sprite.fadingOut = true;
                    // 延迟后淡出并销毁
                    setTimeout(() => {
                        if (sprite && sprite.scene) {
                            sprite.scene.tweens.add({
                                targets: sprite,
                                alpha: 0,
                                duration: 500,
                                onComplete: () => {
                                    if (sprite) sprite.destroy();
                                }
                            });
                        }
                    }, 1000);
                }
            }
        }

        // 如果泡泡掉出屏幕底部太远，直接销毁
        if (sprite.y > sprite.groundY + 200) {
            sprite.destroy();
        }
    }

    // 检查与倾斜地板的碰撞
    function checkSlopedFloorCollision(sprite, floor) {
        // 检查泡泡是否在地板的X范围内
        if (sprite.x < floor.startX || sprite.x > floor.endX) {
            return false;
        }

        // 计算在当前X位置的地板高度
        const progress = (sprite.x - floor.startX) / (floor.endX - floor.startX);
        const floorY = floor.startY + (floor.endY - floor.startY) * progress;

        // 检查泡泡是否接触地板
        return sprite.y + BUBBLE_RADIUS >= floorY;
    }

    // 处理与倾斜地板的碰撞
    function handleSlopedFloorCollision(sprite, floor, deltaSeconds = 0.016) {
        // 计算在当前X位置的地板高度
        const progress = (sprite.x - floor.startX) / (floor.endX - floor.startX);
        const floorY = floor.startY + (floor.endY - floor.startY) * progress;

        // 将泡泡位置调整到地板表面
        sprite.y = floorY - BUBBLE_RADIUS;

        // 水管口位置
        const pipeX = 50;

        // 计算到水管口的距离和方向
        const distanceToPipe = sprite.x - pipeX;

        if (Math.abs(distanceToPipe) > 10) { // 如果距离水管口超过10像素
            // 强制向水管口方向移动
            const slideSpeed = 150;
            if (distanceToPipe > 0) {
                // 在水管口右侧，向左移动
                sprite.velocityX = -slideSpeed;
            } else {
                // 在水管口左侧，向右移动
                sprite.velocityX = slideSpeed;
            }
        } else {
            // 接近水管口时减速
            sprite.velocityX *= 0.3;
        }

        // 控制垂直速度，让泡泡贴着地板
        if (sprite.velocityY > 0) {
            sprite.velocityY *= 0.3; // 减少向下速度
        } else {
            sprite.velocityY *= 0.7; // 减少向上速度
        }

        // 限制最大垂直速度
        sprite.velocityY = Math.max(-30, Math.min(30, sprite.velocityY));
    }

    // 检查泡泡是否进入水管收集区域
    function checkPipeCollection(sprite, pipe) {
        const distance = Math.sqrt(
            Math.pow(sprite.x - pipe.collectorX, 2) +
            Math.pow(sprite.y - pipe.collectorY, 2)
        );
        return distance < pipe.collectorRadius;
    }

    // 处理泡泡被水管收集
    function handlePipeCollection(sprite) {
        if (sprite.beingCollected) return; // 防止重复收集

        sprite.beingCollected = true;
        sprite.isDropping = false; // 停止物理更新

        // 创建收集动画 - 泡泡被吸入水管
        this.tweens.add({
            targets: sprite,
            x: this.pipe.collectorX,
            y: this.pipe.collectorY,
            scaleX: 0.5,
            scaleY: 0.5,
            duration: 300,
            ease: 'Power2.easeIn',
            onComplete: () => {
                // 创建传送动画 - 泡泡通过水管到达角色
                sprite.setVisible(false);

                // 在角色位置创建泡泡出现效果
                const deliveredBubble = this.add.circle(
                    this.pipe.targetX,
                    this.pipe.targetY,
                    BUBBLE_RADIUS,
                    sprite.fillColor
                );
                deliveredBubble.setStrokeStyle(2, 0xffffff);
                deliveredBubble.setDepth(200);
                deliveredBubble.setScale(0.1);

                // 泡泡出现动画
                this.tweens.add({
                    targets: deliveredBubble,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 200,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        // 添加闪光效果
                        const sparkle = this.add.circle(
                            deliveredBubble.x,
                            deliveredBubble.y,
                            30,
                            0xFFFFFF,
                            0.8
                        );
                        sparkle.setDepth(201);

                        this.tweens.add({
                            targets: sparkle,
                            scaleX: 2,
                            scaleY: 2,
                            alpha: 0,
                            duration: 300,
                            onComplete: () => sparkle.destroy()
                        });

                        // 延迟后移除送达的泡泡
                        setTimeout(() => {
                            if (deliveredBubble) {
                                this.tweens.add({
                                    targets: deliveredBubble,
                                    alpha: 0,
                                    duration: 500,
                                    onComplete: () => deliveredBubble.destroy()
                                });
                            }
                        }, 1000);

                        // 增加额外分数
                        bubbleScore += 20;
                        if (bubbleScoreText) {
                            bubbleScoreText.setText('泡泡龙分数: ' + bubbleScore);
                        }

                        // 角色发射同颜色的特殊炮弹
                        fireSpecialProjectile.call(this, sprite.fillColor);
                    }
                });

                // 销毁原始泡泡
                sprite.destroy();
            }
        });
    }

    // 角色发射特殊炮弹
    function fireSpecialProjectile(bubbleColor) {
        if (!player || monsters.length === 0) return;

        // 找到最近的怪物作为目标
        let targetMonster = null;
        let minDistance = Infinity;

        monsters.forEach(monster => {
            const distance = Math.sqrt(
                Math.pow(monster.x - player.x, 2) +
                Math.pow(monster.y - player.y, 2)
            );
            if (distance < minDistance) {
                minDistance = distance;
                targetMonster = monster;
            }
        });

        if (!targetMonster) return;

        // 创建特殊炮弹
        const projectile = this.add.circle(
            player.x + 20,
            player.y - 30,
            12,
            bubbleColor
        );
        projectile.setStrokeStyle(3, 0xFFFFFF);
        projectile.setDepth(150);

        // 添加发光效果
        const glow = this.add.circle(
            projectile.x,
            projectile.y,
            20,
            bubbleColor,
            0.3
        );
        glow.setDepth(149);

        // 计算发射方向
        const angle = Math.atan2(
            targetMonster.y - projectile.y,
            targetMonster.x - projectile.x
        );

        // 炮弹飞行动画
        this.tweens.add({
            targets: [projectile, glow],
            x: targetMonster.x,
            y: targetMonster.y,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                // 爆炸效果
                createExplosion.call(this, targetMonster.x, targetMonster.y, bubbleColor);

                // 范围伤害
                dealAreaDamage.call(this, targetMonster.x, targetMonster.y, 80);

                // 清理炮弹
                projectile.destroy();
                glow.destroy();
            }
        });

        // 炮弹旋转效果
        this.tweens.add({
            targets: projectile,
            rotation: Math.PI * 4,
            duration: 500
        });
    }

    // 创建爆炸效果
    function createExplosion(x, y, color) {
        // 主爆炸圆圈
        const explosion = this.add.circle(x, y, 5, color);
        explosion.setDepth(200);

        this.tweens.add({
            targets: explosion,
            scaleX: 8,
            scaleY: 8,
            alpha: 0,
            duration: 400,
            ease: 'Power2.easeOut',
            onComplete: () => explosion.destroy()
        });

        // 爆炸粒子
        for (let i = 0; i < 8; i++) {
            const particle = this.add.circle(x, y, 3, color);
            particle.setDepth(199);

            const angle = (Math.PI * 2 * i) / 8;
            const distance = 60;

            this.tweens.add({
                targets: particle,
                x: x + Math.cos(angle) * distance,
                y: y + Math.sin(angle) * distance,
                alpha: 0,
                duration: 300,
                ease: 'Power2.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    // 范围伤害
    function dealAreaDamage(centerX, centerY, radius) {
        const damage = 50;

        monsters.forEach((monster, index) => {
            const distance = Math.sqrt(
                Math.pow(monster.x - centerX, 2) +
                Math.pow(monster.y - centerY, 2)
            );

            if (distance <= radius) {
                // 怪物受到伤害
                monster.health = (monster.health || 100) - damage;

                // 伤害数字显示
                const damageText = this.add.text(monster.x, monster.y - 20, `-${damage}`, {
                    fontSize: '16px',
                    fill: '#ff0000',
                    fontWeight: 'bold'
                });
                damageText.setDepth(201);

                this.tweens.add({
                    targets: damageText,
                    y: damageText.y - 30,
                    alpha: 0,
                    duration: 800,
                    onComplete: () => damageText.destroy()
                });

                // 怪物死亡
                if (monster.health <= 0) {
                    // 死亡动画
                    this.tweens.add({
                        targets: monster,
                        scaleX: 0,
                        scaleY: 0,
                        rotation: Math.PI,
                        alpha: 0,
                        duration: 300,
                        onComplete: () => {
                            monster.destroy();
                            monsters.splice(index, 1);
                            monstersKilled++;
                        }
                    });
                }
            }
        });
    }

    // ==================== 战斗游戏代码 ====================

    // 创建波次怪物
    function createWave() {
        // 确保标志位已设置，防止并发创建
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 清除现有血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，在战斗区域（下方1/3）
        const monsterSpacing = 60; // 怪物间距，适应较小空间
        const startX = 400; // 起始X位置
        const startY = player.y; // 与角色底部对齐

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部
            // 根据Y轴位置设置深度，Y轴越大（越靠下）深度越大，显示在前面
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
        }

        monstersKilled = 0;

        // 延迟初始化血条，确保怪物完全渲染后再创建血条
        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            // 血条创建完成后重置标志位
            isCreatingWave = false;
        }, 50);
    }



    // 移除战术棋盘创建函数

    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 多重射击：攻击多个目标
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i];

                // 创建攻击特效 - 从武器最右边中间位置发射
                const weaponRightX = playerWeapon ? playerWeapon.x + 40 : player.x + 60; // 武器最右边
                const weaponCenterY = playerWeapon ? playerWeapon.y - 15 : player.y - 30; // 武器中间高度
                const projectile = this.add.circle(weaponRightX, weaponCenterY, 5, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 30, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 狂战士效果：生命越低攻击越高
                        if (playerStats.berserker) {
                            const healthPercent = playerHealth / maxPlayerHealth;
                            const berserkerBonus = (1 - healthPercent) * 2; // 最多200%加成
                            damage *= (1 + berserkerBonus);
                        }

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 冰冻效果
                        if (playerStats.freeze && Math.random() < playerStats.freeze) {
                            target.frozen = true;
                            target.frozenTime = 2000; // 冰冻2秒
                            target.setTint(0x87ceeb); // 浅蓝色

                            const freezeText = this.add.text(target.x, target.y - 100, 'FROZEN!', {
                                fontSize: '16px',
                                fill: '#87ceeb',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: freezeText,
                                alpha: 0,
                                y: freezeText.y - 20,
                                duration: 1000,
                                onComplete: () => freezeText.destroy()
                            });
                        }

                        // 毒性效果
                        if (playerStats.poison) {
                            if (!target.poisoned) {
                                target.poisoned = true;
                                target.poisonDamage = Math.floor(damage * 0.3);
                                target.poisonDuration = 5000; // 持续5秒
                                target.setTint(0x9acd32); // 绿色
                            }
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 爆炸攻击效果
                        if (playerStats.explosive) {
                            const explosionRadius = 80;
                            const explosionDamage = Math.floor(damage * 0.5);

                            // 爆炸特效
                            const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6b35, 0.3);
                            this.tweens.add({
                                targets: explosion,
                                scaleX: 1.5,
                                scaleY: 1.5,
                                alpha: 0,
                                duration: 300,
                                onComplete: () => explosion.destroy()
                            });

                            // 对范围内的其他怪物造成伤害
                            monsters.forEach(otherMonster => {
                                if (otherMonster !== target) {
                                    const distance = Phaser.Math.Distance.Between(
                                        target.x, target.y, otherMonster.x, otherMonster.y
                                    );
                                    if (distance <= explosionRadius) {
                                        otherMonster.health -= explosionDamage;

                                        // 爆炸伤害特效
                                        this.tweens.add({
                                            targets: otherMonster,
                                            scaleX: 0.3,
                                            scaleY: 0.3,
                                            duration: 100,
                                            yoyo: true
                                        });
                                    }
                                }
                            });
                        }

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 武器开火抖动 - 更明显的后坐力
            if (playerWeapon) {
                // 记录武器原始位置
                const originalWeaponX = playerWeapon.x;
                const originalWeaponY = playerWeapon.y;

                // 水平后坐力
                this.tweens.add({
                    targets: playerWeapon,
                    x: originalWeaponX - 12, // 向后抖动12像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            x: originalWeaponX,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });

                // 垂直抖动
                this.tweens.add({
                    targets: playerWeapon,
                    y: originalWeaponY - 6, // 向上抖动6像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            y: originalWeaponY,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });
            }
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新泡泡龙游戏
        if (bubbleGameRunning) {
            updateMovingBubble.call(this);
            updateDroppingBubbles.call(this, delta);
        }

        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        if (gameState === 'playing') {
            // 更新战斗逻辑
            updateBattle.call(this, time);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 1100, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理毒性伤害
            if (monster.poisoned) {
                monster.poisonDuration -= delta;
                if (monster.poisonDuration <= 0) {
                    monster.poisoned = false;
                    monster.clearTint();
                } else {
                    // 每秒造成毒性伤害
                    if (!monster.lastPoisonDamage) monster.lastPoisonDamage = 0;
                    if (time - monster.lastPoisonDamage > 1000) {
                        monster.health -= monster.poisonDamage;
                        monster.lastPoisonDamage = time;

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 毒性伤害特效
                        const poisonText = this.add.text(monster.x, monster.y - 60, `-${monster.poisonDamage}`, {
                            fontSize: '14px',
                            fill: '#9acd32',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: poisonText,
                            alpha: 0,
                            y: poisonText.y - 20,
                            duration: 800,
                            onComplete: () => poisonText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 15000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.2, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;

                // 护盾系统
                if (playerStats.shield && playerStats.shield > 0) {
                    const shieldAbsorb = Math.min(damage, playerStats.shield);
                    playerStats.shield -= shieldAbsorb;
                    damage -= shieldAbsorb;

                    // 护盾特效
                    const shieldText = this.add.text(player.x, player.y - 80, `SHIELD -${shieldAbsorb}`, {
                        fontSize: '16px',
                        fill: '#3498db',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: shieldText,
                        alpha: 0,
                        y: shieldText.y - 20,
                        duration: 1000,
                        onComplete: () => shieldText.destroy()
                    });
                }

                // 剩余伤害作用于生命值
                if (damage > 0) {
                    playerHealth -= damage;
                }

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 80,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 80,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 80;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true; // 设置标志位，防止重复触发

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
                isCreatingWave = false; // 创建完成后重置标志位
            }, 1000);
        }
    }

    // 移除棋子类型定义

    // 移除商店相关函数

    // 创建UI元素 - 移到画布最上方
    function createUI() {
        // UI位置在画布最上方
        const uiY = 15; // 画布顶部位置

        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(2, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, uiY, 60, 60, 30); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, uiY, 60, 60, 30);
        playerAvatarBg.setDepth(100); // 确保在最上层

        const playerAvatar = this.add.text(45, uiY + 30, '🧙‍♂️', {
            fontSize: '40px'
        }).setOrigin(0.5).setDepth(101);

        const playerLabel = this.add.text(85, uiY + 5, '玩家', {
            fontSize: '14px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setDepth(101);

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(1, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(85, uiY + 30, 120, 12, 6); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(85, uiY + 30, 120, 12, 6);
        playerHealthBarBg.setDepth(100);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        this.playerHealthBar.setDepth(101);
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(145, uiY + 36, '', {
            fontSize: '10px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(2, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(675, uiY, 60, 60, 30); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(675, uiY, 60, 60, 30);
        enemyAvatarBg.setDepth(100);

        const enemyAvatar = this.add.text(705, uiY + 30, '👹', {
            fontSize: '40px'
        }).setOrigin(0.5).setDepth(101);

        const enemyLabel = this.add.text(665, uiY + 5, '敌方', {
            fontSize: '14px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(101);

        // 敌方血条背景 - 移到头像左侧
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(1, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(545, uiY + 30, 120, 12, 6); // 圆角矩形
        healthBarBg.strokeRoundedRect(545, uiY + 30, 120, 12, 6);
        healthBarBg.setDepth(100);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        this.enemyHealthBar.setDepth(101);
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(605, uiY + 36, '', {
            fontSize: '10px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(102);

        // 中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(1, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(325, uiY, 100, 50, 8); // 圆角矩形
        levelBg.strokeRoundedRect(325, uiY, 100, 50, 8);
        levelBg.setDepth(100);

        this.levelText = this.add.text(375, uiY + 20, `关卡 ${currentLevel}`, {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(101);

        this.waveText = this.add.text(375, uiY + 35, `波次 ${currentWave}`, {
            fontSize: '10px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(101);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 118; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;
        const uiY = 15; // UI在画布顶部

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(86, uiY + 31, currentWidth, 10, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 118; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;
        const uiY = 15; // UI在画布顶部

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(546, uiY + 31, currentWidth, 10, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }

    // 下棋函数
    function placeTacticalPiece(row, col, player, pieceType) {
        if (gameBoard[row][col] !== null) return false;

        const piece = pieceTypes[pieceType];

        // 创建棋子数据
        const pieceData = {
            type: pieceType,
            player: player,
            attack: piece.attack,
            health: piece.health,
            maxHealth: piece.health,
            skill: piece.skill,
            row: row,
            col: col
        };

        // 更新棋盘数据
        gameBoard[row][col] = pieceData;

        // 创建棋子图形
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const pieceX = startX + col * cellSize;
        const pieceY = startY + row * cellSize;

        if (player === 'player') {
            // 玩家棋子：白色背景 + emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0xffffff);
            pieceCircle.setStrokeStyle(2, 0x000000);

            const pieceIcon = this.add.text(pieceX, pieceY, piece.emoji, {
                fontSize: '20px'
            }).setOrigin(0.5);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: pieceIcon, data: pieceData };
        } else {
            // 敌方棋子：纯黑色，无emoji
            const pieceCircle = this.add.circle(pieceX, pieceY, 25, 0x000000);
            pieceCircle.setStrokeStyle(2, 0xffffff);

            tacticalCells[row][col].piece = { circle: pieceCircle, icon: null, data: pieceData };
        }

        // 更新计数
        if (player === 'player') {
            playerPieces++;
        } else {
            enemyPieces++;
        }

        // 更新UI
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);

        // 检查是否棋盘已满，开始战斗
        if (playerPieces + enemyPieces >= totalMaxPieces) {
            startBattle.call(this);
            return true;
        }

        // 检查是否需要切换回合
        if (player === 'player') {
            // 玩家下棋后，如果还有空位且敌方还能下棋，切换到敌方回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = false;
                this.tacticalStatusText.setText('敌方下棋中...');
                setTimeout(() => {
                    enemyMove.call(this);
                }, 1000);
            }
        } else if (player === 'enemy') {
            // 敌方下棋后，如果还有空位且玩家还能下棋，切换到玩家回合
            if (playerPieces + enemyPieces < totalMaxPieces) {
                isPlayerTurn = true;
                this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            }
        }

        return true;
    }

    // 敌方AI下棋
    function enemyMove() {
        if (tacticalGameState !== 'placing') return;

        // 随机选择棋子类型
        const pieceTypeKeys = Object.keys(pieceTypes);
        const selectedEnemyPieceType = pieceTypeKeys[Math.floor(Math.random() * pieceTypeKeys.length)];

        // 寻找空位
        const emptySpaces = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] === null) {
                    emptySpaces.push({ row, col });
                }
            }
        }

        if (emptySpaces.length > 0) {
            const randomSpace = emptySpaces[Math.floor(Math.random() * emptySpaces.length)];
            placeTacticalPiece.call(this, randomSpace.row, randomSpace.col, 'enemy', selectedEnemyPieceType);
        }
    }



    // 开始战斗
    function startBattle() {
        tacticalGameState = 'battle';
        gameEnded = false;
        currentBattleIndex = 0;
        this.tacticalStatusText.setText('棋盘已满！战斗开始！');

        // 隐藏商店
        shop.forEach(shopItem => {
            shopItem.playerPiece.setVisible(false);
            shopItem.playerIcon.setVisible(false);
            shopItem.pieceName.setVisible(false);
            shopItem.pieceDesc.setVisible(false);
        });
        // 隐藏商店背景
        if (this.shopBackground) {
            this.shopBackground.setVisible(false);
        }

        // 创建战斗序列（从左上角到右下角）
        battleSequence = [];
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                if (gameBoard[row][col] !== null) {
                    battleSequence.push(gameBoard[row][col]);
                }
            }
        }

        // 开始战斗循环
        setTimeout(() => {
            battleLoop.call(this);
        }, 2000);
    }

    // 战斗循环 - 按格子顺序执行
    function battleLoop() {
        if (tacticalGameState !== 'battle' || gameEnded) return;

        // 检查游戏结束条件
        if (playerHealth <= 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('玩家失败！');
            this.tacticalStatusText.setFill('#ff0000');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        if (monsters.length === 0) {
            gameEnded = true;
            tacticalGameState = 'finished';
            this.tacticalStatusText.setText('所有怪物被击败！玩家获胜！');
            this.tacticalStatusText.setFill('#00ff00');
            clearAllHighlights.call(this);
            clearBoardAfterGame.call(this);
            return;
        }

        // 按格子顺序执行，每个格子一个回合
        if (currentBattleIndex < battleSequence.length) {
            const currentUnit = battleSequence[currentBattleIndex];
            if (currentUnit && currentUnit.health > 0) {
                executeCurrentTurn.call(this, currentUnit);
            }
            currentBattleIndex++;

            // 继续下一个格子
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1500);
        } else {
            // 一轮结束，重新开始
            currentBattleIndex = 0;
            setTimeout(() => {
                clearAllHighlights.call(this);
                battleLoop.call(this);
            }, 1000);
        }
    }

    // 执行当前回合（按格子顺序）
    function executeCurrentTurn(currentUnit) {
        const pieceType = pieceTypes[currentUnit.type];

        // 高亮当前格子
        highlightCurrentPiece.call(this, currentUnit);

        if (currentUnit.player === 'player') {
            // 玩家棋子（白棋）
            if (pieceType.isAttackSkill) {
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 发动攻击！`);
            } else {
                // 非攻击技能执行辅助效果
                executeSkillOnMonsters.call(this, currentUnit);
                this.tacticalStatusText.setText(`玩家 ${pieceType.name} 使用技能！`);
            }
        } else {
            // 敌方棋子（黑棋）
            if (pieceType.isAttackSkill) {
                // 敌方棋子触发怪物攻击
                monsterAttackTriggeredByPiece.call(this, currentUnit);
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 指挥怪物攻击！`);
            } else {
                // 敌方辅助技能（如果有的话）
                this.tacticalStatusText.setText(`敌方 ${pieceType.name} 使用技能！`);
            }
        }
    }

    // 执行技能攻击怪物
    function executeSkillOnMonsters(unit) {
        if (monsters.length === 0) return;

        switch (unit.skill) {
            case 'charge': // 战士冲锋 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    performMeleeAttack.call(this, unit, target, unit.attack, '⚔️');
                }
                break;

            case 'multishot': // 弓箭手多重射击 - 远程，攻击多个
                const targets = monsters.slice(0, 2);
                targets.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.7);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🏹');
                });
                break;

            case 'fireball': // 法师火球术 - 远程，群攻
                monsters.forEach(target => {
                    const damage = Math.floor(unit.attack * 0.5);
                    target.health -= damage;
                    showMonsterDamage.call(this, target, damage, '🔥');
                });
                break;

            case 'heal': // 治疗师治疗玩家
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 30);
                const healText = this.add.text(player.x, player.y - 60, '💚 +30', {
                    fontSize: '16px',
                    fill: '#00ff00',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: healText,
                    alpha: 0,
                    y: healText.y - 30,
                    duration: 1500,
                    onComplete: () => healText.destroy()
                });
                break;

            case 'shield': // 坦克护盾
                playerHealth = Math.min(maxPlayerHealth, playerHealth + 20);
                const shieldText = this.add.text(player.x, player.y - 60, '🛡️ +20', {
                    fontSize: '16px',
                    fill: '#3498db',
                    fontFamily: 'Arial',
                    fontWeight: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: shieldText,
                    alpha: 0,
                    y: shieldText.y - 30,
                    duration: 1500,
                    onComplete: () => shieldText.destroy()
                });
                break;

            case 'critical': // 刺客暴击 - 近战，需要移动
                if (monsters.length > 0) {
                    const target = monsters[0];
                    const damage = unit.attack * 2;
                    performMeleeAttack.call(this, unit, target, damage, '💥');
                }
                break;
        }

        // 移除死亡的怪物并重新排列
        const deadMonsters = [];
        monsters = monsters.filter((monster, index) => {
            if (monster.health <= 0) {
                monster.destroy();
                monstersKilled++;
                deadMonsters.push(index);
                return false;
            }
            return true;
        });

        // 如果有怪物死亡，重新排列剩余怪物
        if (deadMonsters.length > 0) {
            repositionMonsters.call(this);
            updateEnemyHealthBar.call(this); // 更新进度条
            updateMonsterHealthBars.call(this); // 更新怪物血条
        }
    }

    // 执行近战攻击（角色移动）
    function performMeleeAttack(unit, target, damage, icon) {
        // 记录角色原始位置
        const originalX = player.x;
        const originalY = player.y;

        // 移动角色到目标附近
        const targetX = target.x - 80;
        const targetY = target.y;

        this.tweens.add({
            targets: player,
            x: targetX,
            y: targetY,
            duration: 500,
            ease: 'Power2',
            onComplete: () => {
                // 造成伤害
                target.health -= damage;
                showMonsterDamage.call(this, target, damage, icon);

                // 角色攻击动画
                this.tweens.add({
                    targets: player,
                    rotation: 0.3,
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 返回原位
                setTimeout(() => {
                    this.tweens.add({
                        targets: player,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }, 300);
            }
        });
    }

    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }

    // 显示怪物伤害
    function showMonsterDamage(monster, damage, icon) {
        const damageText = this.add.text(monster.x, monster.y - 100, `${icon} -${damage}`, {
            fontSize: '16px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        damageText.setDepth(200); // 确保伤害数字显示在血条之上

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: damageText.y - 30,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });

        // 怪物受击效果
        this.tweens.add({
            targets: monster,
            tint: 0xff0000,
            duration: 200,
            yoyo: true,
            onComplete: () => {
                monster.clearTint();
                // 受击后更新血条
                updateMonsterHealthBars.call(this);
            }
        });
    }

    // 敌方棋子触发怪物攻击
    function monsterAttackTriggeredByPiece(unit) {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击
            const damage = unit.attack; // 使用棋子的攻击力

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 150, // 移动到玩家附近
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });
        }
    }

    // 怪物攻击玩家
    function monsterAttack() {
        // 检查怪物是否存在且存活
        if (monsters.length > 0 && monsters[0] && monsters[0].health > 0) {
            const monster = monsters[0]; // 最前面的怪物攻击

            // 怪物向前移动攻击，保持底部对齐
            const originalX = monster.x;
            const originalY = monster.y;
            this.tweens.add({
                targets: monster,
                x: originalX - 100,
                y: player.y, // 确保与玩家底部对齐
                duration: 500,
                onComplete: () => {
                    // 攻击伤害
                    const damage = 15 + currentLevel * 2;
                    playerHealth -= damage;

                    // 显示伤害
                    const damageText = this.add.text(player.x, player.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1500,
                        onComplete: () => damageText.destroy()
                    });

                    // 玩家受击效果
                    this.tweens.add({
                        targets: player,
                        tint: 0xff0000,
                        duration: 200,
                        yoyo: true,
                        onComplete: () => {
                            player.clearTint();
                        }
                    });

                    // 返回原位
                    this.tweens.add({
                        targets: monster,
                        x: originalX,
                        y: originalY,
                        duration: 500,
                        ease: 'Power2'
                    });
                }
            });

            this.tacticalStatusText.setText('怪物攻击玩家！');
        }
    }

    // 高亮当前攻击的棋子
    function highlightCurrentPiece(unit) {
        // 清除之前的高亮
        clearAllHighlights.call(this);

        // 找到对应的棋盘格子
        const cell = tacticalCells[unit.row][unit.col];
        if (cell && cell.piece) {
            // 创建高亮效果
            const cellSize = 100;
            const gridSize = 4;
            const gridWidth = gridSize * cellSize;
            const startX = (750 - gridWidth) / 2 + cellSize / 2;
            const startY = 550;

            const highlightX = startX + unit.col * cellSize;
            const highlightY = startY + unit.row * cellSize;

            // 创建高亮圆圈
            this.currentHighlight = this.add.circle(highlightX, highlightY, 45, 0xffff00, 0.3);
            this.currentHighlight.setStrokeStyle(3, 0xffff00);

            // 添加脉动效果
            this.tweens.add({
                targets: this.currentHighlight,
                scaleX: 1.2,
                scaleY: 1.2,
                duration: 500,
                yoyo: true,
                repeat: -1
            });
        }
    }

    // 清除所有高亮
    function clearAllHighlights() {
        if (this.currentHighlight) {
            this.currentHighlight.destroy();
            this.currentHighlight = null;
        }
    }

    // 游戏结束后清空棋盘
    function clearBoardAfterGame() {
        // 延迟3秒后清空棋盘，让玩家看到结果
        setTimeout(() => {
            // 清空棋盘数据和图形
            for (let row = 0; row < 4; row++) {
                for (let col = 0; col < 4; col++) {
                    if (gameBoard[row][col] !== null) {
                        // 移除棋子图形
                        const cell = tacticalCells[row][col];
                        if (cell && cell.piece) {
                            if (cell.piece.circle) cell.piece.circle.destroy();
                            if (cell.piece.icon) cell.piece.icon.destroy();
                            cell.piece = null;
                        }
                        gameBoard[row][col] = null;
                    }
                }
            }

            // 重置计数
            playerPieces = 0;
            enemyPieces = 0;

            // 重置游戏状态
            tacticalGameState = 'placing';
            isPlayerTurn = true;
            gameEnded = false;
            currentBattleIndex = 0;
            battleSequence = [];

            // 更新UI
            this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
            this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
            this.tacticalStatusText.setFill('#ffffff');

            // 重新刷新商店
            refreshShop.call(this);
        }, 3000);
    }

    // 显示伤害
    function showDamage(unit, damage, icon) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const damageText = this.add.text(x, y - 40, `${icon} -${damage}`, {
            fontSize: '14px',
            fill: '#ff0000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: damageText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => damageText.destroy()
        });
    }

    // 显示治疗
    function showHeal(unit, heal) {
        const cellSize = 100;
        const gridSize = 4;
        const gridWidth = gridSize * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2;
        const startY = 550;

        const x = startX + unit.col * cellSize;
        const y = startY + unit.row * cellSize;

        const healText = this.add.text(x, y - 40, `💚 +${heal}`, {
            fontSize: '14px',
            fill: '#00ff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: healText,
            alpha: 0,
            y: y - 60,
            duration: 1500,
            onComplete: () => healText.destroy()
        });
    }

    // 更新棋子显示
    function updatePieceDisplay() {
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                const piece = gameBoard[row][col];
                const cell = tacticalCells[row][col];

                if (piece && cell.piece) {
                    // 更新棋子透明度表示血量
                    const healthPercent = piece.health / piece.maxHealth;
                    cell.piece.circle.setAlpha(Math.max(0.3, healthPercent));

                    // 只有玩家棋子才有icon
                    if (cell.piece.icon) {
                        cell.piece.icon.setAlpha(Math.max(0.3, healthPercent));
                    }

                    // 如果血量为0，移除棋子
                    if (piece.health <= 0) {
                        cell.piece.circle.destroy();
                        if (cell.piece.icon) {
                            cell.piece.icon.destroy();
                        }
                        cell.piece = null;
                        gameBoard[row][col] = null;
                    }
                }
            }
        }
    }

    // 重新开始游戏
    function restartGame() {
        // 重置游戏状态
        tacticalGameState = 'placing';
        isPlayerTurn = true;
        currentPlayer = 'player';
        playerPieces = 0;
        enemyPieces = 0;
        selectedPieceType = null;
        currentBattleIndex = 0;
        battleSequence = [];
        gameEnded = false;

        // 清除高亮
        clearAllHighlights.call(this);

        // 清空棋盘数据
        for (let row = 0; row < 4; row++) {
            for (let col = 0; col < 4; col++) {
                gameBoard[row][col] = null;
                // 移除棋子
                if (tacticalCells[row][col] && tacticalCells[row][col].piece) {
                    tacticalCells[row][col].piece.circle.destroy();
                    if (tacticalCells[row][col].piece.icon) {
                        tacticalCells[row][col].piece.icon.destroy();
                    }
                    tacticalCells[row][col].piece = null;
                }
            }
        }

        // 重新刷新商店
        refreshShop.call(this);

        // 重置状态文本
        this.tacticalStatusText.setText('拖动棋子到棋盘下棋');
        this.tacticalStatusText.setFill('#ffff00');
        this.pieceCountText.setText(`玩家: ${playerPieces} | 敌方: ${enemyPieces} | 总计: ${playerPieces + enemyPieces}/16`);
    }

    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
