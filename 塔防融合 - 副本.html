<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji小兵塔防</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#24c061',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let boardCells = []; // 大富翁式边缘格子
    let selectedCell = null;
    let timer = 160;
    let timerText;
    let titleText;
    let gameStarted = false;
    let gameWon = false;
    let gameScene = null;
    let currentLevel = parseInt(localStorage.getItem('currentLevel')) || 1;
    let levelText;
    let dice = null; // 骰子对象
    let diceValue = 1;
    let currentPlayerPosition = 0; // 玩家当前位置
    let isRolling = false; // 是否正在投掷中（防止连续点击）

    const CELL_SIZE = 80; // 格子大小
    const BOARD_SIZE = 6; // 6x6的边缘格子
    const TOTAL_CELLS = (BOARD_SIZE - 1) * 4; // 边缘总格子数：20个

    // 关卡配置 - 每关有不同的emoji小兵组合
    const LEVEL_CONFIG = {
        1: {
            time: 160,
            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0x000000, highlight: 0xFFFF99 },
                    attack: 15,
                    health: 100
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 15,
                    health: 100
                }
            }
        },
        2: {
            time: 180,
            soldiers: {
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                   attack: 15,
                    health: 100
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                   attack: 15,
                    health: 100
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 15,
                    health: 100
                }
            }
        },
        3: {
            time: 200,
            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 100
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 100
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 100
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 100
                }
            }
        },
        4: {
            time: 220,
            soldiers: {
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 4,
                    health: 25
                },
                cyan: {
                    emoji: "🏊‍♂️",
                    name: "水兵",
                    colors: { main: 0x00BCD4, shadow: 0x0097A7, highlight: 0xB2EBF2 },
                    attack: 7,
                    health: 14
                },
                pink: {
                    emoji: "🌸",
                    name: "花仙",
                    colors: { main: 0xE91E63, shadow: 0xC2185B, highlight: 0xF8BBD9 },
                    attack: 9,
                    health: 13
                }
            }
        },
        5: {
            time: 240,
            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 13
                }
            }
        },
        6: {
            time: 260,
            soldiers: {
                yellow: {
                    emoji: "🤴",
                    name: "国王",
                    colors: { main: 0xFFD700, shadow: 0xDAA520, highlight: 0xFFFF99 },
                    attack: 8,
                    health: 15
                },
                blue: {
                    emoji: "🧙‍♂️",
                    name: "法师",
                    colors: { main: 0x2196F3, shadow: 0x1976D2, highlight: 0xBBDEFB },
                    attack: 10,
                    health: 12
                },
                green: {
                    emoji: "🏹",
                    name: "弓箭手",
                    colors: { main: 0x4CAF50, shadow: 0x388E3C, highlight: 0xC8E6C9 },
                    attack: 6,
                    health: 10
                },
                red: {
                    emoji: "⚔️",
                    name: "剑士",
                    colors: { main: 0xF44336, shadow: 0xD32F2F, highlight: 0xFFCDD2 },
                    attack: 12,
                    health: 18
                },
                purple: {
                    emoji: "🛡️",
                    name: "盾兵",
                    colors: { main: 0x9C27B0, shadow: 0x7B1FA2, highlight: 0xE1BEE7 },
                    attack: 4,
                    health: 25
                },
                orange: {
                    emoji: "🔥",
                    name: "火法师",
                    colors: { main: 0xFF9800, shadow: 0xF57C00, highlight: 0xFFE0B2 },
                    attack: 11,
                    health: 13
                }
            }
        }
    };

    // 当前关卡的小兵数据（从关卡配置中获取）
    let currentSoldiers = {};
    let currentSoldierNames = [];

    // 防御塔系统
    let defenseTowers = [];
    let battleUnits = []; // 战斗中的小兵单位
    let battleScene = null;
    let hasAnyUnitsEverSpawned = false; // 是否曾经有小兵出征过

    function preload() {
        // 预加载阶段 - 现在我们动态创建方块，所以这里不需要预先生成纹理
        const levelConfig = LEVEL_CONFIG[currentLevel];

       
       

        
    }

    function create() {
        gameScene = this;

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];

       

        // 关卡显示背景
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x4d68ae, 0.9);
        levelBg.fillRoundedRect(config.width/2 - 70, 80, 140, 60, 12);
        levelBg.lineStyle(2, 0x333333);
        levelBg.strokeRoundedRect(config.width/2 - 70, 80, 140, 60, 12);

        // 关卡显示
        levelText = this.add.text(config.width/2, 110, `LEVEL ${currentLevel}`, {
            fontSize: '30px',
            fontFamily: '"Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#ffffff',
            align: 'center',
            stroke: '#000000',
            strokeThickness: 1
        }).setOrigin(0.5);

        // 初始化当前关卡的小兵数据
        currentSoldiers = levelConfig.soldiers;
        currentSoldierNames = Object.keys(currentSoldiers);

        // 创建防御塔
        createDefenseTowers();

      

        // 进度条
        this.progressBar = this.add.graphics();

     
    

        // 初始化关卡
        initializeLevel();

        // 保存战斗场景引用
        battleScene = this;

       

        // 输入处理
        this.input.on('pointerdown', function(pointer) {
            handleInput(pointer);
        });

        // 移除计时器相关代码
    }

    function initializeLevel() {
        // 清除现有格子
        boardCells.forEach(cell => cell.destroy());
        boardCells = [];

        // 获取当前关卡配置
        const levelConfig = LEVEL_CONFIG[currentLevel] || LEVEL_CONFIG[1];
        const numColors = currentSoldierNames.length; // 使用当前关卡的小兵数量

        // 创建大富翁式的边缘格子布局
        createBoardLayout();

        // 重置玩家位置
        currentPlayerPosition = 0;

        // 创建骰子（在格子创建后）
        gameScene.time.delayedCall(100, () => {
            createDice();
        });
    }

    // 创建大富翁式的边缘格子布局
    function createBoardLayout() {
        const centerX = config.width / 2;
        const centerY = config.height / 2 + 100;
        const boardWidth = (BOARD_SIZE - 1) * CELL_SIZE;
        const boardHeight = (BOARD_SIZE - 1) * CELL_SIZE;

        // 计算边缘格子的位置
        const positions = [];

        // 上边（从左到右）
        for (let i = 0; i < BOARD_SIZE; i++) {
            positions.push({
                x: centerX - boardWidth/2 + i * CELL_SIZE,
                y: centerY - boardHeight/2
            });
        }

        // 右边（从上到下，跳过右上角）
        for (let i = 1; i < BOARD_SIZE; i++) {
            positions.push({
                x: centerX + boardWidth/2,
                y: centerY - boardHeight/2 + i * CELL_SIZE
            });
        }

        // 下边（从右到左，跳过右下角）
        for (let i = BOARD_SIZE - 2; i >= 0; i--) {
            positions.push({
                x: centerX - boardWidth/2 + i * CELL_SIZE,
                y: centerY + boardHeight/2
            });
        }

        // 左边（从下到上，跳过左下角和左上角）
        for (let i = BOARD_SIZE - 2; i > 0; i--) {
            positions.push({
                x: centerX - boardWidth/2,
                y: centerY - boardHeight/2 + i * CELL_SIZE
            });
        }

        // 为每个位置创建格子
        positions.forEach((pos, index) => {
            const cell = createBoardCell(pos.x, pos.y, index);
            boardCells.push(cell);
        });
    }

    // 创建单个格子
    function createBoardCell(x, y, index) {
        const cell = gameScene.add.container(x, y);

        // 格子背景
        const cellBg = gameScene.add.graphics();
        cellBg.fillStyle(0x919bb4, 1);
        cellBg.fillRoundedRect(-CELL_SIZE/2, -CELL_SIZE/2, CELL_SIZE, CELL_SIZE, 8);
        cellBg.lineStyle(2, 0x000000, 1);
        cellBg.strokeRoundedRect(-CELL_SIZE/2, -CELL_SIZE/2, CELL_SIZE, CELL_SIZE, 8);

        // 随机选择一个小兵类型
        const soldierType = Phaser.Utils.Array.GetRandom(currentSoldierNames);
        const soldier = currentSoldiers[soldierType];

        // 方块（小一点，在格子内）
        const cube = gameScene.add.graphics();
        const cubeSize = CELL_SIZE * 0.6;
        // cube.fillStyle(soldier.colors.main);
        // cube.fillRoundedRect(-cubeSize/2, -cubeSize/2 + 10, cubeSize, cubeSize, 6);
        // cube.fillStyle(soldier.colors.shadow);
        // cube.fillRoundedRect(-cubeSize/2 + 3, -cubeSize/2 + 13, cubeSize-6, cubeSize-6, 4);
        // cube.fillStyle(soldier.colors.highlight);
        // cube.fillRoundedRect(-cubeSize/2 + 6, -cubeSize/2 + 16, cubeSize-12, cubeSize-12, 3);

        // emoji
        const emoji = gameScene.add.text(0, 5, soldier.emoji, {
            fontSize: '50px'
        }).setOrigin(0.5);

        
    

        cell.add([cellBg, cube, emoji]);

        // 存储格子数据
        cell.soldierType = soldierType;
        cell.soldier = soldier;
        cell.index = index;

        return cell;
    }

    // 创建骰子（在围墙内正中间）
    function createDice() {
        // 计算围墙内的中心位置
        const centerX = config.width / 2;
        const centerY = config.height / 2 + 100;

        dice = gameScene.add.container(centerX, centerY);

        // 创建立体骰子
        createDice3D();

        // 设置骰子的深度，确保在格子上方但不会覆盖UI
        dice.setDepth(50);

        // 点击骰子投掷
        dice.setSize(60, 60);
        dice.setInteractive();
        dice.on('pointerdown', rollDice);

        // 添加提示文字
        const diceHint = gameScene.add.text(centerX, centerY + 50, '点击骰子投掷', {
            fontSize: '16px',
            fill: '#666666'
        }).setOrigin(0.5);
        diceHint.setDepth(50);
    }

    // 创建立体骰子（简化版：只有暗部和高光）
    function createDice3D() {
        const diceSize = 60;

        // 清除现有的骰子图形
        dice.removeAll(true);

        // 骰子阴影（底部暗部）
        const diceShadow = gameScene.add.graphics();
        diceShadow.fillStyle(0x000000, 0.2);
        diceShadow.fillRoundedRect(-diceSize/2 + 3, -diceSize/2 + 3, diceSize, diceSize, 8);

        // 骰子主体
        const diceMain = gameScene.add.graphics();
        diceMain.fillStyle(0xf8f8f8, 1);
        diceMain.fillRoundedRect(-diceSize/2, -diceSize/2, diceSize, diceSize, 8);

        // 骰子暗部（右下角）
        const diceDark = gameScene.add.graphics();
        diceDark.fillStyle(0xd0d0d0, 1);
        diceDark.fillRoundedRect(-diceSize/2 + 2, -diceSize/2 + 2, diceSize - 4, diceSize - 4, 6);

        // 骰子高光（左上角）
        const diceHighlight = gameScene.add.graphics();
        diceHighlight.fillStyle(0xffffff, 0.8);
        diceHighlight.fillRoundedRect(-diceSize/2 + 4, -diceSize/2 + 4, diceSize - 20, diceSize - 20, 4);

        // 骰子边框
        const diceBorder = gameScene.add.graphics();
        diceBorder.lineStyle(2, 0x333333);
        diceBorder.strokeRoundedRect(-diceSize/2, -diceSize/2, diceSize, diceSize, 8);

        // 骰子点数显示
        const diceText = gameScene.add.text(0, 0, diceValue.toString(), {
            fontSize: '28px',
            fontWeight: 'bold',
            fill: '#333333'
        }).setOrigin(0.5);

        // 底部阴影
        const bottomShadow = gameScene.add.graphics();
        bottomShadow.fillStyle(0x000000, 0.3);
        bottomShadow.fillEllipse(2, diceSize/2 + 6, diceSize + 4, 16);

        dice.add([bottomShadow, diceShadow, diceMain, diceDark, diceHighlight, diceBorder, diceText]);
        dice.diceText = diceText;
        dice.diceMain = diceMain;
        dice.diceDark = diceDark;
        dice.diceHighlight = diceHighlight;
        dice.bottomShadow = bottomShadow;
    }

    // 移除了底部按钮，改为点击骰子操作

    // 高亮移动路径
    function highlightMovementPath(steps) {
        return new Promise((resolve) => {
            let currentStep = 0;

            function highlightNextStep() {
                if (currentStep >= steps) {
                    resolve();
                    return;
                }

                // 计算下一个位置
                const nextPosition = (currentPlayerPosition + currentStep + 1) % TOTAL_CELLS;
                const targetCell = boardCells[nextPosition];

                if (!targetCell) {
                    resolve();
                    return;
                }

                // 高亮当前格子
                highlightCellTemporary(targetCell, 300);

                currentStep++;

                // 延迟后高亮下一个格子
                gameScene.time.delayedCall(400, highlightNextStep);
            }

            highlightNextStep();
        });
    }

    // 临时高亮格子
    function highlightCellTemporary(cell, duration) {
        // 创建临时高亮效果
        const tempHighlight = gameScene.add.graphics();
        tempHighlight.lineStyle(4, 0xFFD700, 3);
        tempHighlight.strokeRoundedRect(-CELL_SIZE/2 - 2, -CELL_SIZE/2 - 2, CELL_SIZE + 4, CELL_SIZE + 4, 10);

        tempHighlight.x = cell.x;
        tempHighlight.y = cell.y;
        tempHighlight.setDepth(200); // 设置为最高层级

        // 脉冲效果
        gameScene.tweens.add({
            targets: tempHighlight,
            alpha: 0.3,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: duration / 2,
            yoyo: true,
            ease: 'Sine.easeInOut',
            onComplete: () => {
                tempHighlight.destroy();
            }
        });
    }

    // 投掷骰子
    function rollDice() {
        if (gameWon || isRolling) return;

        isRolling = true;
        console.log('投掷骰子...');

        // 骰子旋转动画（简化版）
        const rollAnimation = gameScene.tweens.add({
            targets: dice,
            rotation: Math.PI * 4,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 500,
            ease: 'Power2.easeOut',
            onUpdate: (tween) => {
                // 动画过程中随机显示数字
                const randomValue = Math.floor(Math.random() * 6) + 1;
                dice.diceText.setText(randomValue.toString());

                // 简单的立体效果变化
                const progress = tween.progress;

                // 高光和暗部的轻微变化
                if (dice.diceHighlight) {
                    dice.diceHighlight.alpha = 0.8 + Math.sin(progress * Math.PI * 4) * 0.2;
                }
                if (dice.diceDark) {
                    dice.diceDark.alpha = 1 - Math.sin(progress * Math.PI * 4) * 0.1;
                }
                if (dice.bottomShadow) {
                    dice.bottomShadow.scaleX = 1 + progress * 0.1;
                    dice.bottomShadow.alpha = 0.3 + progress * 0.1;
                }
            },
            onComplete: () => {
                // 重置效果
                if (dice.diceHighlight) {
                    dice.diceHighlight.alpha = 0.8;
                }
                if (dice.diceDark) {
                    dice.diceDark.alpha = 1;
                }
                if (dice.bottomShadow) {
                    dice.bottomShadow.scaleX = 1;
                    dice.bottomShadow.alpha = 0.3;
                }

                // 最终确定骰子点数
                diceValue = Math.floor(Math.random() * 6) + 1;
                dice.diceText.setText(diceValue.toString());

                console.log(`骰子点数: ${diceValue}`);

                // 开始高亮移动路径
                movePlayerWithHighlight();
            }
        });
    }

    // 通过高亮移动玩家位置
    async function movePlayerWithHighlight() {
        // 先播放高亮移动动画
        await highlightMovementPath(diceValue);

        // 更新玩家位置
        const oldPosition = currentPlayerPosition;
        currentPlayerPosition = (currentPlayerPosition + diceValue) % TOTAL_CELLS;

        console.log(`玩家位置: ${oldPosition} -> ${currentPlayerPosition}`);

        // 获取目标格子
        const targetCell = boardCells[currentPlayerPosition];
        if (!targetCell) {
            console.error('目标格子不存在!');
            isRolling = false;
            return;
        }

        console.log(`目标格子小兵类型: ${targetCell.soldierType}, 出兵数量: ${diceValue}`);

        // 高亮最终目标格子
        highlightCell(targetCell);

        // 显示出兵提示
        showSpawnPreview(targetCell, diceValue);

        // 延迟后出兵（出兵数量 = 骰子点数）
        gameScene.time.delayedCall(1000, () => {
            hideSpawnPreview();
            spawnUnitsFromCell(targetCell, diceValue);
            // 完成后允许下次投掷
            isRolling = false;
        });
    }

    // 高亮格子
    function highlightCell(cell) {
        // 清除之前的高亮
        boardCells.forEach(c => {
            if (c.highlight) {
                c.highlight.destroy();
                c.highlight = null;
            }
        });

        // 添加新的高亮效果
        const highlight = gameScene.add.graphics();
        highlight.lineStyle(4, 0xFFD700, 1);
        highlight.strokeRoundedRect(-CELL_SIZE/2 - 2, -CELL_SIZE/2 - 2, CELL_SIZE + 4, CELL_SIZE + 4, 10);
        highlight.setDepth(200); // 设置为最高层级

        highlight.x = cell.x;
        highlight.y = cell.y;
        cell.highlight = highlight;

        // 脉冲效果
        gameScene.tweens.add({
            targets: highlight,
            alpha: 0.3,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    // 显示出兵预览
    function showSpawnPreview(cell, count) {
        const soldierData = cell.soldier;

        // 在骰子上方显示出兵预览
        const previewY = dice.y - 100;

        // 背景（更大一些）
        const previewBg = gameScene.add.graphics();
        previewBg.fillStyle(0x000000, 0.8);
        previewBg.fillRoundedRect(dice.x - 80, previewY - 30, 160, 60, 12);
        previewBg.setDepth(150);

        // 小兵图标（更大）
        const soldierIcon = gameScene.add.text(dice.x - 25, previewY, soldierData.emoji, {
            fontSize: '36px'  // 增大图标
        }).setOrigin(0.5);
        soldierIcon.setDepth(150);

        // 数量显示（更大）
        const countText = gameScene.add.text(dice.x + 25, previewY, `×${count}`, {
            fontSize: '28px',  // 增大数量文字
            fontWeight: 'bold',
            fill: '#ffffff'
        }).setOrigin(0.5);
        countText.setDepth(150);

        // 保存引用以便清理
        gameScene.spawnPreview = {
            bg: previewBg,
            icon: soldierIcon,
            count: countText
        };

        // 添加闪烁效果
        gameScene.tweens.add({
            targets: [soldierIcon, countText],
            alpha: 0.5,
            duration: 400,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    // 隐藏出兵预览
    function hideSpawnPreview() {
        if (gameScene.spawnPreview) {
            gameScene.spawnPreview.bg.destroy();
            gameScene.spawnPreview.icon.destroy();
            gameScene.spawnPreview.count.destroy();
            gameScene.spawnPreview = null;
        }
    }

    // 从格子出兵（多个单位）
    function spawnUnitsFromCell(cell, count) {
        const soldierData = cell.soldier;

        console.log(`从格子出兵 ${count} 个 ${soldierData.name}`);

        // 创建多个战斗单位
        for (let i = 0; i < count; i++) {
            gameScene.time.delayedCall(i * 200, () => {
                // 在格子周围随机位置生成小兵
                const offsetX = (Math.random() - 0.5) * 60;
                const offsetY = (Math.random() - 0.5) * 60;
                createBattleUnit(cell.x + offsetX, cell.y + offsetY, soldierData);
            });
        }

        // 出兵特效
        createSpawnEffect(cell.x, cell.y);
    }

    // 创建出兵特效
    function createSpawnEffect(x, y) {
        for (let i = 0; i < 15; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 4 + 2);

            particle.x = x + (Math.random() - 0.5) * 40;
            particle.y = y + (Math.random() - 0.5) * 40;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y - Math.random() * 80 - 40,
                x: particle.x + (Math.random() - 0.5) * 60,
                alpha: 0,
                scaleX: 0.2,
                scaleY: 0.2,
                duration: Math.random() * 800 + 600,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    // 清理游戏状态
    function cleanupGameState() {
        // 清理所有战斗单位
        battleUnits.forEach(unit => unit.destroy());
        battleUnits = [];

        // 清理防御塔
        defenseTowers.forEach(tower => tower.destroy());
        defenseTowers = [];

        // 清理格子
        boardCells.forEach(cell => cell.destroy());
        boardCells = [];

        // 清理骰子
        if (dice) {
            dice.setVisible(false);
            dice.destroy();
            dice = null;
        }

        // 清理出兵预览
        hideSpawnPreview();

        // 重置投掷状态
        isRolling = false;

        // 重置游戏状态
        selectedCell = null;
        gameWon = false;
        hasAnyUnitsEverSpawned = false; // 重置小兵出征标记
        currentPlayerPosition = 0;
    }

    // 创建防御塔
    function createDefenseTowers() {
        // 清除现有防御塔
        defenseTowers.forEach(tower => tower.destroy());
        defenseTowers = [];

        // 在屏幕上方创建3个防御塔
        const towerY = 250;
        const towerSpacing = config.width / 4;

        for (let i = 0; i < 3; i++) {
            const towerX = towerSpacing * (i + 1);
            const tower = createDefenseTower(towerX, towerY, i);
            defenseTowers.push(tower);
        }
    }

    // 创建单个防御塔
    function createDefenseTower(x, y, index) {
        const tower = gameScene.add.container(x, y);

        // 塔身
        const towerBody = gameScene.add.graphics();
        // towerBody.fillStyle(0x8B4513, 1);
        // towerBody.fillRoundedRect(-25, -40, 50, 80, 8);
        // towerBody.lineStyle(3, 0x654321);
        // towerBody.strokeRoundedRect(-25, -40, 50, 80, 8);

        // 塔顶
        const towerTop = gameScene.add.graphics();
        towerTop.fillStyle(0x6c3e1e, 1);
        towerTop.fillRoundedRect(-30, -20, 60, 20, 10);
        towerTop.lineStyle(2, 0x6c3e1e);
        towerTop.strokeRoundedRect(-30, -20, 60, 20, 10);

        // 防御塔emoji
        const towerEmojis = ['🏰', '🗼', '🏯'];
        const towerEmoji = gameScene.add.text(0, -30, towerEmojis[index], {
            fontSize: '60px'
        }).setOrigin(0.5);

        tower.add([towerBody, towerTop, towerEmoji]);

        // 塔的属性 - 强力防御塔，需要大军团才能攻破
        tower.health = 150;
        tower.maxHealth = 150;
        tower.attack = 20;
        tower.attackRange = 125; // 减少50%：250 -> 125
        tower.lastAttackTime = 0;
        tower.attackCooldown = 500; // 0.5秒攻击间隔，非常快

        // 血条背景
        const healthBarBg = gameScene.add.graphics();
        healthBarBg.fillStyle(0x000000, 0.5);
        healthBarBg.fillRoundedRect(-25, -70, 50, 8, 4);

        // 血条
        const healthBar = gameScene.add.graphics();
        tower.healthBar = healthBar;
        tower.updateHealthBar = function() {
            healthBar.clear();
            const healthPercent = this.health / this.maxHealth;
            const barColor = healthPercent > 0.5 ? 0xf44336 : healthPercent > 0.25 ? 0xf44336 : 0x000000;
            healthBar.fillStyle(barColor, 1);
            healthBar.fillRoundedRect(-25, -70, 50 * healthPercent, 8, 4);
        };

        tower.add([healthBarBg, healthBar]);
        tower.updateHealthBar();

        return tower;
    }

    // 创建战斗单位
    function createBattleUnit(startX, startY, soldierData) {
        const unit = gameScene.add.container(startX, startY);

        // 小兵emoji（和方块上的一样大）
        const unitEmoji = gameScene.add.text(0, 0, soldierData.emoji, {
            fontSize: '42px'  // 和方块上的emoji大小一致
        }).setOrigin(0.5);

        unit.add(unitEmoji);

        // 单位属性
        unit.health = soldierData.health;
        unit.maxHealth = soldierData.health;
        unit.attack = soldierData.attack;
        unit.speed = 25; // 移动速度减少50%：50 -> 25
        unit.target = null; // 攻击目标
        unit.lastAttackTime = 0;
        unit.attackCooldown = 800;

        // 血条背景
        const healthBarBg = gameScene.add.graphics();
        healthBarBg.fillStyle(0x000000, 0.5);
        healthBarBg.fillRoundedRect(-15, -25, 30, 4, 2);

        // 血条
        const healthBar = gameScene.add.graphics();
        unit.healthBar = healthBar;
        unit.updateHealthBar = function() {
            healthBar.clear();
            const healthPercent = this.health / this.maxHealth;
            const barColor = healthPercent > 0.5 ? 0x00FF00 : healthPercent > 0.25 ? 0xFFFF00 : 0xFF0000;
            healthBar.fillStyle(barColor, 1);
            healthBar.fillRoundedRect(-15, -25, 30 * healthPercent, 4, 2);
        };

        unit.add([healthBarBg, healthBar]);
        unit.updateHealthBar();

        // 添加到战斗单位列表
        battleUnits.push(unit);
        hasAnyUnitsEverSpawned = true; // 标记已有小兵出征

        // 向上移动到防御塔区域
        gameScene.tweens.add({
            targets: unit,
            y: 250, // 移动到防御塔附近
            duration: 2000,
            ease: 'Power2.easeOut'
        });

        return unit;
    }

    function handleInput(pointer) {
        if (gameWon) return;

        // 新的输入处理逻辑主要是点击骰子，其他交互已经在各自的对象中处理
        // 这里可以添加其他需要的点击处理逻辑
    }

    function checkWinCondition() {
        // 检查是否所有防御塔都被摧毁了
        let allTowersDestroyed = true;
        for (let tower of defenseTowers) {
            if (tower.health > 0 && tower.alpha > 0.1) {
                allTowersDestroyed = false;
                break;
            }
        }

        if (allTowersDestroyed) {
            gameWon = true;

            // 添加胜利粒子效果
            createWinParticles();
            showWinMessage();
        }
    }

    // 移除了eliminateCompletedColumn函数，因为新的游戏机制不需要消除列

    // 移除了createEliminationParticles函数，因为新的游戏机制不需要消除列

    function createWinParticles() {
        // 创建庆祝粒子效果
        for (let i = 0; i < 50; i++) {
            const particle = gameScene.add.graphics();
            const colors = [0xFFD700, 0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4];
            const color = colors[Math.floor(Math.random() * colors.length)];

            particle.fillStyle(color);
            particle.fillCircle(0, 0, Math.random() * 8 + 4);

            particle.x = Math.random() * config.width;
            particle.y = Math.random() * config.height;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y + Math.random() * 200 + 100,
                x: particle.x + (Math.random() - 0.5) * 200,
                alpha: 0,
                rotation: Math.random() * Math.PI * 2,
                duration: Math.random() * 2000 + 1000,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    function showWinMessage() {
        // 隐藏骰子
        if (dice) {
            dice.setVisible(false);
        }

        // 创建胜利背景
        const winBg = gameScene.add.graphics();
        winBg.fillStyle(0x000000, 0.8);
        winBg.fillRect(0, 0, config.width, config.height);

        // 庆祝表情
        const celebrationEmoji = gameScene.add.text(config.width/2, config.height/2 - 120, '🎉', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const winText = gameScene.add.text(config.width/2, config.height/2 - 50, '恭喜过关！', {
            fontSize: '42px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FFD700',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const subText = gameScene.add.text(config.width/2, config.height/2 + 10, '所有防御塔都被摧毁了！', {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 检查是否还有下一关
        const hasNextLevel = LEVEL_CONFIG[currentLevel + 1];

        if (hasNextLevel) {
            // 下一关按钮
            const nextLevelBg = gameScene.add.graphics();
            nextLevelBg.fillStyle(0x4CAF50, 0.9);
            nextLevelBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            nextLevelBg.lineStyle(3, 0x2E7D32);
            nextLevelBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const nextLevelText = gameScene.add.text(config.width/2, config.height/2 + 85, '下一关', {
                fontSize: '24px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 重新开始按钮
            const restartBg = gameScene.add.graphics();
            restartBg.fillStyle(0x2196F3, 0.9);
            restartBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);
            restartBg.lineStyle(3, 0x1976D2);
            restartBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 130, 200, 50, 25);

            const restartText = gameScene.add.text(config.width/2, config.height/2 + 155, '重新开始', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            // 按钮交互
            nextLevelBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
         // 在showWinMessage函数的下一关按钮点击事件中添加重新创建防御塔的代码
nextLevelBg.on('pointerdown', function() {
    currentLevel++;
    levelText.setText(`LEVEL ${currentLevel}`);
    gameWon = false;

    // 更新当前关卡的小兵数据
    const levelConfig = LEVEL_CONFIG[currentLevel];
    currentSoldiers = levelConfig.soldiers;
    currentSoldierNames = Object.keys(currentSoldiers);

    // 保存当前关卡到localStorage
    localStorage.setItem('currentLevel', currentLevel.toString());

    // 清理当前游戏状态
    cleanupGameState();

    // 重新初始化关卡
    initializeLevel();

    // 新增：重新创建防御塔
    createDefenseTowers();

    // 重新显示骰子
    if (dice) {
        dice.setVisible(true);
    }

    // 移除胜利界面
    winBg.destroy();
    celebrationEmoji.destroy();
    winText.destroy();
    subText.destroy();
    nextLevelBg.destroy();
    nextLevelText.destroy();
    restartBg.destroy();
    restartText.destroy();
});

            restartBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 130, 200, 50), Phaser.Geom.Rectangle.Contains);
            restartBg.on('pointerdown', function() {
                location.reload();
            });
        } else {
            // 游戏完成
            const completeBg = gameScene.add.graphics();
            completeBg.fillStyle(0xFF9800, 0.9);
            completeBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
            completeBg.lineStyle(3, 0xF57C00);
            completeBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

            const completeText = gameScene.add.text(config.width/2, config.height/2 + 85, '游戏完成！', {
                fontSize: '20px',
                fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
                fontWeight: 'bold',
                fill: '#ffffff'
            }).setOrigin(0.5);

            completeBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
            completeBg.on('pointerdown', function() {
                location.reload();
            });
        }

        // 动画效果
        gameScene.tweens.add({
            targets: celebrationEmoji,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 600,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        gameScene.tweens.add({
            targets: winText,
            scaleX: 1.05,
            scaleY: 1.05,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }

    // 移除计时器更新函数

    function showGameOverMessage() {
        // 隐藏骰子
        if (dice) {
            dice.setVisible(false);
        }

        // 创建游戏结束背景
        const gameOverBg = gameScene.add.graphics();
        gameOverBg.fillStyle(0x000000, 0.8);
        gameOverBg.fillRect(0, 0, config.width, config.height);

        // 失败表情
        const sadEmoji = gameScene.add.text(config.width/2, config.height/2 - 120, '💀', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const gameOverText = gameScene.add.text(config.width/2, config.height/2 - 50, '全军覆没！', {
            fontSize: '42px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#FF4444',
            stroke: '#000000',
            strokeThickness: 6
        }).setOrigin(0.5);

        const subText = gameScene.add.text(config.width/2, config.height/2 + 10, '防御塔太强了，需要更多小兵！', {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'normal',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 重试按钮
        const retryBg = gameScene.add.graphics();
        retryBg.fillStyle(0x2196F3, 0.9);
        retryBg.fillRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);
        retryBg.lineStyle(3, 0x1976D2);
        retryBg.strokeRoundedRect(config.width/2 - 100, config.height/2 + 60, 200, 50, 25);

        const retryText = gameScene.add.text(config.width/2, config.height/2 + 85, '重试本关', {
            fontSize: '24px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: '#ffffff'
        }).setOrigin(0.5);

        // 按钮交互
        retryBg.setInteractive(new Phaser.Geom.Rectangle(config.width/2 - 100, config.height/2 + 60, 200, 50), Phaser.Geom.Rectangle.Contains);
        retryBg.on('pointerdown', function() {
            location.reload(); // 重试当前关卡
        });

        // 动画效果
        gameScene.tweens.add({
            targets: sadEmoji,
            scaleX: 1.1,
            scaleY: 1.1,
            duration: 800,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });

        gameScene.tweens.add({
            targets: gameOverText,
            scaleX: 1.02,
            scaleY: 1.02,
            duration: 1000,
            yoyo: true,
            repeat: -1,
            ease: 'Sine.easeInOut'
        });
    }


    function update() {
        // 更新战斗系统
        updateBattleSystem();
    }

    // 更新战斗系统
    function updateBattleSystem() {
        const currentTime = gameScene.time.now;

        // 防御塔攻击逻辑
        defenseTowers.forEach(tower => {
            if (tower.health <= 0 || tower.alpha <= 0.1) return; // 跳过已摧毁的塔

            // 寻找攻击范围内的敌方单位
            let nearestUnit = null;
            let nearestDistance = tower.attackRange;

            battleUnits.forEach(unit => {
                if (unit.health <= 0) return;

                const distance = Phaser.Math.Distance.Between(tower.x, tower.y, unit.x, unit.y);
                if (distance < nearestDistance) {
                    nearestUnit = unit;
                    nearestDistance = distance;
                }
            });

            // 攻击最近的单位
            if (nearestUnit && currentTime - tower.lastAttackTime > tower.attackCooldown) {
                attackUnit(tower, nearestUnit);
                tower.lastAttackTime = currentTime;
            }
        });

        // 战斗单位攻击逻辑
        battleUnits.forEach(unit => {
            if (unit.health <= 0) return;

            // 清除已摧毁的目标
            if (unit.target && (unit.target.health <= 0 || unit.target.alpha <= 0.1)) {
                unit.target = null;
            }

            // 如果没有目标，寻找最近的存活防御塔
            if (!unit.target) {
                let nearestTower = null;
                let nearestDistance = Infinity;

                defenseTowers.forEach(tower => {
                    if (tower.health <= 0 || tower.alpha <= 0.1) return; // 跳过已摧毁的塔

                    const distance = Phaser.Math.Distance.Between(unit.x, unit.y, tower.x, tower.y);
                    if (distance < nearestDistance) {
                        nearestTower = tower;
                        nearestDistance = distance;
                    }
                });

                unit.target = nearestTower;
            }

            if (unit.target) {
                const distance = Phaser.Math.Distance.Between(unit.x, unit.y, unit.target.x, unit.target.y);

                if (distance <= 120) { // 攻击范围内
                    // 攻击防御塔
                    if (currentTime - unit.lastAttackTime > unit.attackCooldown) {
                        attackTower(unit, unit.target);
                        unit.lastAttackTime = currentTime;
                    }

                    // 在攻击范围内时，小兵围绕防御塔分散站位
                    if (!unit.attackPosition) {
                        // 为每个小兵分配一个围绕防御塔的位置
                        const angleOffset = Math.random() * Math.PI * 2;
                        const radiusOffset = 80 + Math.random() * 40; // 80-120像素的攻击距离
                        unit.attackPosition = {
                            x: unit.target.x + Math.cos(angleOffset) * radiusOffset,
                            y: unit.target.y + Math.sin(angleOffset) * radiusOffset
                        };
                    }

                    // 移动到攻击位置
                    const dx = unit.attackPosition.x - unit.x;
                    const dy = unit.attackPosition.y - unit.y;
                    const distToPos = Math.sqrt(dx * dx + dy * dy);

                    if (distToPos > 5) {
                        unit.x += (dx / distToPos) * unit.speed * 0.016;
                        unit.y += (dy / distToPos) * unit.speed * 0.016;
                    }
                } else {
                    // 向目标移动
                    const angle = Phaser.Math.Angle.Between(unit.x, unit.y, unit.target.x, unit.target.y);
                    unit.x += Math.cos(angle) * unit.speed * 0.016;
                    unit.y += Math.sin(angle) * unit.speed * 0.016;
                    unit.attackPosition = null; // 清除攻击位置
                }
            } else {
                // 没有目标时继续向上移动
                if (unit.y > 150) {
                    unit.y -= unit.speed * 0.016;
                }
            }
        });

        // 清理死亡的单位
        battleUnits = battleUnits.filter(unit => {
            if (unit.health <= 0) {
                unit.destroy();
                return false;
            }
            return true;
        });

        // 检查失败条件：小兵都死亡了，但还有防御塔存活
        // 在新的游戏机制中，玩家可以通过投掷骰子继续出兵，所以不需要检查失败条件
        // 除非我们想要添加特定的失败条件，比如时间限制等
    }

    // 攻击单位
    function attackUnit(attacker, target) {
        const damage = attacker.attack;
        target.health -= damage;
        target.updateHealthBar();

        // 攻击者显示白色数字，被攻击者显示红色数字
        showDamageNumber(attacker.x, attacker.y - 30, damage, 0xFFFFFF); // 攻击者白色
        showDamageNumber(target.x, target.y - 30, damage, 0xFF0000);     // 被攻击者红色

        // 创建攻击特效
        createAttackEffect(attacker.x, attacker.y, target.x, target.y);

        if (target.health <= 0) {
            // 单位死亡特效
            createDeathEffect(target.x, target.y);
        }
    }

    // 攻击防御塔
    function attackTower(attacker, target) {
        const damage = attacker.attack;
        target.health -= damage;
        target.updateHealthBar();

        // 攻击者显示白色数字，被攻击者显示红色数字
        showDamageNumber(attacker.x, attacker.y - 30, damage, 0xFFFFFF); // 攻击者白色
        showDamageNumber(target.x, target.y - 60, damage, 0xFF0000);     // 被攻击者红色

        // 创建攻击特效
        createAttackEffect(attacker.x, attacker.y, target.x, target.y);

        if (target.health <= 0) {
            // 防御塔被摧毁特效
            createDeathEffect(target.x, target.y);

            // 防御塔完全消失
            gameScene.tweens.add({
                targets: target,
                alpha: 0,
                scaleX: 0,
                scaleY: 0,
                duration: 500,
                ease: 'Back.easeIn',
                onComplete: () => {
                    // 检查胜利条件
                    checkWinCondition();
                }
            });
        }
    }

    // 创建攻击特效
    function createAttackEffect(fromX, fromY, toX, toY) {
        const line = gameScene.add.graphics();
        line.lineStyle(3, 0xFFFF00, 1);
        line.lineBetween(fromX, fromY, toX, toY);

        // 闪烁效果
        gameScene.tweens.add({
            targets: line,
            alpha: 0,
            duration: 200,
            onComplete: () => line.destroy()
        });
    }

    // 创建死亡特效
    function createDeathEffect(x, y) {
        for (let i = 0; i < 10; i++) {
            const particle = gameScene.add.graphics();
            particle.fillStyle(0xFF0000);
            particle.fillCircle(0, 0, Math.random() * 4 + 2);
            particle.x = x + (Math.random() - 0.5) * 20;
            particle.y = y + (Math.random() - 0.5) * 20;

            gameScene.tweens.add({
                targets: particle,
                y: particle.y - Math.random() * 50 - 20,
                x: particle.x + (Math.random() - 0.5) * 40,
                alpha: 0,
                duration: Math.random() * 800 + 400,
                ease: 'Cubic.easeOut',
                onComplete: () => particle.destroy()
            });
        }
    }

    // 显示伤害数字
    function showDamageNumber(x, y, damage, color) {
        const damageText = gameScene.add.text(x, y, `-${damage}`, {
            fontSize: '20px',
            fontFamily: 'Arial, "Microsoft YaHei", "SimHei", sans-serif',
            fontWeight: 'bold',
            fill: `#${color.toString(16).padStart(6, '0')}`,
            stroke: '#000000',
            strokeThickness: 2
        }).setOrigin(0.5);

        // 伤害数字动画
        gameScene.tweens.add({
            targets: damageText,
            y: y - 40,
            alpha: 0,
            scaleX: 1.2,
            scaleY: 1.2,
            duration: 800,
            ease: 'Power2.easeOut',
            onComplete: () => damageText.destroy()
        });
    }

    // 移除了CubeColumn类，因为新的游戏机制使用大富翁式的格子布局




    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
