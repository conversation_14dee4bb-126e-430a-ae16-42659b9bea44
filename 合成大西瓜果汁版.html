<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🍉 合成大西瓜</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.85.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #2c3e50;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            touch-action: manipulation;
        }

        #gameCanvas {
            border-radius: 15px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.5);
            max-width: 100vw;
            max-height: 100vh;
        }

        .loading {
            color: white;
            font-size: 24px;
            text-align: center;
        }
    </style>
</head>
<body>
    <div id="gameCanvas">
        <div class="loading">游戏加载中...</div>
    </div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const CONTAINER_WIDTH = 600;
        const CONTAINER_HEIGHT = 700; // 减少水果区域高度
        const WALL_THICKNESS = 15;
        const UI_HEIGHT = 100;
        const STORAGE_HEIGHT = 100; // 暂存区高度
        const STORAGE_SLOTS = 7; // 暂存区格子数量
        const JUICE_CUP_WIDTH = 80;
        const JUICE_CUP_HEIGHT = 120;

        // 水果配置
        const FRUITS = [
            { emoji: '🍒', size: 32, points: 1, color: 0xff6b6b, juiceColor: 0xff1744 },    // 樱桃 - 鲜红色果汁
            { emoji: '🍓', size: 40, points: 3, color: 0xff8e8e, juiceColor: 0xff4569 },    // 草莓 - 粉红色果汁
            { emoji: '🍇', size: 48, points: 6, color: 0x9b59b6, juiceColor: 0x7b1fa2 },    // 葡萄 - 深紫色果汁
            { emoji: '🍊', size: 56, points: 10, color: 0xf39c12, juiceColor: 0xff6f00 },   // 橙子 - 橙色果汁
            { emoji: '🍋', size: 64, points: 15, color: 0xf1c40f, juiceColor: 0xffc107 },   // 柠檬 - 金黄色果汁
            { emoji: '🍎', size: 72, points: 21, color: 0xe74c3c, juiceColor: 0xd32f2f },   // 苹果 - 深红色果汁
            { emoji: '🍑', size: 80, points: 28, color: 0xfd79a8, juiceColor: 0xe91e63 },   // 樱桃 - 玫红色果汁
            { emoji: '🥭', size: 88, points: 36, color: 0xfdcb6e, juiceColor: 0xff9800 },   // 芒果 - 橙黄色果汁
            { emoji: '🍍', size: 96, points: 45, color: 0xe17055, juiceColor: 0xf57c00 },   // 菠萝 - 深橙色果汁
            { emoji: '🥥', size: 104, points: 55, color: 0x8b4513, juiceColor: 0xf5f5dc },  // 椰子 - 椰汁白色
            { emoji: '🍉', size: 112, points: 66, color: 0x00b894, juiceColor: 0x4caf50 }   // 西瓜 - 绿色果汁
        ];

        let game;
        let score = 0;
        let highScore = 0;

        // 使用内存存储而不是sessionStorage
        try {
            highScore = parseInt(localStorage.getItem('watermelonHighScore') || '0');
        } catch (e) {
            highScore = 0;
        }

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.fruits = [];
                this.currentFruit = null;
                this.nextFruitType = 0;
                this.dropLine = null;
                this.gameOver = false;
                this.merging = new Set();
                this.containerBounds = null;
                this.storageSlots = []; // 暂存区格子
                this.autoDropTimer = null; // 自动下落计时器
                this.isFirstWave = true; // 标记是否是第一波
                this.juiceCups = []; // 果汁杯数组
                this.storageAnimating = false; // 暂存区动画状态
                this.fruitsFlying = false; // 水果飞行状态
            }

            create() {
                this.createBackground();
                this.createUI();

                // 参考demo的简洁设置
                this.matter.world.setBounds();

                this.createContainer();
                this.createStorageArea();
                this.createJuiceCups(); // 创建果汁杯
                this.createDropLine();
                this.setupAutoDropSystem();
                this.setupClickControl();
                this.matter.world.on('collisionstart', this.handleCollision, this);
            }

            createBackground() {
                const graphics = this.add.graphics();
                graphics.fillGradientStyle(0x667eea, 0x667eea, 0x764ba2, 0x764ba2, 1);
                graphics.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);

                graphics.fillStyle(0x2c3e50, 0.9);
                graphics.fillRoundedRect(20, 20, GAME_WIDTH - 40, UI_HEIGHT - 40, 25);

                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;
                graphics.fillStyle(0x87CEEB, 0.3);
                graphics.fillRoundedRect(containerX - 30, containerY - 30, CONTAINER_WIDTH + 60, CONTAINER_HEIGHT + 60, 30);
            }

            createUI() {
           

                this.scoreText = this.add.text(60, 30, `得分: ${score}`, {
                    fontSize: '32px',
                    fill: '#4ecdc4',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                });

                this.highScoreText = this.add.text(GAME_WIDTH - 60, 30, `最高分: ${highScore}`, {
                    fontSize: '32px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(1, 0);

                this.add.text(GAME_WIDTH / 2, 50, '自动下落中...', {
                    fontSize: '24px',
                    fill: '#bdc3c7',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH / 2, GAME_HEIGHT - 40, '点击水果收集到暂存区 | 3个相同可以榨汁', {
                    fontSize: '24px',
                    fill: '#95a5a6',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }

            createContainer() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const containerY = UI_HEIGHT + 50;

                this.containerBounds = {
                    left: containerX,
                    right: containerX + CONTAINER_WIDTH,
                    top: containerY,
                    bottom: containerY + CONTAINER_HEIGHT
                };

                const wallOptions = { 
                    isStatic: true, 
                    label: 'wall',
                    friction: 0.5,
                    restitution: 0.3
                };

                this.matter.add.rectangle(
                    containerX - WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH + WALL_THICKNESS/2, 
                    containerY + CONTAINER_HEIGHT/2, 
                    WALL_THICKNESS, 
                    CONTAINER_HEIGHT, 
                    wallOptions
                );

                this.matter.add.rectangle(
                    containerX + CONTAINER_WIDTH/2, 
                    containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2, 
                    CONTAINER_WIDTH + WALL_THICKNESS*2, 
                    WALL_THICKNESS, 
                    wallOptions
                );

                const graphics = this.add.graphics();
                graphics.lineStyle(6, 0x4ecdc4, 0.8);
                graphics.strokeRoundedRect(containerX - 3, containerY - 3, CONTAINER_WIDTH + 6, CONTAINER_HEIGHT + 6, 15);
            }

            createStorageArea() {
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;
                const storageY = UI_HEIGHT + 50 + CONTAINER_HEIGHT + 50;
                const slotWidth = CONTAINER_WIDTH / STORAGE_SLOTS;

                // 初始化暂存区数组
                this.storageSlots = new Array(STORAGE_SLOTS).fill(null);

                // 绘制暂存区背景
                const graphics = this.add.graphics();
                graphics.fillStyle(0x34495e, 0.8);
                graphics.fillRoundedRect(containerX, storageY, CONTAINER_WIDTH, STORAGE_HEIGHT, 10);

                // 绘制格子分隔线
                graphics.lineStyle(2, 0x7f8c8d, 0.6);
                for (let i = 1; i < STORAGE_SLOTS; i++) {
                    const x = containerX + i * slotWidth;
                    graphics.moveTo(x, storageY);
                    graphics.lineTo(x, storageY + STORAGE_HEIGHT);
                }
                graphics.strokePath();

               
                // 存储暂存区位置信息
                this.storageArea = {
                    x: containerX,
                    y: storageY,
                    width: CONTAINER_WIDTH,
                    height: STORAGE_HEIGHT,
                    slotWidth: slotWidth
                };
            }

            createJuiceCups() {
                const cupY = this.storageArea.y + STORAGE_HEIGHT + 50;
                const cupSpacing = (CONTAINER_WIDTH - 3 * JUICE_CUP_WIDTH) / 4;
                const startX = (GAME_WIDTH - CONTAINER_WIDTH) / 2 + cupSpacing;

                // 创建3个果汁杯
                for (let i = 0; i < 3; i++) {
                    const cupX = startX + i * (JUICE_CUP_WIDTH + cupSpacing);
                    
                    // 创建杯子容器
                    const cup = {
                        x: cupX,
                        y: cupY,
                        width: JUICE_CUP_WIDTH,
                        height: JUICE_CUP_HEIGHT,
                        juiceLevel: 0, // 果汁等级 0-3
                        juiceColor: null, // 果汁颜色
                        fruitType: null, // 对应的水果类型
                        isMixed: false, // 是否为混合果汁
                        originalFruitType: null, // 原始水果类型（混合前）
                        juiceLayers: [], // 果汁分层信息
                        graphics: null,
                        juiceGraphics: null
                    };

                    // 绘制杯子外框
                    const cupGraphics = this.add.graphics();
                    cupGraphics.lineStyle(4, 0x8b4513, 1);
                    cupGraphics.strokeRoundedRect(cupX, cupY, JUICE_CUP_WIDTH, JUICE_CUP_HEIGHT, 8);
                    
                    // 绘制杯子底部
                    cupGraphics.fillStyle(0xf4f4f4, 0.3);
                    cupGraphics.fillRoundedRect(cupX + 2, cupY + 2, JUICE_CUP_WIDTH - 4, JUICE_CUP_HEIGHT - 4, 6);

                    cup.graphics = cupGraphics;

                    // 创建果汁图形对象
                    const juiceGraphics = this.add.graphics();
                    cup.juiceGraphics = juiceGraphics;

                    // 让杯子可点击进行倒果汁操作
                    const cupIndex = i; // 当前杯子的索引
                    cupGraphics.setInteractive(new Phaser.Geom.Rectangle(cup.x, cup.y, cup.width, cup.height), Phaser.Geom.Rectangle.Contains);
                    cupGraphics.on('pointerdown', () => {
                        this.handleCupClick(cupIndex);
                    });

                    this.juiceCups.push(cup);
                }

             
            }

            createDropLine() {
                // 移除警戒线，因为不再判断失败
                // 游戏现在是无限模式，玩家通过收集管理游戏区域
            }

            setupAutoDropSystem() {
                // 第一波投放10个水果
                if (this.isFirstWave) {
                    this.dropFirstWave();
                } else {
                    // 启动自动下落系统
                    this.startAutoDropTimer();
                }
            }

            dropFirstWave() {
                // 第一波快速投放25个水果，快速达到接近30个的数量
                for (let i = 0; i < 25; i++) {
                    this.time.delayedCall(i * 100, () => { // 100ms间隔，更快
                        this.dropRandomFruit();

                        // 最后一个水果投放后，开始正常的自动下落
                        if (i === 24) {
                            this.isFirstWave = false;
                            this.time.delayedCall(1000, () => { // 缩短等待时间
                                this.startAutoDropTimer();
                            });
                        }
                    });
                }
            }

            startAutoDropTimer() {
                // 清除现有计时器
                if (this.autoDropTimer) {
                    this.autoDropTimer.destroy();
                }

                // 创建动态间隔的自动下落计时器
                this.autoDropTimer = this.time.addEvent({
                    delay: 500, // 初始0.5秒间隔，更快补充
                    callback: this.dropRandomFruit,
                    callbackScope: this,
                    loop: true
                });
            }

            dropRandomFruit() {
                if (this.gameOver) return;

                // 检查水果数量，超过30个就不生成新水果
                if (this.fruits.length >= 30) {
                    return;
                }

                // 动态调整生成间隔：水果越少，生成越快
                if (this.autoDropTimer) {
                    let newDelay = 1000; // 默认1秒
                    if (this.fruits.length < 15) {
                        newDelay = 300; // 少于15个时，300ms间隔
                    } else if (this.fruits.length < 25) {
                        newDelay = 500; // 少于25个时，500ms间隔
                    }

                    // 更新计时器间隔
                    if (this.autoDropTimer.delay !== newDelay) {
                        this.autoDropTimer.delay = newDelay;
                    }
                }

                const fruitType = Phaser.Math.Between(0, 4);
                const containerX = (GAME_WIDTH - CONTAINER_WIDTH) / 2;

                // 随机X位置
                const fruitConfig = FRUITS[fruitType];
                const randomX = Phaser.Math.Between(
                    containerX + fruitConfig.size,
                    containerX + CONTAINER_WIDTH - fruitConfig.size
                );
                const startY = UI_HEIGHT + 80;

                // 创建圆形水果
                const fruit = this.add.circle(randomX, startY, fruitConfig.size, fruitConfig.color);

                // 添加物理体（直接设为动态，立即下落）
                this.matter.add.gameObject(fruit, {
                    shape: 'circle',
                    radius: fruitConfig.size,
                    isStatic: false
                });

                // 添加emoji文本
                const emojiText = this.add.text(randomX, startY, fruitConfig.emoji, {
                    fontSize: `${fruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                fruit.emojiText = emojiText;
                fruit.fruitType = fruitType;
                fruit.canMerge = false; // 新生成的水果不能立即合成
                fruit.birthTime = this.time.now; // 记录生成时间

                // 1秒后允许合成
                this.time.delayedCall(1000, () => {
                    if (fruit && fruit.active) {
                        fruit.canMerge = true;
                    }
                });

                // 设置水果为可点击 - 使用通用的水果点击设置方法
                this.setupFruitClick(fruit);

                this.fruits.push(fruit);
            }

            // 新增：统一的水果点击设置方法
            setupFruitClick(fruit) {
                fruit.setInteractive();
                fruit.on('pointerdown', () => {
                    this.collectFruit(fruit);
                });
            }

            collectFruit(fruit) {
                // 找到第一个空的暂存区位置
                const emptySlotIndex = this.storageSlots.findIndex(slot => slot === null);

                if (emptySlotIndex === -1) {
                    // 暂存区已满，游戏结束
                    this.gameOver = true;
                    this.time.delayedCall(500, () => {
                        this.showGameOver();
                    });
                    return;
                }

                // 计算暂存区位置
                const slotX = this.storageArea.x + (emptySlotIndex + 0.5) * this.storageArea.slotWidth;
                const slotY = this.storageArea.y + this.storageArea.height / 2;

                // 创建收集特效
                this.createCollectEffect(fruit.x, fruit.y, slotX, slotY);

                // 将水果信息存储到暂存区
                this.storageSlots[emptySlotIndex] = {
                    fruitType: fruit.fruitType,
                    emoji: FRUITS[fruit.fruitType].emoji,
                    displayObject: null
                };

                // 移除原水果
                this.removeFruit(fruit);

                // 在暂存区显示水果
                this.time.delayedCall(300, () => {
                    this.displayFruitInStorage(emptySlotIndex);
                    this.checkForMatches();
                });
            }

            createCollectEffect(fromX, fromY, toX, toY) {
                // 创建移动特效
                const effect = this.add.text(fromX, fromY, '✨', {
                    fontSize: '32px'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: effect,
                    x: toX,
                    y: toY,
                    scaleX: 0.5,
                    scaleY: 0.5,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        effect.destroy();
                    }
                });
            }

            displayFruitInStorage(slotIndex) {
                const slotData = this.storageSlots[slotIndex];
                if (!slotData) return;

                const slotX = this.storageArea.x + (slotIndex + 0.5) * this.storageArea.slotWidth;
                const slotY = this.storageArea.y + this.storageArea.height / 2;

                // 创建暂存区中的水果显示
                const displayObject = this.add.text(slotX, slotY, slotData.emoji, {
                    fontSize: '48px'
                }).setOrigin(0.5);

                slotData.displayObject = displayObject;
            }

            checkForMatches() {
                // 如果有水果正在飞行，延迟检查
                if (this.fruitsFlying) {
                    this.time.delayedCall(200, () => {
                        this.checkForMatches();
                    });
                    return;
                }

                // 统计每种水果的数量和位置
                const fruitCounts = {};
                const fruitPositions = {};

                this.storageSlots.forEach((slot, index) => {
                    if (slot !== null) {
                        const type = slot.fruitType;
                        if (!fruitCounts[type]) {
                            fruitCounts[type] = 0;
                            fruitPositions[type] = [];
                        }
                        fruitCounts[type]++;
                        fruitPositions[type].push(index);
                    }
                });

                // 检查是否有3个或更多相同的水果
                for (const [fruitType, count] of Object.entries(fruitCounts)) {
                    if (count >= 3) {
                        this.eliminateFruits(parseInt(fruitType), fruitPositions[fruitType]);
                        break; // 一次只处理一种水果的消除
                    }
                }
            }

            eliminateFruits(fruitType, positions) {
                // 取前3个位置进行消除
                const eliminatePositions = positions.slice(0, 3);

                // 寻找可用的果汁杯
                const availableCupIndex = this.findAvailableCup(fruitType);

                if (availableCupIndex === -1) {
                    // 没有可用杯子，游戏结束
                    this.gameOver = true;
                    this.time.delayedCall(1000, () => {
                        this.showGameOver();
                    });
                    return;
                }

                // 设置飞行状态
                this.fruitsFlying = true;

                // 获取目标杯子位置（杯子上方）
                const targetCup = this.juiceCups[availableCupIndex];
                const targetX = targetCup.x + targetCup.width / 2;
                const targetY = targetCup.y - 20; // 飞向杯子上方

                // 创建飞向杯子的动画
                let completedAnimations = 0;
                eliminatePositions.forEach((pos, index) => {
                    const slot = this.storageSlots[pos];
                    if (slot && slot.displayObject) {
                        // 创建飞行的水果副本
                        const flyingFruit = this.add.text(
                            slot.displayObject.x,
                            slot.displayObject.y,
                            slot.emoji,
                            { fontSize: '48px' }
                        ).setOrigin(0.5);

                        // 隐藏原始水果
                        slot.displayObject.setVisible(false);

                        // 飞向杯子上方的动画（不缩放）
                        this.tweens.add({
                            targets: flyingFruit,
                            x: targetX,
                            y: targetY,
                            duration: 600,
                            delay: index * 100, // 错开飞行时间
                            ease: 'Power2',
                            onComplete: () => {
                                // 添加消失特效
                                this.tweens.add({
                                    targets: flyingFruit,
                                    alpha: 0,
                                    scaleX: 1.5,
                                    scaleY: 1.5,
                                    duration: 200,
                                    onComplete: () => {
                                        flyingFruit.destroy();
                                        completedAnimations++;

                                        // 所有动画完成后处理
                                        if (completedAnimations === eliminatePositions.length) {
                                            this.handleEliminationComplete(eliminatePositions, availableCupIndex, fruitType);
                                        }
                                    }
                                });
                            }
                        });

                        // 销毁原始显示对象
                        slot.displayObject.destroy();
                    }
                });
            }

            handleEliminationComplete(eliminatePositions, availableCupIndex, fruitType) {
                // 重置飞行状态
                this.fruitsFlying = false;

                // 清空消除的位置
                eliminatePositions.forEach(pos => {
                    this.storageSlots[pos] = null;
                });

                // 重新排列暂存区
                this.rearrangeStorageWithAnimation();

                // 向果汁杯添加果汁
                this.updateJuiceCup(availableCupIndex, fruitType);

                // 计算得分 - 暂存区消除积分
                const points = FRUITS[fruitType].points * 3 + 50;
                this.updateScore(points);

                // 显示得分特效
                const scoreEffect = this.add.text(GAME_WIDTH / 2, this.storageArea.y - 50, `+${points}`, {
                    fontSize: '36px',
                    fill: '#f39c12',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: scoreEffect,
                    y: this.storageArea.y - 100,
                    alpha: 0,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scoreEffect.destroy();
                    }
                });
            }

            rearrangeStorageWithAnimation() {
                // 如果正在动画中，跳过
                if (this.storageAnimating) {
                    return;
                }

                this.storageAnimating = true;

                // 收集所有非空的水果数据
                const remainingFruits = [];
                this.storageSlots.forEach((slot, index) => {
                    if (slot !== null) {
                        remainingFruits.push({
                            fruitType: slot.fruitType,
                            emoji: slot.emoji
                        });
                    }
                });

                // 清除所有显示对象，避免重叠
                this.storageSlots.forEach(slot => {
                    if (slot && slot.displayObject) {
                        slot.displayObject.destroy();
                    }
                });

                // 重置暂存区数组
                this.storageSlots = new Array(STORAGE_SLOTS).fill(null);

                // 如果没有剩余水果，直接结束动画状态
                if (remainingFruits.length === 0) {
                    this.storageAnimating = false;
                    return;
                }

                let completedAnimations = 0;
                const totalAnimations = remainingFruits.length;

                // 重新排列并添加动画
                remainingFruits.forEach((fruitData, newIndex) => {
                    // 创建新的暂存区数据
                    this.storageSlots[newIndex] = {
                        fruitType: fruitData.fruitType,
                        emoji: fruitData.emoji,
                        displayObject: null
                    };

                    // 计算新位置
                    const newSlotX = this.storageArea.x + (newIndex + 0.5) * this.storageArea.slotWidth;
                    const newSlotY = this.storageArea.y + this.storageArea.height / 2;

                    // 延迟创建显示对象，确保不重叠
                    this.time.delayedCall(newIndex * 80, () => {
                        if (this.storageSlots[newIndex]) { // 确保位置仍然有效
                            const displayObject = this.add.text(newSlotX, newSlotY + 50, fruitData.emoji, {
                                fontSize: '48px',
                                alpha: 0
                            }).setOrigin(0.5);

                            // 添加进入动画
                            this.tweens.add({
                                targets: displayObject,
                                y: newSlotY,
                                alpha: 1,
                                duration: 300,
                                ease: 'Back.easeOut',
                                onComplete: () => {
                                    completedAnimations++;
                                    // 所有动画完成后，重置动画状态
                                    if (completedAnimations === totalAnimations) {
                                        this.storageAnimating = false;
                                    }
                                }
                            });

                            // 更新显示对象引用
                            this.storageSlots[newIndex].displayObject = displayObject;
                        }
                    });
                });
            }

            setupClickControl() {
                // 简化的点击控制，主要用于点击水果收集
                // 水果的点击事件在setupFruitClick中设置
            }

            handleCollision(event) {
                const pairs = event.pairs;

                for (let i = 0; i < pairs.length; i++) {
                    const pair = pairs[i];
                    const fruitA = pair.bodyA.gameObject;
                    const fruitB = pair.bodyB.gameObject;

                    // 检查是否是两个水果碰撞
                    if (fruitA && fruitB &&
                        typeof fruitA.fruitType === 'number' &&
                        typeof fruitB.fruitType === 'number') {

                        // 相同类型的水果才能合成，且都必须过了保护期
                        if (fruitA.fruitType === fruitB.fruitType &&
                            fruitA.fruitType < FRUITS.length - 1 &&
                            fruitA.canMerge !== false && // 检查合成保护期
                            fruitB.canMerge !== false && // 检查合成保护期
                            !this.merging.has(fruitA) &&
                            !this.merging.has(fruitB)) {

                            // 标记为正在合成，防止重复
                            this.merging.add(fruitA);
                            this.merging.add(fruitB);

                            // 延迟合成，避免物理引擎冲突
                            this.time.delayedCall(50, () => {
                                if (fruitA.active && fruitB.active) {
                                    this.mergeFruits(fruitA, fruitB, fruitA.fruitType);
                                }
                            });
                        }
                    }
                }
            }

            mergeFruits(fruitA, fruitB, fruitType) {
                // 计算合成位置
                const mergeX = (fruitA.x + fruitB.x) / 2;
                const mergeY = (fruitA.y + fruitB.y) / 2;

                // 移除旧水果
                this.removeFruit(fruitA);
                this.removeFruit(fruitB);

                // 创建新水果
                const newFruitType = fruitType + 1;
                if (newFruitType >= FRUITS.length) return; // 防止超出数组范围

                const newFruitConfig = FRUITS[newFruitType];
                const newFruit = this.add.circle(mergeX, mergeY, newFruitConfig.size, newFruitConfig.color);

                // 添加物理体
                this.matter.add.gameObject(newFruit, {
                    shape: 'circle',
                    radius: newFruitConfig.size,
                    isStatic: false
                });

                // 添加emoji文本
                const emojiText = this.add.text(mergeX, mergeY, newFruitConfig.emoji, {
                    fontSize: `${newFruitConfig.size * 1.2}px`
                }).setOrigin(0.5);

                newFruit.emojiText = emojiText;
                newFruit.fruitType = newFruitType;
                newFruit.canMerge = false; // 合成后的新水果也需要保护期
                newFruit.birthTime = this.time.now; // 记录生成时间

                // 1秒后允许合成
                this.time.delayedCall(1000, () => {
                    if (newFruit && newFruit.active) {
                        newFruit.canMerge = true;
                    }
                });

                // 关键修复：为合成后的新水果设置点击事件
                this.setupFruitClick(newFruit);

                this.fruits.push(newFruit);

                // 计算并添加积分
                const points = newFruitConfig.points;
                this.updateScore(points);

                // 添加合成特效
                this.createMergeEffect(mergeX, mergeY, newFruitConfig.emoji, points);
            }

            createMergeEffect(x, y, emoji, points = 0) {
                // 创建合成特效文字
                const effect = this.add.text(x, y, emoji, {
                    fontSize: '60px'
                }).setOrigin(0.5);

                // 主要动画效果
                this.tweens.add({
                    targets: effect,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    y: y - 80,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        effect.destroy();
                    }
                });

                // 创建积分显示
                if (points > 0) {
                    const scoreEffect = this.add.text(x, y + 60, `+${points}`, {
                        fontSize: '32px',
                        fill: '#f39c12',
                        fontStyle: 'bold',
                        stroke: '#ffffff',
                        strokeThickness: 2
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: scoreEffect,
                        y: y - 20,
                        alpha: 0,
                        scaleX: 1.5,
                        scaleY: 1.5,
                        duration: 1200,
                        ease: 'Power2',
                        onComplete: () => {
                            scoreEffect.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(x, y, 30);

                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });

                // 创建星星粒子效果
                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const star = this.add.text(x, y, '✨', {
                        fontSize: '24px'
                    }).setOrigin(0.5);

                    const targetX = x + Math.cos(angle) * 100;
                    const targetY = y + Math.sin(angle) * 100;

                    this.tweens.add({
                        targets: star,
                        x: targetX,
                        y: targetY,
                        alpha: 0,
                        duration: 800,
                        ease: 'Power2',
                        onComplete: () => {
                            star.destroy();
                        }
                    });
                }
            }

            removeFruit(fruit) {
                // 从合成标记中移除
                this.merging.delete(fruit);

                // 从水果数组中移除
                const index = this.fruits.indexOf(fruit);
                if (index > -1) {
                    this.fruits.splice(index, 1);
                }

                // 销毁emoji文本
                if (fruit.emojiText) {
                    fruit.emojiText.destroy();
                }

                // 销毁水果对象
                fruit.destroy();
            }

            updateScore(points) {
                score += points;
                this.scoreText.setText(`得分: ${score}`);

                if (score > highScore) {
                    highScore = score;
                    try {
                        localStorage.setItem('watermelonHighScore', highScore.toString());
                    } catch (e) {
                        console.log('无法保存最高分');
                    }
                    this.highScoreText.setText(`最高分: ${highScore}`);
                }
            }

            checkGameOver() {
                // 移除警戒线失败判断，游戏可以无限进行
                // 玩家可以通过收集水果到暂存区来管理游戏区域
            }

            showGameOver() {
                const overlay = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.8);
                overlay.setInteractive();

                const panel = this.add.graphics();
                panel.fillStyle(0x2c3e50, 0.95);
                panel.fillRoundedRect(GAME_WIDTH/2 - 250, GAME_HEIGHT/2 - 200, 500, 400, 30);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 120, '🎮 游戏结束 🎮', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 40, `最终得分: ${score}`, {
                    fontSize: '36px',
                    fill: '#f39c12',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const restartButton = this.add.graphics();
                restartButton.fillStyle(0x4ecdc4, 0.8);
                restartButton.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60, 30);

                const restartText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 110, '🔄 重新开始', {
                    fontSize: '28px',
                    fill: '#ffffff',
                    fontFamily: 'Arial',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const buttonBounds = new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 80, 240, 60);
                restartButton.setInteractive(buttonBounds, Phaser.Geom.Rectangle.Contains);

                restartButton.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });

                overlay.on('pointerdown', () => {
                    score = 0;
                    this.scene.restart();
                });
            }

            updateJuiceCup(cupIndex, fruitType) {
                const cup = this.juiceCups[cupIndex];
                if (!cup) return false;

                // 检查杯子是否已满，防止超过3层
                if (cup.juiceLevel >= 3) {
                    console.log(`杯子 ${cupIndex} 已满，拒绝添加果汁`);
                    return false;
                }

                // 添加果汁层
                cup.juiceLayers.push({
                    fruitType: fruitType,
                    color: FRUITS[fruitType].juiceColor
                });

                // 检查是否为混合果汁
                if (cup.juiceLevel === 0) {
                    // 第一层果汁
                    cup.fruitType = fruitType;
                    cup.juiceColor = FRUITS[fruitType].juiceColor;
                    cup.isMixed = false;
                } else {
                    // 检查是否与之前的果汁类型不同
                    const hasMultipleTypes = cup.juiceLayers.some(layer => layer.fruitType !== fruitType);
                    if (hasMultipleTypes) {
                        cup.isMixed = true;
                        cup.fruitType = -1; // 标记为混合
                    }
                }

                // 添加1/3果汁
                cup.juiceLevel++;

                // 更新果汁显示
                this.drawJuice(cup);

                // 检查杯子是否满了
                if (cup.juiceLevel >= 3) {
                    this.time.delayedCall(500, () => {
                        this.checkCupCompletion(cupIndex);
                    });
                }

                return true;
            }

            // 混合两种颜色
            mixColors(color1, color2) {
                const r1 = (color1 >> 16) & 0xFF;
                const g1 = (color1 >> 8) & 0xFF;
                const b1 = color1 & 0xFF;

                const r2 = (color2 >> 16) & 0xFF;
                const g2 = (color2 >> 8) & 0xFF;
                const b2 = color2 & 0xFF;

                const r = Math.floor((r1 + r2) / 2);
                const g = Math.floor((g1 + g2) / 2);
                const b = Math.floor((b1 + b2) / 2);

                return (r << 16) | (g << 8) | b;
            }

            drawJuice(cup) {
                cup.juiceGraphics.clear();

                if (cup.juiceLevel > 0 && cup.juiceLayers.length > 0) {
                    const totalJuiceHeight = (cup.height - 8) * (cup.juiceLevel / 3);
                    const layerHeight = totalJuiceHeight / cup.juiceLayers.length;

                    // 从底部开始绘制每一层果汁
                    for (let i = 0; i < cup.juiceLayers.length; i++) {
                        const layer = cup.juiceLayers[i];
                        const layerY = cup.y + cup.height - 4 - (i + 1) * layerHeight;

                        // 绘制果汁层
                        cup.juiceGraphics.fillStyle(layer.color, 0.95);
                        cup.juiceGraphics.fillRoundedRect(
                            cup.x + 4,
                            layerY,
                            cup.width - 8,
                            layerHeight,
                            i === 0 ? 4 : 0 // 只有底层有圆角
                        );

                        // 添加层间分隔线（如果是混合果汁）
                        if (i > 0 && cup.isMixed) {
                            cup.juiceGraphics.lineStyle(1, 0xffffff, 0.3);
                            cup.juiceGraphics.moveTo(cup.x + 4, layerY);
                            cup.juiceGraphics.lineTo(cup.x + cup.width - 4, layerY);
                            cup.juiceGraphics.strokePath();
                        }
                    }

                    // 添加顶层光泽效果
                    const topLayerY = cup.y + cup.height - 4 - totalJuiceHeight;
                    cup.juiceGraphics.fillStyle(0xffffff, 0.4);
                    cup.juiceGraphics.fillRoundedRect(
                        cup.x + 4,
                        topLayerY,
                        cup.width - 8,
                        Math.min(8, totalJuiceHeight),
                        4
                    );
                }
            }

            checkCupCompletion(cupIndex) {
                const cup = this.juiceCups[cupIndex];

                // 检查是否所有果汁层都是相同颜色
                if (cup.juiceLayers.length === 3) {
                    const firstType = cup.juiceLayers[0].fruitType;
                    const allSameType = cup.juiceLayers.every(layer => layer.fruitType === firstType);

                    if (allSameType) {
                        // 相同颜色，可以消除
                        this.completeCup(cupIndex);
                    } else {
                        // 不同颜色，不能消除，杯子保持满状态
                        console.log(`杯子 ${cupIndex} 已满但颜色不同，不能消除`);
                        // 可以添加倒果汁的提示
                        this.showPourJuiceHint(cupIndex);
                    }
                }
            }

            handleCupClick(cupIndex) {
                const cup = this.juiceCups[cupIndex];

                // 检查杯子是否有果汁可以倒
                if (cup.juiceLevel === 0) {
                    console.log('空杯子无法倒果汁');
                    return;
                }

                // 开始倒果汁操作
                this.startPourJuice(cupIndex);
            }

            showPourJuiceHint(cupIndex) {
                const cup = this.juiceCups[cupIndex];

                // 显示倒果汁提示
                const hintText = this.add.text(cup.x + cup.width/2, cup.y - 50, '点击倒果汁', {
                    fontSize: '16px',
                    fill: '#ff6b6b',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 提示文字3秒后消失
                this.time.delayedCall(3000, () => {
                    if (hintText && hintText.destroy) {
                        hintText.destroy();
                    }
                });
            }

            completeCup(cupIndex) {
                const cup = this.juiceCups[cupIndex];

                // 根据果汁类型显示不同的完成特效
                let completionText = '🎉 完成！';
                let bonusMultiplier = 10;

                if (cup.isMixed) {
                    completionText = '🌈 混合果汁！';
                    bonusMultiplier = 15; // 混合果汁奖励更高
                }

                // 创建完成特效
                const completionEffect = this.add.text(cup.x + cup.width/2, cup.y - 30, completionText, {
                    fontSize: '20px',
                    fill: cup.isMixed ? '#9b59b6' : '#f39c12',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: completionEffect,
                    y: cup.y - 60,
                    alpha: 0,
                    duration: 1000,
                    onComplete: () => {
                        completionEffect.destroy();
                    }
                });

                // 计算奖励分数
                let bonusPoints;
                if (cup.isMixed) {
                    // 混合果汁：基础分数 + 混合奖励
                    bonusPoints = 100 + (bonusMultiplier * 5);
                } else {
                    // 纯果汁：根据水果类型计算
                    bonusPoints = FRUITS[cup.fruitType].points * bonusMultiplier;
                }

                this.updateScore(bonusPoints);

                // 显示奖励分数
                const scoreEffect = this.add.text(cup.x + cup.width/2, cup.y + cup.height/2, `+${bonusPoints}`, {
                    fontSize: '28px',
                    fill: cup.isMixed ? '#9b59b6' : '#e74c3c',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: scoreEffect,
                    y: cup.y,
                    alpha: 0,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    duration: 1200,
                    onComplete: () => {
                        scoreEffect.destroy();
                    }
                });

                // 杯子直接重置
                this.time.delayedCall(1000, () => {
                    this.resetCup(cupIndex);
                });
            }

            resetCup(cupIndex) {
                const cup = this.juiceCups[cupIndex];

                // 安全销毁旧的图形对象
                if (cup.graphics && cup.graphics.destroy) {
                    cup.graphics.destroy();
                }
                if (cup.juiceGraphics && cup.juiceGraphics.destroy) {
                    cup.juiceGraphics.destroy();
                }

                // 重置杯子数据
                cup.juiceLevel = 0;
                cup.juiceColor = null;
                cup.fruitType = null;
                cup.isMixed = false;
                cup.originalFruitType = null;
                cup.juiceLayers = [];

                // 直接在当前位置创建新杯子，无动画
                const cupGraphics = this.add.graphics();
                cupGraphics.lineStyle(4, 0x8b4513, 1);
                cupGraphics.strokeRoundedRect(cup.x, cup.y, cup.width, cup.height, 8);

                // 绘制杯子底部
                cupGraphics.fillStyle(0xf4f4f4, 0.3);
                cupGraphics.fillRoundedRect(cup.x + 2, cup.y + 2, cup.width - 4, cup.height - 4, 6);

                // 创建果汁图形对象
                const juiceGraphics = this.add.graphics();

                // 更新引用
                cup.graphics = cupGraphics;
                cup.juiceGraphics = juiceGraphics;

                // 重新添加点击事件
                cupGraphics.setInteractive(new Phaser.Geom.Rectangle(cup.x, cup.y, cup.width, cup.height), Phaser.Geom.Rectangle.Contains);
                cupGraphics.on('pointerdown', () => {
                    this.handleCupClick(cupIndex);
                });

                console.log(`杯子 ${cupIndex} 已重置`);
            }

            startPourJuice(fromCupIndex) {
                const fromCup = this.juiceCups[fromCupIndex];

                if (fromCup.juiceLevel === 0) {
                    console.log('空杯子无法倒果汁');
                    return;
                }

                // 显示可倒入的杯子选择界面
                this.showPourTargets(fromCupIndex);
            }

            showPourTargets(fromCupIndex) {
                const fromCup = this.juiceCups[fromCupIndex];
                const topLayer = fromCup.juiceLayers[fromCup.juiceLayers.length - 1];

                // 高亮可倒入的杯子
                this.juiceCups.forEach((cup, index) => {
                    if (index === fromCupIndex) return; // 跳过自己

                    const canPour = this.canPourInto(fromCupIndex, index);
                    if (canPour) {
                        // 添加高亮效果
                        const highlight = this.add.graphics();
                        highlight.lineStyle(4, 0x00ff00, 1);
                        highlight.strokeRoundedRect(cup.x - 2, cup.y - 2, cup.width + 4, cup.height + 4, 10);

                        // 让杯子可点击
                        const clickArea = this.add.rectangle(cup.x + cup.width/2, cup.y + cup.height/2, cup.width, cup.height);
                        clickArea.setInteractive();
                        clickArea.on('pointerdown', () => {
                            this.pourJuice(fromCupIndex, index);
                            this.clearPourTargets();
                        });

                        // 存储高亮和点击区域，用于清理
                        if (!this.pourHighlights) this.pourHighlights = [];
                        this.pourHighlights.push(highlight, clickArea);
                    }
                });

                // 添加取消按钮
                const cancelButton = this.add.text(GAME_WIDTH/2, GAME_HEIGHT - 100, '取消倒果汁', {
                    fontSize: '24px',
                    fill: '#ff6b6b',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                cancelButton.setInteractive();
                cancelButton.on('pointerdown', () => {
                    this.clearPourTargets();
                });

                if (!this.pourHighlights) this.pourHighlights = [];
                this.pourHighlights.push(cancelButton);
            }

            canPourInto(fromIndex, toIndex) {
                const fromCup = this.juiceCups[fromIndex];
                const toCup = this.juiceCups[toIndex];

                if (fromCup.juiceLevel === 0) return false; // 源杯子为空
                if (toCup.juiceLevel >= 3) return false; // 目标杯子已满

                const topLayer = fromCup.juiceLayers[fromCup.juiceLayers.length - 1];

                // 可以倒入空杯子
                if (toCup.juiceLevel === 0) return true;

                // 可以倒入相同颜色的杯子
                const targetTopLayer = toCup.juiceLayers[toCup.juiceLayers.length - 1];
                return topLayer.fruitType === targetTopLayer.fruitType;
            }

            pourJuice(fromIndex, toIndex) {
                const fromCup = this.juiceCups[fromIndex];
                const toCup = this.juiceCups[toIndex];

                if (!this.canPourInto(fromIndex, toIndex)) {
                    console.log('无法倒入此杯子');
                    return;
                }

                // 开始倒果汁动画
                this.animatePourJuice(fromIndex, toIndex);
            }

            animatePourJuice(fromIndex, toIndex) {
                const fromCup = this.juiceCups[fromIndex];
                const toCup = this.juiceCups[toIndex];

                // 计算两个杯子之间的位置差异
                const deltaX = toCup.x - fromCup.x; // 水平距离
                const deltaY = toCup.y - fromCup.y; // 垂直距离

                // 计算目标位置：目标杯子正上方
                // X方向：移动到目标杯子中心对齐
                const targetOffsetX = deltaX;
                // Y方向：移动到目标杯子上方（杯子高度 + 间距）
                const targetOffsetY = deltaY - fromCup.height - 40;

                console.log(`倒果汁动画：从杯子${fromIndex}(${fromCup.x}, ${fromCup.y}) 到杯子${toIndex}(${toCup.x}, ${toCup.y})`);
                console.log(`移动偏移：X=${targetOffsetX}, Y=${targetOffsetY}`);

                // 第一阶段：杯子移动到目标杯子上方
                this.tweens.add({
                    targets: [fromCup.graphics, fromCup.juiceGraphics],
                    x: `+=${targetOffsetX}`,
                    y: `+=${targetOffsetY}`,
                    duration: 500,
                    ease: 'Power2.easeOut',
                    onComplete: () => {
                        // 第二阶段：杯子倾斜并倒果汁
                        this.tweens.add({
                            targets: [fromCup.graphics, fromCup.juiceGraphics],
                            rotation: 0.01, // 减小倾斜角度避免位置偏移
                            duration: 300,
                            ease: 'Power2.easeOut',
                            onComplete: () => {
                                // 第三阶段：倒果汁（保持倾斜状态）
                                this.time.delayedCall(400, () => {
                                    // 执行果汁转移逻辑
                                    this.transferJuice(fromIndex, toIndex);

                                    // 第四阶段：杯子回正
                                    this.tweens.add({
                                        targets: [fromCup.graphics, fromCup.juiceGraphics],
                                        rotation: 0,
                                        duration: 300,
                                        ease: 'Power2.easeIn',
                                        onComplete: () => {
                                            // 第五阶段：杯子回到原位
                                            this.tweens.add({
                                                targets: [fromCup.graphics, fromCup.juiceGraphics],
                                                x: `-=${targetOffsetX}`,
                                                y: `-=${targetOffsetY}`,
                                                duration: 500,
                                                ease: 'Power2.easeIn',
                                                onComplete: () => {
                                                    console.log(`倒果汁动画完成：从杯子 ${fromIndex} 到杯子 ${toIndex}`);
                                                }
                                            });
                                        }
                                    });
                                });
                            }
                        });
                    }
                });

                // 延迟创建果汁流动效果（在倾斜后）
                this.time.delayedCall(800, () => {
                    this.createJuiceFlowEffect(fromCup, toCup);
                });
            }

            createJuiceFlowEffect(fromCup, toCup) {
                // 获取顶层果汁颜色
                const topLayer = fromCup.juiceLayers[fromCup.juiceLayers.length - 1];

                // 计算当前杯子位置（已经移动到目标上方并倾斜）
                // 源杯子当前位置 = 原位置 + 移动偏移
                const deltaX = toCup.x - fromCup.x;
                const deltaY = toCup.y - fromCup.y;
                const currentFromX = fromCup.x + deltaX;
                const currentFromY = fromCup.y + deltaY - fromCup.height - 40;

                // 创建果汁流动粒子效果（从倾斜的杯子口流出）
                const startX = currentFromX + fromCup.width * 0.8; // 杯子右侧边缘（倾斜后的出口）
                const startY = currentFromY + fromCup.height * 0.6; // 杯子中部偏下
                const endX = toCup.x + toCup.width / 2; // 目标杯子中心
                const endY = toCup.y + toCup.height * 0.2; // 目标杯子上部

                console.log(`果汁流动：从(${startX}, ${startY}) 到(${endX}, ${endY})`);

                // 创建多个果汁滴
                for (let i = 0; i < 8; i++) {
                    this.time.delayedCall(i * 80, () => {
                        const drop = this.add.circle(startX, startY, 4, topLayer.color);

                        // 添加重力效果的抛物线运动
                        this.tweens.add({
                            targets: drop,
                            x: endX + (Math.random() - 0.5) * 15,
                            y: endY,
                            scaleX: 0.3,
                            scaleY: 0.3,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2.easeIn',
                            onComplete: () => {
                                drop.destroy();
                            }
                        });

                        // 添加果汁滴的闪烁效果
                        this.tweens.add({
                            targets: drop,
                            alpha: 0.7,
                            duration: 100,
                            yoyo: true,
                            repeat: 1
                        });
                    });
                }

                // 添加果汁流的连续效果
                const stream = this.add.graphics();
                stream.fillStyle(topLayer.color, 0.6);
                stream.fillRect(startX - 2, startY, 4, endY - startY);

                // 流动效果动画
                this.tweens.add({
                    targets: stream,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2.easeOut',
                    onComplete: () => {
                        stream.destroy();
                    }
                });
            }

            transferJuice(fromIndex, toIndex) {
                const fromCup = this.juiceCups[fromIndex];
                const toCup = this.juiceCups[toIndex];

                // 移除源杯子的顶层果汁
                const topLayer = fromCup.juiceLayers.pop();
                fromCup.juiceLevel--;

                // 添加到目标杯子
                toCup.juiceLayers.push(topLayer);
                toCup.juiceLevel++;

                // 更新杯子状态
                if (fromCup.juiceLevel === 0) {
                    fromCup.fruitType = null;
                    fromCup.juiceColor = null;
                    fromCup.isMixed = false;
                } else {
                    // 检查剩余果汁是否还是混合的
                    const firstType = fromCup.juiceLayers[0].fruitType;
                    const allSameType = fromCup.juiceLayers.every(layer => layer.fruitType === firstType);
                    fromCup.isMixed = !allSameType;
                    if (allSameType) {
                        fromCup.fruitType = firstType;
                    }
                }

                // 更新目标杯子状态
                if (toCup.juiceLevel === 1) {
                    toCup.fruitType = topLayer.fruitType;
                    toCup.juiceColor = topLayer.color;
                    toCup.isMixed = false;
                } else {
                    const firstType = toCup.juiceLayers[0].fruitType;
                    const allSameType = toCup.juiceLayers.every(layer => layer.fruitType === firstType);
                    toCup.isMixed = !allSameType;
                    if (allSameType) {
                        toCup.fruitType = firstType;
                    } else {
                        toCup.fruitType = -1;
                    }
                }

                // 重新绘制两个杯子
                this.drawJuice(fromCup);
                this.drawJuice(toCup);

                // 检查目标杯子是否满了
                if (toCup.juiceLevel >= 3) {
                    this.time.delayedCall(500, () => {
                        this.checkCupCompletion(toIndex);
                    });
                }
            }

            clearPourTargets() {
                if (this.pourHighlights) {
                    this.pourHighlights.forEach(obj => {
                        if (obj && obj.destroy) {
                            obj.destroy();
                        }
                    });
                    this.pourHighlights = [];
                }
            }

            findAvailableCup(fruitType) {
                console.log(`寻找果汁杯，水果类型: ${fruitType}`);

                // 优先级1：寻找同色果汁且未满的杯子
                for (let i = 0; i < this.juiceCups.length; i++) {
                    const cup = this.juiceCups[i];
                    console.log(`杯子 ${i}: 类型=${cup.fruitType}, 等级=${cup.juiceLevel}`);
                    if (cup.fruitType === fruitType && cup.juiceLevel < 3 && cup.juiceLevel > 0) {
                        console.log(`找到同色未满杯子: ${i}`);
                        return i;
                    }
                }

                // 优先级2：寻找空杯子
                for (let i = 0; i < this.juiceCups.length; i++) {
                    const cup = this.juiceCups[i];
                    if (cup.juiceLevel === 0) {
                        console.log(`找到空杯子: ${i}`);
                        return i;
                    }
                }

                // 优先级3：寻找任何未满的杯子（混合果汁）
                for (let i = 0; i < this.juiceCups.length; i++) {
                    const cup = this.juiceCups[i];
                    if (cup.juiceLevel < 3) {
                        console.log(`找到未满杯子（混合）: ${i}`);
                        return i;
                    }
                }

                console.log('没有找到可用杯子');
                return -1; // 没有可用杯子
            }

            update() {
                if (this.gameOver) return;

                // 过滤无效水果
                this.fruits = this.fruits.filter(fruit => fruit && fruit.active);

                // 同步emoji文本位置
                this.fruits.forEach(fruit => {
                    if (fruit && fruit.emojiText && fruit.body && fruit.body.position) {
                        fruit.emojiText.x = fruit.body.position.x;
                        fruit.emojiText.y = fruit.body.position.y;
                    }
                });

                // 检查游戏结束
                this.checkGameOver();
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'gameCanvas',
            backgroundColor: '#2c3e50',
            physics: {
                default: 'matter',
                matter: {
                    debug: false
                }
            },
            scene: GameScene,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            input: {
                touch: {
                    capture: true
                },
                activePointers: 1
            }
        };

        function startGame() {
            console.log('开始初始化游戏...');
            
            if (typeof Phaser === 'undefined') {
                console.error('Phaser没有加载成功');
                document.getElementById('gameCanvas').innerHTML = '<div class="loading">游戏加载失败，请刷新重试</div>';
                return;
            }
            
            console.log('Phaser版本:', Phaser.VERSION);
            
            document.getElementById('gameCanvas').innerHTML = '';
            game = new Phaser.Game(config);
            console.log('游戏已创建');
        }

        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', startGame);
        } else {
            startGame();
        }

        window.addEventListener('load', () => {
            if (!game) {
                startGame();
            }
        });

    </script>
</body>
</html>
