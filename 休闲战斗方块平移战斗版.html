<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗方块平移战斗版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'waiting'; // 默认等待状态，不是playing
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;
    let isCreatingWave = false; // 防止重复创建波次的标志位
    let gameStarted = false; // 游戏是否已开始
    let allGameObjects = []; // 存储所有游戏对象，用于整体移动

    // 移除战术棋盘相关变量

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600, // 从2000减少到800，攻击更快
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        input: {
            dragDistanceThreshold: 16
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 6x6格子相关变量
    let gridCells = [];
    let gridStartX = 200; // 格子起始X位置
    let gridStartY = 300; // 格子起始Y位置（向上移动300px）
    let cellSize = 75; // 每个格子的大小
    let battleButton; // 开始战斗按钮
    let exitBattleButton; // 退出战斗按钮

    // 地图构建相关变量
    let mapBlocks = []; // 存储地图上的方块 [row][col] = blockType
    let selectedBlockType = null; // 当前选中的方块类型
    let blockPalette = []; // 方块选择面板
    let isMapBuilding = true; // 是否处于地图构建模式
    let draggedBlock = null; // 当前拖拽的方块
    let previewBlocks = []; // 投影预览方块
    let isPlayerMoving = false; // 主角是否正在移动

    // 金币系统
    let playerGold = 200; // 初始金币
    let goldText; // 金币显示文本
    let refreshButton; // 刷新按钮

    // 步数限制系统
    let maxSteps = 10; // 第一关最大步数
    let currentSteps = 0; // 当前已使用步数
    let stepsText; // 步数显示文本

    // 俄罗斯方块组合定义
    const blockTypes = {
        I: {
            color: 0x00FFFF,
            name: 'I形',
            shape: [[1], [1], [1], [1]] // 竖直4格
        },
        O: {
            color: 0xFFFF00,
            name: 'O形',
            shape: [[1, 1], [1, 1]] // 2x2方块
        },
        T: {
            color: 0x800080,
            name: 'T形',
            shape: [[0, 1, 0], [1, 1, 1]] // T形状
        },
        L: {
            color: 0xFF8000,
            name: 'L形',
            shape: [[1, 0], [1, 0], [1, 1]] // L形状
        },
        S: {
            color: 0x00FF00,
            name: 'S形',
            shape: [[0, 1, 1], [1, 1, 0]] // S形状
        },
        single: {
            color: 0xFF0000,
            name: '单格',
            shape: [[1]] // 单个格子
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/Image_fx (14).jpg');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/knife4.png');

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 清空游戏对象数组
        allGameObjects = [];

        // 启用拖放系统
        const scene = this; // 保存scene引用

        // 移除全局drag事件监听器，改为单独设置

        // 移除所有全局拖拽事件监听器，改为单独设置

        // 添加主角滑动移动控制
        setupPlayerMovement.call(this);

        // 添加背景图片
        const background = this.add.image(375, 665, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小
        allGameObjects.push(background);

        // 创建主角 - 初始隐藏，战斗时显示
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6);
        player.setOrigin(0.5, 1);
        player.setDepth(100 + player.y * 0.1);
        player.setVisible(false); // 初始隐藏
        allGameObjects.push(player);

        // 创建武器 - 初始隐藏，战斗时显示
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        playerWeapon.setVisible(false); // 初始隐藏
        allGameObjects.push(playerWeapon);

        // 不立即创建怪物，等待开始战斗
        // createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建6x6格子
        create6x6Grid.call(this);

        // 初始化地图方块数组
        initializeMapBlocks.call(this);

        // 创建方块选择面板
        createBlockPalette.call(this);

        // 创建地图构建提示
        createMapBuildingHint.call(this);

        // 创建开始战斗按钮
        createBattleButton.call(this);

        // 创建退出战斗按钮（初始隐藏）
        createExitBattleButton.call(this);

        // 创建金币显示和刷新按钮
        createGoldUI.call(this);
    }

    // 创建波次怪物
    function createWave() {
        // 确保标志位已设置，防止并发创建
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 清除现有血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80; // 怪物间距
        const startX = 450; // 起始X位置
        const startY = player.y; // 与角色底部对齐

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部
            // 根据Y轴位置设置深度，Y轴越大（越靠下）深度越大，显示在前面
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 25 + currentLevel * 3; // 调整为两次攻击可击败
            monster.maxHealth = monster.health;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
            allGameObjects.push(monster); // 添加到游戏对象数组
        }

        monstersKilled = 0;

        // 延迟初始化血条，确保怪物完全渲染后再创建血条
        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            // 血条创建完成后重置标志位
            isCreatingWave = false;
        }, 50);
    }



    // 创建6x6格子
    function create6x6Grid() {
        // 清空之前的格子
        gridCells = [];

        for (let row = 0; row < 6; row++) {
            gridCells[row] = [];
            for (let col = 0; col < 6; col++) {
                const x = gridStartX + col * cellSize;
                const y = gridStartY + row * cellSize;

                // 创建格子背景
                const cell = this.add.rectangle(x, y, cellSize - 2, cellSize - 2, 0x34495e);
                cell.setStrokeStyle(2, 0x2c3e50);
                allGameObjects.push(cell);

                gridCells[row][col] = {
                    graphic: cell,
                    row: row,
                    col: col,
                    block: null, // 存储放置的方块
                    preview: null // 存储投影预览
                };
            }
        }
    }

    // 初始化地图方块数组
    function initializeMapBlocks() {
        mapBlocks = [];
        for (let row = 0; row < 6; row++) {
            mapBlocks[row] = [];
            for (let col = 0; col < 6; col++) {
                mapBlocks[row][col] = null;
            }
        }
    }

    // 创建方块选择面板
    function createBlockPalette() {
        blockPalette = [];
        const paletteY = gridStartY + 6 * cellSize + 200; // 在按钮下方

        // 随机选择3个方块组合
        const blockTypeKeys = Object.keys(blockTypes);
        const selectedTypes = [];
        while (selectedTypes.length < 3) {
            const randomType = blockTypeKeys[Math.floor(Math.random() * blockTypeKeys.length)];
            if (!selectedTypes.includes(randomType)) {
                selectedTypes.push(randomType);
            }
        }

        // 计算3个方块的居中分布
        const screenWidth = 750;
        const blockWidth = 100; // 估算每个方块组合的宽度
        const totalBlocksWidth = 3 * blockWidth;
        const availableSpace = screenWidth - totalBlocksWidth;
        const spacing = availableSpace / 4; // 4个间隔（左边1个，中间2个，右边1个）
        const startX = spacing + blockWidth / 2; // 第一个方块的中心位置

        selectedTypes.forEach((blockType, index) => {
            const x = startX + index * (blockWidth + spacing);
            const block = blockTypes[blockType];
            const shape = block.shape;

            // 计算组合大小
            const containerWidth = shape[0].length * cellSize;
            const containerHeight = shape.length * cellSize;

            // 使用最简单的方法：创建一个文本对象作为拖拽目标
            // 使用 Zone 创建一个不可见的拖拽句柄，这是Phaser推荐的做法
            const dragTarget = this.add.zone(x, paletteY, containerWidth + 20, containerHeight + 20);
            
            // 将 Zone 设置为可交互的，它会自动使用自身的尺寸作为点击区域
            dragTarget.setInteractive();
            this.input.setDraggable(dragTarget);

            // 附加自定义属性
            dragTarget.blockType = blockType;
            dragTarget.paletteIndex = index; // 添加唯一标识

            allGameObjects.push(dragTarget);

            // *** FIX START: Store visual elements directly on the dragTarget object ***
            dragTarget.visuals = []; // 为dragTarget创建一个属性来存储其所有的视觉元素

            // 创建方块组合的视觉元素（不可交互）
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const blockX = x + (col - shape[0].length / 2 + 0.5) * cellSize;
                        const blockY = paletteY + (row - shape.length / 2 + 0.5) * cellSize;

                        const miniBlock = this.add.rectangle(blockX, blockY, cellSize - 4, cellSize - 4, block.color);
                        miniBlock.setStrokeStyle(2, 0x000000);
                        allGameObjects.push(miniBlock);

                        // 设置偏移量用于跟随
                        miniBlock.offsetX = blockX - x;
                        miniBlock.offsetY = blockY - paletteY;
                        dragTarget.visuals.push(miniBlock); // 添加到visuals数组
                    }
                }
            }

            // 创建方块名称
            const blockName = this.add.text(x, paletteY + containerHeight/2 + 40, block.name, {
                fontSize: '14px',
                fill: '#ffffff',
                fontFamily: 'Arial'
            }).setOrigin(0.5);
            allGameObjects.push(blockName);
            blockName.offsetX = 0;
            blockName.offsetY = containerHeight/2 + 40;
            dragTarget.visuals.push(blockName); // 添加到visuals数组
            // *** FIX END ***

            // 拖动结束后回到原位
            dragTarget.originalX = x;
            dragTarget.originalY = paletteY;

            // 为每个拖拽目标单独设置事件
            const currentScene = this; // 保存scene引用

            // *** FIX START: Update event handlers to use this.visuals ***
            dragTarget.on('drag', function(pointer, dragX, dragY) {
                this.x = dragX;
                this.y = dragY;

                // 让所有视觉元素跟随移动
                this.visuals.forEach(visual => {
                    if (visual) {
                        visual.x = dragX + visual.offsetX;
                        visual.y = dragY + visual.offsetY;
                    }
                });
                
                // 检查并显示投影预览
                if (this.blockType && isMapBuilding) {
                    checkPlacementPreview.call(currentScene, dragX, dragY, this.blockType);
                }
            });

            dragTarget.on('dragstart', function() {
                this.setAlpha(0.7);
            });

            dragTarget.on('dragend', function() {
                this.setAlpha(1);
                clearPlacementPreview.call(currentScene);

                // 尝试放置方块
                const success = tryPlaceBlockAtPosition.call(currentScene, this.x, this.y, this.blockType);
                if (success) {
                    // 成功放置后隐藏所有相关对象
                    this.setVisible(false);
                    this.disableInteractive(); // 修复：使用正确的方法禁用交互

                    this.visuals.forEach(visual => {
                        if (visual) visual.setVisible(false);
                    });

                    console.log('方块放置成功，方块组合已移除');
                } else {
                    // 放置失败，回到原位
                    currentScene.tweens.add({
                        targets: this,
                        x: this.originalX,
                        y: this.originalY,
                        duration: 300,
                        ease: 'Back.easeOut',
                        onUpdate: () => {
                            // 让所有视觉元素跟随回到原位
                            this.visuals.forEach(visual => {
                                if (visual) {
                                    visual.x = this.x + visual.offsetX;
                                    visual.y = this.y + visual.offsetY;
                                }
                            });
                        }
                    });
                }
            });
            // *** FIX END ***
            
            // Push only the container to the palette
            blockPalette.push({
                type: blockType,
                container: dragTarget
            });
        });
    }



    // 清除单个格子
    function clearGridCell(row, col) {
        if (gridCells[row] && gridCells[row][col] && gridCells[row][col].block) {
            const block = gridCells[row][col].block;

            // 安全销毁方块对象
            try {
                if (block.graphic && block.graphic.destroy) {
                    block.graphic.destroy();
                }
                if (block.icon && block.icon.destroy) {
                    block.icon.destroy();
                }
                // 如果block本身有destroy方法
                if (block.destroy && typeof block.destroy === 'function') {
                    block.destroy();
                }
            } catch (error) {
                console.error('Error destroying block:', error);
            }

            gridCells[row][col].block = null;
            mapBlocks[row][col] = null;
        }
    }

    // 清除所有格子方块，恢复到初始状态
    function clearAllGridBlocks() {
        for (let row = 0; row < 6; row++) {
            for (let col = 0; col < 6; col++) {
                clearGridCell.call(this, row, col);
            }
        }

        // 重新初始化地图方块数组
        initializeMapBlocks.call(this);

        console.log('所有格子已清除，恢复到初始状态');
    }

    // 放置方块组合
    function placeBlockShape(centerRow, centerCol, blockType) {
        const block = blockTypes[blockType];
        const shape = block.shape;

        // 计算放置位置（以拖放点为中心）
        const shapeHeight = shape.length;
        const shapeWidth = shape[0].length;
        const startRow = centerRow - Math.floor(shapeHeight / 2);
        const startCol = centerCol - Math.floor(shapeWidth / 2);

        // 检查是否可以放置（边界检查）
        for (let row = 0; row < shapeHeight; row++) {
            for (let col = 0; col < shapeWidth; col++) {
                if (shape[row][col] === 1) {
                    const targetRow = startRow + row;
                    const targetCol = startCol + col;

                    if (targetRow < 0 || targetRow >= 6 || targetCol < 0 || targetCol >= 6) {
                        // 超出边界，无法放置
                        return false;
                    }
                }
            }
        }

        // 不再自动清除重叠区域，如果有重叠则不能放置

        // 放置新的方块组合
        for (let row = 0; row < shapeHeight; row++) {
            for (let col = 0; col < shapeWidth; col++) {
                if (shape[row][col] === 1) {
                    const targetRow = startRow + row;
                    const targetCol = startCol + col;
                    const x = gridStartX + targetCol * cellSize;
                    const y = gridStartY + targetRow * cellSize;

                    // 创建方块（不可交互，防止二次拖动）
                    const blockGraphic = this.add.rectangle(x, y, cellSize - 4, cellSize - 4, block.color);
                    blockGraphic.setStrokeStyle(2, 0x000000);
                    blockGraphic.disableInteractive(); // 确保不可交互
                    allGameObjects.push(blockGraphic);

                    gridCells[targetRow][targetCol].block = {
                        graphic: blockGraphic,
                        type: blockType
                    };
                    mapBlocks[targetRow][targetCol] = blockType;
                }
            }
        }

        return true; // 成功放置
    }

    // 重新生成方块选择面板
    function regenerateBlockPalette() {
        // *** FIX START: Update cleanup logic to use container.visuals ***
        blockPalette.forEach(item => {
            if (item.container) {
                // 销毁所有关联的视觉元素
                if (item.container.visuals) {
                    item.container.visuals.forEach(visual => {
                        if (visual && visual.destroy) {
                            visual.destroy();
                        }
                    });
                }
                // 销毁容器 (dragTarget)
                item.container.destroy();
            }
        });
        // *** FIX END ***

        // 清理allGameObjects数组中的无效引用 (This can be improved, but is out of scope of the fix)
        allGameObjects = allGameObjects.filter(obj => obj && obj.active);

        // 清空数组
        blockPalette = [];

        // 重新创建方块面板
        createBlockPalette.call(this);
    }

    // 检查并显示投影预览
    function checkPlacementPreview(dragX, dragY, blockType) {
        // 优化：减少频繁的清除和重建，只在位置真正改变时才更新
        const gridPos = screenToGrid(dragX, dragY);

        // 检查是否与上次位置相同
        if (this.lastPreviewPos &&
            this.lastPreviewPos.row === gridPos.row &&
            this.lastPreviewPos.col === gridPos.col &&
            this.lastPreviewType === blockType) {
            return; // 位置没变，不需要重新创建预览
        }

        clearPlacementPreview.call(this);

        if (!blockType || blockType === 'clear') return;

        // 记录当前预览位置和类型
        this.lastPreviewPos = { row: gridPos.row, col: gridPos.col };
        this.lastPreviewType = blockType;

        if (canPlaceBlockAt(blockType, gridPos.row, gridPos.col)) {
            showPlacementPreview.call(this, blockType, gridPos.row, gridPos.col);
        }
    }

    // 清除投影预览
    function clearPlacementPreview() {
        previewBlocks.forEach(preview => {
            if (preview && preview.destroy) {
                preview.destroy();
            }
        });
        previewBlocks = [];

        // 清除格子中的预览引用
        for (let row = 0; row < 6; row++) {
            for (let col = 0; col < 6; col++) {
                if (gridCells[row] && gridCells[row][col]) {
                    gridCells[row][col].preview = null;
                }
            }
        }

        // 重置预览位置缓存
        this.lastPreviewPos = null;
        this.lastPreviewType = null;
    }

    // 显示投影预览
    function showPlacementPreview(blockType, centerRow, centerCol) {
        const block = blockTypes[blockType];
        const shape = block.shape;
        const shapeHeight = shape.length;
        const shapeWidth = shape[0].length;
        const startRow = centerRow - Math.floor(shapeHeight / 2);
        const startCol = centerCol - Math.floor(shapeWidth / 2);

        for (let row = 0; row < shapeHeight; row++) {
            for (let col = 0; col < shapeWidth; col++) {
                if (shape[row][col] === 1) {
                    const targetRow = startRow + row;
                    const targetCol = startCol + col;

                    if (targetRow >= 0 && targetRow < 6 && targetCol >= 0 && targetCol < 6) {
                        const x = gridStartX + targetCol * cellSize;
                        const y = gridStartY + targetRow * cellSize;

                        // 创建半透明的预览方块
                        const preview = this.add.rectangle(x, y, cellSize - 4, cellSize - 4, block.color);
                        preview.setAlpha(0.6);
                        preview.setStrokeStyle(3, 0xffffff);

                        // 预览方块默认不可交互，不需要设置任何交互

                        // 添加脉动效果
                        this.tweens.add({
                            targets: preview,
                            alpha: 0.3,
                            duration: 500,
                            yoyo: true,
                            repeat: -1,
                            ease: 'Sine.easeInOut'
                        });

                        previewBlocks.push(preview);
                        gridCells[targetRow][targetCol].preview = preview;
                    }
                }
            }
        }
    }

    // 屏幕坐标转格子坐标
    function screenToGrid(screenX, screenY) {
        const col = Math.round((screenX - gridStartX) / cellSize);
        const row = Math.round((screenY - gridStartY) / cellSize);
        return {
            row: Math.max(0, Math.min(5, row)),
            col: Math.max(0, Math.min(5, col))
        };
    }

    // 检查是否可以在指定位置放置方块
    function canPlaceBlockAt(blockType, centerRow, centerCol) {
        if (blockType === 'clear') return true;

        const block = blockTypes[blockType];
        const shape = block.shape;
        const shapeHeight = shape.length;
        const shapeWidth = shape[0].length;
        const startRow = centerRow - Math.floor(shapeHeight / 2);
        const startCol = centerCol - Math.floor(shapeWidth / 2);

        for (let row = 0; row < shapeHeight; row++) {
            for (let col = 0; col < shapeWidth; col++) {
                if (shape[row][col] === 1) {
                    const targetRow = startRow + row;
                    const targetCol = startCol + col;

                    // 边界检查
                    if (targetRow < 0 || targetRow >= 6 || targetCol < 0 || targetCol >= 6) {
                        return false;
                    }

                    // 重叠检查 - 如果目标位置已有方块则不能放置
                    if (mapBlocks[targetRow] && mapBlocks[targetRow][targetCol] !== null) {
                        return false;
                    }
                }
            }
        }
        return true;
    }

    // 尝试在指定位置放置方块
    function tryPlaceBlockAtPosition(screenX, screenY, blockType) {
        if (!blockType) return false;

        const gridPos = screenToGrid(screenX, screenY);

        if (blockType === 'clear') {
            clearGridCell.call(this, gridPos.row, gridPos.col);
            return true;
        } else {
            return placeBlockShape.call(this, gridPos.row, gridPos.col, blockType);
        }
    }

    // 设置主角移动控制
    function setupPlayerMovement() {
        let startPointer = null;
        let isPlayerMoving = false;

        // 触摸/鼠标开始
        this.input.on('pointerdown', (pointer) => {
            if (gameState === 'playing' && !isMapBuilding && !isPlayerMoving) {
                startPointer = { x: pointer.x, y: pointer.y };
            }
        });

        // 触摸/鼠标结束 - 检测滑动方向
        this.input.on('pointerup', (pointer) => {
            if (gameState === 'playing' && !isMapBuilding && !isPlayerMoving && startPointer) {
                const deltaX = pointer.x - startPointer.x;
                const deltaY = pointer.y - startPointer.y;
                const minSwipeDistance = 30; // 最小滑动距离

                // 判断滑动方向
                if (Math.abs(deltaX) > minSwipeDistance || Math.abs(deltaY) > minSwipeDistance) {
                    let direction = '';

                    if (Math.abs(deltaX) > Math.abs(deltaY)) {
                        // 水平滑动
                        direction = deltaX > 0 ? 'right' : 'left';
                    } else {
                        // 垂直滑动
                        direction = deltaY > 0 ? 'down' : 'up';
                    }

                    movePlayer.call(this, direction);
                }

                startPointer = null;
            }
        });
    }

    // 移动主角
    function movePlayer(direction) {
        if (!player || isPlayerMoving) return;

        const currentPos = getPlayerGridPosition();
        let targetRow = currentPos.row;
        let targetCol = currentPos.col;

        // 根据方向计算移动路径
        const deltaMap = {
            'up': { row: -1, col: 0 },
            'down': { row: 1, col: 0 },
            'left': { row: 0, col: -1 },
            'right': { row: 0, col: 1 }
        };

        const delta = deltaMap[direction];
        if (!delta) return;

        // 找到最远可移动的位置，收集移动路径上的怪物
        let finalRow = currentPos.row;
        let finalCol = currentPos.col;
        let hitMonsters = []; // 收集移动路径上的所有怪物

        for (let step = 1; step <= 6; step++) {
            const checkRow = currentPos.row + delta.row * step;
            const checkCol = currentPos.col + delta.col * step;

            // 边界检查
            if (checkRow < 0 || checkRow >= 6 || checkCol < 0 || checkCol >= 6) {
                break;
            }

            // 检查是否有方块阻挡
            if (mapBlocks[checkRow] && mapBlocks[checkRow][checkCol] !== null) {
                break;
            }

            // 更新最终位置（无论是否有怪物都继续移动）
            finalRow = checkRow;
            finalCol = checkCol;
        }

        // 移动完成后，检查整个移动路径上的怪物
        for (let step = 1; step <= Math.abs(finalRow - currentPos.row) + Math.abs(finalCol - currentPos.col); step++) {
            const pathRow = currentPos.row + delta.row * step;
            const pathCol = currentPos.col + delta.col * step;

            if (pathRow >= 0 && pathRow < 6 && pathCol >= 0 && pathCol < 6) {
                const monster = monsters.find(m => m.gridRow === pathRow && m.gridCol === pathCol);
                if (monster && !hitMonsters.includes(monster)) {
                    hitMonsters.push(monster);
                }
            }
        }

        // 如果有移动距离或者遇到怪物，执行移动
        if (finalRow !== currentPos.row || finalCol !== currentPos.col || hitMonsters.length > 0) {
            // 增加步数计数
            currentSteps++;
            updateStepsDisplay.call(this);

            // 检查是否超过步数限制
            if (currentSteps >= maxSteps) {
                // 延迟检查失败，让这次移动完成
                this.time.delayedCall(500, () => {
                    showGameFailure.call(this);
                });
            }

            isPlayerMoving = true;

            const finalX = gridStartX + finalCol * cellSize;
            const finalY = gridStartY + finalRow * cellSize;

            // 添加移动方向指示
            const moveIndicator = this.add.text(player.x, player.y - 60, `→${direction.toUpperCase()}`, {
                fontSize: '16px',
                fill: '#00ff00',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 移动指示消失动画
            this.tweens.add({
                targets: moveIndicator,
                alpha: 0,
                y: moveIndicator.y - 20,
                duration: 500,
                onComplete: () => moveIndicator.destroy()
            });

            this.tweens.add({
                targets: player,
                x: finalX,
                y: finalY,
                duration: 300,
                ease: 'Power2.easeOut',
                onUpdate: () => {
                    // 更新武器位置跟随主角
                    if (playerWeapon && player) {
                        playerWeapon.x = player.x + 20;
                        playerWeapon.y = player.y - 30;
                    }
                },
                onComplete: () => {
                    isPlayerMoving = false;

                    // 对路径上的所有怪物造成伤害
                    if (hitMonsters.length > 0) {
                        // 显示穿透攻击特效
                        const slashEffect = this.add.text(finalX, finalY - 40, '⚔️SLASH!', {
                            fontSize: '20px',
                            fill: '#ffff00',
                            fontFamily: 'Arial',
                            fontWeight: 'bold'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: slashEffect,
                            alpha: 0,
                            y: slashEffect.y - 30,
                            duration: 1000,
                            onComplete: () => slashEffect.destroy()
                        });

                        // 穿透攻击所有怪物
                        hitMonsters.forEach((monster, index) => {
                            if (monster && monsters.includes(monster) && monster.health > 0) {
                                // 保存位置用于特效
                                const monsterX = monster.x;
                                const monsterY = monster.y;

                                // 执行攻击
                                performSlideAttack.call(this, monster);

                                // 攻击特效
                                const hitEffect = this.add.text(monsterX, monsterY - 80, '💥', {
                                    fontSize: '24px'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: hitEffect,
                                    alpha: 0,
                                    y: hitEffect.y - 30,
                                    scale: 2,
                                    duration: 600,
                                    delay: index * 100,
                                    onComplete: () => hitEffect.destroy()
                                });
                            }
                        });
                    }
                }
            });
        }
    }

    // 执行滑动近战攻击
    function performSlideAttack(monster) {
        // 简化的安全检查
        if (!monster || !monsters.includes(monster) || monster.health <= 0) {
            return;
        }

        // 计算攻击伤害
        let damage = playerStats.attackDamage + currentLevel * 2;

        // 暴击判定
        const isCrit = Math.random() < playerStats.critChance;
        if (isCrit) {
            damage *= 2;
        }

        // 造成伤害
        monster.health -= damage;

        // 显示伤害飘字
        showDamageText.call(this, monster, damage);

        // 怪物受击效果
        monsterHitEffect.call(this, monster);

        // 主角攻击动画
        this.tweens.add({
            targets: player,
            scaleX: 0.7,
            scaleY: 0.7,
            duration: 100,
            yoyo: true,
            ease: 'Power2'
        });

        // 武器攻击动画
        if (playerWeapon) {
            this.tweens.add({
                targets: playerWeapon,
                rotation: 0.5,
                duration: 150,
                yoyo: true,
                ease: 'Power2'
            });
        }

        // 更新血条
        updateMonsterHealthBars.call(this);

        // 检查怪物是否死亡
        if (monster.health <= 0) {
            // 从数组中移除
            const index = monsters.indexOf(monster);
            if (index > -1) {
                monsters.splice(index, 1);
                monstersKilled++;

                // 获得金币
                playerGold += 50;
                updateGoldDisplay.call(this);

                // 显示金币获得提示
                showGoldGain.call(this, monster.x, monster.y, 50);
            }

            // 延迟销毁
            this.time.delayedCall(100, () => {
                if (monster && monster.destroy) {
                    monster.destroy();
                }
            });
        }
    }

    // 创建开始战斗按钮
    function createBattleButton() {
        // 按钮位置在6x6格子下方
        const buttonY = gridStartY + 6 * cellSize + 50;
        const buttonX = 375; // 屏幕中央

        // 按钮背景
        const buttonBg = this.add.rectangle(buttonX, buttonY, 200, 60, 0xe74c3c);
        buttonBg.setStrokeStyle(3, 0xc0392b);
        buttonBg.setInteractive();
        allGameObjects.push(buttonBg); // 添加到游戏对象数组

        // 按钮文字
        const buttonText = this.add.text(buttonX, buttonY, '开始战斗', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(buttonText); // 添加到游戏对象数组

        // 按钮点击事件
        buttonBg.on('pointerdown', () => {
            onBattleButtonClick.call(this);
        });

        // 按钮悬停效果
        buttonBg.on('pointerover', () => {
            buttonBg.setFillStyle(0xc0392b);
            buttonText.setScale(1.1);
        });

        buttonBg.on('pointerout', () => {
            buttonBg.setFillStyle(0xe74c3c);
            buttonText.setScale(1.0);
        });

        battleButton = {
            background: buttonBg,
            text: buttonText,
            setVisible: function(visible) {
                this.background.setVisible(visible);
                this.text.setVisible(visible);
            }
        };
    }



    // 开始战斗按钮点击事件
    function onBattleButtonClick() {
        if (gameStarted) return; // 防止重复点击

        gameStarted = true;
        gameState = 'playing'; // 设置游戏状态为进行中
        isMapBuilding = false; // 退出地图构建模式

        // 重置步数计数
        currentSteps = 0;
        updateStepsDisplay.call(this);

        // 隐藏方块选择面板和提示
        hideBlockPalette.call(this);
        if (this.mapBuildingHint) {
            this.mapBuildingHint.setVisible(false);
        }

        // 找到空的格子位置
        const emptyPositions = getEmptyGridPositions();

        if (emptyPositions.length < 2) {
            alert('地图上空格子太少，无法开始战斗！请至少保留2个空格子。');
            gameStarted = false;
            gameState = 'waiting';
            isMapBuilding = true;
            showBlockPalette.call(this);
            return;
        }

        // 将主角放置到第一个空格子
        const playerPos = emptyPositions[0];
        const playerX = gridStartX + playerPos.col * cellSize;
        const playerY = gridStartY + playerPos.row * cellSize;

        player.setPosition(playerX, playerY);
        player.setVisible(true); // 显示主角

        playerWeapon.setPosition(playerX + 20, playerY - 30);
        playerWeapon.setVisible(true); // 显示武器

        // 创建怪物，放置在其他空格子上
        createWaveOnGrid.call(this, emptyPositions.slice(1));

        // 显示战斗信息
        const battleInfo = this.add.text(375, 150, '战斗开始！滑动屏幕移动主角进行近战攻击！', {
            fontSize: '18px',
            fill: '#e74c3c',
            fontFamily: 'Arial',
            fontWeight: 'bold',
            wordWrap: { width: 600 }
        }).setOrigin(0.5);

        // 5秒后移除信息文字
        this.time.delayedCall(5000, () => {
            if (battleInfo) {
                battleInfo.destroy();
            }
        });

        console.log('开始战斗！');

        // 隐藏开始战斗按钮
        if (battleButton) {
            battleButton.setVisible(false);
        }

        // 显示退出战斗按钮
        if (exitBattleButton) {
            exitBattleButton.setVisible(true);
        }
    }

    // 创建退出战斗按钮
    function createExitBattleButton() {
        // 按钮位置在右上角
        const buttonX = 650;
        const buttonY = 100;

        // 创建一个不可见的交互区域
        const buttonArea = this.add.zone(buttonX, buttonY, 120, 40);
        buttonArea.setInteractive();
        buttonArea.setDepth(1000);

        // 创建按钮背景
        const buttonBg = this.add.graphics();
        buttonBg.fillStyle(0xe74c3c); // 红色背景
        buttonBg.lineStyle(3, 0x000000); // 黑色边框
        buttonBg.fillRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
        buttonBg.strokeRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
        buttonBg.setDepth(999);

        // 创建按钮文字
        const buttonText = this.add.text(buttonX, buttonY, '退出战斗', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        buttonText.setDepth(1001);

        // 创建按钮容器
        exitBattleButton = this.add.container(0, 0, [buttonArea, buttonBg, buttonText]);
        exitBattleButton.setVisible(false); // 初始隐藏
        allGameObjects.push(exitBattleButton);

        // 保存引用以便后续使用
        exitBattleButton.buttonBg = buttonBg;
        exitBattleButton.buttonArea = buttonArea;

        // 按钮点击事件
        buttonArea.on('pointerdown', () => {
            console.log('退出战斗按钮被点击');
            onExitBattleClick.call(this);
        });

        // 按钮悬停效果
        buttonArea.on('pointerover', () => {
            buttonBg.clear();
            buttonBg.fillStyle(0xc0392b); // 深红色
            buttonBg.lineStyle(3, 0x000000);
            buttonBg.fillRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
            buttonBg.strokeRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
        });

        buttonArea.on('pointerout', () => {
            buttonBg.clear();
            buttonBg.fillStyle(0xe74c3c); // 恢复原色
            buttonBg.lineStyle(3, 0x000000);
            buttonBg.fillRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
            buttonBg.strokeRoundedRect(buttonX - 60, buttonY - 20, 120, 40, 20);
        });
    }

    // 退出战斗按钮点击事件
    function onExitBattleClick() {
        // 重置游戏状态
        gameStarted = false;
        gameState = 'waiting';
        isMapBuilding = true;

        // 清除所有怪物
        monsters.forEach(monster => {
            if (monster && monster.destroy) {
                monster.destroy();
            }
        });
        monsters = [];

        // 清除怪物血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar && bar.destroy) {
                    bar.destroy();
                }
            });
            this.monsterHealthBars = [];
        }

        // 隐藏主角和武器
        if (player) {
            player.setVisible(false);
        }
        if (playerWeapon) {
            playerWeapon.setVisible(false);
        }

        // 清除格子区域所有方块，恢复到初始状态
        clearAllGridBlocks.call(this);

        // 重置步数
        currentSteps = 0;
        updateStepsDisplay.call(this);

        // 重新生成方块选择面板
        regenerateBlockPalette.call(this);

        // 显示开始战斗按钮
        if (battleButton) {
            battleButton.setVisible(true);
        }

        // 隐藏退出战斗按钮
        if (exitBattleButton) {
            exitBattleButton.setVisible(false);
        }

        // 显示方块选择面板
        showBlockPalette.call(this);
        if (this.mapBuildingHint) {
            this.mapBuildingHint.setVisible(true);
        }

        console.log('退出战斗，返回地图构建模式');
    }

    // 创建金币UI和刷新按钮
    function createGoldUI() {
        // 金币显示 - 左上角（玩家UI区域）
        const goldBg = this.add.graphics();
        goldBg.fillStyle(0xFFD700, 0.8); // 金色背景
        goldBg.lineStyle(2, 0x000000);
        goldBg.fillRoundedRect(100, 70, 150, 20, 10);
        goldBg.strokeRoundedRect(100, 70, 150, 20, 10);
        allGameObjects.push(goldBg);

        goldText = this.add.text(150, 70, `💰 ${playerGold}`, {
            fontSize: '18px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(goldText);

        // 步数显示 - 右上角
        const stepsBg = this.add.graphics();
        stepsBg.fillStyle(0xFF6B6B, 0.8); // 红色背景
        stepsBg.lineStyle(2, 0x000000);
        stepsBg.fillRoundedRect(300, 210, 150, 40, 10);
        stepsBg.strokeRoundedRect(300, 210, 150, 40, 10);
        allGameObjects.push(stepsBg);

        stepsText = this.add.text(375, 230, `步数: ${currentSteps}/${maxSteps}`, {
            fontSize: '16px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(stepsText);

        // 刷新按钮 - 最下方居中显示
        const refreshX = 375; // 屏幕中央
        const refreshY = 1280; // 最下方

        // 创建一个不可见的交互区域
        const refreshArea = this.add.zone(refreshX, refreshY, 100, 50);
        refreshArea.setInteractive();
        refreshArea.setDepth(1000);

        const refreshBg = this.add.graphics();
        refreshBg.fillStyle(0x3498db); // 蓝色背景
        refreshBg.lineStyle(3, 0x000000);
        refreshBg.fillRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
        refreshBg.strokeRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
        refreshBg.setDepth(999);

        const refreshText = this.add.text(refreshX, refreshY - 5, '🔄 刷新', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        refreshText.setDepth(1001);

        const refreshCost = this.add.text(refreshX, refreshY + 15, '100💰', {
            fontSize: '12px',
            fill: '#ffff00',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        refreshCost.setDepth(1001);

        refreshButton = this.add.container(0, 0, [refreshArea, refreshBg, refreshText, refreshCost]);
        allGameObjects.push(refreshButton);

        // 保存引用以便后续使用
        refreshButton.refreshBg = refreshBg;
        refreshButton.refreshArea = refreshArea;

        // 刷新按钮点击事件
        refreshArea.on('pointerdown', () => {
            console.log('刷新按钮被点击');
            onRefreshButtonClick.call(this);
        });

        // 悬停效果
        refreshArea.on('pointerover', () => {
            refreshBg.clear();
            refreshBg.fillStyle(0x2980b9); // 深蓝色
            refreshBg.lineStyle(3, 0x000000);
            refreshBg.fillRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
            refreshBg.strokeRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
        });

        refreshArea.on('pointerout', () => {
            refreshBg.clear();
            refreshBg.fillStyle(0x3498db); // 恢复原色
            refreshBg.lineStyle(3, 0x000000);
            refreshBg.fillRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
            refreshBg.strokeRoundedRect(refreshX - 50, refreshY - 25, 100, 50, 15);
        });
    }

    // 刷新按钮点击事件
    function onRefreshButtonClick() {
        if (!isMapBuilding) return; // 只在地图构建模式下可用

        if (playerGold >= 100) {
            // 扣除金币
            playerGold -= 100;
            updateGoldDisplay.call(this);

            // 刷新方块组合
            regenerateBlockPalette.call(this);

            console.log('刷新方块组合，消耗100金币');
        } else {
            // 金币不足提示
            const warningText = this.add.text(375, 200, '金币不足！需要100💰', {
                fontSize: '20px',
                fill: '#e74c3c',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: warningText,
                alpha: 0,
                y: warningText.y - 50,
                duration: 2000,
                onComplete: () => warningText.destroy()
            });
        }
    }

    // 更新金币显示
    function updateGoldDisplay() {
        if (goldText) {
            goldText.setText(`💰 ${playerGold}`);
        }
    }

    // 更新步数显示
    function updateStepsDisplay() {
        if (stepsText) {
            stepsText.setText(`步数: ${currentSteps}/${maxSteps}`);
            // 如果步数接近上限，改变颜色警告
            if (currentSteps >= maxSteps - 2) {
                stepsText.setFill('#ff0000'); // 红色警告
            } else if (currentSteps >= maxSteps - 4) {
                stepsText.setFill('#ff8800'); // 橙色警告
            }
        }
    }

    // 显示游戏失败界面
    function showGameFailure() {
        // 暂停游戏
        gameState = 'gameOver';

        // 创建失败界面背景
        const failureBg = this.add.graphics();
        failureBg.fillStyle(0x000000, 0.8);
        failureBg.fillRect(0, 0, 750, 1334);
        failureBg.setDepth(2000);

        // 失败标题
        const failureTitle = this.add.text(375, 500, '挑战失败！', {
            fontSize: '48px',
            fill: '#ff4444',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        failureTitle.setDepth(2001);

        // 失败原因
        const failureReason = this.add.text(375, 580, `步数用完了！(${currentSteps}/${maxSteps})`, {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        failureReason.setDepth(2001);

        // 重新开始按钮
        const restartBg = this.add.graphics();
        restartBg.fillStyle(0x27ae60);
        restartBg.lineStyle(3, 0x000000);
        restartBg.fillRoundedRect(275, 650, 200, 60, 15);
        restartBg.strokeRoundedRect(275, 650, 200, 60, 15);
        restartBg.setDepth(2001);
        restartBg.setInteractive();

        const restartText = this.add.text(375, 680, '重新开始', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        restartText.setDepth(2002);

        // 重新开始按钮点击事件
        restartBg.on('pointerdown', () => {
            restartGame.call(this);
        });

        // 悬停效果
        restartBg.on('pointerover', () => {
            restartBg.clear();
            restartBg.fillStyle(0x229954);
            restartBg.lineStyle(3, 0x000000);
            restartBg.fillRoundedRect(275, 650, 200, 60, 15);
            restartBg.strokeRoundedRect(275, 650, 200, 60, 15);
        });

        restartBg.on('pointerout', () => {
            restartBg.clear();
            restartBg.fillStyle(0x27ae60);
            restartBg.lineStyle(3, 0x000000);
            restartBg.fillRoundedRect(275, 650, 200, 60, 15);
            restartBg.strokeRoundedRect(275, 650, 200, 60, 15);
        });
    }

    // 重新开始游戏
    function restartGame() {
        // 重置游戏状态
        gameStarted = false;
        gameState = 'waiting';
        isMapBuilding = true;
        currentSteps = 0;

        // 清除所有怪物
        monsters.forEach(monster => {
            if (monster && monster.destroy) {
                monster.destroy();
            }
        });
        monsters = [];

        // 清除怪物血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar && bar.destroy) {
                    bar.destroy();
                }
            });
            this.monsterHealthBars = [];
        }

        // 隐藏主角和武器
        if (player) {
            player.setVisible(false);
        }
        if (playerWeapon) {
            playerWeapon.setVisible(false);
        }

        // 清除格子区域所有方块，恢复到初始状态
        clearAllGridBlocks.call(this);

        // 重新生成方块选择面板
        regenerateBlockPalette.call(this);

        // 显示开始战斗按钮
        if (battleButton) {
            battleButton.setVisible(true);
        }

        // 隐藏退出战斗按钮
        if (exitBattleButton) {
            exitBattleButton.setVisible(false);
        }

        // 显示方块选择面板
        showBlockPalette.call(this);
        if (this.mapBuildingHint) {
            this.mapBuildingHint.setVisible(true);
        }

        // 更新步数显示
        updateStepsDisplay.call(this);

        // 重新加载场景以清除失败界面
        this.scene.restart();
    }

    // 显示金币获得提示
    function showGoldGain(x, y, amount) {
        const goldGainText = this.add.text(x, y - 40, `+${amount}💰`, {
            fontSize: '16px',
            fill: '#FFD700',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: goldGainText,
            alpha: 0,
            y: goldGainText.y - 30,
            duration: 1500,
            onComplete: () => goldGainText.destroy()
        });
    }

    // 获取空的格子位置
    function getEmptyGridPositions() {
        const emptyPositions = [];
        for (let row = 0; row < 6; row++) {
            for (let col = 0; col < 6; col++) {
                if (mapBlocks[row][col] === null) {
                    emptyPositions.push({ row: row, col: col });
                }
            }
        }
        return emptyPositions;
    }

    // 隐藏方块选择面板
    function hideBlockPalette() {
        blockPalette.forEach(item => {
            item.container.setVisible(false);
            if (item.container.visuals) {
                 item.container.visuals.forEach(v => v.setVisible(false));
            }
        });
        if(refreshButton) refreshButton.setVisible(false);
    }

    // 显示方块选择面板
    function showBlockPalette() {
        blockPalette.forEach(item => {
            // Only show if it hasn't been placed successfully
            if (item.container.active) {
                item.container.setVisible(true);
                if (item.container.visuals) {
                    item.container.visuals.forEach(v => v.setVisible(true));
                }
            }
        });
        if (this.mapBuildingHint) {
            this.mapBuildingHint.setVisible(true);
        }
        if(refreshButton) refreshButton.setVisible(true);
    }

    // 在格子上创建怪物波次
    function createWaveOnGrid(availablePositions) {
        // 确保标志位已设置，防止并发创建
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 清除现有血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 随机打乱可用位置数组
        const shuffledPositions = [...availablePositions];
        for (let i = shuffledPositions.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffledPositions[i], shuffledPositions[j]] = [shuffledPositions[j], shuffledPositions[i]];
        }

        // 创建新怪物 - 随机放置在可用的空格子上
        const monstersToCreate = Math.min(totalMonstersInWave, shuffledPositions.length);

        for (let i = 0; i < monstersToCreate; i++) {
            const pos = shuffledPositions[i];
            const xPos = gridStartX + pos.col * cellSize;
            const yPos = gridStartY + pos.row * cellSize;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.4); // 增大尺寸，让怪物更好地填充格子
            monster.setOrigin(0.5, 0.5); // 设置中心对齐，让怪物完全在格子内
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 25 + currentLevel * 3; // 调整为两次攻击可击败
            monster.maxHealth = monster.health;
            monster.isRanged = false;
            monster.isMoving = false;
            monster.originalX = xPos;
            monster.originalY = yPos;
            monster.gridRow = pos.row; // 记录格子位置
            monster.gridCol = pos.col;
            monsters.push(monster);
            allGameObjects.push(monster);
        }

        monstersKilled = 0;

        // 延迟初始化血条，确保怪物完全渲染后再创建血条
        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            // 血条创建完成后重置标志位
            isCreatingWave = false;
        }, 50);
    }

    // 获取主角当前的格子位置
    function getPlayerGridPosition() {
        const col = Math.round((player.x - gridStartX) / cellSize);
        const row = Math.round((player.y - gridStartY) / cellSize);
        return { row: Math.max(0, Math.min(5, row)), col: Math.max(0, Math.min(5, col)) };
    }

    // 找到最接近目标的空格子
    function findNearestEmptyGrid(fromRow, fromCol, targetPos) {
        const emptyPositions = getEmptyGridPositions();
        if (emptyPositions.length === 0) return null;

        // 过滤掉当前怪物占用的格子
        const availablePositions = emptyPositions.filter(pos => {
            // 检查是否有其他怪物在这个位置
            const occupied = monsters.some(m => m.gridRow === pos.row && m.gridCol === pos.col);
            return !occupied;
        });

        if (availablePositions.length === 0) return null;

        // 找到距离目标最近的空格子
        let nearestPos = availablePositions[0];
        let minDistance = Math.abs(nearestPos.row - targetPos.row) + Math.abs(nearestPos.col - targetPos.col);

        availablePositions.forEach(pos => {
            const distance = Math.abs(pos.row - targetPos.row) + Math.abs(pos.col - targetPos.col);
            if (distance < minDistance) {
                minDistance = distance;
                nearestPos = pos;
            }
        });

        return nearestPos;
    }

    // 创建地图构建提示
    function createMapBuildingHint() {
        const hintY = gridStartY - 50;
        
        // 保存提示文本引用，以便后续隐藏
        // this.mapBuildingHint = hintText;
    }

    // 主角远程攻击系统 - 已禁用，现在使用近战攻击
    function playerAttack_disabled() {
        if (monsters.length > 0) {
            // 多重射击：攻击多个目标
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i];

                // 创建攻击特效 - 从武器最右边中间位置发射
                const weaponRightX = playerWeapon ? playerWeapon.x + 40 : player.x + 60; // 武器最右边
                const weaponCenterY = playerWeapon ? playerWeapon.y - 15 : player.y - 30; // 武器中间高度
                const projectile = this.add.circle(weaponRightX, weaponCenterY, 5, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 30, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 狂战士效果：生命越低攻击越高
                        if (playerStats.berserker) {
                            const healthPercent = playerHealth / maxPlayerHealth;
                            const berserkerBonus = (1 - healthPercent) * 2; // 最多200%加成
                            damage *= (1 + berserkerBonus);
                        }

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 冰冻效果
                        if (playerStats.freeze && Math.random() < playerStats.freeze) {
                            target.frozen = true;
                            target.frozenTime = 2000; // 冰冻2秒
                            target.setTint(0x87ceeb); // 浅蓝色

                            const freezeText = this.add.text(target.x, target.y - 100, 'FROZEN!', {
                                fontSize: '16px',
                                fill: '#87ceeb',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: freezeText,
                                alpha: 0,
                                y: freezeText.y - 20,
                                duration: 1000,
                                onComplete: () => freezeText.destroy()
                            });
                        }

                        // 毒性效果
                        if (playerStats.poison) {
                            if (!target.poisoned) {
                                target.poisoned = true;
                                target.poisonDamage = Math.floor(damage * 0.3);
                                target.poisonDuration = 5000; // 持续5秒
                                target.setTint(0x9acd32); // 绿色
                            }
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 爆炸攻击效果
                        if (playerStats.explosive) {
                            const explosionRadius = 80;
                            const explosionDamage = Math.floor(damage * 0.5);

                            // 爆炸特效
                            const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6b35, 0.3);
                            this.tweens.add({
                                targets: explosion,
                                scaleX: 1.5,
                                scaleY: 1.5,
                                alpha: 0,
                                duration: 300,
                                onComplete: () => explosion.destroy()
                            });

                            // 对范围内的其他怪物造成伤害
                            monsters.forEach(otherMonster => {
                                if (otherMonster !== target) {
                                    const distance = Phaser.Math.Distance.Between(
                                        target.x, target.y, otherMonster.x, otherMonster.y
                                    );
                                    if (distance <= explosionRadius) {
                                        otherMonster.health -= explosionDamage;

                                        // 爆炸伤害特效
                                        this.tweens.add({
                                            targets: otherMonster,
                                            scaleX: 0.3,
                                            scaleY: 0.3,
                                            duration: 100,
                                            yoyo: true
                                        });
                                    }
                                }
                            });
                        }

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 武器开火抖动 - 更明显的后坐力
            if (playerWeapon) {
                // 记录武器原始位置
                const originalWeaponX = playerWeapon.x;
                const originalWeaponY = playerWeapon.y;

                // 水平后坐力
                this.tweens.add({
                    targets: playerWeapon,
                    x: originalWeaponX - 12, // 向后抖动12像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            x: originalWeaponX,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });

                // 垂直抖动
                this.tweens.add({
                    targets: playerWeapon,
                    y: originalWeaponY - 6, // 向上抖动6像素，更明显
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        // 回到原位
                        this.tweens.add({
                            targets: playerWeapon,
                            y: originalWeaponY,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });
            }
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        if (gameState === 'playing') {
            // 更新战斗逻辑
            updateBattle.call(this, time);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角现在使用近战攻击，通过滑动移动触发

        // 怪物AI - 只处理状态效果，不再攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理毒性伤害
            if (monster.poisoned) {
                monster.poisonDuration -= delta;
                if (monster.poisonDuration <= 0) {
                    monster.poisoned = false;
                    monster.clearTint();
                } else {
                    // 每秒造成毒性伤害
                    if (!monster.lastPoisonDamage) monster.lastPoisonDamage = 0;
                    if (time - monster.lastPoisonDamage > 1000) {
                        monster.health -= monster.poisonDamage;
                        monster.lastPoisonDamage = time;

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 毒性伤害特效
                        const poisonText = this.add.text(monster.x, monster.y - 60, `-${monster.poisonDamage}`, {
                            fontSize: '14px',
                            fill: '#9acd32',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: poisonText,
                            alpha: 0,
                            y: poisonText.y - 20,
                            duration: 800,
                            onComplete: () => poisonText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            // 怪物不再攻击，只是静静地待在格子里
            // 可以添加一些简单的待机动画，比如轻微的呼吸效果
            if (!monster.idleAnimation) {
                monster.idleAnimation = this.tweens.add({
                    targets: monster,
                    scaleX: monster.scaleX * 1.05,
                    scaleY: monster.scaleY * 1.05,
                    duration: 2000,
                    yoyo: true,
                    repeat: -1,
                    ease: 'Sine.easeInOut'
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 24; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 80;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 80,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 80;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 24, // 血条左边缘位置
                            monster.y - 80,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 24; // 血条左边缘位置
                        bar.y = monster.y - 80;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true; // 设置标志位，防止重复触发

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次 - 随机分布到空格子
            setTimeout(() => {
                const emptyPositions = getEmptyGridPositions();
                if (emptyPositions.length > 0) {
                    createWaveOnGrid.call(this, emptyPositions);
                }
                isCreatingWave = false; // 创建完成后重置标志位
            }, 1000);
        }
    }

    // 移除棋子类型定义

    // 移除商店相关函数

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40);
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);
        allGameObjects.push(playerAvatarBg);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5);
        allGameObjects.push(playerAvatar);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });
        allGameObjects.push(playerLabel);

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7);
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);
        allGameObjects.push(playerHealthBarBg);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        allGameObjects.push(this.playerHealthBar);
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(this.playerHealthText);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40);
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);
        allGameObjects.push(enemyAvatarBg);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5);
        allGameObjects.push(enemyAvatar);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(enemyLabel);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7);
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);
        allGameObjects.push(healthBarBg);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        allGameObjects.push(this.enemyHealthBar);
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(this.enemyHealthText);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10);
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);
        allGameObjects.push(levelBg);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        allGameObjects.push(this.levelText);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        allGameObjects.push(this.waveText);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, Math.floor(playerHealth))}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;
        if(gameState !== 'playing') {
            this.enemyHealthBar.clear();
            if(this.enemyHealthText) this.enemyHealthText.setText('N/A');
            return;
        }

        this.enemyHealthBar.clear();
        
        const killedThisWave = monstersKilled;
        const progressPercent = Math.min(1, killedThisWave / totalMonstersInWave);
        const barWidth = 146; 
        const currentWidth = barWidth * progressPercent;

        let barColor = 0xe74c3c; 
        if (progressPercent > 0.3) barColor = 0xf39c12; 
        if (progressPercent > 0.6) barColor = 0x27ae60; 

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); 

        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedThisWave}/${totalMonstersInWave}`);
        }
    }

    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 检查怪物是否还存在
        if (!monster || monster.x === undefined || !monsters.includes(monster)) return;

        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 检查怪物是否还存在
        if (!monster || !monster.x || !monster.y) return;

        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        // The rest of the unused functions from the original file are omitted for clarity
        // as they are not part of the active game logic.
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>