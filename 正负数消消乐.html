<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正负数消消乐</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            /*padding: 20px;*/
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .game-title {
            color: white;
            font-size: 2.5em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 20px;
        }
        .game-info {
            color: white;
            /*font-size: 1.2em;*/
            text-align: center;
            margin-bottom: 15px;
            background: rgba(255,255,255,0.1);
            /*padding: 15px;*/
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        #game-container {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <h1 class="game-title">正负数消消乐 🎮</h1>
    <div class="game-info">
        <p>🎯 游戏规则：点击相邻的正负数对（如 +3 和 -3）来消除它们！</p>
        <p>💡 学习目标：理解正数和负数是相反的概念</p>
    </div>
    <div id="game-container"></div>

    <script>
        class NumberMatchGame extends Phaser.Scene {
            constructor() {
                super({ key: 'NumberMatchGame' });
                this.gridSize = 8;
                this.tileSize = 70; // 增大方块尺寸
                this.grid = [];
                this.selectedTile = null;
                this.score = 0;
                this.moves = 0;
                this.numbers = [1, 2, 3, 4, 5, 6];
                this.totalTiles = 64; // 8x8 = 64个方块
                this.tilesRemaining = 64;
                this.gameStartTime = 0;
                this.gameWon = false;
            }

            preload() {
                // 创建数字方块纹理
                this.createNumberTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(450, 375, 900, 750, 0x2c3e50);
                
                // 创建游戏标题区域
             
                // 创建信息面板背景
                this.add.rectangle(450, 60, 880, 60, 0x34495e);

                // 分数和移动次数显示 - 重新排列
                this.scoreText = this.add.text(80, 60, '分数: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                this.movesText = this.add.text(250, 60, '移动: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tilesText = this.add.text(420, 60, '剩余: 64', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.timeText = this.add.text(590, 60, '时间: 0:00', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 刷新按钮
                const refreshBtn = this.add.rectangle(770, 60, 140, 45, 0xe74c3c);
                refreshBtn.setStrokeStyle(3, 0xc0392b);
                this.add.text(770, 60, '刷新', {
                    fontSize: '18px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                refreshBtn.setInteractive();
                refreshBtn.on('pointerdown', () => this.shuffleGrid());

                // 初始化游戏网格
                this.initializeGrid();
                this.createGrid();
                
                // 确保有可消除的对
                this.ensureValidMoves();
                
                // 开始计时
                this.gameStartTime = this.time.now;
                this.timeEvent = this.time.addEvent({
                    delay: 1000,
                    callback: this.updateTime,
                    callbackScope: this,
                    loop: true
                });
            }

            createNumberTextures() {
                const graphics = this.add.graphics();
                
                // 创建正数纹理（蓝色系）
                this.numbers.forEach(num => {
                    graphics.clear();
                    graphics.fillStyle(0x3498db);
                    graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    graphics.lineStyle(4, 0x2980b9);
                    graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    
                    // 添加渐变效果
                    graphics.fillGradientStyle(0x5dade2, 0x3498db, 0x2980b9, 0x1f3a93, 1);
                    graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                    
                    graphics.generateTexture(`positive_${num}`, this.tileSize - 6, this.tileSize - 6);
                });

                // 创建负数纹理（红色系）
                this.numbers.forEach(num => {
                    graphics.clear();
                    graphics.fillStyle(0xe74c3c);
                    graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    graphics.lineStyle(4, 0xc0392b);
                    graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    
                    // 添加渐变效果
                    graphics.fillGradientStyle(0xff6b6b, 0xe74c3c, 0xc0392b, 0xa93226, 1);
                    graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                    
                    graphics.generateTexture(`negative_${num}`, this.tileSize - 6, this.tileSize - 6);
                });

                // 创建选中效果纹理
                graphics.clear();
                graphics.fillStyle(0xf1c40f);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(6, 0xf39c12);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.generateTexture('selected', this.tileSize - 6, this.tileSize - 6);

                graphics.destroy();
            }

            initializeGrid() {
                this.grid = [];
                // 创建成对的正负数，确保都能被消除
                const pairs = [];
                
                // 生成32对正负数（64个方块）
                for (let i = 0; i < 32; i++) {
                    const number = this.numbers[i % this.numbers.length];
                    pairs.push({ number: number, isPositive: true });
                    pairs.push({ number: number, isPositive: false });
                }
                
                // 打乱数组
                Phaser.Utils.Array.Shuffle(pairs);
                
                // 填充网格
                let pairIndex = 0;
                for (let row = 0; row < this.gridSize; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.gridSize; col++) {
                        this.grid[row][col] = {
                            number: pairs[pairIndex].number,
                            isPositive: pairs[pairIndex].isPositive,
                            sprite: null,
                            isEmpty: false
                        };
                        pairIndex++;
                    }
                }
            }

            createGrid() {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const tile = this.grid[row][col];
                        if (!tile.isEmpty) {
                            const x = startX + col * this.tileSize + this.tileSize / 2;
                            const y = startY + row * this.tileSize + this.tileSize / 2;

                            const textureName = tile.isPositive ? `positive_${tile.number}` : `negative_${tile.number}`;
                            const sprite = this.add.image(x, y, textureName);
                            
                            // 添加数字文本 - 优化字体和大小
                            const sign = tile.isPositive ? '+' : '-';
                            const text = this.add.text(x, y, `${sign}${tile.number}`, {
                                fontSize: '28px',
                                fill: '#ffffff',
                                fontStyle: 'bold',
                                fontFamily: 'Arial, sans-serif',
                                stroke: tile.isPositive ? '#2980b9' : '#c0392b',
                                strokeThickness: 2,
                                shadow: {
                                    offsetX: 1,
                                    offsetY: 1,
                                    color: '#000000',
                                    blur: 2,
                                    fill: true
                                }
                            }).setOrigin(0.5);

                            sprite.setInteractive();
                            sprite.on('pointerdown', () => this.selectTile(row, col));
                            sprite.on('pointerover', () => {
                                if (!tile.isEmpty) {
                                    sprite.setScale(1.05);
                                    this.tweens.add({
                                        targets: sprite,
                                        scaleX: 1.05,
                                        scaleY: 1.05,
                                        duration: 100
                                    });
                                }
                            });
                            sprite.on('pointerout', () => {
                                if (!tile.isEmpty) {
                                    this.tweens.add({
                                        targets: sprite,
                                        scaleX: 1,
                                        scaleY: 1,
                                        duration: 100
                                    });
                                }
                            });
                            
                            tile.sprite = sprite;
                            tile.text = text;
                            tile.row = row;
                            tile.col = col;
                        }
                    }
                }
            }

            selectTile(row, col) {
                const tile = this.grid[row][col];
                
                if (tile.isEmpty) return;
                
                if (this.selectedTile === null) {
                    // 选择第一个方块
                    this.selectedTile = tile;
                    tile.sprite.setTint(0xffff00);
                } else if (this.selectedTile === tile) {
                    // 取消选择
                    tile.sprite.clearTint();
                    this.selectedTile = null;
                } else {
                    // 选择第二个方块，检查是否可以匹配
                    if (this.canMatch(this.selectedTile, tile)) {
                        this.matchTiles(this.selectedTile, tile);
                        this.moves++;
                        this.movesText.setText(`移动: ${this.moves}`);
                    } else {
                        // 显示错误提示
                        this.showMessage('只能消除相邻的正负数对！', 0xff0000);
                    }
                    
                    this.selectedTile.sprite.clearTint();
                    this.selectedTile = null;
                }
            }

            canMatch(tile1, tile2) {
                // 检查是否是相邻的正负数对
                const isAdjacent = this.isAdjacent(tile1, tile2);
                const isOpposite = tile1.number === tile2.number && tile1.isPositive !== tile2.isPositive;
                return isAdjacent && isOpposite;
            }

            isAdjacent(tile1, tile2) {
                const rowDiff = Math.abs(tile1.row - tile2.row);
                const colDiff = Math.abs(tile1.col - tile2.col);
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }

            matchTiles(tile1, tile2) {
                // 添加消除动画
                this.tweens.add({
                    targets: [tile1.sprite, tile1.text, tile2.sprite, tile2.text],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        tile1.sprite.destroy();
                        tile1.text.destroy();
                        tile2.sprite.destroy();
                        tile2.text.destroy();
                        
                        // 标记为空
                        tile1.isEmpty = true;
                        tile2.isEmpty = true;
                        tile1.sprite = null;
                        tile1.text = null;
                        tile2.sprite = null;
                        tile2.text = null;
                        
                        // 更新剩余数量
                        this.tilesRemaining -= 2;
                        this.tilesText.setText(`剩余: ${this.tilesRemaining}`);
                        
                        // 检查游戏是否结束
                        if (this.tilesRemaining === 0) {
                            this.gameWon = true;
                            this.showGameComplete();
                        } else {
                            // 确保仍有可消除的对
                            this.ensureValidMoves();
                        }
                    }
                });

                // 增加分数
                this.score += tile1.number * 10;
                this.scoreText.setText(`分数: ${this.score}`);

                // 显示成功消息
                this.showMessage(`消除 ${tile1.isPositive ? '+' : '-'}${tile1.number} 和 ${tile2.isPositive ? '+' : '-'}${tile2.number}！`, 0x00ff00);
            }

            ensureValidMoves() {
                // 检查是否还有可消除的相邻正负数对
                let hasValidMoves = false;
                
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            const tile = this.grid[row][col];
                            
                            // 检查右边
                            if (col < this.gridSize - 1 && !this.grid[row][col + 1].isEmpty) {
                                const rightTile = this.grid[row][col + 1];
                                if (tile.number === rightTile.number && tile.isPositive !== rightTile.isPositive) {
                                    hasValidMoves = true;
                                    break;
                                }
                            }
                            
                            // 检查下边
                            if (row < this.gridSize - 1 && !this.grid[row + 1][col].isEmpty) {
                                const downTile = this.grid[row + 1][col];
                                if (tile.number === downTile.number && tile.isPositive !== downTile.isPositive) {
                                    hasValidMoves = true;
                                    break;
                                }
                            }
                        }
                    }
                    if (hasValidMoves) break;
                }
                
                // 如果没有可消除的对，重新排列
                if (!hasValidMoves && this.tilesRemaining > 0) {
                    this.shuffleGrid();
                }
            }

            shuffleGrid() {
                // 收集所有非空方块的数据
                const tiles = [];
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            tiles.push({
                                number: this.grid[row][col].number,
                                isPositive: this.grid[row][col].isPositive
                            });
                            // 销毁现有的精灵
                            this.grid[row][col].sprite.destroy();
                            this.grid[row][col].text.destroy();
                            this.grid[row][col].isEmpty = true;
                        }
                    }
                }
                
                // 打乱方块数据
                Phaser.Utils.Array.Shuffle(tiles);
                
                // 重新分配到网格
                let tileIndex = 0;
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (tileIndex < tiles.length) {
                            this.grid[row][col].number = tiles[tileIndex].number;
                            this.grid[row][col].isPositive = tiles[tileIndex].isPositive;
                            this.grid[row][col].isEmpty = false;
                            tileIndex++;
                        }
                    }
                }
                
                // 重新创建精灵
                this.createGrid();
                this.showMessage('重新排列！', 0x00ffff);
            }

            updateTime() {
                if (!this.gameWon) {
                    const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = elapsed % 60;
                    this.timeText.setText(`时间: ${minutes}:${seconds.toString().padStart(2, '0')}`);
                }
            }

            showGameComplete() {
                const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                
                // 停止计时
                if (this.timeEvent) {
                    this.timeEvent.remove();
                }
                
                // 显示胜利消息
                const winMessage = this.add.text(450, 400, 
                    `🎉 恭喜通关！🎉\n用时: ${minutes}:${seconds.toString().padStart(2, '0')}\n移动次数: ${this.moves}\n最终分数: ${this.score}`, 
                    {
                        fontSize: '28px',
                        fill: '#ffffff',
                        fontStyle: 'bold',
                        align: 'center',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        padding: { x: 25, y: 20 }
                    }
                ).setOrigin(0.5);
                
                // 添加闪烁效果
                this.tweens.add({
                    targets: winMessage,
                    alpha: 0.5,
                    duration: 500,
                    yoyo: true,
                    repeat: -1
                });
            }

            showMessage(text, color) {
                const colorString = `#${color.toString(16).padStart(6, '0')}`;
                const message = this.add.text(450, 180, text, {
                    fontSize: '20px',
                    fill: colorString,
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: { x: 15, y: 8 }
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: message,
                    alpha: 0,
                    y: 150,
                    duration: 2000,
                    ease: 'Power2',
                    onComplete: () => message.destroy()
                });
            }
        }

        // 游戏配置
      const config = {
    type: Phaser.AUTO,
    width: 900,
    height: 700,
    resolution: window.devicePixelRatio || 1,
    render: {
        pixelArt: true,
        antialias: false
    },
    parent: 'game-container',
    backgroundColor: '#2c3e50',
    scene: NumberMatchGame,
    physics: {
        default: 'arcade',
        arcade: {
            gravity: { y: 0 }
        }
    }
};

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>