<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竖屏塔防游戏</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: #1a1a2e;
            font-family: Arial, sans-serif;
        }
        #game-container {
            border: 2px solid #16213e;
            border-radius: 10px;
            overflow: hidden;
        }
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            color: white;
            font-size: 18px;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div id="ui">
        <div>生命值: <span id="health">10</span></div>
        <div>分数: <span id="score">0</span></div>
        <div>波次: <span id="wave">1</span></div>
    </div>
    <div id="game-container"></div>

    <script>
        class TowerDefenseGame extends Phaser.Scene {
            constructor() {
                super({ key: 'TowerDefenseGame' });
                this.snakes = []; // 改为蛇数组
                this.bullets = [];
                this.towers = [];
                this.path = [];
                this.pathPoints = []; // 详细路径点
                this.health = 10;
                this.score = 0;
                this.wave = 1;
                this.snakeSpawnTimer = 0;
                this.waveTimer = 0;
                this.snakesInWave = 0;
                this.maxSnakesPerWave = 3; // 每波3条蛇
                this.segmentRadius = 15; // 蛇身节段半径
                this.segmentDiameter = 30; // 蛇身节段直径
                this.snakeLength = 8; // 每条蛇8个节段
                this.segmentSpacing = 15; // 蛇身节段间距（非常紧密）
            }

            preload() {
                // 创建纹理
                this.createTextures();
            }

            createTextures() {
                // 定义4种颜色
                const colors = [
                    { main: 0xff4444, dark: 0xcc2222 }, // 红色
                    { main: 0x4444ff, dark: 0x2222cc }, // 蓝色
                    { main: 0x44ff44, dark: 0x22cc22 }, // 绿色
                    { main: 0xffff44, dark: 0xcccc22 }  // 黄色
                ];

                // 创建4种颜色的蛇头纹理
                colors.forEach((color, index) => {
                    this.add.graphics()
                        .fillStyle(color.main)
                        .fillCircle(15, 15, 15)
                        .fillStyle(color.dark)
                        .fillCircle(15, 15, 10)
                        // 添加眼睛
                        .fillStyle(0xffffff)
                        .fillCircle(10, 10, 3)
                        .fillCircle(20, 10, 3)
                        .fillStyle(0x000000)
                        .fillCircle(10, 10, 1.5)
                        .fillCircle(20, 10, 1.5)
                        .generateTexture(`snake_head_${index}`, 30, 30);
                });

                // 创建4种颜色的蛇身纹理
                colors.forEach((color, index) => {
                    this.add.graphics()
                        .fillStyle(color.main)
                        .fillCircle(15, 15, 15)
                        .fillStyle(color.dark)
                        .fillCircle(15, 15, 12)
                        // 添加鳞片纹理
                        .fillStyle(color.main)
                        .fillCircle(12, 12, 4)
                        .fillCircle(18, 18, 4)
                        .generateTexture(`snake_body_${index}`, 30, 30);
                });

                // 创建4种颜色的炮塔纹理
                colors.forEach((color, index) => {
                    this.add.graphics()
                        .fillStyle(color.main)
                        .fillRect(0, 0, 40, 40)
                        .fillStyle(color.dark)
                        .fillRect(5, 5, 30, 30)
                        .generateTexture(`tower_${index}`, 40, 40);
                });

                // 创建子弹纹理
                this.add.graphics()
                    .fillStyle(0xffff44)
                    .fillCircle(3, 3, 3)
                    .generateTexture('bullet', 6, 6);

                // 创建路径纹理
                this.add.graphics()
                    .fillStyle(0x666666)
                    .fillRect(0, 0, 40, 40)
                    .generateTexture('path', 40, 40);

                // 创建背景纹理
                this.add.graphics()
                    .fillStyle(0x2d4a22)
                    .fillRect(0, 0, 40, 40)
                    .generateTexture('grass', 40, 40);
            }

            create() {
                // 创建简单S型路径
                this.createPath();
                
                // 绘制背景和路径
                this.drawBackground();
                
                // 创建炮塔
                this.createTowers();
                
                // 创建蛇群组
                this.snakesGroup = this.add.group();
                this.bulletsGroup = this.add.group();
                this.snakeConnections = this.add.group(); // 蛇身连接线群组

                // 设置碰撞检测
                this.physics.add.overlap(this.bulletsGroup, this.snakesGroup, this.bulletHitSnake, null, this);

                // 更新UI
                this.updateUI();
            }

            createPath() {
                const width = this.sys.game.config.width;
                const height = this.sys.game.config.height;
                
                // 创建简单的S型路径，只有7个关键控制点
                this.path = [
                    { x: -50, y: 120 },           // 起点（左侧入口）
                    { x: 260, y: 120 },          // 进入第一个弯道
                    { x: 320, y: 190 },          // 右上弯道顶点
                    { x: 260, y: 240 },          // 右侧直线段
                    { x: 50, y: 280 }, 
                    { x: 60, y: 380 },           // 左下弯道顶点
                            // 左侧直线段
                    { x: width -30, y: 400 }    // 终点（右侧出口）
                ];

                // 生成平滑的路径点
                this.generateSmoothPath();
            }

            generateSmoothPath() {
                this.pathPoints = [];
                
                // 使用Catmull-Rom样条插值创建平滑路径
                for (let i = 0; i < this.path.length - 1; i++) {
                    const p0 = this.path[Math.max(0, i - 1)];
                    const p1 = this.path[i];
                    const p2 = this.path[i + 1];
                    const p3 = this.path[Math.min(this.path.length - 1, i + 2)];
                    
                    const steps = 50; // 每段50个点，确保平滑
                    
                    for (let t = 0; t < steps; t++) {
                        const u = t / steps;
                        const point = this.catmullRomSpline(p0, p1, p2, p3, u);
                        this.pathPoints.push(point);
                    }
                }
                
                // 添加最后一个点
                const lastPoint = this.path[this.path.length - 1];
                this.pathPoints.push({ x: lastPoint.x, y: lastPoint.y });
            }

            catmullRomSpline(p0, p1, p2, p3, t) {
                const t2 = t * t;
                const t3 = t2 * t;
                
                const x = 0.5 * (
                    (2 * p1.x) +
                    (-p0.x + p2.x) * t +
                    (2 * p0.x - 5 * p1.x + 4 * p2.x - p3.x) * t2 +
                    (-p0.x + 3 * p1.x - 3 * p2.x + p3.x) * t3
                );
                
                const y = 0.5 * (
                    (2 * p1.y) +
                    (-p0.y + p2.y) * t +
                    (2 * p0.y - 5 * p1.y + 4 * p2.y - p3.y) * t2 +
                    (-p0.y + 3 * p1.y - 3 * p2.y + p3.y) * t3
                );
                
                return { x, y };
            }

            drawBackground() {
                const width = this.sys.game.config.width;
                const height = this.sys.game.config.height;
                
                // 绘制草地背景
                for (let x = 0; x < width; x += 40) {
                    for (let y = 0; y < height; y += 40) {
                        this.add.image(x, y, 'grass').setOrigin(0, 0);
                    }
                }

                // 绘制平滑路径轨道
                const graphics = this.add.graphics();
                graphics.lineStyle(35, 0x8B4513); // 棕色轨道外边
                graphics.strokePoints(this.pathPoints, false, false);
                
                graphics.lineStyle(25, 0xDEB887); // 浅棕色轨道内部
                graphics.strokePoints(this.pathPoints, false, false);
            }

            createTowers() {
                // 在游戏底部创建4x4炮塔阵列，每行一种颜色
                const towerSize = 40;
                const spacing = 10;
                const totalWidth = 4 * towerSize + 3 * spacing;
                const startX = (this.sys.game.config.width - totalWidth) / 2 + towerSize / 2;
                // const startY = this.sys.game.config.height - 4 * (towerSize + spacing) + towerSize / 2;
                const startY = 500;

                for (let row = 0; row < 4; row++) {
                    for (let col = 0; col < 4; col++) {
                        const x = startX + col * (towerSize + spacing);
                        const y = startY + row * (towerSize + spacing);
                        
                        // 随机选择颜色
                        const colorIndex = Math.floor(Math.random() * 4);
                        const tower = this.add.sprite(x, y, `tower_${colorIndex}`);
                        tower.range = 300; // 攻击距离扩大2倍
                        tower.fireRate = 800; // 毫秒
                        tower.lastFired = 0;
                        tower.colorIndex = colorIndex; // 记录炮塔颜色
                        tower.canFire = row === 0; // 只有第一排炮塔可以发射子弹
                        tower.setDepth(100); // 炮塔在背景上方，但在蛇下方
                        this.towers.push(tower);
                    }
                }
            }

            spawnSnake() {
                if (this.snakesInWave >= this.maxSnakesPerWave) return;

                // 随机选择一种颜色（0-3）
                const colorIndex = Math.floor(Math.random() * 4);

                // 创建一条蛇（包含多个节段）
                const snake = {
                    segments: [],
                    connections: [], // 存储连接线
                    colorIndex: colorIndex,
                    speed: 25,
                    health: this.snakeLength * 2, // 每个节段2点血
                    pathDistance: -this.snakes.length * this.segmentSpacing * this.snakeLength * 2 // 蛇之间的间距
                };

                // 创建蛇的所有节段
                for (let i = 0; i < this.snakeLength; i++) {
                    const isHead = i === 0;
                    const texture = isHead ? `snake_head_${colorIndex}` : `snake_body_${colorIndex}`;
                    const segment = this.physics.add.sprite(-50, 120, texture);
                    segment.setCollideWorldBounds(false);
                    segment.body.setSize(25, 25);
                    segment.isHead = isHead;
                    segment.segmentIndex = i;
                    segment.snake = snake; // 引用所属的蛇
                    segment.colorIndex = colorIndex;

                    // 设置节段在蛇中的相对位置（更紧密的间距）
                    segment.pathDistance = snake.pathDistance - i * this.segmentSpacing;

                    // 设置层级：蛇头在最上层，身体按顺序递减
                    segment.setDepth(1000 - i);

                    this.snakesGroup.add(segment);
                    snake.segments.push(segment);
                }

                // 创建蛇身连接线
                this.createSnakeConnections(snake);

                this.snakes.push(snake);
                this.snakesInWave++;
            }

            updateSnakes(delta) {
                // 更新所有蛇的位置
                this.snakes.forEach((snake, snakeIndex) => {
                    if (snake.segments.length === 0) return;

                    // 蛇头前进
                    snake.pathDistance += snake.speed * delta / 1000;

                    // 计算路径总长度（近似）
                    const totalPathLength = this.pathPoints.length * 2;

                    // 检查蛇头是否到达终点
                    if (snake.pathDistance >= totalPathLength) {
                        // 蛇到达终点，扣血
                        this.health--;
                        this.updateUI();

                        // 销毁整条蛇
                        snake.segments.forEach(segment => {
                            if (segment.active) segment.destroy();
                        });
                        // 销毁连接线
                        snake.connections.forEach(connection => {
                            if (connection) connection.destroy();
                        });
                        this.snakes.splice(snakeIndex, 1);

                        if (this.health <= 0) {
                            this.gameOver();
                        }
                        return;
                    }

                    // 更新蛇的所有节段位置
                    this.updateSnakeSegments(snake);
                });
            }

            createSnakeConnections(snake) {
                // 为蛇的节段之间创建连接线
                const colors = [0xff4444, 0x4444ff, 0x44ff44, 0xffff44];
                const color = colors[snake.colorIndex];

                for (let i = 0; i < snake.segments.length - 1; i++) {
                    const connection = this.add.graphics();
                    connection.lineStyle(22, color, 0.9); // 更粗的连接线，让蛇身更连贯
                    connection.setDepth(500 - i); // 连接线在蛇身下方
                    snake.connections.push(connection);
                    this.snakeConnections.add(connection);
                }
            }

            updateSnakeSegments(snake) {
                // 更新蛇的每个节段位置
                snake.segments.forEach((segment, index) => {
                    if (!segment.active) return;

                    // 计算每个节段的路径距离（蛇头在前，身体紧密跟随）
                    segment.pathDistance = snake.pathDistance - index * this.segmentSpacing;

                    // 设置节段在路径上的位置
                    this.setSegmentPositionOnPath(segment);
                });

                // 更新连接线
                this.updateSnakeConnections(snake);
            }

            updateSnakeConnections(snake) {
                // 更新蛇身节段之间的连接线
                for (let i = 0; i < snake.connections.length; i++) {
                    const connection = snake.connections[i];
                    const segment1 = snake.segments[i];
                    const segment2 = snake.segments[i + 1];

                    if (segment1.active && segment2.active && connection) {
                        connection.clear();
                        const colors = [0xff4444, 0x4444ff, 0x44ff44, 0xffff44];
                        connection.lineStyle(22, colors[snake.colorIndex], 0.9);
                        connection.lineBetween(segment1.x, segment1.y, segment2.x, segment2.y);
                    }
                }
            }

            setSegmentPositionOnPath(segment) {
                if (segment.pathDistance < 0) {
                    // 节段还没进入路径
                    segment.x = -50 + segment.pathDistance;
                    segment.y = 120;
                    return;
                }

                // 在路径上找到对应位置
                const pathIndex = Math.floor(segment.pathDistance / 2);
                const progress = (segment.pathDistance / 2) - pathIndex;

                if (pathIndex >= 0 && pathIndex < this.pathPoints.length - 1) {
                    const currentPoint = this.pathPoints[pathIndex];
                    const nextPoint = this.pathPoints[pathIndex + 1];

                    segment.x = Phaser.Math.Interpolation.Linear([currentPoint.x, nextPoint.x], progress);
                    segment.y = Phaser.Math.Interpolation.Linear([currentPoint.y, nextPoint.y], progress);
                } else if (pathIndex >= 0 && pathIndex < this.pathPoints.length) {
                    const point = this.pathPoints[pathIndex];
                    segment.x = point.x;
                    segment.y = point.y;
                }
            }

            // 蛇的节段自然跟随，不需要额外的队形维护函数

            updateTowers(time, delta) {
                this.towers.forEach(tower => {
                    if (tower.canFire && time - tower.lastFired >= tower.fireRate) {
                        // 寻找范围内的相同颜色目标（蛇的节段）
                        let target = null;
                        let minDistance = tower.range;

                        this.snakes.forEach(snake => {
                            snake.segments.forEach(segment => {
                                if (!segment.active) return;

                                // 只攻击相同颜色的蛇节段
                                if (segment.colorIndex !== tower.colorIndex) return;

                                const distance = Phaser.Math.Distance.Between(
                                    tower.x, tower.y, segment.x, segment.y
                                );

                                if (distance < minDistance) {
                                    minDistance = distance;
                                    target = segment;
                                }
                            });
                        });

                        if (target) {
                            this.fireBullet(tower, target);
                            tower.lastFired = time;
                        }
                    }
                });
            }

            fireBullet(tower, target) {
                const bullet = this.physics.add.sprite(tower.x, tower.y, 'bullet');
                bullet.target = target;
                bullet.speed = 300;
                bullet.damage = 1;
                bullet.colorIndex = tower.colorIndex; // 子弹继承炮塔颜色
                bullet.setDepth(1500); // 子弹在最上层

                this.bulletsGroup.add(bullet);
                this.bullets.push(bullet);
            }

            updateBullets(delta) {
                this.bullets.forEach((bullet, index) => {
                    if (!bullet.active || !bullet.target || !bullet.target.active) {
                        bullet.destroy();
                        this.bullets.splice(index, 1);
                        return;
                    }

                    // 子弹追踪目标
                    const angle = Phaser.Math.Angle.Between(
                        bullet.x, bullet.y, bullet.target.x, bullet.target.y
                    );
                    
                    bullet.x += Math.cos(angle) * bullet.speed * delta / 1000;
                    bullet.y += Math.sin(angle) * bullet.speed * delta / 1000;

                    // 检查是否超出边界
                    if (bullet.x < -50 || bullet.x > this.sys.game.config.width + 50 ||
                        bullet.y < -50 || bullet.y > this.sys.game.config.height + 50) {
                        bullet.destroy();
                        this.bullets.splice(index, 1);
                    }
                });
            }

            bulletHitSnake(bullet, segment) {
                // 检查颜色匹配，只有相同颜色才能造成伤害
                if (bullet.colorIndex !== segment.colorIndex) {
                    return; // 不同颜色，子弹穿过
                }

                // 处理子弹击中蛇节段
                const snake = segment.snake;
                snake.health -= bullet.damage;
                bullet.destroy();

                const bulletIndex = this.bullets.indexOf(bullet);
                if (bulletIndex > -1) {
                    this.bullets.splice(bulletIndex, 1);
                }

                // 蛇节段受伤效果（可以添加闪烁等效果）
                segment.setTint(0xff0000);
                this.time.delayedCall(100, () => {
                    if (segment.active) segment.clearTint();
                });

                if (snake.health <= 0) {
                    // 整条蛇死亡
                    this.score += snake.segments.length * 10; // 根据蛇的长度给分
                    this.updateUI();

                    // 销毁整条蛇的所有节段
                    snake.segments.forEach(seg => {
                        if (seg.active) seg.destroy();
                    });

                    // 销毁连接线
                    snake.connections.forEach(connection => {
                        if (connection) connection.destroy();
                    });

                    // 从蛇数组中移除
                    const snakeIndex = this.snakes.indexOf(snake);
                    if (snakeIndex > -1) {
                        this.snakes.splice(snakeIndex, 1);
                    }
                }
            }

            update(time, delta) {
                // 生成蛇
                this.snakeSpawnTimer += delta;
                if (this.snakeSpawnTimer >= 2000 && this.snakesInWave < this.maxSnakesPerWave) {
                    this.spawnSnake();
                    this.snakeSpawnTimer = 0;
                }

                // 检查是否开始下一波
                this.waveTimer += delta;
                if (this.snakes.length === 0 && this.snakesInWave >= this.maxSnakesPerWave) {
                    if (this.waveTimer >= 3000) {
                        this.nextWave();
                    }
                }

                // 更新游戏对象
                this.updateSnakes(delta);
                this.updateTowers(time, delta);
                this.updateBullets(delta);
            }

            nextWave() {
                this.wave++;
                this.snakesInWave = 0;
                this.maxSnakesPerWave += 1; // 每波增加1条蛇
                this.snakeLength += 1; // 蛇变得更长
                this.waveTimer = 0;
                this.updateUI();
            }

            updateUI() {
                document.getElementById('health').textContent = this.health;
                document.getElementById('score').textContent = this.score;
                document.getElementById('wave').textContent = this.wave;
            }

            gameOver() {
                this.add.text(this.sys.game.config.width / 2, this.sys.game.config.height / 2, 
                    '游戏结束!\n点击重新开始', {
                    fontSize: '32px',
                    fill: '#ffffff',
                    align: 'center'
                }).setOrigin(0.5);

                this.input.once('pointerdown', () => {
                    this.scene.restart();
                    this.health = 10;
                    this.score = 0;
                    this.wave = 1;
                    this.snakes = [];
                    this.bullets = [];
                    this.snakesInWave = 0;
                    this.maxSnakesPerWave = 3;
                    this.snakeLength = 8;
                    this.updateUI();
                });
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 360,
            height: 800,
            parent: 'game-container',
            backgroundColor: '#2d4a22',
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
            scene: TowerDefenseGame
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>