<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>羊了个羊 - 横屏版</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }
        #game-container {
            text-align: center;
            position: relative;
        }
        #ui {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
            color: white;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        }
        #score {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        #level {
            font-size: 18px;
            margin-bottom: 10px;
        }
        #tools {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
            display: flex;
            gap: 10px;
        }
        .tool-btn {
            background: rgba(255,255,255,0.9);
            border: 2px solid #4CAF50;
            border-radius: 8px;
            padding: 8px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s;
        }
        .tool-btn:hover {
            background: #4CAF50;
            color: white;
        }
        .tool-btn:disabled {
            background: rgba(128,128,128,0.5);
            border-color: #888;
            cursor: not-allowed;
        }
        #game-over {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 30px;
            border-radius: 15px;
            text-align: center;
            z-index: 200;
            display: none;
        }
        #restart-btn {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            font-size: 16px;
            border-radius: 8px;
            cursor: pointer;
            margin-top: 15px;
        }
        #restart-btn:hover {
            background: #45a049;
        }
        canvas {
            border: 2px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="ui">
            <div id="score">分数: 0</div>
            <div id="level">关卡: 1</div>
            <div style="font-size: 14px; color: #FFD700; margin-top: 5px;">横屏版 1280×720</div>
        </div>
        <div id="tools">
            <button class="tool-btn" id="undo-btn" onclick="undoMove()">撤销</button>
            <button class="tool-btn" id="shuffle-btn" onclick="shuffleTiles()">洗牌</button>
            <button class="tool-btn" id="hint-btn" onclick="showHint()">提示</button>
            <button class="tool-btn" id="reset-btn" onclick="emergencyReset()" style="background: #ff6b6b;">重置</button>
        </div>
        <div id="game"></div>
        <div id="game-over">
            <h2 id="game-over-title">游戏结束</h2>
            <p id="game-over-message">收集槽已满！</p>
            <button id="restart-btn" onclick="restartGame()">重新开始</button>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        class SheepGame extends Phaser.Scene {
            constructor() {
                super({ key: 'SheepGame' });
                
                // 游戏配置 - 横屏适配
                this.TILE_SIZE = 50; // 稍微增大方块尺寸
                this.SLOT_SIZE = 55;
                this.MAX_SLOTS = 12; // 横屏可以容纳更多槽位
                this.TILE_TYPES = ['red', 'blue', 'yellow', 'green', 'orange', 'purple', 'cyan', 'pink'];
                
                // 游戏状态
                this.tiles = [];
                this.slots = [];
                this.score = 0;
                this.level = 1;
                this.isProcessing = false;
                this.gameOver = false;

                // 道具系统
                this.moveHistory = []; // 记录移动历史用于撤销
                this.undoCount = 3; // 每关可用撤销次数
                this.shuffleCount = 2; // 每关可用洗牌次数
                this.hintCount = 5; // 每关可用提示次数

                // 防抖标志
                this.isUpdatingBlocked = false;
            }

            preload() {
                // 创建方块纹理
                this.createTileTextures();
                // 创建槽位纹理
                this.createSlotTexture();
            }

            create() {
                // 初始化游戏
                this.initGame();

                // 创建收集槽
                this.createSlots();

                // 生成关卡
                this.generateLevel();

                // 添加输入处理
                this.input.on('gameobjectdown', this.onTileClick, this);

                // 添加定期状态检查，防止卡死
                this.time.addEvent({
                    delay: 5000, // 每5秒检查一次
                    callback: this.checkGameHealth,
                    callbackScope: this,
                    loop: true
                });
            }

            checkGameHealth() {
                // 简化健康检查，移除动画相关的复杂逻辑
                if (this.isProcessing) {
                    console.warn('Game stuck in processing state, resetting...');
                    this.isProcessing = false;
                    this.processingStartTime = null;
                }

                if (this.isUpdatingBlocked) {
                    console.warn('updateBlockedStatus stuck, resetting...');
                    this.isUpdatingBlocked = false;
                }

                // 检查游戏状态
                const slotsWithTiles = this.slots.filter(slot => slot.getData('tile') !== null).length;
                console.log('Health check - Tiles in game:', this.tiles.length, 'Tiles in slots:', slotsWithTiles);
            }

            createTileTextures() {
                this.TILE_TYPES.forEach((type, index) => {
                    const graphics = this.add.graphics();

                    // 参考Block Blast游戏的鲜艳颜色
                    const colors = [
                        0xFF4444, // 红色 - red
                        0x44AAFF, // 蓝色 - blue
                        0xFFDD44, // 黄色 - yellow
                        0x44FF44, // 绿色 - green
                        0xFF8844, // 橙色 - orange
                        0xAA44FF, // 紫色 - purple
                        0x44FFAA, // 青色 - cyan
                        0xFF44AA  // 粉色 - pink
                    ];
                    const baseColor = colors[index];
                    const lightColor = this.lightenColor(baseColor, 0.4);
                    const darkColor = this.darkenColor(baseColor, 0.2);

                    // 绘制主体 - 更现代的立体效果
                    graphics.fillStyle(baseColor, 1.0);
                    graphics.fillRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 8);

                    // 添加高光效果（顶部和左侧）
                    graphics.fillStyle(lightColor, 0.8);
                    graphics.fillRoundedRect(3, 3, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 6);

                    // 添加阴影效果（底部和右侧）
                    graphics.fillStyle(darkColor, 0.6);
                    graphics.fillRoundedRect(3, this.TILE_SIZE / 2, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 6);

                    // 添加内部高光
                    graphics.fillStyle(0xFFFFFF, 0.3);
                    graphics.fillRoundedRect(6, 6, this.TILE_SIZE - 12, this.TILE_SIZE / 3, 4);

                    // 添加边框
                    graphics.lineStyle(1, this.darkenColor(baseColor, 0.4), 1.0);
                    graphics.strokeRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 8);

                    graphics.generateTexture(type, this.TILE_SIZE, this.TILE_SIZE);
                    graphics.destroy();
                });
            }



            createSlotTexture() {
                const graphics = this.add.graphics();

                // 绘制现代风格的槽位背景
                graphics.fillStyle(0x2A2A3A, 0.9);
                graphics.fillRoundedRect(1, 1, this.SLOT_SIZE - 2, this.SLOT_SIZE - 2, 10);

                // 添加内部阴影效果
                graphics.fillStyle(0x1A1A2A, 0.8);
                graphics.fillRoundedRect(3, 3, this.SLOT_SIZE - 6, this.SLOT_SIZE - 6, 8);

                // 添加高光边框
                graphics.lineStyle(1, 0x4A4A5A, 1.0);
                graphics.strokeRoundedRect(1, 1, this.SLOT_SIZE - 2, this.SLOT_SIZE - 2, 10);

                // 添加内部边框
                graphics.lineStyle(1, 0x0A0A1A, 0.8);
                graphics.strokeRoundedRect(3, 3, this.SLOT_SIZE - 6, this.SLOT_SIZE - 6, 8);

                graphics.generateTexture('slot', this.SLOT_SIZE, this.SLOT_SIZE);
                graphics.destroy();
            }

            lightenColor(color, factor) {
                const r = Math.min(255, Math.floor(((color >> 16) & 0xFF) * (1 + factor)));
                const g = Math.min(255, Math.floor(((color >> 8) & 0xFF) * (1 + factor)));
                const b = Math.min(255, Math.floor((color & 0xFF) * (1 + factor)));
                return (r << 16) | (g << 8) | b;
            }

            darkenColor(color, factor) {
                const r = Math.floor(((color >> 16) & 0xFF) * (1 - factor));
                const g = Math.floor(((color >> 8) & 0xFF) * (1 - factor));
                const b = Math.floor((color & 0xFF) * (1 - factor));
                return (r << 16) | (g << 8) | b;
            }

            initGame() {
                this.tiles = [];
                this.slots = [];
                this.score = 0;
                this.isProcessing = false;
                this.gameOver = false;
                this.moveHistory = [];
                this.isUpdatingBlocked = false;
                this.updateUI();
                this.updateToolButtons();
            }

            cleanupAnimations() {
                // 移除动画清理功能
            }

            createSlots() {
                // 横屏布局：将槽位放在底部中央
                const startX = (this.sys.game.config.width - this.MAX_SLOTS * this.SLOT_SIZE) / 2;
                const slotY = this.sys.game.config.height - 60;

                for (let i = 0; i < this.MAX_SLOTS; i++) {
                    const x = startX + i * this.SLOT_SIZE + this.SLOT_SIZE / 2;
                    const slot = this.add.image(x, slotY, 'slot');
                    slot.setData('index', i);
                    slot.setData('tile', null);
                    this.slots.push(slot);
                }
            }

            generateLevel() {
                // 清除现有方块
                this.tiles.forEach(tile => {
                    if (tile && tile.active) {
                        tile.destroy();
                    }
                });
                this.tiles = [];

                // 重置状态标志
                this.isUpdatingBlocked = false;

                // 根据关卡生成方块布局
                const patterns = this.getLevelPattern(this.level);

                patterns.forEach(pattern => {
                    this.createTileGroup(pattern);
                });

                // 确保有解
                this.ensureSolvable();
            }

            getLevelPattern(level) {
                // 横屏布局：充分利用1280x720空间，创建更宽的布局
                const centerX = this.sys.game.config.width / 2;
                const centerY = this.sys.game.config.height / 2;

                const basePatterns = [
                    // 关卡1: 横屏金字塔布局
                    [
                        { x: centerX - 200, y: centerY + 100, layer: 0, count: 35, rows: 5, cols: 7 },
                        { x: centerX - 150, y: centerY + 50, layer: 1, count: 28, rows: 4, cols: 7 },
                        { x: centerX - 100, y: centerY, layer: 2, count: 21, rows: 3, cols: 7 },
                        { x: centerX - 50, y: centerY - 50, layer: 3, count: 15, rows: 3, cols: 5 },
                        { x: centerX, y: centerY - 100, layer: 4, count: 9, rows: 3, cols: 3 }
                    ],
                    // 关卡2: 双塔布局
                    [
                        { x: centerX - 300, y: centerY + 80, layer: 0, count: 24, rows: 4, cols: 6 },
                        { x: centerX - 250, y: centerY + 40, layer: 1, count: 18, rows: 3, cols: 6 },
                        { x: centerX - 200, y: centerY, layer: 2, count: 12, rows: 3, cols: 4 },
                        { x: centerX + 100, y: centerY + 80, layer: 3, count: 24, rows: 4, cols: 6 },
                        { x: centerX + 150, y: centerY + 40, layer: 4, count: 18, rows: 3, cols: 6 },
                        { x: centerX + 200, y: centerY, layer: 5, count: 12, rows: 3, cols: 4 }
                    ],
                    // 关卡3: 复杂横向分布
                    [
                        { x: centerX - 400, y: centerY + 120, layer: 0, count: 30, rows: 5, cols: 6 },
                        { x: centerX - 350, y: centerY + 80, layer: 1, count: 24, rows: 4, cols: 6 },
                        { x: centerX - 300, y: centerY + 40, layer: 2, count: 18, rows: 3, cols: 6 },
                        { x: centerX - 100, y: centerY + 60, layer: 3, count: 21, rows: 3, cols: 7 },
                        { x: centerX - 50, y: centerY + 20, layer: 4, count: 15, rows: 3, cols: 5 },
                        { x: centerX + 200, y: centerY + 100, layer: 5, count: 28, rows: 4, cols: 7 },
                        { x: centerX + 250, y: centerY + 60, layer: 6, count: 21, rows: 3, cols: 7 }
                    ]
                ];

                // 循环使用模式，但每次都增加一些难度
                const patternIndex = Math.min(level - 1, basePatterns.length - 1);
                const pattern = JSON.parse(JSON.stringify(basePatterns[patternIndex])); // 深拷贝

                // 根据关卡增加额外的方块
                if (level > basePatterns.length) {
                    const extraFactor = level - basePatterns.length;
                    pattern.forEach(layer => {
                        layer.count += Math.floor(layer.count * 0.2 * extraFactor);
                    });
                }

                return pattern;
            }

            createTileGroup(pattern) {
                const { x: baseX, y: baseY, layer, count, rows, cols } = pattern;

                // 使用指定的行列数，如果没有指定则自动计算
                const actualRows = rows || Math.ceil(Math.sqrt(count));
                const actualCols = cols || Math.ceil(count / actualRows);

                // 横屏优化：调整间距以适应更大的屏幕
                const spacingX = this.TILE_SIZE * 1.15; // 横屏可以有更大的间距
                const spacingY = this.TILE_SIZE * 1.15;

                // 添加随机偏移，让布局更自然
                const randomOffset = layer * 5; // 每层有不同的随机偏移范围

                for (let i = 0; i < count; i++) {
                    const row = Math.floor(i / actualCols);
                    const col = i % actualCols;

                    // 如果当前行的方块数不足一行，居中显示
                    const tilesInThisRow = Math.min(actualCols, count - row * actualCols);
                    const rowOffset = (actualCols - tilesInThisRow) * spacingX / 2;

                    let x = baseX + col * spacingX - rowOffset;
                    let y = baseY - row * spacingY; // 向上堆叠

                    // 添加随机偏移，让布局更自然
                    x += (Math.random() - 0.5) * randomOffset;
                    y += (Math.random() - 0.5) * randomOffset;

                    // 确保方块在游戏区域内（横屏适配）
                    x = Math.max(this.TILE_SIZE / 2, Math.min(this.sys.game.config.width - this.TILE_SIZE / 2, x));
                    y = Math.max(this.TILE_SIZE / 2, Math.min(this.sys.game.config.height - 120, y));

                    const tileTypeIndex = Math.floor(Math.random() * this.TILE_TYPES.length);
                    const tileType = this.TILE_TYPES[tileTypeIndex];

                    // 创建方块容器
                    const tileContainer = this.add.container(x, y);

                    // 创建方块背景
                    const tile = this.add.image(0, 0, tileType);

                    // 创建数字文本
                    const numberText = this.add.text(0, 0, (tileTypeIndex + 1).toString(), {
                        fontSize: Math.floor(this.TILE_SIZE * 0.6) + 'px',
                        fontFamily: 'Arial Black, Arial',
                        color: '#FFFFFF',
                        stroke: '#000000',
                        strokeThickness: 3,
                        align: 'center'
                    });
                    numberText.setOrigin(0.5, 0.5);

                    // 将背景和文字添加到容器
                    tileContainer.add([tile, numberText]);
                    tileContainer.setSize(this.TILE_SIZE, this.TILE_SIZE);
                    tileContainer.setInteractive();

                    // 设置数据
                    tileContainer.setData('type', tileType);
                    tileContainer.setData('typeIndex', tileTypeIndex);
                    tileContainer.setData('layer', layer);
                    tileContainer.setData('blocked', false);
                    tileContainer.setData('hasBreathing', false);
                    tileContainer.setData('numberText', numberText);
                    tileContainer.setDepth(layer * 10);

                    // 改善阴影效果，让层次更明显
                    const tintValue = Math.max(0x888888, 0xFFFFFF - layer * 0x222222);
                    tile.setTint(tintValue);

                    // 添加轻微的缩放效果，让下层方块看起来更远
                    const scale = 1 - layer * 0.05;
                    tileContainer.setScale(scale);

                    // 保存原始缩放值
                    tileContainer.setData('originalScale', scale);

                    this.tiles.push(tileContainer);
                }

                this.updateBlockedStatus();
            }

            updateBlockedStatus() {
                // 防止重复调用
                if (this.isUpdatingBlocked) {
                    console.log('updateBlockedStatus already running, skipping');
                    return;
                }
                this.isUpdatingBlocked = true;

                console.log('Starting updateBlockedStatus, tiles count:', this.tiles.length);

                try {
                    // 重置所有方块的阻挡状态
                    this.tiles.forEach(tileContainer => {
                        if (tileContainer && tileContainer.active) {
                            tileContainer.setData('blocked', false);
                            tileContainer.setData('hasBreathing', false);

                            // 获取方块背景图像（容器中的第一个元素）
                            const tile = tileContainer.list[0];
                            const numberText = tileContainer.getData('numberText');

                            // 恢复原始颜色和透明度
                            tile.setTint(0xFFFFFF);
                            tile.setAlpha(1.0);
                            tileContainer.setAlpha(1.0);

                            // 确保数字文本也是白色
                            if (numberText) {
                                numberText.setAlpha(1.0);
                            }
                        }
                    });

                    // 检查每个方块是否被上层方块阻挡
                    this.tiles.forEach(tileContainer => {
                        if (!tileContainer || !tileContainer.active) return;

                        const tileLayer = tileContainer.getData('layer');
                        const tileScale = 1 - tileLayer * 0.05;
                        let isBlocked = false;

                        this.tiles.forEach(otherContainer => {
                            if (!otherContainer || !otherContainer.active || otherContainer === tileContainer) return;

                            const otherLayer = otherContainer.getData('layer');
                            if (otherLayer > tileLayer) {
                                // 检查是否重叠，考虑缩放因素
                                const distance = Phaser.Math.Distance.Between(
                                    tileContainer.x, tileContainer.y, otherContainer.x, otherContainer.y
                                );
                                const overlapThreshold = this.TILE_SIZE * 0.6 * tileScale;
                                if (distance < overlapThreshold) {
                                    isBlocked = true;
                                }
                            }
                        });

                        tileContainer.setData('blocked', isBlocked);

                        // 获取方块背景和数字文本
                        const tile = tileContainer.list[0];
                        const numberText = tileContainer.getData('numberText');

                        if (isBlocked) {
                            // 被遮挡的方块变灰色
                            tile.setTint(0x666666);
                            tileContainer.setAlpha(1.0); // 保持100%透明度
                            if (numberText) {
                                numberText.setTint(0x888888); // 数字也变灰
                            }
                        } else {
                            // 不被遮挡的方块保持原色和100%透明度
                            tile.setTint(0xFFFFFF);
                            tileContainer.setAlpha(1.0);
                            if (numberText) {
                                numberText.setTint(0xFFFFFF); // 数字保持白色
                            }
                            // 移除呼吸效果
                            this.addBreathingEffect(tileContainer);
                        }
                    });

                    console.log('updateBlockedStatus completed successfully');
                } catch (error) {
                    console.error('Error in updateBlockedStatus:', error);
                } finally {
                    this.isUpdatingBlocked = false;
                }
            }

            addBreathingEffect(tile) {
                // 移除呼吸动画效果
                tile.setData('hasBreathing', true);
            }

            ensureSolvable() {
                // 确保每种类型的方块数量是3的倍数
                const typeCounts = {};
                this.tiles.forEach(tileContainer => {
                    if (tileContainer && tileContainer.active) {
                        const type = tileContainer.getData('type');
                        typeCounts[type] = (typeCounts[type] || 0) + 1;
                    }
                });

                // 调整方块类型使其可解
                Object.keys(typeCounts).forEach(type => {
                    const count = typeCounts[type];
                    const remainder = count % 3;
                    if (remainder !== 0) {
                        // 找到该类型的方块并调整
                        const tilesOfType = this.tiles.filter(tileContainer =>
                            tileContainer && tileContainer.active && tileContainer.getData('type') === type
                        );

                        for (let i = 0; i < remainder; i++) {
                            if (tilesOfType[i]) {
                                // 随机选择一个其他类型索引
                                const otherTypeIndices = [];
                                for (let j = 0; j < this.TILE_TYPES.length; j++) {
                                    if (this.TILE_TYPES[j] !== type) {
                                        otherTypeIndices.push(j);
                                    }
                                }
                                const newTypeIndex = otherTypeIndices[Math.floor(Math.random() * otherTypeIndices.length)];
                                const newType = this.TILE_TYPES[newTypeIndex];

                                // 更新容器中的背景纹理
                                const tile = tilesOfType[i].list[0];
                                tile.setTexture(newType);

                                // 更新数字文本
                                const numberText = tilesOfType[i].getData('numberText');
                                if (numberText) {
                                    numberText.setText((newTypeIndex + 1).toString());
                                }

                                // 更新容器数据
                                tilesOfType[i].setData('type', newType);
                                tilesOfType[i].setData('typeIndex', newTypeIndex);
                            }
                        }
                    }
                });
            }

            onTileClick(pointer, tile) {
                if (this.isProcessing || this.gameOver) return;

                // 检查方块是否被阻挡
                if (tile.getData('blocked')) {
                    // 显示提示文字（移除摇摆动画）
                    this.showHint(tile.x, tile.y - 30, '被遮挡!');
                    return;
                }

                // 找到空的槽位
                const emptySlot = this.slots.find(slot => slot.getData('tile') === null);
                if (!emptySlot) {
                    // 槽位已满，游戏结束
                    this.endGame(false);
                    return;
                }

                // 播放点击成功的效果
                this.playClickEffect(tile);
                this.moveTileToSlot(tile, emptySlot);
            }

            showHint(x, y, text) {
                const hintText = this.add.text(x, y, text, {
                    fontSize: '16px',
                    fontFamily: 'Arial',
                    color: '#ff4444',
                    stroke: '#ffffff',
                    strokeThickness: 2
                });
                hintText.setOrigin(0.5);

                // 移除动画，直接延时销毁
                this.time.delayedCall(1000, () => {
                    if (hintText && hintText.active) {
                        hintText.destroy();
                    }
                });
            }

            playClickEffect(tile) {
                // 移除所有点击动画效果
                // 只保留基本的视觉反馈
                const originalScale = tile.getData('originalScale') || tile.scaleX;
                tile.setScale(originalScale);
            }

            moveTileToSlot(tile, slot) {
                console.log('Moving tile to slot...');

                // 记录移动历史用于撤销
                this.moveHistory.push({
                    tile: tile,
                    slot: slot,
                    originalX: tile.x,
                    originalY: tile.y,
                    originalScale: tile.getData('originalScale') || tile.scaleX
                });

                // 直接移动到目标位置
                tile.x = slot.x;
                tile.y = slot.y;
                tile.setScale(0.9);

                // 直接完成移动
                this.completeTileMove(tile, slot);
            }

            completeTileMove(tile, slot) {
                try {
                    console.log('Completing tile move...');

                    // 将方块放入槽位
                    slot.setData('tile', tile);
                    tile.setData('slot', slot);

                    // 确保方块位置正确
                    tile.x = slot.x;
                    tile.y = slot.y;
                    tile.setScale(0.9);

                    // 从游戏区域移除
                    const index = this.tiles.indexOf(tile);
                    if (index > -1) {
                        this.tiles.splice(index, 1);
                        console.log('Removed tile from game area, remaining tiles:', this.tiles.length);
                    }

                    // 更新道具按钮状态
                    this.updateToolButtons();

                    // 立即更新阻挡状态和检查匹配
                    this.updateBlockedStatus();
                    this.checkMatches();

                } catch (error) {
                    console.error('Error in completeTileMove:', error);
                }
            }

            checkMatches() {
                console.log('Checking matches...');

                // 统计槽位中每种类型的数量
                const typeCounts = {};
                const typePositions = {};

                this.slots.forEach((slot, index) => {
                    const tile = slot.getData('tile');
                    if (tile) {
                        const type = tile.getData('type');
                        typeCounts[type] = (typeCounts[type] || 0) + 1;
                        if (!typePositions[type]) typePositions[type] = [];
                        typePositions[type].push(index);
                    }
                });

                console.log('Type counts:', typeCounts);

                // 检查是否有三个相同的
                let hasMatch = false;
                Object.keys(typeCounts).forEach(type => {
                    if (typeCounts[type] >= 3) {
                        hasMatch = true;
                        console.log('Found match for type:', type);
                        this.removeMatches(type, typePositions[type]);
                    }
                });

                if (!hasMatch) {
                    console.log('No matches found, checking game state');
                    // 检查游戏状态
                    this.checkGameState();
                }
            }

            removeMatches(type, positions) {
                console.log('Removing matches for type:', type, 'positions:', positions);

                // 移除前三个相同类型的方块
                const tilesToRemove = positions.slice(0, 3);

                tilesToRemove.forEach((slotIndex) => {
                    const slot = this.slots[slotIndex];
                    const tile = slot.getData('tile');

                    if (tile) {
                        this.playRemoveAnimation(tile, slot, null);
                    }
                });

                // 更新分数
                this.score += 30;
                this.updateUI();

                // 立即重新排列槽位
                this.rearrangeSlots();
            }

            playRemoveAnimation(tile, slot, callback) {
                // 移除动画效果，直接销毁方块
                try {
                    if (tile && tile.active) {
                        tile.destroy();
                    }
                    slot.setData('tile', null);

                    // 调用回调函数
                    if (callback) {
                        callback();
                    }
                } catch (error) {
                    console.error('Error in playRemoveAnimation completion:', error);
                    if (callback) {
                        callback();
                    }
                }
            }

            createExplosionEffect(x, y) {
                // 移除爆炸动画效果
                // 保留空函数以避免调用错误
            }

            rearrangeSlots() {
                console.log('Rearranging slots...');

                // 将所有方块向左移动，填补空隙
                const activeTiles = [];

                this.slots.forEach(slot => {
                    const tile = slot.getData('tile');
                    if (tile) {
                        activeTiles.push(tile);
                        slot.setData('tile', null);
                    }
                });

                console.log('Active tiles after rearrange:', activeTiles.length);

                if (activeTiles.length === 0) {
                    // 没有方块需要重排，直接完成
                    this.checkGameState();
                    return;
                }

                // 重新分配到槽位
                activeTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.setData('tile', tile);
                    tile.setData('slot', slot);

                    // 直接移动到目标位置
                    tile.x = slot.x;
                    tile.y = slot.y;
                });

                // 完成重新排列
                this.completeRearrange(activeTiles);
            }

            completeRearrange(activeTiles) {
                console.log('Completing rearrange...');

                // 立即检查匹配
                if (activeTiles.length > 0) {
                    this.checkMatches();
                } else {
                    console.log('No tiles left in slots, checking game state');
                    this.checkGameState();
                }
            }

            checkGameState() {
                // 检查是否获胜
                if (this.tiles.length === 0) {
                    this.endGame(true);
                    return;
                }

                // 检查是否还有可点击的方块
                const clickableTiles = this.tiles.filter(tile =>
                    tile && tile.active && !tile.getData('blocked')
                );

                if (clickableTiles.length === 0 && this.tiles.length > 0) {
                    // 没有可点击的方块但还有剩余方块
                    this.endGame(false);
                }
            }

            endGame(isWin) {
                this.gameOver = true;

                const gameOverDiv = document.getElementById('game-over');
                const titleElement = document.getElementById('game-over-title');
                const messageElement = document.getElementById('game-over-message');

                if (isWin) {
                    titleElement.textContent = '恭喜过关！';
                    messageElement.textContent = `关卡 ${this.level} 完成！得分: ${this.score}`;

                    // 延迟后进入下一关
                    this.time.delayedCall(2000, () => {
                        this.level++;
                        this.nextLevel();
                        gameOverDiv.style.display = 'none';
                    });
                } else {
                    titleElement.textContent = '游戏结束';
                    messageElement.textContent = '没有可移动的方块了！';
                }

                gameOverDiv.style.display = 'block';
            }

            nextLevel() {
                this.gameOver = false;
                // 重置道具计数
                this.undoCount = 3;
                this.shuffleCount = 2;
                this.hintCount = 5;
                this.moveHistory = [];
                this.generateLevel();
                this.updateUI();
                this.updateToolButtons();
            }

            updateUI() {
                document.getElementById('score').textContent = `分数: ${this.score}`;
                document.getElementById('level').textContent = `关卡: ${this.level}`;
            }

            updateToolButtons() {
                const undoBtn = document.getElementById('undo-btn');
                const shuffleBtn = document.getElementById('shuffle-btn');
                const hintBtn = document.getElementById('hint-btn');

                undoBtn.disabled = this.undoCount <= 0 || this.moveHistory.length === 0;
                shuffleBtn.disabled = this.shuffleCount <= 0;
                hintBtn.disabled = this.hintCount <= 0;

                undoBtn.textContent = `撤销(${this.undoCount})`;
                shuffleBtn.textContent = `洗牌(${this.shuffleCount})`;
                hintBtn.textContent = `提示(${this.hintCount})`;
            }

            // 道具功能实现
            undoLastMove() {
                if (this.undoCount <= 0 || this.moveHistory.length === 0 || this.isProcessing) return;

                const lastMove = this.moveHistory.pop();
                this.undoCount--;

                // 将方块从槽位移回原位置
                const tile = lastMove.tile;
                const slot = lastMove.slot;

                slot.setData('tile', null);
                tile.setData('slot', null);

                // 恢复方块到游戏区域
                this.tiles.push(tile);

                // 移除动画，直接移动到原位置
                tile.x = lastMove.originalX;
                tile.y = lastMove.originalY;
                tile.setScale(lastMove.originalScale);

                this.updateBlockedStatus();
                this.rearrangeSlots();
                this.updateToolButtons();
            }

            shuffleBoard() {
                if (this.shuffleCount <= 0) return;

                this.shuffleCount--;

                // 收集所有方块的类型索引
                const tileTypeIndices = this.tiles.map(tileContainer => tileContainer.getData('typeIndex'));

                // 打乱类型索引数组
                for (let i = tileTypeIndices.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [tileTypeIndices[i], tileTypeIndices[j]] = [tileTypeIndices[j], tileTypeIndices[i]];
                }

                // 重新分配类型给方块
                this.tiles.forEach((tileContainer, index) => {
                    const newTypeIndex = tileTypeIndices[index];
                    const newType = this.TILE_TYPES[newTypeIndex];

                    // 更新背景纹理
                    const tile = tileContainer.list[0];
                    tile.setTexture(newType);

                    // 更新数字文本
                    const numberText = tileContainer.getData('numberText');
                    if (numberText) {
                        numberText.setText((newTypeIndex + 1).toString());
                    }

                    // 更新数据
                    tileContainer.setData('type', newType);
                    tileContainer.setData('typeIndex', newTypeIndex);
                });

                // 立即执行
                this.ensureSolvable();
                this.updateBlockedStatus();
                this.updateToolButtons();
            }

            showGameHint() {
                if (this.hintCount <= 0 || this.isProcessing) return;

                this.hintCount--;

                // 找到一个可点击的方块
                const clickableTiles = this.tiles.filter(tile =>
                    tile && tile.active && !tile.getData('blocked')
                );

                if (clickableTiles.length > 0) {
                    const hintTile = clickableTiles[Math.floor(Math.random() * clickableTiles.length)];

                    // 移除高亮动画，直接改变颜色提示
                    hintTile.setTint(0xFFFF00);

                    // 延时恢复原色
                    this.time.delayedCall(1000, () => {
                        const layer = hintTile.getData('layer');
                        const tintValue = Math.max(0x888888, 0xFFFFFF - layer * 0x222222);
                        hintTile.setTint(tintValue);
                    });

                    // 显示箭头指示
                    this.showArrowHint(hintTile.x, hintTile.y - 40);
                }

                this.updateToolButtons();
            }

            showArrowHint(x, y) {
                const arrow = this.add.text(x, y, '↓', {
                    fontSize: '24px',
                    color: '#FFD700',
                    stroke: '#000000',
                    strokeThickness: 2
                });
                arrow.setOrigin(0.5);
                arrow.setDepth(1000);

                // 移除动画，直接延时销毁
                this.time.delayedCall(1000, () => {
                    if (arrow && arrow.active) {
                        arrow.destroy();
                    }
                });
            }
        }

        // 游戏配置 - 横板1280x720，固定尺寸不缩放
        const config = {
            type: Phaser.AUTO,
            width: 1280,
            height: 720,
            parent: 'game',
            backgroundColor: '#87CEEB',
            scene: SheepGame,
            scale: {
                mode: Phaser.Scale.NONE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            render: {
                antialias: true,
                pixelArt: false
            }
        };

        let game;

        function startGame() {
            game = new Phaser.Game(config);
        }

        function restartGame() {
            if (game) {
                game.destroy(true);
            }
            document.getElementById('game-over').style.display = 'none';
            startGame();
        }

        // 道具功能的全局函数
        function undoMove() {
            if (game && game.scene.scenes[0]) {
                game.scene.scenes[0].undoLastMove();
            }
        }

        function shuffleTiles() {
            if (game && game.scene.scenes[0]) {
                game.scene.scenes[0].shuffleBoard();
            }
        }

        function showHint() {
            if (game && game.scene.scenes[0]) {
                game.scene.scenes[0].showGameHint();
            }
        }

        function emergencyReset() {
            console.log('Emergency reset triggered');
            if (game && game.scene.scenes[0]) {
                const scene = game.scene.scenes[0];
                // 强制重置所有状态
                scene.isProcessing = false;
                scene.isUpdatingBlocked = false;

                // 清理所有槽位
                scene.slots.forEach(slot => {
                    const tile = slot.getData('tile');
                    if (tile && tile.active) {
                        tile.destroy();
                    }
                    slot.setData('tile', null);
                });

                // 重新生成关卡
                scene.generateLevel();
                console.log('Emergency reset completed');
            }
        }

        // 启动游戏
        startGame();
    </script>
</body>
</html>
