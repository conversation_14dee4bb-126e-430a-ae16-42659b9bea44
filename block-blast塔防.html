<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Blast - 塔防</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
           
            margin: 0;
            padding: 0;
              background: linear-gradient(135deg, #1e3c72, #2a5298);
               display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
          border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        

        // 游戏配置 - 竖屏优化
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: {
                preload: preload,
                create: create,
                update: update
            },
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            },
             scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            },
            input: {
                activePointers: 3 // 支持多点触控
            }
        };

        // 游戏变量
        let game;
        let gameScene;
        let grid = [];
        let gridSize = 20;
        let cellSize = 30; // 增大格子尺寸
        let gridOffsetX = 100; // (750 - 20*30) / 2 = 75
        let gridOffsetY = 200;
        let score = 0;
        let scoreText;
        let bestScore = 0;
        let bestScoreText;
        let currentBlocks = [];
        let nextBlocks = [];
        let draggedBlock = null;
        let blockPreviewY = 1050; // 下移方块组合区域
        let gameWidth = 750;
        let gameHeight = 1334;

        // 回合制系统变量
        let isPlayerTurn = true; // 当前是否为玩家回合
        let turnText; // 回合提示文本
        let enemyTurnDelay = 100; // 敌方回合延迟时间（0.1秒）

        // 炮塔系统变量
        let playerTowers = []; // 我方炮塔数组
        let enemyTowers = []; // 敌方炮塔数组
        let selectedTower = null; // 当前选中的炮塔
        let rangeDisplay = null; // 攻击范围显示
        let towerAttackInterval = 1500; // 炮塔攻击间隔（毫秒）

        // 无敌时间系统
        let invulnerableBlocks = []; // 无敌方块数组
        let invulnerableTime = 1000; // 2秒无敌时间

        // 胜利条件
        let winScore = 700;
        
        // 俄罗斯方块经典7种形状定义（包含旋转变体）
        const tetrisShapes = {
            // I形方块 (直线)
            I: [
                [[1, 1, 1, 1]],
                [[1], [1], [1], [1]]
            ],
            // O形方块 (正方形)
            O: [
                [[1, 1], [1, 1]]
            ],
            // T形方块
            T: [
                [[0, 1, 0], [1, 1, 1]],
                [[1, 0], [1, 1], [1, 0]],
                [[1, 1, 1], [0, 1, 0]],
                [[0, 1], [1, 1], [0, 1]]
            ],
            // S形方块
            S: [
                [[0, 1, 1], [1, 1, 0]],
                [[1, 0], [1, 1], [0, 1]]
            ],
            // Z形方块
            Z: [
                [[1, 1, 0], [0, 1, 1]],
                [[0, 1], [1, 1], [1, 0]]
            ],
            // J形方块
            J: [
                [[1, 0, 0], [1, 1, 1]],
                [[1, 1], [1, 0], [1, 0]],
                [[1, 1, 1], [0, 0, 1]],
                [[0, 1], [0, 1], [1, 1]]
            ],
            // L形方块
            L: [
                [[0, 0, 1], [1, 1, 1]],
                [[1, 0], [1, 0], [1, 1]],
                [[1, 1, 1], [1, 0, 0]],
                [[1, 1], [0, 1], [0, 1]]
            ]
        };

        // 将所有形状和旋转变体展开为一个数组
        const blockShapes = [];
        Object.keys(tetrisShapes).forEach(shapeType => {
            tetrisShapes[shapeType].forEach(rotation => {
                blockShapes.push(rotation);
            });
        });

        // 添加一些小方块用于填充
        blockShapes.push(
            [[1]], // 单个方块
            [[1, 1]], // 2个方块
            [[1], [1]], // 竖直2个方块
            [[1, 1, 1]], // 3个方块
            [[1], [1], [1]] // 竖直3个方块
        );

        function preload() {
            // Phaser会自动处理预加载
        }

        function create() {
            gameScene = this;

            // 加载最高分
            loadBestScore();

            // 创建方块纹理
            createBlockTextures();

            // 初始化网格
            initGrid();

            // 创建UI
            createUI();

            // 生成初始方块
            generateNewBlocks();

            // 设置输入事件
            setupInput();
        }

        function update(time, delta) {
            // 游戏主循环

            // 更新炮塔攻击
            updateTowerAttacks(delta);

            // 更新无敌时间
            updateInvulnerableBlocks(delta);

            // 检查胜利条件
            if (score >= winScore) {
                showVictory();
            }
        }

        // 更新无敌时间
        function updateInvulnerableBlocks(delta) {
            for (let i = invulnerableBlocks.length - 1; i >= 0; i--) {
                const invBlock = invulnerableBlocks[i];
                invBlock.timeLeft -= delta;

                // 无敌时间结束
                if (invBlock.timeLeft <= 0) {
                    // 移除无敌效果
                    if (grid[invBlock.row][invBlock.col].sprite) {
                        grid[invBlock.row][invBlock.col].sprite.clearTint();
                    }
                    invulnerableBlocks.splice(i, 1);
                }
            }
        }

        // 检查方块是否无敌
        function isBlockInvulnerable(row, col) {
            return invulnerableBlocks.some(invBlock =>
                invBlock.row === row && invBlock.col === col
            );
        }

        // 添加无敌时间
        function addInvulnerableTime(row, col) {
            invulnerableBlocks.push({
                row: row,
                col: col,
                timeLeft: invulnerableTime
            });

            // 无敌视觉效果（淡蓝色高亮，不闪烁）
            if (grid[row][col].sprite) {
                grid[row][col].sprite.setTint(0x87ceeb); // 淡蓝色表示无敌
            }
        }

        function createBlockTextures() {
            const graphics = gameScene.add.graphics();

            // 创建普通方块纹理（蓝色 - 我方）
            graphics.clear();
            graphics.fillStyle(0x3498db);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.lineStyle(2, 0x2980b9);
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.generateTexture('block', cellSize, cellSize);

            // 创建敌方方块纹理（红色）
            graphics.clear();
            graphics.fillStyle(0xe74c3c);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.lineStyle(2, 0xc0392b);
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.generateTexture('enemyBlock', cellSize, cellSize);

            // 创建网格背景纹理
            graphics.clear();
            graphics.fillStyle(0x34495e);
            graphics.fillRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
            graphics.lineStyle(1, 0x2c3e50);
            graphics.strokeRoundedRect(0, 0, cellSize-1, cellSize-1, 3);
            graphics.generateTexture('gridCell', cellSize, cellSize);

            // 创建高亮方块纹理
            graphics.clear();
            graphics.fillStyle(0xf39c12);
            graphics.fillRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.lineStyle(2, 0xe67e22);
            graphics.strokeRoundedRect(0, 0, cellSize-2, cellSize-2, 5);
            graphics.generateTexture('highlightBlock', cellSize, cellSize);

            // 创建我方炮塔纹理
            graphics.clear();
            graphics.fillStyle(0x27ae60);
            graphics.fillCircle(cellSize/2, cellSize/2, cellSize/2 - 2);
            graphics.lineStyle(3, 0x229954);
            graphics.strokeCircle(cellSize/2, cellSize/2, cellSize/2 - 2);
            // 添加炮管
            graphics.lineStyle(4, 0x1e8449);
            graphics.lineBetween(cellSize/2, cellSize/2, cellSize/2, cellSize/4);
            graphics.generateTexture('playerTower', cellSize, cellSize);

            // 创建敌方炮塔纹理
            graphics.clear();
            graphics.fillStyle(0x8e44ad);
            graphics.fillCircle(cellSize/2, cellSize/2, cellSize/2 - 2);
            graphics.lineStyle(3, 0x7d3c98);
            graphics.strokeCircle(cellSize/2, cellSize/2, cellSize/2 - 2);
            // 添加炮管
            graphics.lineStyle(4, 0x6c3483);
            graphics.lineBetween(cellSize/2, cellSize/2, cellSize/2, cellSize/4);
            graphics.generateTexture('enemyTower', cellSize, cellSize);

            graphics.destroy();
        }

        function initGrid() {
            // 初始化游戏网格数据
            grid = [];
            for (let row = 0; row < gridSize; row++) {
                grid[row] = [];
                for (let col = 0; col < gridSize; col++) {
                    grid[row][col] = {
                        filled: false,
                        sprite: null,
                        isEnemy: false, // 标记是否为敌方方块
                        health: 2, // 方块血量，需要攻击2次才能摧毁
                        maxHealth: 2,
                        hasTower: false // 标记是否已被炮塔占用
                    };
                }
            }
            
            // 创建网格视觉效果
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    const x = gridOffsetX + col * cellSize;
                    const y = gridOffsetY + row * cellSize;
                    const cell = gameScene.add.image(x, y, 'gridCell');
                    cell.setOrigin(0, 0);
                }
            }
        }

        function createUI() {
            // 固定字体大小
            const titleFontSize = '45px';
            const scoreFontSize = '32px';
            const bestScoreFontSize = '26px';
            const instructionFontSize = '22px';

            // 标题
            const title = gameScene.add.text(375, 50, 'Block Blast', {
                fontSize: titleFontSize,
                fontWeight: 'bold',
                fill: '#ecf0f1',
                align: 'center'
            });
            title.setOrigin(0.5, 0);

            // 添加标题发光效果
            title.setShadow(2, 2, '#34495e', 2, true, true);

            // 得分显示
            scoreText = gameScene.add.text(375, 110, '🔥: 0', {
                fontSize: scoreFontSize,
                fill: '#ecf0f1',
                align: 'center'
            });
            scoreText.setOrigin(0.5, 0);
            scoreText.setShadow(1, 1, '#2c3e50', 1, true, true);

            // 最高分显示
            bestScoreText = gameScene.add.text(375, 150, '👑: ' + bestScore, {
                fontSize: bestScoreFontSize,
                fill: '#f39c12',
                align: 'center'
            });
            bestScoreText.setOrigin(0.5, 0);
            bestScoreText.setShadow(1, 1, '#2c3e50', 1, true, true);

            // 回合提示文本
            turnText = gameScene.add.text(375, 180, 'Your Turn', {
                fontSize: '24px',
                fill: '#2ecc71',
                align: 'center',
                fontWeight: 'bold'
            });
            turnText.setOrigin(0.5, 0);
            turnText.setShadow(1, 1, '#2c3e50', 1, true, true);

            // 下一个方块区域背景
            const previewBg = gameScene.add.graphics();
            previewBg.fillStyle(0x34495e, 0.8);
            const bgWidth = 675; // gameWidth * 0.9
            const bgHeight = 280; // 增加高度
            const bgX = 37.5; // (750 - 675) / 2
            const bgY = 900; // blockPreviewY - 70，下移到1050-70=980
            previewBg.fillRoundedRect(bgX, bgY, bgWidth, bgHeight, 10);
            previewBg.lineStyle(2, 0x2c3e50);
            previewBg.strokeRoundedRect(bgX, bgY, bgWidth, bgHeight, 10);

            // 添加游戏说明文本
            const instructionText = gameScene.add.text(375, 930, 'Drag blocks to the grid', {
                fontSize: instructionFontSize,
                fill: '#bdc3c7',
                align: 'center'
            });
            instructionText.setOrigin(0.5, 0);
        }

        function generateNewBlocks() {
            // 清除当前方块
            currentBlocks.forEach(blockGroup => {
                if (blockGroup.container) {
                    blockGroup.container.destroy();
                }
            });

            currentBlocks = [];

            // 生成3个新方块
            for (let i = 0; i < 3; i++) {
                const shapeIndex = Phaser.Math.Between(0, blockShapes.length - 1);
                const shape = blockShapes[shapeIndex];
                const blockGroup = createBlockGroup(shape, i);
                currentBlocks.push(blockGroup);
            }

            console.log('Generated new blocks:', currentBlocks.length);
        }

        // 智能敌方AI放置方块
        function placeEnemyBlock() {
            if (isPlayerTurn) return; // 只在敌方回合执行

            const shapeIndex = Phaser.Math.Between(0, blockShapes.length - 1);
            const shape = blockShapes[shapeIndex];

            // 快速选择位置：简单随机放置
            const possiblePositions = [];
            for (let row = 0; row <= gridSize - shape.length; row++) {
                for (let col = 0; col <= gridSize - shape[0].length; col++) {
                    if (canPlaceBlockAt(shape, row, col)) {
                        possiblePositions.push({ row, col });
                    }
                }
            }

            let bestPosition = null;
            if (possiblePositions.length > 0) {
                bestPosition = possiblePositions[Phaser.Math.Between(0, possiblePositions.length - 1)];
            }

            if (bestPosition) {
                // 在网格中放置敌方方块
                for (let row = 0; row < shape.length; row++) {
                    for (let col = 0; col < shape[row].length; col++) {
                        if (shape[row][col] === 1) {
                            const gridRow = bestPosition.row + row;
                            const gridCol = bestPosition.col + col;

                            if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                                grid[gridRow][gridCol].filled = true;
                                grid[gridRow][gridCol].isEnemy = true; // 标记为敌方方块
                                grid[gridRow][gridCol].health = 2; // 设置血量
                                grid[gridRow][gridCol].maxHealth = 2;

                                // 创建敌方方块精灵
                                const x = gridOffsetX + gridCol * cellSize;
                                const y = gridOffsetY + gridRow * cellSize;
                                const blockSprite = gameScene.add.image(x, y, 'enemyBlock');
                                blockSprite.setOrigin(0, 0);

                                // 添加放置动画
                                blockSprite.setScale(0);
                                gameScene.tweens.add({
                                    targets: blockSprite,
                                    scaleX: 1,
                                    scaleY: 1,
                                    duration: 300,
                                    ease: 'Back.easeOut',
                                    delay: (row + col) * 50
                                });

                                grid[gridRow][gridCol].sprite = blockSprite;

                                // 添加无敌时间
                                addInvulnerableTime(gridRow, gridCol);
                            }
                        }
                    }
                }

                // 放置后检查消除和炮塔生成
                setTimeout(() => {
                    checkAndClearLines();
                    checkForEnemyTowers(); // 检查敌方炮塔生成

                    // 切换回玩家回合
                    switchToPlayerTurn();
                }, 500);
            } else {
                // 敌方无法放置，直接切换回玩家回合
                switchToPlayerTurn();
            }
        }



        // 切换到敌方回合
        function switchToEnemyTurn() {
            isPlayerTurn = false;
            turnText.setText('Enemy Turn');
            turnText.setFill('#e74c3c');

            // 延迟执行敌方回合
            setTimeout(() => {
                placeEnemyBlock();
            }, enemyTurnDelay);
        }

        // 切换到玩家回合
        function switchToPlayerTurn() {
            isPlayerTurn = true;
            turnText.setText('Your Turn');
            turnText.setFill('#2ecc71');
        }

        // 检查并生成炮塔（4个连续方块可生成炮塔）
        function checkForTowers(isEnemy = false) {
            const towers = isEnemy ? enemyTowers : playerTowers;
            const newTowers = [];

            // 检查2x2方块组合
            for (let row = 0; row < gridSize - 1; row++) {
                for (let col = 0; col < gridSize - 1; col++) {
                    if (canFormTower(row, col, isEnemy)) {
                        // 检查是否已经有炮塔在这个位置
                        const existingTower = towers.find(t =>
                            t.row === row && t.col === col && t.size === 2
                        );

                        if (!existingTower) {
                            const tower = createTower(row, col, 2, isEnemy);
                            newTowers.push(tower);
                        }
                    }
                }
            }

            towers.push(...newTowers);
            return newTowers.length > 0;
        }

        function checkForEnemyTowers() {
            checkForTowers(true);
        }

        function checkForPlayerTowers() {
            checkForTowers(false);
        }

        // 检查是否可以在指定位置形成炮塔
        function canFormTower(startRow, startCol, isEnemy) {
            for (let row = 0; row < 2; row++) {
                for (let col = 0; col < 2; col++) {
                    const gridRow = startRow + row;
                    const gridCol = startCol + col;

                    if (!grid[gridRow][gridCol].filled ||
                        grid[gridRow][gridCol].isEnemy !== isEnemy ||
                        grid[gridRow][gridCol].hasTower) { // 检查是否已被炮塔占用
                        return false;
                    }
                }
            }
            return true;
        }

        // 创建炮塔
        function createTower(row, col, size, isEnemy) {
            const centerX = gridOffsetX + (col + size/2) * cellSize;
            const centerY = gridOffsetY + (row + size/2) * cellSize;

            const towerSprite = gameScene.add.image(centerX, centerY, isEnemy ? 'enemyTower' : 'playerTower');
            towerSprite.setOrigin(0.5, 0.5);
            towerSprite.setDepth(20);
            towerSprite.setScale(1.5); // 炮塔比普通方块大一些

            // 标记炮塔占用的格子
            for (let r = 0; r < size; r++) {
                for (let c = 0; c < size; c++) {
                    grid[row + r][col + c].hasTower = true;
                }
            }

            // 添加生成动画
            towerSprite.setScale(0);
            gameScene.tweens.add({
                targets: towerSprite,
                scaleX: 1.5,
                scaleY: 1.5,
                duration: 500,
                ease: 'Back.easeOut'
            });

            const tower = {
                row: row,
                col: col,
                size: size,
                sprite: towerSprite,
                isEnemy: isEnemy,
                lastAttackTime: 0,
                attackRange: 3 // 攻击范围增加到3格
            };

            // 添加点击事件
            towerSprite.setInteractive();
            towerSprite.on('pointerdown', () => {
                selectTower(tower);
            });

            return tower;
        }

        // 选择炮塔并显示攻击范围
        function selectTower(tower) {
            // 如果点击的是已选中的炮塔，则取消选择
            if (selectedTower === tower) {
                selectedTower.sprite.clearTint();
                selectedTower = null;
                clearRangeDisplay();
                return;
            }

            // 清除之前的选择
            if (selectedTower) {
                selectedTower.sprite.clearTint();
            }
            clearRangeDisplay();

            selectedTower = tower;

            // 高亮选中的炮塔
            tower.sprite.setTint(0xffff00);

            // 显示攻击范围
            showAttackRange(tower);
        }

        // 显示攻击范围
        function showAttackRange(tower) {
            if (rangeDisplay) {
                rangeDisplay.destroy();
            }

            rangeDisplay = gameScene.add.graphics();
            rangeDisplay.setDepth(15);

            // 计算攻击范围
            const range = tower.attackRange;
            const startRow = Math.max(0, tower.row - range);
            const endRow = Math.min(gridSize - 1, tower.row + tower.size - 1 + range);
            const startCol = Math.max(0, tower.col - range);
            const endCol = Math.min(gridSize - 1, tower.col + tower.size - 1 + range);

            // 绘制攻击范围
            for (let row = startRow; row <= endRow; row++) {
                for (let col = startCol; col <= endCol; col++) {
                    // 排除炮塔自身占据的格子
                    if (row >= tower.row && row < tower.row + tower.size &&
                        col >= tower.col && col < tower.col + tower.size) {
                        continue;
                    }

                    const x = gridOffsetX + col * cellSize;
                    const y = gridOffsetY + row * cellSize;

                    // 更明显的攻击范围显示
                    rangeDisplay.fillStyle(0xff4444, 0.4); // 增加透明度和亮度
                    rangeDisplay.fillRect(x, y, cellSize, cellSize);
                    rangeDisplay.lineStyle(2, 0xff0000, 0.8); // 增加边框粗细和透明度
                    rangeDisplay.strokeRect(x, y, cellSize, cellSize);
                }
            }

            // 添加攻击范围的外边框
            const outerX = gridOffsetX + startCol * cellSize;
            const outerY = gridOffsetY + startRow * cellSize;
            const outerWidth = (endCol - startCol + 1) * cellSize;
            const outerHeight = (endRow - startRow + 1) * cellSize;

            rangeDisplay.lineStyle(3, 0xff0000, 1.0);
            rangeDisplay.strokeRect(outerX, outerY, outerWidth, outerHeight);
        }

        // 清除攻击范围显示
        function clearRangeDisplay() {
            if (rangeDisplay) {
                rangeDisplay.destroy();
                rangeDisplay = null;
            }
        }

        // 更新炮塔攻击
        function updateTowerAttacks(delta) {
            // 更新我方炮塔攻击
            playerTowers.forEach(tower => {
                tower.lastAttackTime += delta;
                if (tower.lastAttackTime >= towerAttackInterval) {
                    performTowerAttack(tower);
                    tower.lastAttackTime = 0;
                }
            });

            // 更新敌方炮塔攻击
            enemyTowers.forEach(tower => {
                tower.lastAttackTime += delta;
                if (tower.lastAttackTime >= towerAttackInterval) {
                    performTowerAttack(tower);
                    tower.lastAttackTime = 0;
                }
            });
        }

        // 执行炮塔攻击
        function performTowerAttack(tower) {
            const targets = findAttackTargets(tower);

            if (targets.length > 0) {
                // 优先攻击敌方炮塔
                let target = targets.find(t => t.isTower);
                if (!target) {
                    // 如果没有敌方炮塔，攻击普通方块
                    target = targets[0];
                }

                // 执行攻击
                attackTarget(tower, target);
            }
        }

        // 寻找攻击目标
        function findAttackTargets(tower) {
            const targets = [];
            const range = tower.attackRange;
            const startRow = Math.max(0, tower.row - range);
            const endRow = Math.min(gridSize - 1, tower.row + tower.size - 1 + range);
            const startCol = Math.max(0, tower.col - range);
            const endCol = Math.min(gridSize - 1, tower.col + tower.size - 1 + range);

            // 寻找范围内的敌方目标
            for (let row = startRow; row <= endRow; row++) {
                for (let col = startCol; col <= endCol; col++) {
                    // 排除炮塔自身占据的格子
                    if (row >= tower.row && row < tower.row + tower.size &&
                        col >= tower.col && col < tower.col + tower.size) {
                        continue;
                    }

                    // 检查是否有敌方方块
                    if (grid[row][col].filled && grid[row][col].isEnemy !== tower.isEnemy) {
                        // 检查是否是敌方炮塔
                        const enemyTowerList = tower.isEnemy ? playerTowers : enemyTowers;
                        const isTower = enemyTowerList.some(t =>
                            row >= t.row && row < t.row + t.size &&
                            col >= t.col && col < t.col + t.size
                        );

                        targets.push({
                            row: row,
                            col: col,
                            isTower: isTower
                        });
                    }
                }
            }

            return targets;
        }

        // 攻击目标
        function attackTarget(tower, target) {
            const targetX = gridOffsetX + target.col * cellSize + cellSize/2;
            const targetY = gridOffsetY + target.row * cellSize + cellSize/2;
            const towerX = tower.sprite.x;
            const towerY = tower.sprite.y;

            // 创建攻击特效（激光束）
            const laser = gameScene.add.graphics();
            laser.setDepth(25);
            laser.lineStyle(3, tower.isEnemy ? 0x8e44ad : 0x27ae60);
            laser.lineBetween(towerX, towerY, targetX, targetY);

            // 激光消失动画
            gameScene.tweens.add({
                targets: laser,
                alpha: 0,
                duration: 200,
                onComplete: () => laser.destroy()
            });

            // 攻击目标方块
            attackBlock(target.row, target.col);

            // 攻击音效（如果有的话）
            if (navigator.vibrate) {
                navigator.vibrate(30);
            }
        }

        // 攻击方块（减少血量）
        function attackBlock(row, col) {
            if (grid[row][col].sprite && grid[row][col].filled) {
                // 检查是否无敌
                if (isBlockInvulnerable(row, col)) {
                    // 无敌方块显示防护效果
                    createShieldEffect(
                        gridOffsetX + col * cellSize,
                        gridOffsetY + row * cellSize
                    );
                    return;
                }

                // 减少血量
                grid[row][col].health--;

                // 创建受伤效果
                createDamageEffect(
                    gridOffsetX + col * cellSize,
                    gridOffsetY + row * cellSize
                );

                // 根据血量改变方块颜色
                const healthRatio = grid[row][col].health / grid[row][col].maxHealth;
                if (healthRatio <= 0.5) {
                    grid[row][col].sprite.setTint(0x888888); // 受伤变暗
                }

                // 如果血量归零，销毁方块
                if (grid[row][col].health <= 0) {
                    destroyBlock(row, col);
                }
            }
        }

        // 销毁方块
        function destroyBlock(row, col) {
            if (grid[row][col].sprite) {
                // 爆炸效果
                createExplosionEffect(
                    gridOffsetX + col * cellSize,
                    gridOffsetY + row * cellSize
                );

                // 销毁方块
                grid[row][col].sprite.destroy();
                grid[row][col].sprite = null;
                grid[row][col].filled = false;
                grid[row][col].health = 2;
                grid[row][col].hasTower = false;

                const wasEnemy = grid[row][col].isEnemy;
                grid[row][col].isEnemy = false;

                // 如果销毁的是敌方方块，给玩家加分
                if (wasEnemy) {
                    updateScore(5);
                }

                // 检查是否需要移除炮塔
                checkTowerDestruction(row, col);
            }
        }

        // 检查炮塔是否需要被移除
        function checkTowerDestruction(destroyedRow, destroyedCol) {
            // 检查我方炮塔
            for (let i = playerTowers.length - 1; i >= 0; i--) {
                const tower = playerTowers[i];
                if (destroyedRow >= tower.row && destroyedRow < tower.row + tower.size &&
                    destroyedCol >= tower.col && destroyedCol < tower.col + tower.size) {
                    // 炮塔的基础方块被破坏，移除炮塔
                    tower.sprite.destroy();
                    playerTowers.splice(i, 1);

                    // 清除炮塔占用标记
                    for (let r = 0; r < tower.size; r++) {
                        for (let c = 0; c < tower.size; c++) {
                            const row = tower.row + r;
                            const col = tower.col + c;
                            if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
                                grid[row][col].hasTower = false;
                            }
                        }
                    }

                    if (selectedTower === tower) {
                        selectedTower = null;
                        clearRangeDisplay();
                    }
                }
            }

            // 检查敌方炮塔
            for (let i = enemyTowers.length - 1; i >= 0; i--) {
                const tower = enemyTowers[i];
                if (destroyedRow >= tower.row && destroyedRow < tower.row + tower.size &&
                    destroyedCol >= tower.col && destroyedCol < tower.col + tower.size) {
                    // 炮塔的基础方块被破坏，移除炮塔
                    tower.sprite.destroy();
                    enemyTowers.splice(i, 1);

                    // 清除炮塔占用标记
                    for (let r = 0; r < tower.size; r++) {
                        for (let c = 0; c < tower.size; c++) {
                            const row = tower.row + r;
                            const col = tower.col + c;
                            if (row >= 0 && row < gridSize && col >= 0 && col < gridSize) {
                                grid[row][col].hasTower = false;
                            }
                        }
                    }
                }
            }
        }

        // 创建受伤效果
        function createDamageEffect(x, y) {
            for (let i = 0; i < 3; i++) {
                const particle = gameScene.add.circle(
                    x + cellSize/2,
                    y + cellSize/2,
                    Phaser.Math.Between(1, 3),
                    0xff6b6b
                );

                const angle = Math.random() * Math.PI * 2;
                const speed = Phaser.Math.Between(20, 40);

                gameScene.tweens.add({
                    targets: particle,
                    x: particle.x + Math.cos(angle) * speed,
                    y: particle.y + Math.sin(angle) * speed,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => particle.destroy()
                });
            }
        }

        // 创建防护效果
        function createShieldEffect(x, y) {
            const shield = gameScene.add.circle(
                x + cellSize/2,
                y + cellSize/2,
                cellSize/2,
                0x00ffff,
                0.5
            );

            gameScene.tweens.add({
                targets: shield,
                scaleX: 1.5,
                scaleY: 1.5,
                alpha: 0,
                duration: 300,
                onComplete: () => shield.destroy()
            });
        }

        // 显示胜利界面
        function showVictory() {
            // 更新最高分
            updateBestScore();

            // 创建胜利遮罩
            const overlay = gameScene.add.graphics();
            overlay.fillStyle(0x000000, 0.7);
            overlay.fillRect(0, 0, gameWidth, gameHeight);
            overlay.setDepth(200);

            // 胜利文本
            const victoryText = gameScene.add.text(375, 467, 'Victory!', {
                fontSize: '80px',
                fontWeight: 'bold',
                fill: '#f1c40f',
                align: 'center'
            });
            victoryText.setOrigin(0.5);
            victoryText.setDepth(201);

            // 最终得分
            const finalScoreText = gameScene.add.text(375, 600, 'Final Score: ' + score, {
                fontSize: '40px',
                fill: '#ecf0f1',
                align: 'center'
            });
            finalScoreText.setOrigin(0.5);
            finalScoreText.setDepth(201);

            // 重新开始按钮
            const restartButton = gameScene.add.text(375, 733, 'Play Again', {
                fontSize: '40px',
                fill: '#3498db',
                align: 'center',
                backgroundColor: '#2c3e50',
                padding: { x: 37, y: 20 }
            });
            restartButton.setOrigin(0.5);
            restartButton.setDepth(201);
            restartButton.setInteractive();

            restartButton.on('pointerdown', function() {
                // 移除胜利界面
                overlay.destroy();
                victoryText.destroy();
                finalScoreText.destroy();
                restartButton.destroy();

                // 重新开始游戏
                restartGame();
            });

            restartButton.on('pointerover', function() {
                this.setFill('#5dade2');
            });

            restartButton.on('pointerout', function() {
                this.setFill('#3498db');
            });
        }

        // 创建爆炸效果
        function createExplosionEffect(x, y) {
            for (let i = 0; i < 6; i++) {
                const particle = gameScene.add.circle(
                    x + cellSize/2,
                    y + cellSize/2,
                    Phaser.Math.Between(2, 4),
                    0xffd700
                );

                const angle = (i / 6) * Math.PI * 2;
                const speed = Phaser.Math.Between(30, 60);

                gameScene.tweens.add({
                    targets: particle,
                    x: particle.x + Math.cos(angle) * speed,
                    y: particle.y + Math.sin(angle) * speed,
                    alpha: 0,
                    scaleX: 0,
                    scaleY: 0,
                    duration: 400,
                    onComplete: () => particle.destroy()
                });
            }
        }



        function createBlockGroup(shape, index) {
            // 固定方块预览位置 - 3个方块平均分布
            const sectionWidth = 225; // 675 / 3 = 225
            const startX = 37.5 + sectionWidth * index + sectionWidth / 2; // 112.5, 337.5, 562.5
            const startY = blockPreviewY;

            const container = gameScene.add.container(startX, startY);
            const blocks = [];

            // 计算方块组的中心偏移，使其在容器中居中
            const shapeWidth = shape[0].length * cellSize;
            const shapeHeight = shape.length * cellSize;
            const offsetX = -shapeWidth / 2;
            const offsetY = -shapeHeight / 2;

            // 创建方块组
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const block = gameScene.add.image(
                            offsetX + col * cellSize,
                            offsetY + row * cellSize,
                            'block'
                        );
                        block.setOrigin(0, 0);

                        // 给每个方块添加交互事件
                        block.setInteractive();
                        block.blockGroupIndex = index;
                        block.shapeRow = row;
                        block.shapeCol = col;

                        // 添加悬停效果
                        block.on('pointerover', function() {
                            if (currentBlocks[index] && !currentBlocks[index].used && !currentBlocks[index].isDragging) {
                                this.setTint(0xaaaaaa);
                                gameScene.input.setDefaultCursor('pointer');
                            }
                        });

                        block.on('pointerout', function() {
                            if (currentBlocks[index] && !currentBlocks[index].used) {
                                this.clearTint();
                                gameScene.input.setDefaultCursor('default');
                            }
                        });

                        container.add(block);
                        blocks.push(block);
                    }
                }
            }

            // 设置容器大小但不设置拖拽（改为通过单个方块处理）
            container.setSize(shape[0].length * cellSize, shape.length * cellSize);

            return {
                container: container,
                shape: shape,
                blocks: blocks,
                originalX: startX,
                originalY: startY,
                used: false,
                isDragging: false
            };
        }

        function setupInput() {
            let dragStartPos = { x: 0, y: 0 };
            let dragOffset = { x: 0, y: 0 };

            // 鼠标/触摸按下
            gameScene.input.on('pointerdown', function (pointer, currentlyOver) {
                // 检查是否点击了炮塔（炮塔点击事件会自动处理）
                let clickedTower = false;
                if (currentlyOver.length > 0) {
                    const gameObject = currentlyOver[0];
                    // 检查是否点击了炮塔
                    const allTowers = [...playerTowers, ...enemyTowers];
                    clickedTower = allTowers.some(tower => tower.sprite === gameObject);
                }

                if (currentlyOver.length > 0 && !clickedTower && isPlayerTurn) {
                    const gameObject = currentlyOver[0];

                    // 检查是否点击了方块
                    if (gameObject.blockGroupIndex !== undefined) {
                        const blockGroup = currentBlocks[gameObject.blockGroupIndex];

                        if (blockGroup && !blockGroup.used) {
                            // 检查是否在方块预览区域内
                            if (pointer.y >= 980 && pointer.y <= 1260) { // 背景区域范围，下移到新位置
                                draggedBlock = blockGroup;
                                draggedBlock.isDragging = true;

                                // 记录拖拽开始位置和偏移
                                dragStartPos.x = pointer.x;
                                dragStartPos.y = pointer.y;

                                // 计算点击位置相对于容器的偏移
                                dragOffset.x = pointer.x - draggedBlock.container.x;
                                dragOffset.y = pointer.y - draggedBlock.container.y;

                                // 视觉效果
                                draggedBlock.container.setScale(1.1);
                                draggedBlock.container.setDepth(100);
                                draggedBlock.container.setAlpha(0.8);

                                // 添加拖拽开始的震动效果
                                gameScene.tweens.add({
                                    targets: draggedBlock.container,
                                    scaleX: 1.15,
                                    scaleY: 1.15,
                                    duration: 100,
                                    yoyo: true,
                                    ease: 'Power2'
                                });

                                // 清除所有方块的悬停效果
                                draggedBlock.blocks.forEach(block => {
                                    block.clearTint();
                                });

                                // 触觉反馈（如果支持）
                                if (navigator.vibrate) {
                                    navigator.vibrate(50);
                                }
                            }
                        }
                    }
                } else {
                    // 点击空白区域，取消炮塔选择
                    if (selectedTower) {
                        selectedTower.sprite.clearTint();
                        selectedTower = null;
                        clearRangeDisplay();
                    }
                }
            });

            // 鼠标/触摸移动
            gameScene.input.on('pointermove', function (pointer) {
                if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                    // 更新容器位置
                    let newX = pointer.x - dragOffset.x;
                    let newY = pointer.y - dragOffset.y;

                    // 边界检测，防止拖拽到屏幕外
                    const shapeWidth = draggedBlock.shape[0].length * cellSize;
                    const shapeHeight = draggedBlock.shape.length * cellSize;

                    newX = Math.max(-shapeWidth/2, Math.min(gameWidth - shapeWidth/2, newX));
                    newY = Math.max(0, Math.min(gameHeight - shapeHeight, newY));

                    // 磁性对齐效果：当接近网格时自动吸附
                    // 计算方块的实际左上角位置（考虑居中偏移）
                    const actualX = newX - shapeWidth / 2;
                    const actualY = newY - shapeHeight / 2;
                    const gridPos = screenToGrid(actualX, actualY);
                    const alignedPos = gridToScreen(gridPos.row, gridPos.col);
                    const snapDistance = cellSize * 0.3; // 吸附距离

                    if (Math.abs(actualX - alignedPos.x) < snapDistance &&
                        Math.abs(actualY - alignedPos.y) < snapDistance &&
                        canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                        // 在吸附范围内且可以放置，则吸附到网格中心
                        newX = alignedPos.x + shapeWidth / 2;
                        newY = alignedPos.y + shapeHeight / 2;
                    }

                    draggedBlock.container.x = newX;
                    draggedBlock.container.y = newY;

                    // 检查是否可以放置并显示预览
                    checkPlacementPreview(newX, newY);
                }
            });

            // 鼠标/触摸释放
            gameScene.input.on('pointerup', function (pointer) {
                if (draggedBlock && draggedBlock.isDragging && !draggedBlock.used) {
                    const canPlace = tryPlaceBlock(draggedBlock.container.x, draggedBlock.container.y);

                    if (canPlace) {
                        placeBlock();

                        // 保存对当前拖拽方块的引用，避免异步回调中的null引用
                        const currentDraggedBlock = draggedBlock;
                        currentDraggedBlock.used = true;

                        // 放置成功的视觉反馈
                        gameScene.tweens.add({
                            targets: currentDraggedBlock.container,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 200,
                            ease: 'Power2.easeIn',
                            onComplete: () => {
                                if (currentDraggedBlock.container) {
                                    currentDraggedBlock.container.setVisible(false);
                                }
                            }
                        });

                        // 触觉反馈
                        if (navigator.vibrate) {
                            navigator.vibrate([30, 10, 30]);
                        }

                        // 检查消除和炮塔生成
                        setTimeout(() => {
                            checkAndClearLines();
                            checkForPlayerTowers(); // 检查我方炮塔生成

                            // 检查是否所有方块都已使用
                            if (currentBlocks.every(b => b.used)) {
                                setTimeout(() => {
                                    generateNewBlocks();
                                    // 切换到敌方回合
                                    switchToEnemyTurn();
                                }, 300);
                            } else {
                                // 如果还有方块未使用，也切换到敌方回合
                                switchToEnemyTurn();
                            }
                        }, 200);
                    } else {
                        // 返回原位置
                        const currentDraggedBlock = draggedBlock;
                        gameScene.tweens.add({
                            targets: currentDraggedBlock.container,
                            x: currentDraggedBlock.originalX,
                            y: currentDraggedBlock.originalY,
                            duration: 300,
                            ease: 'Back.easeOut'
                        });
                    }

                    // 恢复视觉效果
                    if (draggedBlock && draggedBlock.container) {
                        draggedBlock.container.setScale(1);
                        draggedBlock.container.setDepth(0);
                        draggedBlock.container.setAlpha(1);
                        draggedBlock.isDragging = false;
                    }
                    clearPlacementPreview();
                }

                draggedBlock = null;
            });
        }

        function checkPlacementPreview(x, y) {
            clearPlacementPreview();

            if (!draggedBlock) return;

            // 计算方块形状的实际左上角位置（考虑居中偏移）
            const shapeWidth = draggedBlock.shape[0].length * cellSize;
            const shapeHeight = draggedBlock.shape.length * cellSize;
            const actualX = x - shapeWidth / 2;
            const actualY = y - shapeHeight / 2;

            // 使用实际左上角位置来计算网格位置
            const gridPos = screenToGrid(actualX, actualY);

            if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                showPlacementPreview(draggedBlock.shape, gridPos.row, gridPos.col);
                return true;
            }
            return false;
        }

        function clearPlacementPreview() {
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    if (grid[row][col].previewSprite) {
                        grid[row][col].previewSprite.destroy();
                        grid[row][col].previewSprite = null;
                    }
                }
            }
        }

        function showPlacementPreview(shape, startRow, startCol) {
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = startRow + row;
                        const gridCol = startCol + col;

                        if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                            const x = gridOffsetX + gridCol * cellSize;
                            const y = gridOffsetY + gridRow * cellSize;

                            const preview = gameScene.add.image(x, y, 'highlightBlock');
                            preview.setOrigin(0, 0);
                            preview.setAlpha(0.6);
                            grid[gridRow][gridCol].previewSprite = preview;
                        }
                    }
                }
            }
        }

        function screenToGrid(screenX, screenY) {
            // 添加半个格子的偏移来改善对齐
            const col = Math.round((screenX - gridOffsetX) / cellSize);
            const row = Math.round((screenY - gridOffsetY) / cellSize);
            return { row, col };
        }

        function gridToScreen(row, col) {
            return {
                x: gridOffsetX + col * cellSize,
                y: gridOffsetY + row * cellSize
            };
        }

        function canPlaceBlockAt(shape, startRow, startCol) {
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = startRow + row;
                        const gridCol = startCol + col;

                        // 检查边界
                        if (gridRow < 0 || gridRow >= gridSize || gridCol < 0 || gridCol >= gridSize) {
                            return false;
                        }

                        // 检查是否已被占用
                        if (grid[gridRow][gridCol].filled) {
                            return false;
                        }
                    }
                }
            }
            return true;
        }

        function tryPlaceBlock(screenX, screenY) {
            if (!draggedBlock || !draggedBlock.container) return false;

            // 计算方块形状的实际左上角位置（考虑居中偏移）
            const shapeWidth = draggedBlock.shape[0].length * cellSize;
            const shapeHeight = draggedBlock.shape.length * cellSize;
            const actualX = screenX - shapeWidth / 2;
            const actualY = screenY - shapeHeight / 2;

            const gridPos = screenToGrid(actualX, actualY);

            // 检查是否可以在计算出的网格位置放置方块
            if (canPlaceBlockAt(draggedBlock.shape, gridPos.row, gridPos.col)) {
                // 如果可以放置，将容器位置对齐到网格中心
                const alignedPos = gridToScreen(gridPos.row, gridPos.col);
                draggedBlock.container.x = alignedPos.x + shapeWidth / 2;
                draggedBlock.container.y = alignedPos.y + shapeHeight / 2;
                return true;
            }

            return false;
        }

        function placeBlock() {
            if (!draggedBlock || !draggedBlock.container) return;

            // 计算方块形状的实际左上角位置（考虑居中偏移）
            const shapeWidth = draggedBlock.shape[0].length * cellSize;
            const shapeHeight = draggedBlock.shape.length * cellSize;
            const actualX = draggedBlock.container.x - shapeWidth / 2;
            const actualY = draggedBlock.container.y - shapeHeight / 2;

            const gridPos = screenToGrid(actualX, actualY);
            const shape = draggedBlock.shape;

            // 在网格中放置我方方块
            for (let row = 0; row < shape.length; row++) {
                for (let col = 0; col < shape[row].length; col++) {
                    if (shape[row][col] === 1) {
                        const gridRow = gridPos.row + row;
                        const gridCol = gridPos.col + col;

                        if (gridRow >= 0 && gridRow < gridSize && gridCol >= 0 && gridCol < gridSize) {
                            grid[gridRow][gridCol].filled = true;
                            grid[gridRow][gridCol].isEnemy = false; // 标记为我方方块
                            grid[gridRow][gridCol].health = 2; // 设置血量
                            grid[gridRow][gridCol].maxHealth = 2;

                            // 创建我方方块精灵
                            const x = gridOffsetX + gridCol * cellSize;
                            const y = gridOffsetY + gridRow * cellSize;
                            const blockSprite = gameScene.add.image(x, y, 'block');
                            blockSprite.setOrigin(0, 0);

                            // 添加放置动画
                            blockSprite.setScale(0);
                            gameScene.tweens.add({
                                targets: blockSprite,
                                scaleX: 1,
                                scaleY: 1,
                                duration: 200,
                                ease: 'Back.easeOut',
                                delay: (row + col) * 50 // 错开动画时间
                            });

                            grid[gridRow][gridCol].sprite = blockSprite;

                            // 添加无敌时间
                            addInvulnerableTime(gridRow, gridCol);
                        }
                    }
                }
            }
        }

        function checkAndClearLines() {
            const linesToClear = [];
            let linesCleared = 0;

            // 检查行（只检查我方方块）
            for (let row = 0; row < gridSize; row++) {
                let fullPlayerRow = true;
                let hasPlayerBlocks = false;

                for (let col = 0; col < gridSize; col++) {
                    if (!grid[row][col].filled || grid[row][col].isEnemy) {
                        fullPlayerRow = false;
                    } else {
                        hasPlayerBlocks = true;
                    }
                }

                if (fullPlayerRow && hasPlayerBlocks) {
                    linesToClear.push({ type: 'row', index: row });
                }
            }

            // 检查列（只检查我方方块）
            for (let col = 0; col < gridSize; col++) {
                let fullPlayerCol = true;
                let hasPlayerBlocks = false;

                for (let row = 0; row < gridSize; row++) {
                    if (!grid[row][col].filled || grid[row][col].isEnemy) {
                        fullPlayerCol = false;
                    } else {
                        hasPlayerBlocks = true;
                    }
                }

                if (fullPlayerCol && hasPlayerBlocks) {
                    linesToClear.push({ type: 'col', index: col });
                }
            }

            // 清除满行/满列（只清除我方方块）
            linesToClear.forEach(line => {
                if (line.type === 'row') {
                    clearPlayerRow(line.index);
                } else {
                    clearPlayerColumn(line.index);
                }
                linesCleared++;
            });

            // 更新得分
            if (linesCleared > 0) {
                updateScore(linesCleared * 10);
                playLinesClearEffect(linesToClear);
            }
        }

        function clearPlayerRow(rowIndex) {
            for (let col = 0; col < gridSize; col++) {
                if (grid[rowIndex][col].sprite && !grid[rowIndex][col].isEnemy) {
                    // 添加消除动画（只消除我方方块）
                    gameScene.tweens.add({
                        targets: grid[rowIndex][col].sprite,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2.easeIn',
                        delay: col * 30,
                        onComplete: () => {
                            if (grid[rowIndex][col].sprite) {
                                grid[rowIndex][col].sprite.destroy();
                                grid[rowIndex][col].sprite = null;
                                grid[rowIndex][col].filled = false;
                                grid[rowIndex][col].isEnemy = false;
                            }
                        }
                    });
                }
            }
        }

        function clearPlayerColumn(colIndex) {
            for (let row = 0; row < gridSize; row++) {
                if (grid[row][colIndex].sprite && !grid[row][colIndex].isEnemy) {
                    // 添加消除动画（只消除我方方块）
                    gameScene.tweens.add({
                        targets: grid[row][colIndex].sprite,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2.easeIn',
                        delay: row * 30,
                        onComplete: () => {
                            if (grid[row][colIndex].sprite) {
                                grid[row][colIndex].sprite.destroy();
                                grid[row][colIndex].sprite = null;
                                grid[row][colIndex].filled = false;
                                grid[row][colIndex].isEnemy = false;
                            }
                        }
                    });
                }
            }
        }

        function updateScore(points) {
            score += points;
            scoreText.setText('Score: ' + score);

            // 分数动画效果
            gameScene.tweens.add({
                targets: scoreText,
                scaleX: 1.2,
                scaleY: 1.2,
                duration: 200,
                yoyo: true,
                ease: 'Power2'
            });
        }

        function playLinesClearEffect(linesToClear) {
            // 创建粒子效果
            linesToClear.forEach(line => {
                if (line.type === 'row') {
                    createRowClearEffect(line.index);
                } else {
                    createColumnClearEffect(line.index);
                }
            });
        }

        function createRowClearEffect(rowIndex) {
            const y = gridOffsetY + rowIndex * cellSize + cellSize / 2;

            for (let i = 0; i < 10; i++) {
                const particle = gameScene.add.circle(
                    gridOffsetX + Math.random() * (gridSize * cellSize),
                    y,
                    3,
                    0xf39c12
                );

                gameScene.tweens.add({
                    targets: particle,
                    alpha: 0,
                    scaleX: 2,
                    scaleY: 2,
                    duration: 500,
                    onComplete: () => particle.destroy()
                });
            }
        }

        function createColumnClearEffect(colIndex) {
            const x = gridOffsetX + colIndex * cellSize + cellSize / 2;

            for (let i = 0; i < 10; i++) {
                const particle = gameScene.add.circle(
                    x,
                    gridOffsetY + Math.random() * (gridSize * cellSize),
                    3,
                    0xf39c12
                );

                gameScene.tweens.add({
                    targets: particle,
                    alpha: 0,
                    scaleX: 2,
                    scaleY: 2,
                    duration: 500,
                    onComplete: () => particle.destroy()
                });
            }
        }

        function isGameOver() {
            // 检查是否还有可用的方块可以放置
            let hasUnusedBlocks = false;
            let canPlaceAnyBlock = false;

            console.log('Checking game over. Current blocks:', currentBlocks.length);

            for (let blockGroup of currentBlocks) {
                if (!blockGroup.used) {
                    hasUnusedBlocks = true;
                    console.log('Found unused block, checking placement...');
                    // 检查这个方块是否可以放置在网格的任何位置
                    for (let row = 0; row < gridSize; row++) {
                        for (let col = 0; col < gridSize; col++) {
                            if (canPlaceBlockAt(blockGroup.shape, row, col)) {
                                canPlaceAnyBlock = true;
                                console.log('Block can be placed at', row, col);
                                return false; // 还有可放置的位置
                            }
                        }
                    }
                }
            }

            console.log('Game over check result:', hasUnusedBlocks, !canPlaceAnyBlock);
            // 只有当有未使用的方块但无法放置任何方块时才算游戏结束
            return hasUnusedBlocks && !canPlaceAnyBlock;
        }

        function showGameOver() {
            // 更新最高分
            updateBestScore();

            // 创建游戏结束遮罩
            const overlay = gameScene.add.graphics();
            overlay.fillStyle(0x000000, 0.7);
            overlay.fillRect(0, 0, gameWidth, gameHeight);
            overlay.setDepth(200);

            // 固定字体大小
            const gameOverFontSize = '60px';
            const scoreFontSize = '40px';
            const buttonFontSize = '40px';

            // 游戏结束文本
            const gameOverText = gameScene.add.text(375, 467, 'Game Over', {
                fontSize: gameOverFontSize,
                fontWeight: 'bold',
                fill: '#e74c3c',
                align: 'center'
            });
            gameOverText.setOrigin(0.5);
            gameOverText.setDepth(201);

            // 最终得分
            const finalScoreText = gameScene.add.text(375, 600, 'Final Score: ' + score, {
                fontSize: scoreFontSize,
                fill: '#ecf0f1',
                align: 'center'
            });
            finalScoreText.setOrigin(0.5);
            finalScoreText.setDepth(201);

            // 重新开始按钮
            const restartButton = gameScene.add.text(375, 733, 'Restart', {
                fontSize: buttonFontSize,
                fill: '#3498db',
                align: 'center',
                backgroundColor: '#2c3e50',
                padding: { x: 37, y: 20 }
            });
            restartButton.setOrigin(0.5);
            restartButton.setDepth(201);
            restartButton.setInteractive();

            // 重新开始游戏
            restartButton.on('pointerdown', function() {
                restartGame();
            });

            // 动画效果
            gameScene.tweens.add({
                targets: [gameOverText, finalScoreText, restartButton],
                alpha: { from: 0, to: 1 },
                y: { from: '+=50', to: '-=50' },
                duration: 500,
                ease: 'Back.easeOut'
            });
        }

        function restartGame() {
            // 重置游戏状态
            score = 0;
            scoreText.setText('Score: 0');

            // 重置回合制状态
            isPlayerTurn = true;
            turnText.setText('Your Turn');
            turnText.setFill('#2ecc71');

            // 清除炮塔选择
            selectedTower = null;
            clearRangeDisplay();

            // 清除所有炮塔
            playerTowers.forEach(tower => tower.sprite.destroy());
            enemyTowers.forEach(tower => tower.sprite.destroy());
            playerTowers = [];
            enemyTowers = [];

            // 清除无敌方块
            invulnerableBlocks = [];

            // 清空网格
            for (let row = 0; row < gridSize; row++) {
                for (let col = 0; col < gridSize; col++) {
                    if (grid[row][col].sprite) {
                        grid[row][col].sprite.destroy();
                        grid[row][col].sprite = null;
                    }
                    if (grid[row][col].previewSprite) {
                        grid[row][col].previewSprite.destroy();
                        grid[row][col].previewSprite = null;
                    }
                    grid[row][col].filled = false;
                    grid[row][col].isEnemy = false;
                    grid[row][col].health = 2;
                    grid[row][col].maxHealth = 2;
                    grid[row][col].hasTower = false;
                }
            }

            // 清除当前方块
            currentBlocks.forEach(blockGroup => {
                if (blockGroup.container) {
                    blockGroup.container.destroy();
                }
            });

            // 重新开始游戏
            gameScene.scene.restart();
        }

        function loadBestScore() {
            const saved = localStorage.getItem('blockBlastBestScore');
            if (saved) {
                bestScore = parseInt(saved);
            }
        }

        function saveBestScore() {
            localStorage.setItem('blockBlastBestScore', bestScore.toString());
        }

        function updateBestScore() {
            if (score > bestScore) {
                bestScore = score;
                bestScoreText.setText('Best: ' + bestScore);
                saveBestScore();

                // 新纪录动画
                gameScene.tweens.add({
                    targets: bestScoreText,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    duration: 300,
                    yoyo: true,
                    ease: 'Power2'
                });
            }
        }

        // 错误处理
        window.addEventListener('error', function(e) {
            console.error('Game Error:', e.error);
            // 可以在这里添加错误报告或恢复逻辑
        });

        // 窗口大小变化监听器
      

        // 启动游戏
        try {
            game = new Phaser.Game(config);
        } catch (error) {
            console.error('Failed to start game:', error);
            document.getElementById('game-container').innerHTML =
                '<div style="color: white; text-align: center; padding: 50px;">游戏加载失败，请刷新页面重试</div>';
        }
    </script>
</body>
</html>
