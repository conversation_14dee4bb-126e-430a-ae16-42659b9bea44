<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Block Puzzle + 消灭星星</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
        }

       .game-container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

       .score-display {
            color: white;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
    </style>
</head>

<body>
    <div class="game-container">
        <div class="score-display">得分: <span id="score">0</span></div>
        <div id="game"></div>
    </div>

    <script>
        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.score = 0;
                this.blockSize = 35;
                this.boardRows = 10;
                this.boardCols = 10;
                this.bottomRows = 8;
                this.bottomCols = 10;
                this.selectedBlocks = [];
                this.isDragging = false;
                this.dragGroup = null;
                this.colors = [0xff4444, 0x44ff44, 0x4444ff, 0xffff44, 0xff44ff, 0x44ffff];
                this.isPlacing = false; // 防止放置过程中重复操作
            }

            preload() {
                // 不需要预加载纹理，使用Phaser内置图形
            }

            create() {
                // 初始化棋盘
                this.initializeBoard();
                this.initializeBottomBoard();

                // 添加拖拽功能
                this.input.on('dragstart', this.onDragStart, this);
                this.input.on('drag', this.onDrag, this);
                this.input.on('dragend', this.onDragEnd, this);

                // 添加点击事件
                this.input.on('gameobjectdown', this.onBlockClick, this);
            }

            initializeBoard() {
                this.board = [];
                this.boardSprites = [];
                const boardWidth = (this.boardCols-1) * this.blockSize;
                const startX = (this.sys.game.config.width - boardWidth) / 2;
                const startY = 50;
                this.boardStartX = startX; // 保存用于后续引用
                this.boardStartY = startY;

                for (let row = 0; row < this.boardRows; row++) {
                    this.board[row] = [];
                    this.boardSprites[row] = [];
                    for (let col = 0; col < this.boardCols; col++) {
                        this.board[row][col] = 0;

                        // 创建背景格子
                        const bg = this.add.rectangle(
                            startX + col * this.blockSize,
                            startY + row * this.blockSize,
                            this.blockSize - 2,
                            this.blockSize - 2,
                            0x333333
                        );
                        bg.setStrokeStyle(1, 0x666666);

                        this.boardSprites[row][col] = null;
                    }
                }
            }

            initializeBottomBoard() {
                this.bottomBoard = [];
                this.bottomSprites = [];
                const bottomWidth = (this.bottomCols-1) * this.blockSize;
                const startX = (this.sys.game.config.width - bottomWidth) / 2;
                const startY = 450;
                this.bottomStartX = startX; // 保存用于后续引用
                this.bottomStartY = startY;

                for (let row = 0; row < this.bottomRows; row++) {
                    this.bottomBoard[row] = [];
                    this.bottomSprites[row] = [];
                    for (let col = 0; col < this.bottomCols; col++) {
                        // 随机生成颜色
                        const colorIndex = Phaser.Math.Between(0, this.colors.length - 1);
                        this.bottomBoard[row][col] = colorIndex + 1;

                        const block = this.add.rectangle(
                            startX + col * this.blockSize,
                            startY + row * this.blockSize,
                            this.blockSize - 2,
                            this.blockSize - 2,
                            this.colors[colorIndex]
                        );
                        block.setStrokeStyle(2, 0x000000);
                        block.setInteractive();
                        block.gridX = col;
                        block.gridY = row;
                        block.isBottomBlock = true;

                        this.bottomSprites[row][col] = block;
                    }
                }
            }

            onBlockClick(pointer, gameObject) {
                if (!gameObject.isBottomBlock || this.isDragging || this.isPlacing) return;

                // 清除之前的选择
                this.clearSelection();

                // 获取相连的同色方块
                const color = this.bottomBoard[gameObject.gridY][gameObject.gridX];
                this.selectedBlocks = this.getConnectedBlocks(gameObject.gridX, gameObject.gridY, color);

                if (this.selectedBlocks.length >= 2) {
                    // 高亮选中的方块
                    this.highlightSelectedBlocks();

                    // 创建拖拽组
                    this.createDragGroup();
                }
            }

            getConnectedBlocks(startX, startY, color) {
                const visited = [];
                const blocks = [];

                for (let i = 0; i < this.bottomRows; i++) {
                    visited[i] = new Array(this.bottomCols).fill(false);
                }

                const queue = [{ x: startX, y: startY }];
                visited[startY][startX] = true;

                while (queue.length > 0) {
                    const { x, y } = queue.shift();
                    blocks.push({ x, y });

                    // 检查四个方向
                    const directions = [{ dx: 0, dy: -1 }, { dx: 0, dy: 1 }, { dx: -1, dy: 0 }, { dx: 1, dy: 0 }];

                    for (const { dx, dy } of directions) {
                        const nx = x + dx;
                        const ny = y + dy;

                        if (nx >= 0 && nx < this.bottomCols && ny >= 0 && ny < this.bottomRows) {
                            if (!visited[ny][nx] && this.bottomBoard[ny][nx] === color) {
                                visited[ny][nx] = true;
                                queue.push({ x: nx, y: ny });
                            }
                        }
                    }
                }

                return blocks;
            }

            highlightSelectedBlocks() {
                this.selectedBlocks.forEach(({ x, y }) => {
                    const sprite = this.bottomSprites[y][x];
                    if (sprite) {
                        sprite.setStrokeStyle(3, 0xffffff);
                        sprite.setScale(1.1);
                    }
                });
            }

            clearSelection() {
                this.selectedBlocks.forEach(({ x, y }) => {
                    const sprite = this.bottomSprites[y][x];
                    if (sprite) {
                        sprite.setStrokeStyle(2, 0x000000);
                        sprite.setScale(1);
                    }
                });
                this.selectedBlocks = [];

                // 确保销毁拖拽组
                if (this.dragGroup) {
                    this.dragGroup.destroy(true);
                    this.dragGroup = null;
                }
            }

            createDragGroup() {
                if (this.selectedBlocks.length === 0) return;

                this.dragGroup = this.add.group();
                const color = this.bottomBoard[this.selectedBlocks[0].y][this.selectedBlocks[0].x];

                // 创建可拖拽的形状
                this.selectedBlocks.forEach(({ x, y }, index) => {
                    const worldX = this.bottomStartX + x * this.blockSize;
                    const worldY = this.bottomStartY + y * this.blockSize;

                    const dragBlock = this.add.rectangle(
                        worldX, worldY,
                        this.blockSize - 2,
                        this.blockSize - 2,
                        this.colors[color - 1]
                    );
                    dragBlock.setStrokeStyle(2, 0x000000);
                    dragBlock.setAlpha(0.8);
                    dragBlock.offsetX = x - this.selectedBlocks[0].x;
                    dragBlock.offsetY = y - this.selectedBlocks[0].y;
                    dragBlock.originalX = worldX;
                    dragBlock.originalY = worldY;

                    this.dragGroup.add(dragBlock);
                });

                // 设置拖拽
                const mainBlock = this.dragGroup.children.entries[0];
                mainBlock.setInteractive();
                this.input.setDraggable(mainBlock);
            }

            onDragStart(pointer, gameObject) {
                if (!this.dragGroup) return;
                this.isDragging = true;
                this.dragGroup.children.entries.forEach(block => {
                    block.setScale(1.2);
                });
            }

            onDrag(pointer, gameObject, dragX, dragY) {
                if (!this.dragGroup) return;

                // 移动整个组
                this.dragGroup.children.entries.forEach(block => {
                    block.x = pointer.x + block.offsetX * this.blockSize;
                    block.y = pointer.y + block.offsetY * this.blockSize;
                });
            }

            onDragEnd(pointer, gameObject) {
                if (!this.dragGroup || !this.isDragging || this.isPlacing) return;

                const mainBlock = this.dragGroup.children.entries[0];
                const boardX = Math.round((mainBlock.x - this.boardStartX) / this.blockSize);
                const boardY = Math.round((mainBlock.y - this.boardStartY) / this.blockSize);

                // 检查是否可以放置
                if (this.canPlaceShape(boardX, boardY)) {
                    this.isPlacing = true;
                    this.placeShape(boardX, boardY);
                } else {
                    // 放置失败，恢复位置
                    this.resetDragGroup();
                }

                this.clearSelection();
                this.isDragging = false;
            }

            canPlaceShape(startX, startY) {
                for (const block of this.dragGroup.children.entries) {
                    const x = startX + block.offsetX;
                    const y = startY + block.offsetY;

                    if (x < 0 || x >= this.boardCols || y < 0 || y >= this.boardRows) {
                        return false;
                    }

                    if (this.board[y][x] !== 0) {
                        return false;
                    }
                }
                return true;
            }

            placeShape(startX, startY) {
                const color = this.bottomBoard[this.selectedBlocks[0].y][this.selectedBlocks[0].x];
                
                // 先销毁底部选择的方块
                this.removeSelectedBlocks();
                
                // 创建新方块并添加到棋盘
                this.dragGroup.children.entries.forEach(block => {
                    const x = startX + block.offsetX;
                    const y = startY + block.offsetY;
                    
                    this.board[y][x] = color;
                    
                    // 先创建方块但不显示
                    const sprite = this.add.rectangle(
                        this.boardStartX + x * this.blockSize,
                        this.boardStartY + y * this.blockSize,
                        this.blockSize - 2,
                        this.blockSize - 2,
                        this.colors[color - 1]
                    );
                    sprite.setStrokeStyle(2, 0x000000);
                    sprite.setScale(0); // 初始隐藏
                    this.boardSprites[y][x] = sprite;
                });
                
                // 等待一小段时间确保底部方块已销毁
                this.time.delayedCall(100, () => {
                    // 销毁拖动组
                    if (this.dragGroup) {
                        this.dragGroup.destroy(true);
                        this.dragGroup = null;
                    }
                    
                    // 显示新方块
                    const newBlocks = [];
                    for (let row = 0; row < this.boardRows; row++) {
                        for (let col = 0; col < this.boardCols; col++) {
                            if (this.board[row][col] === color && this.boardSprites[row][col] && this.boardSprites[row][col].scale === 0) {
                                newBlocks.push(this.boardSprites[row][col]);
                            }
                        }
                    }
                    
                    // 添加动画效果
                    this.tweens.add({
                        targets: newBlocks,
                        scale: { from: 0, to: 1 },
                        duration: 200,
                        ease: 'Back.easeOut',
                        onComplete: () => {
                            // 动画完成后，执行后续操作
                            this.dropBottomBlocks();
                            this.checkLines();
                            this.isPlacing = false;
                        }
                    });
                }, [], this);
            }

            removeSelectedBlocks() {
                this.selectedBlocks.forEach(({ x, y }) => {
                    if (this.bottomSprites[y][x]) {
                        // 添加淡出效果
                        this.tweens.add({
                            targets: this.bottomSprites[y][x],
                            alpha: 0,
                            duration: 100,
                            onComplete: (tween, targets) => {
                                targets[0].destroy();
                            }
                        });
                        this.bottomSprites[y][x] = null;
                        this.bottomBoard[y][x] = 0;
                    }
                });
            }

            dropBottomBlocks() {
                for (let col = 0; col < this.bottomCols; col++) {
                    // 收集非空方块
                    const blocks = [];
                    for (let row = this.bottomRows - 1; row >= 0; row--) {
                        if (this.bottomBoard[row][col] !== 0) {
                            blocks.push({
                                color: this.bottomBoard[row][col],
                                sprite: this.bottomSprites[row][col]
                            });
                            this.bottomBoard[row][col] = 0;
                            this.bottomSprites[row][col] = null;
                        }
                    }

                    // 重新放置方块
                    for (let i = 0; i < blocks.length; i++) {
                        const newRow = this.bottomRows - 1 - i;
                        this.bottomBoard[newRow][col] = blocks[i].color;
                        this.bottomSprites[newRow][col] = blocks[i].sprite;

                        if (blocks[i].sprite) {
                            blocks[i].sprite.gridX = col;
                            blocks[i].sprite.gridY = newRow;

                            // 动画移动到新位置
                            this.tweens.add({
                                targets: blocks[i].sprite,
                                y: this.bottomStartY + newRow * this.blockSize,
                                duration: 300,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }

                    // 填充新方块
                    for (let row = 0; row < this.bottomRows - blocks.length; row++) {
                        const colorIndex = Phaser.Math.Between(0, this.colors.length - 1);
                        this.bottomBoard[row][col] = colorIndex + 1;

                        const block = this.add.rectangle(
                            this.bottomStartX + col * this.blockSize,
                            this.bottomStartY + row * this.blockSize - 200,
                            this.blockSize - 2,
                            this.blockSize - 2,
                            this.colors[colorIndex]
                        );
                        block.setStrokeStyle(2, 0x000000);
                        block.setInteractive();
                        block.gridX = col;
                        block.gridY = row;
                        block.isBottomBlock = true;

                        this.bottomSprites[row][col] = block;

                        // 动画掉落
                        this.tweens.add({
                            targets: block,
                            y: this.bottomStartY + row * this.blockSize,
                            duration: 400,
                            ease: 'Bounce.easeOut'
                        });
                    }
                }
            }

            resetDragGroup() {
                if (!this.dragGroup) return;

                this.dragGroup.children.entries.forEach(block => {
                    this.tweens.add({
                        targets: block,
                        x: block.originalX,
                        y: block.originalY,
                        scale: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                });
            }

            checkLines() {
                const linesToClear = [];

                // 检查水平线
                for (let row = 0; row < this.boardRows; row++) {
                    let complete = true;
                    for (let col = 0; col < this.boardCols; col++) {
                        if (this.board[row][col] === 0) {
                            complete = false;
                            break;
                        }
                    }
                    if (complete) {
                        linesToClear.push({ type: 'row', index: row });
                    }
                }

                // 检查垂直线
                for (let col = 0; col < this.boardCols; col++) {
                    let complete = true;
                    for (let row = 0; row < this.boardRows; row++) {
                        if (this.board[row][col] === 0) {
                            complete = false;
                            break;
                        }
                    }
                    if (complete) {
                        linesToClear.push({ type: 'col', index: col });
                    }
                }

                // 清除完整的线
                if (linesToClear.length > 0) {
                    this.clearLines(linesToClear);
                    this.score += linesToClear.length * 100;
                    document.getElementById('score').textContent = this.score;
                }
            }

            clearLines(lines) {
                lines.forEach(line => {
                    if (line.type === 'row') {
                        for (let col = 0; col < this.boardCols; col++) {
                            if (this.boardSprites[line.index][col]) {
                                this.boardSprites[line.index][col].destroy();
                                this.boardSprites[line.index][col] = null;
                            }
                            this.board[line.index][col] = 0;
                        }
                    } else {
                        for (let row = 0; row < this.boardRows; row++) {
                            if (this.boardSprites[row][line.index]) {
                                this.boardSprites[row][line.index].destroy();
                                this.boardSprites[row][line.index] = null;
                            }
                            this.board[row][line.index] = 0;
                        }
                    }
                });

                // 添加消除特效
                this.cameras.main.shake(200, 0.02);
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: 450,
            height: 750,
            parent: 'game',
            scene: [GameScene]
        };

        const game = new Phaser.Game(config);
    </script>
</body>

</html>