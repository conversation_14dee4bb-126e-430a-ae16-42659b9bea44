<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>幻方游戏</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }
        #game-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .game-info {
            text-align: center;
            color: white;
            margin-bottom: 20px;
        }
        .game-info h1 {
            margin: 0 0 10px 0;
            font-size: 28px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }
        .game-info p {
            margin: 5px 0;
            font-size: 16px;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="game-info">
            <h1>🎯 幻方游戏</h1>
            <p>将-4到4的数字填入九宫格</p>
            <p>使每行、每列、每条对角线的和都为0</p>
        </div>
        <div id="game"></div>
    </div>

    <script>
        class MagicSquareGame extends Phaser.Scene {
            constructor() {
                super({ key: 'MagicSquareGame' });
                this.currentLevel = 1;
                this.gridSize = 3;
                this.cellSize = 80;
                this.grid = [];
                this.numberButtons = [];
                this.selectedCell = null;
                this.availableNumbers = [];
                this.usedNumbers = [];
            }

            preload() {
                // 不需要预加载资源，直接使用Phaser的图形API
            }

            create() {
                this.setupLevel();
                this.createGrid();
                this.createNumberPad();
                this.createUI();
                this.createInstructions();
            }

            setupLevel() {
                // 根据关卡设置可用数字
                switch(this.currentLevel) {
                    case 1:
                        this.availableNumbers = [-4, -3, -2, -1, 0, 1, 2, 3, 4];
                        this.targetSum = 0;
                        break;
                    case 2:
                        this.availableNumbers = [-8, -6, -4, -2, 0, 2, 4, 6, 8];
                        this.targetSum = 0;
                        break;
                    case 3:
                        this.availableNumbers = [-12, -9, -6, -3, 0, 3, 6, 9, 12];
                        this.targetSum = 0;
                        break;
                    default:
                        // 生成随机数字组合
                        this.generateRandomLevel();
                        break;
                }
                this.usedNumbers = [];
            }

            generateRandomLevel() {
                const base = this.currentLevel * 2;
                this.availableNumbers = [];
                for (let i = -4; i <= 4; i++) {
                    this.availableNumbers.push(i * base);
                }
                this.targetSum = 0;
            }

            createGrid() {
                this.grid = [];
                const startX = 400 - (this.gridSize * this.cellSize) / 2;
                const startY = 200;

                for (let row = 0; row < this.gridSize; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.gridSize; col++) {
                        const x = startX + col * this.cellSize;
                        const y = startY + row * this.cellSize;

                        // 创建格子背景
                        const cellBg = this.add.rectangle(x, y, this.cellSize - 4, this.cellSize - 4, 0xffffff, 0.2);
                        cellBg.setStrokeStyle(2, 0xffffff, 0.8);
                        cellBg.setInteractive();
                        
                        // 创建数字文本
                        const numberText = this.add.text(x, y, '', {
                            fontSize: '24px',
                            fill: '#ffffff',
                            fontStyle: 'bold'
                        }).setOrigin(0.5);

                        // 创建选中指示器
                        const selector = this.add.rectangle(x, y, this.cellSize - 4, this.cellSize - 4, 0x00ff00, 0);
                        selector.setStrokeStyle(3, 0x00ff00, 0);

                        const cell = {
                            background: cellBg,
                            text: numberText,
                            selector: selector,
                            value: null,
                            row: row,
                            col: col
                        };

                        this.grid[row][col] = cell;

                        // 添加点击事件
                        cellBg.on('pointerdown', () => this.selectCell(cell));
                        cellBg.on('pointerover', () => cellBg.setFillStyle(0xffffff, 0.3));
                        cellBg.on('pointerout', () => cellBg.setFillStyle(0xffffff, 0.2));
                    }
                }
            }

            createNumberPad() {
                this.numberButtons = [];
                const startX = 150;
                const startY = 450;
                const buttonSize = 50;
                const spacing = 60;

                this.availableNumbers.forEach((number, index) => {
                    const x = startX + (index % 5) * spacing;
                    const y = startY + Math.floor(index / 5) * spacing;

                    const button = this.add.rectangle(x, y, buttonSize, buttonSize, 0x4a90e2, 0.8);
                    button.setStrokeStyle(2, 0xffffff, 0.8);
                    button.setInteractive();

                    const text = this.add.text(x, y, number.toString(), {
                        fontSize: '20px',
                        fill: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);

                    const buttonObj = {
                        background: button,
                        text: text,
                        value: number,
                        used: false
                    };

                    this.numberButtons.push(buttonObj);

                    button.on('pointerdown', () => this.selectNumber(buttonObj));
                    button.on('pointerover', () => {
                        if (!buttonObj.used) {
                            button.setFillStyle(0x5ba0f2, 0.9);
                        }
                    });
                    button.on('pointerout', () => {
                        if (!buttonObj.used) {
                            button.setFillStyle(0x4a90e2, 0.8);
                        }
                    });
                });
            }

            createUI() {
                // 标题
                this.add.text(400, 50, `第 ${this.currentLevel} 关`, {
                    fontSize: '32px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 检查按钮
                const checkButton = this.add.rectangle(600, 300, 120, 50, 0x27ae60, 0.8);
                checkButton.setStrokeStyle(2, 0xffffff);
                checkButton.setInteractive();
                this.add.text(600, 300, '检查', {
                    fontSize: '20px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                checkButton.on('pointerdown', () => this.checkSolution());
                checkButton.on('pointerover', () => checkButton.setFillStyle(0x2ecc71, 0.9));
                checkButton.on('pointerout', () => checkButton.setFillStyle(0x27ae60, 0.8));

                // 重置按钮
                const resetButton = this.add.rectangle(600, 370, 120, 50, 0xe74c3c, 0.8);
                resetButton.setStrokeStyle(2, 0xffffff);
                resetButton.setInteractive();
                this.add.text(600, 370, '重置', {
                    fontSize: '20px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                resetButton.on('pointerdown', () => this.resetGame());
                resetButton.on('pointerover', () => resetButton.setFillStyle(0xec5e50, 0.9));
                resetButton.on('pointerout', () => resetButton.setFillStyle(0xe74c3c, 0.8));

                // 状态文本
                this.statusText = this.add.text(400, 600, '选择一个格子，然后选择数字', {
                    fontSize: '18px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
            }

            createInstructions() {
                const instructions = [
                    '游戏规则：',
                    '1. 将所有数字填入九宫格',
                    '2. 每行、每列、每条对角线的和都必须为 0',
                    '3. 每个数字只能使用一次'
                ];

                instructions.forEach((instruction, index) => {
                    this.add.text(50, 150 + index * 25, instruction, {
                        fontSize: '16px',
                        fill: '#ffffff',
                        alpha: 0.9
                    });
                });
            }

            selectCell(cell) {
                // 清除之前的选择
                if (this.selectedCell) {
                    this.selectedCell.selector.setStrokeStyle(3, 0x00ff00, 0);
                }

                this.selectedCell = cell;
                cell.selector.setStrokeStyle(3, 0x00ff00, 1);
                this.statusText.setText('已选择格子，现在选择一个数字');
            }

            selectNumber(numberButton) {
                if (!this.selectedCell) {
                    this.statusText.setText('请先选择一个格子！');
                    return;
                }

                if (numberButton.used) {
                    this.statusText.setText('这个数字已经被使用了！');
                    return;
                }

                // 如果格子已有数字，将原数字标记为未使用
                if (this.selectedCell.value !== null) {
                    const oldButton = this.numberButtons.find(btn => btn.value === this.selectedCell.value);
                    if (oldButton) {
                        oldButton.used = false;
                        oldButton.background.setFillStyle(0x4a90e2, 0.8);
                        oldButton.text.setAlpha(1);
                    }
                }

                // 设置新数字
                this.selectedCell.value = numberButton.value;
                this.selectedCell.text.setText(numberButton.value.toString());
                
                // 标记数字为已使用
                numberButton.used = true;
                numberButton.background.setFillStyle(0x7f8c8d, 0.6);
                numberButton.text.setAlpha(0.5);

                // 清除选择
                this.selectedCell.selector.setStrokeStyle(3, 0x00ff00, 0);
                this.selectedCell = null;

                this.statusText.setText('继续选择格子和数字');
            }

            checkSolution() {
                // 检查是否所有格子都填满了
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (this.grid[row][col].value === null) {
                            this.statusText.setText('请先填满所有格子！');
                            return;
                        }
                    }
                }

                let isCorrect = true;
                const results = [];

                // 检查行
                for (let row = 0; row < this.gridSize; row++) {
                    let sum = 0;
                    for (let col = 0; col < this.gridSize; col++) {
                        sum += this.grid[row][col].value;
                    }
                    results.push(`第${row + 1}行: ${sum}`);
                    if (sum !== this.targetSum) isCorrect = false;
                }

                // 检查列
                for (let col = 0; col < this.gridSize; col++) {
                    let sum = 0;
                    for (let row = 0; row < this.gridSize; row++) {
                        sum += this.grid[row][col].value;
                    }
                    results.push(`第${col + 1}列: ${sum}`);
                    if (sum !== this.targetSum) isCorrect = false;
                }

                // 检查对角线
                let diag1Sum = 0, diag2Sum = 0;
                for (let i = 0; i < this.gridSize; i++) {
                    diag1Sum += this.grid[i][i].value;
                    diag2Sum += this.grid[i][this.gridSize - 1 - i].value;
                }
                results.push(`主对角线: ${diag1Sum}`);
                results.push(`副对角线: ${diag2Sum}`);
                if (diag1Sum !== this.targetSum || diag2Sum !== this.targetSum) isCorrect = false;

                if (isCorrect) {
                    this.statusText.setText('🎉 恭喜！进入下一关！');
                    this.time.delayedCall(2000, () => {
                        this.nextLevel();
                    });
                } else {
                    this.statusText.setText('还没有正确，继续努力！');
                }
            }

            resetGame() {
                // 清空格子
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        this.grid[row][col].value = null;
                        this.grid[row][col].text.setText('');
                        this.grid[row][col].selector.setStrokeStyle(3, 0x00ff00, 0);
                    }
                }

                // 重置数字按钮
                this.numberButtons.forEach(button => {
                    button.used = false;
                    button.background.setFillStyle(0x4a90e2, 0.8);
                    button.text.setAlpha(1);
                });

                this.selectedCell = null;
                this.statusText.setText('游戏已重置，选择一个格子开始');
            }

            nextLevel() {
                this.currentLevel++;
                this.scene.restart();
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 650,
            parent: 'game',
            backgroundColor: 'transparent',
            scene: MagicSquareGame,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        const game = new Phaser.Game(config);
    </script>
</body>
</html>