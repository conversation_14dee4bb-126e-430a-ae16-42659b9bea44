<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Tile Match</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .ui-text {
            font-family: 'Arial', sans-serif;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 78;
        const SLOT_COUNT = 7;
        const TILE_TYPES = [
            { color: 0xFF6B6B, emoji: '🍎', name: '苹果' },
            { color: 0x4ECDC4, emoji: '🍊', name: '橙子' },
            { color: 0x45B7D1, emoji: '🍌', name: '香蕉' },
            { color: 0x96CEB4, emoji: '🍇', name: '葡萄' },
            { color: 0xFECA57, emoji: '🍓', name: '草莓' },
            { color: 0xFF9FF3, emoji: '🥝', name: '猕猴桃' },
            { color: 0xA8E6CF, emoji: '🍑', name: '樱桃' },
            { color: 0xFD79A8, emoji: '🍒', name: '樱桃' }
        ];

        const PLATE_TYPES = [
            { color: 0xFF6B6B, name: '红盘' },
            { color: 0x4ECDC4, name: '青盘' },
            { color: 0x45B7D1, name: '蓝盘' },
            { color: 0x96CEB4, name: '绿盘' },
            { color: 0xFECA57, name: '黄盘' },
            { color: 0xFF9FF3, name: '粉盘' },
            { color: 0xA8E6CF, name: '浅绿盘' },
            { color: 0xFD79A8, name: '紫盘' }
        ];

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.tiles = [];
                this.slots = [];
                this.enemySlots = [];
                this.score = 0;
                this.level = 1;
                this.isAnimating = false;
                this.gameOver = false;
            }

            preload() {
                this.createTileTextures();
            }

            create() {
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x2C3E50);

                this.createSettingsButton();

                this.scoreText = this.add.text(GAME_WIDTH - 30, 30, '🪙 0', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);

                this.levelText = this.add.text(GAME_WIDTH/2, 50, '1', {
                    fontSize: '72px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                this.createInstructionArea();
                this.createTopFruitArea();
                this.createSlotArea();

                this.plateArea = {
                    x: 40,
                    y: 600,
                    width: GAME_WIDTH - 80,
                    height: 200
                };

                this.stackArea = {
                    x: 40,
                    y: 350,
                    width: GAME_WIDTH - 80,
                    height: 200
                };

                this.createToolArea();
                this.initializeNewGame();
            }

            createTileTextures() {
                TILE_TYPES.forEach((tileType, index) => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(tileType.color);
                    graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);
                    graphics.lineStyle(2, 0x000000, 0.2);
                    graphics.strokeRoundedRect(2, 2, TILE_SIZE-4, TILE_SIZE-4, 6);
                    graphics.generateTexture(`tile_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }

            createTopFruitArea() {
                if (this.topFruits) {
                    this.topFruits.forEach(row => {
                        if (row) {
                            row.forEach(fruit => {
                                if (fruit) fruit.destroy();
                            });
                        }
                    });
                }

                const topAreaY = 160;
                const rows = 4;
                const cols = 3;
                const fruitSpacing = 80;
                const totalWidth = (cols - 1) * fruitSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const startY = topAreaY;

                this.topFruits = [];
                for (let row = 0; row < rows; row++) {
                    this.topFruits[row] = [];
                    for (let col = 0; col < cols; col++) {
                        const fruitX = startX + col * fruitSpacing;
                        const fruitY = startY + row * fruitSpacing;
                        const fruitType = this.getRandomFruitTypeForLevel();
                        const fruit = this.createTopFruit(fruitX, fruitY, fruitType);
                        this.topFruits[row][col] = fruit;
                    }
                }
            }

            createSettingsButton() {
                const buttonSize = 50;
                const buttonX = 30;
                const buttonY = 30;

                const settingsBg = this.add.graphics();
                settingsBg.fillStyle(0x34495E, 0.9);
                settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                settingsBg.lineStyle(2, 0x2C3E50, 1);
                settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                settingsBg.setInteractive(new Phaser.Geom.Rectangle(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize), Phaser.Geom.Rectangle.Contains);

                const settingsIcon = this.add.text(buttonX, buttonY, '⚙️', {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                settingsBg.on('pointerover', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x5D6D7E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x34495E, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.1);
                });

                settingsBg.on('pointerout', () => {
                    settingsBg.clear();
                    settingsBg.fillStyle(0x34495E, 0.9);
                    settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsBg.lineStyle(2, 0x2C3E50, 1);
                    settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                    settingsIcon.setScale(1.0);
                });

                settingsBg.on('pointerdown', () => {
                    this.showToolTip('设置', buttonX, buttonY - 35);
                });
            }

            createInstructionArea() {
                const loadingText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2, '加载游戏中...', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                loadingText.setDepth(1000);

                this.time.delayedCall(2000, () => {
                    this.tweens.add({
                        targets: loadingText,
                        alpha: 0,
                        duration: 500,
                        ease: 'Power2',
                        onComplete: () => {
                            loadingText.destroy();
                        }
                    });
                });
            }

            createSlotArea() {
                // 暂存区域（向下移动）
                const slotAreaY = 500;

                // 创建暂存区槽位
                this.slots = [];
                const totalSlotWidth = SLOT_COUNT * TILE_SIZE + (SLOT_COUNT - 1) * 5;
                const startX = (GAME_WIDTH - totalSlotWidth) / 2 + TILE_SIZE / 2;

                for (let i = 0; i < SLOT_COUNT; i++) {
                    const slotX = startX + i * (TILE_SIZE + 5);
                    const slotY = slotAreaY;
                    const isLastSlot = i === SLOT_COUNT - 1; // 最后一个槽位

                    // 槽位背景（圆角矩形，与方块样式一致）
                    const slotBg = this.add.graphics();

                    if (isLastSlot) {
                        // 锁住的槽位使用不同颜色
                        slotBg.fillStyle(0x7F8C8D, 0.8);
                        slotBg.fillRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                        slotBg.lineStyle(2, 0x95A5A6, 0.9);
                        slotBg.strokeRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                    } else {
                        // 普通槽位
                        slotBg.fillStyle(0x2C3E50);
                        slotBg.fillRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                        slotBg.lineStyle(2, 0x7F8C8D);
                        slotBg.strokeRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                    }

                    const slot = {
                        x: slotX,
                        y: slotY,
                        tile: null,
                        background: slotBg,
                        isLocked: isLastSlot
                    };

                    // 如果是锁住的槽位，添加锁图标
                    if (isLastSlot) {
                        const lockIcon = this.add.text(slotX, slotY, '🔒', {
                            fontSize: '32px',
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        slot.lockIcon = lockIcon;
                    }

                    this.slots.push(slot);
                }
            }

            createSmallFruit(x, y, typeIndex, size) {
                const tileType = TILE_TYPES[typeIndex];
                const container = this.add.container(x + size/2, y + size/2);

                // 只创建水果emoji，不要背景
                const emoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: Math.floor(size * 0.8) + 'px', // 增大字体，因为没有背景了
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                container.add([emoji]);
                container.setSize(size, size);
                container.setData('typeIndex', typeIndex);
                container.setData('isDark', true);
                container.setData('isMatched', false);
                container.setData('isReserved', false);
                container.setData('matchedSlotIndex', -1);

                container.setAlpha(0.6);



                return container;
            }

            getPlateCountForLevel(level) {
                // 根据关卡返回水果盘数量
                if (level === 1) return 1;      // 第1关：1个水果盘
                if (level === 2) return 2;      // 第2关：2个水果盘
                if (level === 3) return 3;      // 第3关：3个水果盘
                return 4;                       // 第4关及以后：4个水果盘
            }

            getFruitTypeForPlate(plateIndex, fruitIndex) {
                // 根据关卡和水果盘索引生成水果类型
                const level = this.level;

                if (level === 1) {
                    // 第1关：1个水果盘，4个相同水果
                    return 7; // 使用樱桃（最后一个水果类型）
                }

                if (level === 2) {
                    // 第2关：2个水果盘，每个盘子4个相同水果，但两个盘子不同
                    return plateIndex === 0 ? 7 : 6; // 第一个盘子樱桃，第二个盘子猕猴桃
                }

                if (level === 3) {
                    // 第3关：3个水果盘，每个盘子4个相同水果
                    const fruitTypes = [7, 6, 5]; // 樱桃、猕猴桃、草莓
                    return fruitTypes[plateIndex];
                }

                // 第4关及以后：更复杂的组合
                if (level === 4) {
                    // 第4关：4个水果盘，前两个盘子相同水果，后两个盘子混合水果
                    if (plateIndex < 2) {
                        return plateIndex === 0 ? 7 : 6; // 樱桃和猕猴桃
                    } else {
                        // 后两个盘子：2个樱桃 + 2个草莓
                        return fruitIndex < 2 ? 7 : 5;
                    }
                }

                // 第5关及以后：完全随机但保证有解
                const availableTypes = [7, 6, 5, 4]; // 使用4种水果类型
                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getPlateTypeForLevel() {
                // 根据关卡返回盘子颜色类型（最多增加一种干扰颜色）
                let availableTypes = [];

                if (this.level === 1) {
                    // 第1关：1种主要颜色 + 1种干扰颜色
                    availableTypes = [7, 6]; // 樱桃色 + 猕猴桃色
                } else if (this.level === 2) {
                    // 第2关：2种主要颜色 + 1种干扰颜色
                    availableTypes = [7, 6, 5]; // 樱桃色、猕猴桃色 + 草莓色
                } else if (this.level === 3) {
                    // 第3关：3种主要颜色 + 1种干扰颜色
                    availableTypes = [7, 6, 5, 4]; // 樱桃色、猕猴桃色、草莓色 + 葡萄色
                } else if (this.level === 4) {
                    // 第4关：4种主要颜色 + 1种干扰颜色
                    availableTypes = [7, 6, 5, 4, 3]; // 4种主要颜色 + 香蕉色
                } else if (this.level === 5) {
                    // 第5关：5种主要颜色 + 1种干扰颜色
                    availableTypes = [7, 6, 5, 4, 3, 2]; // 5种主要颜色 + 橙子色
                } else {
                    // 第6关及以后：使用更多颜色
                    availableTypes = [];
                    for (let i = 0; i < PLATE_TYPES.length; i++) {
                        availableTypes.push(i);
                    }
                }

                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getRandomFruitTypeForLevel() {
                // 根据关卡返回随机的可用水果类型（与盘子颜色匹配）
                return this.getPlateTypeForLevel(); // 使用相同的颜色系统
            }

            getGridSizeForLevel() {
                // 根据关卡返回网格大小
                if (this.level === 1) {
                    return { rows: 4, cols: 4 }; // 第1关：4x4
                } else if (this.level === 2) {
                    return { rows: 4, cols: 5 }; // 第2关：4x5
                } else if (this.level === 3) {
                    return { rows: 5, cols: 5 }; // 第3关：5x5
                } else if (this.level === 4) {
                    return { rows: 5, cols: 6 }; // 第4关：5x6
                } else {
                    return { rows: 6, cols: 6 }; // 第5关及以后：6x6
                }
            }

            getTargetFruitTypes() {
                // 获取当前关卡目标水果盘中需要的所有水果类型
                const targetTypes = new Set();

                if (this.fruitPlates) {
                    this.fruitPlates.forEach(plate => {
                        plate.fruits.forEach(fruit => {
                            targetTypes.add(fruit.getData('typeIndex'));
                        });
                    });
                }

                return Array.from(targetTypes);
            }

            createToolArea() {
                // 道具区域位置（在屏幕最下方）
                const toolAreaHeight = 100;
                const toolAreaY = GAME_HEIGHT - toolAreaHeight;
                const toolSize = 80;
                const toolSpacing = 60;
                const totalToolsWidth = 3 * toolSize + 2 * toolSpacing;
                const startToolX = (GAME_WIDTH - totalToolsWidth) / 2;

                // 创建道具背景区域
                const toolBgHeight = toolAreaHeight - 20; // 留10像素上下边距
                const toolBgY = toolAreaY + 10; // 从顶部留10像素开始

                const toolBg = this.add.graphics();
                toolBg.fillStyle(0x2C3E50, 0.8);
                toolBg.fillRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);
                toolBg.lineStyle(2, 0x34495E, 0.9);
                toolBg.strokeRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);

                // 道具信息
                const tools = [
                    { icon: '🔨', name: '锤子' },
                    { icon: '↶', name: '撤销' },
                    { icon: '🔄', name: '交换' }
                ];

                // 创建道具按钮（在背景框内上下居中）
                tools.forEach((tool, index) => {
                    const toolX = startToolX + index * (toolSize + toolSpacing);
                    const toolY = toolBgY + toolBgHeight / 2 - toolSize / 2; // 在背景框内垂直居中

                    // 创建道具按钮背景
                    const buttonBg = this.add.graphics();
                    buttonBg.fillStyle(0x3498DB, 0.9);
                    buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    buttonBg.lineStyle(3, 0x2980B9, 1);
                    buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);

                    // 设置按钮可交互
                    buttonBg.setInteractive(new Phaser.Geom.Rectangle(toolX, toolY, toolSize, toolSize), Phaser.Geom.Rectangle.Contains);

                    // 创建道具图标
                    const toolIcon = this.add.text(toolX + toolSize/2, toolY + toolSize/2, tool.icon, {
                        fontSize: '48px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    // 添加按钮悬停效果
                    buttonBg.on('pointerover', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x5DADE2, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x3498DB, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        toolIcon.setScale(1.1);
                    });

                    buttonBg.on('pointerout', () => {
                        buttonBg.clear();
                        buttonBg.fillStyle(0x3498DB, 0.9);
                        buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        buttonBg.lineStyle(3, 0x2980B9, 1);
                        buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                        toolIcon.setScale(1.0);
                    });

                    // 添加点击事件（暂时只显示提示）
                    buttonBg.on('pointerdown', () => {
                        this.showToolTip(tool.name, toolX + toolSize/2, toolY - 20);
                    });
                });
            }

            showToolTip(toolName, x, y) {
                // 显示道具提示
                const tooltip = this.add.text(x, y, `${toolName}功能开发中...`, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    backgroundColor: '#2C3E50',
                    padding: { x: 10, y: 5 },
                    resolution: 2
                }).setOrigin(0.5);
                tooltip.setDepth(2000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (tooltip) {
                        this.tweens.add({
                            targets: tooltip,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                tooltip.destroy();
                            }
                        });
                    }
                });
            }

            spawnTargetFruits() {
                this.createEnemyArea();
            }

            createEnemyArea() {
                if (this.fruitPlates) {
                    this.fruitPlates.forEach(plate => {
                        if (plate.background) {
                            plate.background.destroy();
                        }
                        plate.fruits.forEach(fruit => {
                            if (fruit) fruit.destroy();
                        });
                    });
                }

                if (this.fruitPlateTitle) {
                    this.fruitPlateTitle.destroy();
                }

                const enemyAreaY = 220;
                const plateCount = this.getPlateCountForLevel(this.level);
                const plateWidth = 140;
                const plateSpacing = 20;
                const totalPlatesWidth = plateCount * plateWidth + (plateCount - 1) * plateSpacing;
                const startPlateX = (GAME_WIDTH - totalPlatesWidth) / 2;

                this.fruitPlates = [];

                for (let plateIndex = 0; plateIndex < plateCount; plateIndex++) {
                    const plateX = startPlateX + plateIndex * (plateWidth + plateSpacing);
                    const plateY = enemyAreaY;

                    const plateBg = this.add.graphics();
                    plateBg.setDepth(10); // 设置较高的深度，确保在前面

                    // 绘制投影（更明显）
                    plateBg.fillStyle(0x000000, 0.4);
                    plateBg.fillRoundedRect(plateX + 4, plateY - 56, plateWidth, 120, 10);

                    // 绘制盘子外圈（主体，更明显的颜色）
                    plateBg.fillStyle(0xD2691E, 0.8); // 更亮的棕色
                    plateBg.fillRoundedRect(plateX, plateY - 60, plateWidth, 120, 10);

                    // 绘制盘子内圈（凹陷效果）
                    const innerMargin = 10;
                    plateBg.fillStyle(0x8B4513, 0.9); // 深棕色
                    plateBg.fillRoundedRect(plateX + innerMargin, plateY - 60 + innerMargin,
                                          plateWidth - innerMargin * 2, 120 - innerMargin * 2, 8);

                    // 绘制最内层（食物放置区域）
                    const innerestMargin = 16;
                    plateBg.fillStyle(0x654321, 0.7); // 最深的棕色
                    plateBg.fillRoundedRect(plateX + innerestMargin, plateY - 60 + innerestMargin,
                                          plateWidth - innerestMargin * 2, 120 - innerestMargin * 2, 6);

                    // 绘制外边框（更粗更明显）
                    plateBg.lineStyle(3, 0x8B4513, 1.0);
                    plateBg.strokeRoundedRect(plateX, plateY - 60, plateWidth, 120, 10);

                    // 绘制内圈边框
                    plateBg.lineStyle(2, 0x654321, 0.8);
                    plateBg.strokeRoundedRect(plateX + innerMargin, plateY - 60 + innerMargin,
                                            plateWidth - innerMargin * 2, 120 - innerMargin * 2, 8);

                    const plate = {
                        x: plateX + plateWidth / 2,
                        y: plateY,
                        background: plateBg,
                        fruits: [],
                        isComplete: false
                    };

                    const smallFruitSize = 50;
                    const fruitSpacing = 10;
                    const startFruitX = plateX + (plateWidth - (2 * smallFruitSize + fruitSpacing)) / 2;
                    const startFruitY = plateY - (smallFruitSize + fruitSpacing / 2);

                    for (let row = 0; row < 2; row++) {
                        for (let col = 0; col < 2; col++) {
                            const fruitX = startFruitX + col * (smallFruitSize + fruitSpacing);
                            const fruitY = startFruitY + row * (smallFruitSize + fruitSpacing);
                            const fruitTypeIndex = this.getFruitTypeForPlate(plateIndex, row * 2 + col);
                            const smallFruit = this.createSmallFruit(fruitX, fruitY, fruitTypeIndex, smallFruitSize);
                            plate.fruits.push(smallFruit);
                        }
                    }

                    this.fruitPlates.push(plate);
                }
            }

            initializeGame() {
                if (this.tileGroup) {
                    this.tileGroup.clear(true, true);
                }

                this.tileGroup = this.add.group();
                this.tileGrid = [];

                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.gameInputDisabled = false;
                this.createEnemyArea();
                this.createStackLayout();
            }

            createStackLayout() {
                // 根据关卡创建不同大小的方块网格
                const gridSize = this.getGridSizeForLevel();
                const rows = gridSize.rows;
                const cols = gridSize.cols;
                const tileSpacing = 102; // 120% (85 * 1.2)

                // 计算位置（水平居中，垂直从区域顶部开始）
                const totalWidth = (cols - 1) * tileSpacing;
                const totalHeight = (rows - 1) * tileSpacing;
                const startX = this.stackArea.x + (this.stackArea.width - totalWidth) / 2;
                const startY = this.stackArea.y + 20; // 从区域顶部开始，留20像素边距

                // 初始化网格数组
                this.tileGrid = [];
                for (let row = 0; row < rows; row++) {
                    this.tileGrid[row] = [];
                    for (let col = 0; col < cols; col++) {
                        this.tileGrid[row][col] = null;
                    }
                }

                // 生成方块类型数组，确保每种类型都是3的倍数
                const totalTiles = rows * cols;
                const tileTypes = this.generateTileTypes(totalTiles);
                let tileIndex = 0;

                // 创建方块网格
                for (let row = 0; row < rows; row++) {
                    for (let col = 0; col < cols; col++) {
                        const tileX = startX + col * tileSpacing;
                        const tileY = startY + row * tileSpacing;
                        const tileType = tileTypes[tileIndex++];

                        const tile = this.createGridTile(tileX, tileY, tileType, row, col);
                        this.tileGrid[row][col] = tile;
                    }
                }

                // 更新可点击状态（只有第一行可点击）
                this.updateGridClickableStates();
            }

            createGridTile(x, y, typeIndex, row, col) {
                const tileType = TILE_TYPES[typeIndex];

                // 创建方块容器
                const container = this.add.container(x, y);

                // 创建方块背景（占满格子）
                const tile = this.add.image(0, 0, `tile_${typeIndex}`);
                tile.setScale(1.2); // 增大到占满格子

                // 创建emoji文字（占满格子）
                const emoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: '69px', // 150% (46 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 不再需要等级数字，水果没有等级概念

                container.add([tile, emoji]);
                container.setSize(TILE_SIZE, TILE_SIZE);
                container.setInteractive();

                // 设置数据
                container.setData('typeIndex', typeIndex);
                container.setData('gridRow', row);
                container.setData('gridCol', col);
                container.setData('isClickable', false);

                // 添加悬停效果
                container.on('pointerover', () => {
                    if (container.getData('isClickable') && !this.isAnimating) {
                        container.setScale(1.1);
                    }
                });

                container.on('pointerout', () => {
                    if (!this.isAnimating) {
                        container.setScale(1.0);
                    }
                });

                this.tileGroup.add(container);

                return container;
            }

            updateGridClickableStates() {
                // 只有第一行的方块可以点击
                for (let row = 0; row < this.tileGrid.length; row++) {
                    for (let col = 0; col < this.tileGrid[row].length; col++) {
                        const tile = this.tileGrid[row][col];
                        if (tile) {
                            const isClickable = (row === 0);
                            tile.setData('isClickable', isClickable);
                            tile.setAlpha(isClickable ? 1.0 : 0.8);
                        }
                    }
                }
            }



            generateTileTypes(totalTiles) {
                const types = [];

                // 根据关卡确定可用的水果种类（前5关只有1种干扰水果）
                let availableTypes = [];

                if (this.level === 1) {
                    // 第1关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6]; // 樱桃（目标） + 猕猴桃（干扰）
                } else if (this.level === 2) {
                    // 第2关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5]; // 樱桃、猕猴桃（目标） + 草莓（干扰）
                } else if (this.level === 3) {
                    // 第3关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4]; // 樱桃、猕猴桃、草莓（目标） + 葡萄（干扰）
                } else if (this.level === 4) {
                    // 第4关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 3]; // 4种目标水果 + 香蕉（干扰）
                } else if (this.level === 5) {
                    // 第5关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 2]; // 使用橙子作为干扰
                } else {
                    // 第6关及以后：使用更多干扰水果
                    availableTypes = [];
                    for (let i = 0; i < TILE_TYPES.length; i++) {
                        availableTypes.push(i);
                    }
                }

                // 计算需要的水果组数（每组3个）
                const typesNeeded = Math.ceil(totalTiles / 3);

                // 为了增加挑战性，优先生成目标水果，然后添加干扰水果
                const targetTypes = this.getTargetFruitTypes(); // 获取当前关卡的目标水果类型
                const interferenceTypes = availableTypes.filter(type => !targetTypes.includes(type));

                // 先确保有足够的目标水果（至少每种目标水果有3个）
                targetTypes.forEach(targetType => {
                    types.push(targetType, targetType, targetType);
                });

                // 然后添加干扰水果和额外的目标水果
                const remainingSlots = typesNeeded - targetTypes.length;
                for (let i = 0; i < remainingSlots; i++) {
                    const typeIndex = availableTypes[i % availableTypes.length];
                    types.push(typeIndex, typeIndex, typeIndex);
                }

                // 打乱数组
                for (let i = types.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [types[i], types[j]] = [types[j], types[i]];
                }

                return types.slice(0, totalTiles);
            }



            onTileClick(pointer) {
                if (this.isAnimating || this.gameOver || this.gameInputDisabled) return;

                // 找到被点击的方块
                let clickedTile = null;
                let clickedRow = -1;
                let clickedCol = -1;

                // 遍历网格找到被点击的方块
                for (let row = 0; row < this.tileGrid.length; row++) {
                    for (let col = 0; col < this.tileGrid[row].length; col++) {
                        const tile = this.tileGrid[row][col];
                        if (tile && tile.getBounds().contains(pointer.x, pointer.y) && tile.getData('isClickable')) {
                            clickedTile = tile;
                            clickedRow = row;
                            clickedCol = col;
                            break;
                        }
                    }
                    if (clickedTile) break;
                }

                if (clickedTile) {
                    this.selectGridTile(clickedTile, clickedRow, clickedCol);
                }
            }

            selectGridTile(tile, row, col) {
                this.isAnimating = true;

                const emptySlot = this.slots.find(slot => slot.tile === null && !slot.isLocked);

                if (!emptySlot) {
                    this.gameOver = true;
                    this.showGameOver();
                    this.isAnimating = false;
                    return;
                }

                this.tileGrid[row][col] = null;

                this.tweens.add({
                    targets: tile,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    scaleX: 0.83,
                    scaleY: 0.83,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        console.log('🎯 TILE LANDED: Tile landed in slot, calling processNewFruitInSlot');
                        emptySlot.tile = tile;
                        this.processNewFruitInSlot();
                        this.moveColumnUp(col);
                    }
                });
            }

            moveColumnUp(col) {
                if (this.gameOver || this.isAnimating) return;

                const gridSize = this.getGridSizeForLevel();
                const rows = gridSize.rows;
                const cols = gridSize.cols;
                const tileSpacing = 102;
                const startX = this.stackArea.x + (this.stackArea.width - (cols - 1) * tileSpacing) / 2;
                const startY = this.stackArea.y + 20;

                let emptyRow = -1;
                for (let row = 0; row < rows; row++) {
                    if (!this.tileGrid[row][col]) {
                        emptyRow = row;
                        break;
                    }
                }

                if (emptyRow !== -1) {
                    for (let row = emptyRow; row < rows - 1; row++) {
                        if (this.tileGrid[row + 1][col]) {
                            const tile = this.tileGrid[row + 1][col];
                            this.tileGrid[row][col] = tile;
                            this.tileGrid[row + 1][col] = null;

                            tile.setData('gridRow', row);

                            const newY = startY + row * tileSpacing;
                            this.tweens.add({
                                targets: tile,
                                y: newY,
                                duration: 200,
                                ease: 'Power2'
                            });
                        }
                    }
                }

                const newTileType = this.getRandomFruitTypeForLevel();
                const newX = startX + col * tileSpacing;
                const newY = startY + (rows - 1) * tileSpacing;

                try {
                    const newTile = this.createGridTile(newX, newY, newTileType, rows - 1, col);
                    if (!newTile) {
                        console.error(`Failed to create new tile at col ${col}, row ${rows - 1}`);
                        alert(`创建新水果失败！列${col}，行${rows - 1}`);
                        return;
                    }

                    this.tileGrid[rows - 1][col] = newTile;
                    console.log(`New tile created at col ${col}, row ${rows - 1}, type ${newTileType}`);

                    newTile.y += 100;
                    newTile.setAlpha(0);
                    this.tweens.add({
                        targets: newTile,
                        y: newY,
                        alpha: 1,
                        duration: 300,
                        ease: 'Power2',
                        onComplete: () => {
                            console.log(`New tile animation complete at col ${col}`);
                            this.updateGridClickableStates();
                        }
                    });
                } catch (error) {
                    console.error('Failed to create new tile:', error);
                    alert(`创建新水果出错：${error.message}`);
                    this.updateGridClickableStates();
                }
            }



            processNewFruitInSlot() {
                console.log('=== processNewFruitInSlot CALLED ===');
                console.log('🎯 DETECTION START: Checking for matches');
                this.logSlotStatus();

                const allMatches = this.findAllMatchingFruits();

                if (allMatches.length > 0) {
                    console.log(`✅ MATCHES FOUND: ${allMatches.length} fruits will fly to plates`);
                    this.animateAllFruitsToPlates(allMatches);
                } else {
                    console.log('❌ NO MATCH: No matching fruits found');
                    this.isAnimating = false;
                    this.checkGameOverCondition();
                }
                console.log('=== processNewFruitInSlot END ===');
            }

            logSlotStatus() {
                console.log('📦 SLOT STATUS:');
                this.slots.forEach((slot, index) => {
                    if (slot.tile) {
                        const fruitType = slot.tile.getData('typeIndex');
                        const fruitEmoji = TILE_TYPES[fruitType].emoji;
                        console.log(`  Slot ${index}: ${fruitEmoji} (type ${fruitType})`);
                    } else {
                        console.log(`  Slot ${index}: empty`);
                    }
                });

                const occupiedSlots = this.slots.filter(slot => slot.tile !== null).length;
                console.log(`📊 SLOTS SUMMARY: ${occupiedSlots}/${this.slots.length} slots occupied`);
            }

            findAllMatchingFruits() {
                console.log('🔍 MATCHING: Starting fruit matching process');
                const matches = [];

                // 按优先级排序盘子：1. 有水果但未满的盘子 2. 空盘子 3. 按x坐标从左到右
                const prioritizedPlates = this.getPrioritizedPlates();

                console.log('🍽️ PRIORITIZED PLATES:');
                prioritizedPlates.forEach((plate, index) => {
                    const matchedCount = plate.fruits.filter(f => f.getData('isMatched')).length;
                    const availableFruits = plate.fruits.filter(f => !f.getData('isMatched') && !f.getData('isReserved'));
                    const fruitTypes = availableFruits.map(f => TILE_TYPES[f.getData('typeIndex')].emoji);
                    const priority = matchedCount > 0 ? '🔥HIGH' : '⭐LOW';
                    console.log(`  ${priority} Plate ${index} (x=${plate.x}, ${matchedCount}/4 filled): ${fruitTypes.join('')} (${availableFruits.length} available)`);
                });

                for (let slotIndex = 0; slotIndex < this.slots.length; slotIndex++) {
                    const slot = this.slots[slotIndex];
                    if (!slot.tile) continue;

                    const fruitType = slot.tile.getData('typeIndex');
                    const fruitEmoji = TILE_TYPES[fruitType].emoji;
                    console.log(`🎯 CHECKING: Slot ${slotIndex} has ${fruitEmoji} (type ${fruitType})`);

                    for (let plateIndex = 0; plateIndex < prioritizedPlates.length; plateIndex++) {
                        const plate = prioritizedPlates[plateIndex];
                        if (plate.isBeingCompleted) {
                            console.log(`  ⏭️ SKIP: Plate ${plateIndex} is being completed`);
                            continue;
                        }

                        const availableFruit = plate.fruits.find(fruit =>
                            fruit &&
                            fruit.active &&
                            fruit.getData('typeIndex') === fruitType &&
                            !fruit.getData('isMatched') &&
                            !fruit.getData('isReserved')
                        );

                        if (availableFruit) {
                            availableFruit.setData('isReserved', true);
                            const matchedCount = plate.fruits.filter(f => f.getData('isMatched')).length;
                            const priority = matchedCount > 0 ? '🔥HIGH' : '⭐LOW';
                            console.log(`  ✅ MATCH (${priority}): Slot ${slotIndex} ${fruitEmoji} → Plate ${plateIndex} (x=${plate.x}, ${matchedCount}/4 filled)`);
                            matches.push({
                                slot: slot,
                                fruit: availableFruit,
                                plate: plate
                            });
                            break;
                        } else {
                            console.log(`  ❌ NO MATCH: Plate ${plateIndex} has no available ${fruitEmoji}`);
                        }
                    }
                }

                console.log(`🎯 TOTAL MATCHES: Found ${matches.length} matches`);
                return matches;
            }

            getPrioritizedPlates() {
                const platesWithProgress = [];
                const emptyPlates = [];

                this.fruitPlates.forEach(plate => {
                    const matchedCount = plate.fruits.filter(f => f.getData('isMatched')).length;
                    if (matchedCount > 0 && matchedCount < 4) {
                        platesWithProgress.push(plate);
                    } else if (matchedCount === 0) {
                        emptyPlates.push(plate);
                    }
                });

                // 有进度的盘子按匹配数量降序排列（越接近满的优先级越高）
                platesWithProgress.sort((a, b) => {
                    const aMatched = a.fruits.filter(f => f.getData('isMatched')).length;
                    const bMatched = b.fruits.filter(f => f.getData('isMatched')).length;
                    if (aMatched !== bMatched) {
                        return bMatched - aMatched; // 匹配数量多的优先
                    }
                    return a.x - b.x; // 匹配数量相同时按位置排序
                });

                // 空盘子按x坐标从左到右排列
                emptyPlates.sort((a, b) => a.x - b.x);

                // 返回优先级排序：有进度的盘子 + 空盘子
                return [...platesWithProgress, ...emptyPlates];
            }

            animateAllFruitsToPlates(matches) {
                let completedAnimations = 0;
                const totalAnimations = matches.length;

                console.log(`🚀 BATCH ANIMATION: Starting ${totalAnimations} fruit animations`);

                matches.forEach((matchResult, index) => {
                    const { slot, fruit, plate } = matchResult;
                    const tileToMove = slot.tile;
                    const fruitEmoji = TILE_TYPES[fruit.getData('typeIndex')].emoji;

                    console.log(`🚀 FRUIT ${index + 1}/${totalAnimations}: ${fruitEmoji} flying to plate at x=${plate.x}`);

                    slot.tile = null;

                    this.tweens.add({
                        targets: tileToMove,
                        x: fruit.x,
                        y: fruit.y,
                        scaleX: 0.5,
                        scaleY: 0.5,
                        alpha: 0.8,
                        duration: 350,
                        ease: 'Power2',
                        onComplete: () => {
                            console.log(`💥 FRUIT LANDED ${index + 1}/${totalAnimations}: ${fruitEmoji} landed on plate`);
                            this.createFruitMergeEffect(fruit.x, fruit.y);
                            tileToMove.destroy();

                            fruit.setData('isReserved', false);
                            fruit.setData('isMatched', true);
                            fruit.setAlpha(1.0);

                            this.checkPlateCompletion(plate);

                            completedAnimations++;
                            if (completedAnimations === totalAnimations) {
                                console.log('🎉 ALL ANIMATIONS COMPLETE: All fruits have landed');
                                this.time.delayedCall(100, () => {
                                    console.log('🔄 CONTINUE: Checking for more matches after all fruits landed');
                                    console.log('📦 SLOTS AFTER ALL LANDINGS:');
                                    this.logSlotStatus();
                                    this.processNewFruitInSlot();
                                });
                            }
                        }
                    });
                });
            }

            checkPlateCompletion(plate) {
                console.log(`🔍 PLATE CHECK START: Checking plate at x=${plate.x}`);

                const matchedCount = plate.fruits.filter(fruit =>
                    fruit.getData('isMatched')
                ).length;

                console.log(`🍽️ PLATE CHECK: Plate at x=${plate.x} has ${matchedCount}/4 matched fruits`);

                if (matchedCount === 4) {
                    alert(`盘子满了！位置x=${plate.x}，即将消除`);
                    console.log(`🎉 PLATE COMPLETE: Plate at x=${plate.x} is full! Starting completion in 200ms...`);
                    plate.isBeingCompleted = true;
                    this.time.delayedCall(200, () => {
                        console.log(`⏰ COMPLETION TIMER: Calling completePlate for plate at x=${plate.x}`);
                        this.completePlate(plate);
                    });
                } else {
                    console.log(`⏳ PLATE WAITING: Plate at x=${plate.x} needs ${4-matchedCount} more fruits`);
                }
            }

            completePlate(plate) {
                console.log(`💥 PLATE COMPLETION: Starting completion for plate at x=${plate.x}`);
                console.log(`🔧 DEBUG: completePlate called, this.fruitPlates.length = ${this.fruitPlates.length}`);

                const points = 100;
                this.score += points;
                this.scoreText.setText(`🪙 ${this.score}`);

                this.showCoinFlyAnimation(plate.x, plate.y, points);
                this.showScoreAnimation(points);

                console.log(`🎬 PLATE ANIMATION: Starting disappear animation for plate at x=${plate.x}`);
                this.animatePlateDisappear(plate);
                this.reorganizeSlots();

                this.isAnimating = false;

                this.time.delayedCall(600, () => {
                    console.log(`📊 PLATES REMAINING: ${this.fruitPlates.length} plates left`);
                    if (this.fruitPlates.length === 0) {
                        console.log(`🏆 LEVEL COMPLETE: All plates cleared!`);
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                    } else {
                        console.log(`🔄 CONTINUE GAME: Calling processNewFruitInSlot after plate completion`);
                        console.log('📦 SLOTS AFTER PLATE COMPLETION:');
                        this.logSlotStatus();
                        this.processNewFruitInSlot();
                    }
                });
            }



            createFruitMergeEffect(x, y) {
                const sparkles = ['✨', '💫', '⭐'];
                const sparkle = sparkles[Phaser.Math.Between(0, sparkles.length - 1)];

                const effect = this.add.text(x, y, sparkle, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                effect.setScale(0);
                this.tweens.add({
                    targets: effect,
                    scaleX: 1.5,
                    scaleY: 1.5,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2',
                    onComplete: () => effect.destroy()
                });
            }



            animatePlateDisappear(plate) {
                const plateElements = [plate.background, ...plate.fruits];

                this.tweens.add({
                    targets: plateElements,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    alpha: 0.8,
                    duration: 200,
                    ease: 'Power2',
                    yoyo: true,
                    onComplete: () => {
                        this.tweens.add({
                            targets: plateElements,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 400,
                            ease: 'Back.easeIn',
                            onComplete: () => {
                                this.destroyPlate(plate);
                            }
                        });
                    }
                });
            }

            destroyPlate(plate) {
                console.log(`🗑️ PLATE DESTROY: Removing plate at x=${plate.x}`);
                console.log(`📊 BEFORE REMOVAL: ${this.fruitPlates.length} plates total`);

                if (plate.background && plate.background.active) {
                    plate.background.destroy();
                }

                plate.fruits.forEach(fruit => {
                    if (fruit && fruit.active) {
                        fruit.destroy();
                    }
                });
                plate.fruits = [];

                const plateIndex = this.fruitPlates.indexOf(plate);
                if (plateIndex > -1) {
                    this.fruitPlates.splice(plateIndex, 1);
                    console.log(`✅ PLATE REMOVED: Removed plate at index ${plateIndex}`);
                } else {
                    console.log(`❌ PLATE NOT FOUND: Could not find plate in array`);
                }

                console.log(`📊 AFTER REMOVAL: ${this.fruitPlates.length} plates remaining`);
                const remainingPlates = this.fruitPlates.map((p, i) => ({
                    index: i,
                    x: p.x,
                    fruitsCount: p.fruits.length,
                    isBeingCompleted: p.isBeingCompleted
                }));
                console.log(`📋 REMAINING PLATES:`, remainingPlates);

                this.time.delayedCall(300, () => {
                    console.log(`🔄 REORGANIZE: Starting plate reorganization`);
                    this.reorganizeFruitPlates();
                });
            }

            completeLevel() {
                // 关卡完成处理
                this.level++;
                this.levelText.setText(`${this.level}`);

                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '🤣', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示关卡完成文字
                const levelCompleteText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, `第${this.level - 1}关完成！`, {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                levelCompleteText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建下一关按钮
                const nextButton = this.createNextLevelButton();

                // 启动散花特效
                this.startConfettiEffect();

                // 存储界面元素以便清理
                this.levelCompleteUI = {
                    overlay,
                    bigEmoji,
                    levelCompleteText,
                    scoreText,
                    nextButton
                };
            }

            createNextLevelButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0x4CAF50);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0x45A049);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100,
                    this.level > 10 ? '游戏完成' : '下一关', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onNextLevelClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            disableGameInput() {
                // 禁用游戏输入
                this.gameInputDisabled = true;

                // 禁用所有方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.disableInteractive();
                    });
                }
            }

            enableGameInput() {
                // 启用游戏输入
                this.gameInputDisabled = false;

                // 重新启用方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.setInteractive();
                    });
                }
            }

            onNextLevelClick() {
                // 清理关卡完成界面
                if (this.levelCompleteUI) {
                    Object.values(this.levelCompleteUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.levelCompleteUI = null;
                }

                // 停止散花特效
                this.stopConfettiEffect();

                // 重新启用游戏输入
                this.enableGameInput();

                // 检查是否达到最大关卡
                if (this.level > 10) {
                    this.showVictory();
                } else {
                    // 重新初始化游戏进入下一关
                    this.initializeGame();
                }
            }

            startConfettiEffect() {
                // 创建散花特效
                this.confettiParticles = [];
                this.confettiTimer = this.time.addEvent({
                    delay: 100,
                    callback: this.createConfettiParticle,
                    callbackScope: this,
                    loop: true
                });

                // 3秒后停止生成新的散花
                this.time.delayedCall(3000, () => {
                    if (this.confettiTimer) {
                        this.confettiTimer.destroy();
                        this.confettiTimer = null;
                    }
                });
            }

            createConfettiParticle() {
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0xFECA57, 0xFF9FF3, 0xA8E6CF];
                const shapes = ['●', '★', '♦', '▲'];

                // 随机选择颜色和形状
                const color = colors[Phaser.Math.Between(0, colors.length - 1)];
                const shape = shapes[Phaser.Math.Between(0, shapes.length - 1)];

                // 在屏幕顶部随机位置创建粒子
                const x = Phaser.Math.Between(0, GAME_WIDTH);
                const y = -20;

                const particle = this.add.text(x, y, shape, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: `#${color.toString(16).padStart(6, '0')}`,
                    resolution: 2
                }).setOrigin(0.5);
                particle.setDepth(1003);

                // 随机旋转和缩放
                particle.setRotation(Phaser.Math.Between(0, 360) * Math.PI / 180);
                particle.setScale(Phaser.Math.FloatBetween(0.5, 1.5));

                // 添加下落和旋转动画
                this.tweens.add({
                    targets: particle,
                    y: GAME_HEIGHT + 50,
                    x: x + Phaser.Math.Between(-100, 100),
                    rotation: particle.rotation + Phaser.Math.Between(2, 6) * Math.PI,
                    alpha: 0,
                    duration: Phaser.Math.Between(2000, 4000),
                    ease: 'Power2',
                    onComplete: () => {
                        particle.destroy();
                        // 从数组中移除
                        const index = this.confettiParticles.indexOf(particle);
                        if (index > -1) {
                            this.confettiParticles.splice(index, 1);
                        }
                    }
                });

                this.confettiParticles.push(particle);
            }

            stopConfettiEffect() {
                // 停止生成新的散花
                if (this.confettiTimer) {
                    this.confettiTimer.destroy();
                    this.confettiTimer = null;
                }

                // 清理现有的散花粒子
                if (this.confettiParticles) {
                    this.confettiParticles.forEach(particle => {
                        if (particle && particle.destroy) {
                            particle.destroy();
                        }
                    });
                    this.confettiParticles = [];
                }
            }

            showCoinFlyAnimation(startX, startY, points) {
                // 创建多个金币图标飞向右上角的金币显示位置
                const coinCount = Math.min(3, Math.ceil(points / 50)); // 减少金币数量，根据分数决定，最多3个

                // 获取金币显示文字的准确位置
                const scoreTextBounds = this.scoreText.getBounds();
                const targetX = scoreTextBounds.x + 18; // 金币图标在文字中的大概位置
                const targetY = scoreTextBounds.y + scoreTextBounds.height / 2; // 垂直居中

                for (let i = 0; i < coinCount; i++) {
                    // 减少延迟时间，让金币更快飞出
                    this.time.delayedCall(i * 50, () => {
                        // 创建金币图标
                        const coin = this.add.text(startX, startY, '🪙', {
                            fontSize: '32px',
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        coin.setDepth(1000);

                        // 添加初始的轻微随机偏移
                        const randomOffsetX = Phaser.Math.Between(-15, 15);
                        const randomOffsetY = Phaser.Math.Between(-15, 15);
                        coin.x += randomOffsetX;
                        coin.y += randomOffsetY;

                        // 金币飞向目标位置的动画
                        this.tweens.add({
                            targets: coin,
                            x: targetX,
                            y: targetY,
                            scaleX: 0.9,
                            scaleY: 0.9,
                            duration: 600, // 减少飞行时间
                            ease: 'Power2',
                            onComplete: () => {
                                // 到达目标位置后的闪烁效果
                                this.tweens.add({
                                    targets: coin,
                                    scaleX: 1.3,
                                    scaleY: 1.3,
                                    alpha: 0,
                                    duration: 150,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        coin.destroy();

                                        // 让金币显示文字闪烁一下
                                        if (i === coinCount - 1) { // 最后一个金币到达时
                                            this.tweens.add({
                                                targets: this.scoreText,
                                                scaleX: 1.15,
                                                scaleY: 1.15,
                                                duration: 120,
                                                ease: 'Power2',
                                                yoyo: true,
                                                repeat: 1
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        // 添加旋转动画
                        this.tweens.add({
                            targets: coin,
                            rotation: Math.PI * 1.5, // 减少旋转角度
                            duration: 600,
                            ease: 'Linear'
                        });
                    });
                }
            }

            showCompleteAnimation(x, y) {
                // 显示完成特效文字
                const completeText = this.add.text(x, y - 30, '完成!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#00FF00',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                completeText.setDepth(500);

                this.tweens.add({
                    targets: completeText,
                    y: completeText.y - 30,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        completeText.destroy();
                    }
                });

                // 添加光环特效
                const halo = this.add.graphics();
                halo.lineStyle(4, 0x00FF00, 0.8);
                halo.strokeCircle(x, y, 30);
                halo.setDepth(499);

                this.tweens.add({
                    targets: halo,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        halo.destroy();
                    }
                });
            }









            reorganizeFruitPlates() {
                console.log(`🔄 REORGANIZE START: ${this.fruitPlates.length} plates to reorganize`);

                if (this.fruitPlates.length === 0) {
                    console.log(`❌ NO PLATES: No plates to reorganize`);
                    return;
                }

                const plateWidth = 140;
                const plateSpacing = 20;
                const remainingCount = this.fruitPlates.length;
                const totalPlatesWidth = remainingCount * plateWidth + (remainingCount - 1) * plateSpacing;
                const startPlateX = (GAME_WIDTH - totalPlatesWidth) / 2;
                const enemyAreaY = 220;

                console.log(`📐 LAYOUT: ${remainingCount} plates, starting at x=${startPlateX}`);

                let totalFruitsToMove = 0;
                let movedFruitsCount = 0;

                this.fruitPlates.forEach((plate, index) => {
                    const newPlateX = startPlateX + index * (plateWidth + plateSpacing);
                    const newCenterX = newPlateX + plateWidth / 2;
                    const oldX = plate.x;

                    console.log(`📦 PLATE ${index}: Moving from x=${oldX} to x=${newCenterX}`);

                    plate.background.clear();
                    plate.background.setDepth(10); // 确保在前面

                    // 绘制投影（更明显）
                    plate.background.fillStyle(0x000000, 0.4);
                    plate.background.fillRoundedRect(newPlateX + 4, enemyAreaY - 56, plateWidth, 120, 10);

                    // 绘制盘子外圈（主体，更明显的颜色）
                    plate.background.fillStyle(0xD2691E, 0.8); // 更亮的棕色
                    plate.background.fillRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);

                    // 绘制盘子内圈（凹陷效果）
                    const innerMargin = 10;
                    plate.background.fillStyle(0x8B4513, 0.9); // 深棕色
                    plate.background.fillRoundedRect(newPlateX + innerMargin, enemyAreaY - 60 + innerMargin,
                                                   plateWidth - innerMargin * 2, 120 - innerMargin * 2, 8);

                    // 绘制最内层（食物放置区域）
                    const innerestMargin = 16;
                    plate.background.fillStyle(0x654321, 0.7); // 最深的棕色
                    plate.background.fillRoundedRect(newPlateX + innerestMargin, enemyAreaY - 60 + innerestMargin,
                                                   plateWidth - innerestMargin * 2, 120 - innerestMargin * 2, 6);

                    // 绘制外边框（更粗更明显）
                    plate.background.lineStyle(3, 0x8B4513, 1.0);
                    plate.background.strokeRoundedRect(newPlateX, enemyAreaY - 60, plateWidth, 120, 10);

                    // 绘制内圈边框
                    plate.background.lineStyle(2, 0x654321, 0.8);
                    plate.background.strokeRoundedRect(newPlateX + innerMargin, enemyAreaY - 60 + innerMargin,
                                                     plateWidth - innerMargin * 2, 120 - innerMargin * 2, 8);

                    plate.x = newCenterX;

                    const smallFruitSize = 50;
                    const fruitSpacing = 10;
                    const startFruitX = newPlateX + (plateWidth - (2 * smallFruitSize + fruitSpacing)) / 2;
                    const startFruitY = enemyAreaY - (smallFruitSize + fruitSpacing / 2);

                    totalFruitsToMove += plate.fruits.length;

                    plate.fruits.forEach((fruit, fruitIndex) => {
                        const row = Math.floor(fruitIndex / 2);
                        const col = fruitIndex % 2;
                        const newFruitX = startFruitX + col * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;
                        const newFruitY = startFruitY + row * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;

                        fruit.setData('isMatched', false);
                        fruit.setData('isReserved', false);
                        fruit.setData('matchedSlotIndex', -1);
                        fruit.setAlpha(0.6);
                        fruit.setData('isDark', true);

                        this.tweens.add({
                            targets: fruit,
                            x: newFruitX,
                            y: newFruitY,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                movedFruitsCount++;
                                if (movedFruitsCount === totalFruitsToMove) {
                                    console.log(`✅ REORGANIZE COMPLETE: All ${totalFruitsToMove} fruits moved to new positions`);
                                    console.log(`🔍 DETECTION: Calling processNewFruitInSlot in 50ms`);
                                    this.time.delayedCall(50, () => {
                                        alert(`盘子重新排列完成！即将检测匹配，暂存区状态：${this.slots.filter(s => s.tile).length}/${this.slots.length}`);
                                        console.log(`🎯 REORGANIZE CALLBACK: About to call processNewFruitInSlot`);
                                        console.log('📦 SLOTS AFTER REORGANIZATION:');
                                        this.logSlotStatus();
                                        this.processNewFruitInSlot();
                                    });
                                }
                            }
                        });
                    });
                });
            }



            areAllTargetsFulfilled() {
                // 检查是否所有水果盘都已消失
                return this.fruitPlates.length === 0;
            }

            calculateStars() {
                // 根据分数计算星星数量
                const baseScore = this.level * 100; // 基础分数
                const scoreRatio = this.score / baseScore;

                if (scoreRatio >= 2.0) {
                    return 3; // 3星：分数达到基础分数的200%
                } else if (scoreRatio >= 1.5) {
                    return 2; // 2星：分数达到基础分数的150%
                } else {
                    return 1; // 1星：完成关卡即可
                }
            }



            checkGameOverCondition() {
                // 检查暂存区是否已满
                const isSlotsFull = this.slots.every(slot => slot.tile !== null);

                if (isSlotsFull) {
                    // 检查是否还有剩余的水果盘
                    if (this.fruitPlates.length === 0) {
                        // 没有水果盘了，关卡完成
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                        return;
                    }

                    // 在一对一匹配的情况下，检查是否有任何剩余水果盘可以完成
                    let canCompleteAnyPlate = false;

                    // 检查每个剩余的水果盘
                    this.fruitPlates.forEach(plate => {
                        // 统计这个水果盘需要的水果类型
                        const requiredTypes = [];
                        plate.fruits.forEach(fruit => {
                            requiredTypes.push(fruit.getData('typeIndex'));
                        });

                        // 统计暂存区的水果类型
                        const availableTypes = [];
                        this.slots.forEach(slot => {
                            if (slot.tile) {
                                availableTypes.push(slot.tile.getData('typeIndex'));
                            }
                        });

                        // 检查是否能一对一匹配完成这个水果盘
                        const tempAvailable = [...availableTypes];
                        let matchCount = 0;

                        requiredTypes.forEach(requiredType => {
                            const index = tempAvailable.indexOf(requiredType);
                            if (index >= 0) {
                                tempAvailable.splice(index, 1); // 移除已匹配的
                                matchCount++;
                            }
                        });

                        if (matchCount === 4) { // 如果能匹配完整个水果盘
                            canCompleteAnyPlate = true;
                        }
                    });

                    // 如果暂存区满了且无法完成任何剩余水果盘，游戏结束
                    if (!canCompleteAnyPlate) {
                        this.gameOver = true;
                        this.time.delayedCall(500, () => {
                            this.showGameOver();
                        });
                    }
                }
            }

            reorganizeSlots() {
                // 收集所有非空的方块
                const remainingTiles = [];
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        remainingTiles.push(slot.tile);
                        slot.tile = null;
                    }
                });

                // 重新排列到左侧
                remainingTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.tile = tile;

                    this.tweens.add({
                        targets: tile,
                        x: slot.x,
                        y: slot.y,
                        duration: 200,
                        ease: 'Power2'
                    });
                });
            }

            showScoreAnimation(points) {
                const scorePopup = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 100, `+${points}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scorePopup.setDepth(500); // 设置较高深度确保分数动画可见

                this.tweens.add({
                    targets: scorePopup,
                    y: scorePopup.y - 50,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scorePopup.destroy();
                    }
                });
            }

            showVictory() {
                // 立即清空暂存区，防止显示残留动物
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 计算关卡评星
                const stars = this.calculateStars();

                // 显示胜利信息
                const victoryBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                victoryBg.setDepth(1000); // 设置最高深度确保在最上层

                const victoryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 180, '恭喜过关！', {
                    fontSize: '90px', // 150% (48 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                victoryText.setDepth(1001); // 设置最高深度确保在最上层

                // 显示星星评级
                this.showStarRating(stars);

                const nextButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, 240, 72, 0x27AE60); // 120%按钮
                nextButton.setStrokeStyle(3, 0x2ECC71);
                nextButton.setInteractive();
                nextButton.setDepth(1001); // 设置最高深度确保在最上层

                const nextText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, '下一关', {
                    fontSize: '39px', // 150% (26 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                nextText.setDepth(1002); // 设置最高深度确保在最上层

                // 在UI界面上添加撒花庆祝特效
                this.showConfettiCelebration();

                nextButton.on('pointerdown', () => {
                    victoryBg.destroy();
                    victoryText.destroy();
                    nextButton.destroy();
                    nextText.destroy();
                    this.nextLevel();
                });
            }

            showConfettiCelebration() {
                // 创建撒花庆祝特效，2次爆炸效果，共2秒
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0xA8E6CF, 0xFD79A8];
                const centerX = GAME_WIDTH / 2;
                const centerY = GAME_HEIGHT / 2 - 80; // "恭喜过关"字上方位置
                const explosionCount = 2; // 2次爆炸
                const explosionInterval = 1000; // 1秒间隔（0秒和1秒时爆炸）

                for (let explosion = 0; explosion < explosionCount; explosion++) {
                    this.time.delayedCall(explosion * explosionInterval, () => {
                        // 每次爆炸创建多个花瓣
                        const confettiPerExplosion = 25; // 增加每次爆炸的花瓣数量
                        for (let i = 0; i < confettiPerExplosion; i++) {
                            this.createConfettiExplosion(centerX, centerY, colors);
                        }
                    });
                }
            }

            createConfettiExplosion(centerX, centerY, colors) {
                // 随机选择颜色
                const color = colors[Math.floor(Math.random() * colors.length)];

                // 创建花瓣形状（更大的尺寸）
                const confetti = this.add.graphics();
                confetti.fillStyle(color);

                // 随机选择花瓣形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形花瓣
                    confetti.fillCircle(0, 0, Math.random() * 8 + 6);
                } else if (shapeType === 1) {
                    // 方形花瓣
                    const size = Math.random() * 12 + 8;
                    confetti.fillRect(-size/2, -size/2, size, size);
                } else {
                    // 三角形花瓣
                    const size = Math.random() * 10 + 8;
                    confetti.fillTriangle(0, -size, -size/2, size/2, size/2, size/2);
                }

                // 从中心点开始
                confetti.x = centerX;
                confetti.y = centerY;
                confetti.setDepth(1003); // 确保在UI界面之上

                // 随机的爆炸方向和距离
                const angle = Math.random() * Math.PI * 2; // 随机角度
                const explosionRadius = Math.random() * 150 + 100; // 爆炸半径
                const explosionX = centerX + Math.cos(angle) * explosionRadius;
                const explosionY = centerY + Math.sin(angle) * explosionRadius;

                // 第一阶段：爆炸扩散
                this.tweens.add({
                    targets: confetti,
                    x: explosionX,
                    y: explosionY,
                    rotation: (Math.random() - 0.5) * 360 * Math.PI / 180,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段：重力下落
                        const fallDistance = GAME_HEIGHT - explosionY + 50;
                        const horizontalDrift = (Math.random() - 0.5) * 100;

                        this.tweens.add({
                            targets: confetti,
                            x: explosionX + horizontalDrift,
                            y: explosionY + fallDistance,
                            rotation: confetti.rotation + (Math.random() - 0.5) * 720 * Math.PI / 180,
                            alpha: 0,
                            duration: 1300, // 调整为1300ms，总时长1.5秒
                            ease: 'Power1',
                            onComplete: () => {
                                confetti.destroy();
                            }
                        });
                    }
                });

                // 添加轻微的摆动效果
                this.tweens.add({
                    targets: confetti,
                    scaleX: 0.8,
                    scaleY: 1.2,
                    duration: 300 + Math.random() * 100,
                    yoyo: true,
                    repeat: 2, // 重复2次，总共约1.2秒
                    ease: 'Sine.easeInOut'
                });
            }

            showStarRating(stars) {
                // 显示星星评级
                const starY = GAME_HEIGHT/2 - 80;
                const starSize = 60;
                const starSpacing = 80;
                const totalWidth = 3 * starSize + 2 * starSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2 + starSize / 2;

                // 创建3个星星位置
                for (let i = 0; i < 3; i++) {
                    const starX = startX + i * (starSize + starSpacing);
                    const isLit = i < stars; // 是否点亮这颗星星

                    // 延迟显示每颗星星
                    this.time.delayedCall(500 + i * 300, () => {
                        this.createAnimatedStar(starX, starY, starSize, isLit);
                    });
                }

                // 显示评级文字
                this.time.delayedCall(1400, () => {
                    let ratingText = '';
                    let ratingColor = '';

                    switch(stars) {
                        case 3:
                            ratingText = '完美通关！';
                            ratingColor = '#FFD700';
                            break;
                        case 2:
                            ratingText = '表现优秀！';
                            ratingColor = '#C0C0C0';
                            break;
                        case 1:
                            ratingText = '成功通关！';
                            ratingColor = '#CD7F32';
                            break;
                    }

                    const rating = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 10, ratingText, {
                        fontSize: '36px',
                        fontFamily: 'Arial, sans-serif',
                        color: ratingColor,
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);
                    rating.setDepth(1003);

                    // 评级文字出现动画
                    rating.setScale(0);
                    this.tweens.add({
                        targets: rating,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 400,
                        ease: 'Back.easeOut'
                    });
                });
            }

            createAnimatedStar(x, y, size, isLit) {
                // 创建星星图形
                const star = this.add.graphics();
                star.setDepth(1003);

                // 设置星星颜色
                const fillColor = isLit ? 0xFFD700 : 0x666666; // 金色或灰色
                const strokeColor = isLit ? 0xFFA500 : 0x444444; // 橙色或深灰色

                star.fillStyle(fillColor);
                star.lineStyle(3, strokeColor);

                // 绘制五角星
                const points = [];
                const outerRadius = size / 2;
                const innerRadius = outerRadius * 0.4;

                for (let i = 0; i < 10; i++) {
                    const angle = (i * Math.PI) / 5;
                    const radius = i % 2 === 0 ? outerRadius : innerRadius;
                    const px = x + Math.cos(angle - Math.PI / 2) * radius;
                    const py = y + Math.sin(angle - Math.PI / 2) * radius;
                    points.push(px, py);
                }

                star.fillPoints(points);
                star.strokePoints(points);

                // 星星出现动画
                star.setScale(0);
                star.setRotation(0);

                this.tweens.add({
                    targets: star,
                    scaleX: 1,
                    scaleY: 1,
                    rotation: Math.PI * 2,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 如果是点亮的星星，添加闪烁效果
                if (isLit) {
                    this.time.delayedCall(600, () => {
                        this.tweens.add({
                            targets: star,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 200,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 添加光芒效果
                        const glow = this.add.graphics();
                        glow.setDepth(1002);
                        glow.fillStyle(0xFFD700, 0.3);
                        glow.fillCircle(x, y, size);
                        glow.setScale(0);

                        this.tweens.add({
                            targets: glow,
                            scaleX: 1.5,
                            scaleY: 1.5,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });
                    });
                }
            }

            showGameOver() {
                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '😭', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示游戏结束文字
                const gameOverText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, '游戏结束！', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#E74C3C',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                gameOverText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建重新开始按钮
                const retryButton = this.createRetryButton();

                // 存储界面元素以便清理
                this.gameOverUI = {
                    overlay,
                    bigEmoji,
                    gameOverText,
                    scoreText,
                    retryButton
                };
            }

            createRetryButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0xE74C3C);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0xC0392B);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100, '重新开始', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onRetryClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            onRetryClick() {
                // 清理游戏结束界面
                if (this.gameOverUI) {
                    Object.values(this.gameOverUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.gameOverUI = null;
                }

                // 重新启用游戏输入
                this.enableGameInput();

                // 重新开始游戏
                this.restartGame();
            }

            nextLevel() {
                this.level++;
                this.levelText.setText(`${this.level}`);
                this.gameOver = false;
                this.isAnimating = false; // 重置动画状态

                // 强制清空暂存区（确保清空）
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }



            restartGame() {
                this.score = 0;
                this.level = 1;
                this.scoreText.setText('🪙 0');
                this.levelText.setText('1');
                this.gameOver = false;
                this.isAnimating = false;

                // 清空暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 清空敌人区域
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }

            // 新游戏逻辑方法
            initializeNewGame() {
                // 清空现有内容
                if (this.tileGroup) {
                    this.tileGroup.clear(true, true);
                }
                if (this.plates) {
                    this.plates.forEach(plate => {
                        if (plate.background) plate.background.destroy();
                        if (plate.icon) plate.icon.destroy();
                    });
                }

                // 创建下方盘子区域和上方水果区域
                this.createPlateArea();
                this.createTopFruitArea();
            }

            createPlateArea() {
                // 创建下方盘子网格（类似上方水果区域的布局）
                const plateAreaY = this.plateArea.y + 50;
                const rows = 3; // 3行
                const cols = 5; // 5列
                const plateSpacing = 90; // 增加盘子间距
                const plateSize = 90; // 盘子大小（再次放大）

                const totalWidth = (cols - 1) * plateSpacing;
                const totalHeight = (rows - 1) * plateSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const startY = plateAreaY;

                this.plates = [];
                this.plateGrid = []; // 创建盘子网格数组

                for (let row = 0; row < rows; row++) {
                    this.plateGrid[row] = [];
                    for (let col = 0; col < cols; col++) {
                        const plateX = startX + col * plateSpacing;
                        const plateY = startY + row * plateSpacing;



                        // 根据关卡选择盘子颜色类型（包含一种干扰颜色）
                        const plateTypeIndex = this.getPlateTypeForLevel();
                        const plateType = PLATE_TYPES[plateTypeIndex];

                        // 创建盘子背景（使用相对坐标）
                        const plateBg = this.add.graphics();
                        plateBg.fillStyle(plateType.color, 0.8);
                        plateBg.fillCircle(0, 0, plateSize/2); // 使用相对坐标(0,0)
                        plateBg.lineStyle(3, 0x2C3E50, 1);
                        plateBg.strokeCircle(0, 0, plateSize/2); // 使用相对坐标(0,0)

                        // 设置Graphics对象的位置
                        plateBg.x = plateX;
                        plateBg.y = plateY;

                        // 设置盘子可交互（使用相对坐标）
                        plateBg.setInteractive(new Phaser.Geom.Circle(0, 0, plateSize/2), Phaser.Geom.Circle.Contains);

                        const plate = {
                            x: plateX,
                            y: plateY,
                            background: plateBg,
                            typeIndex: plateTypeIndex,
                            row: row,
                            col: col,
                            fruits: [], // 存储收集到的水果
                            maxFruits: 4 // 每个盘子最多4个水果
                        };

                        // 添加点击事件
                        plateBg.on('pointerdown', () => {
                            this.onPlateClick(plate);
                        });

                        this.plates.push(plate);
                        this.plateGrid[row][col] = plate;
                    }
                }
            }

            createTopFruit(x, y, typeIndex) {
                // 创建上方水果区域的水果（带背景色）
                const tileType = TILE_TYPES[typeIndex];
                const plateType = PLATE_TYPES[typeIndex];

                // 创建容器
                const fruitContainer = this.add.container(x, y);

                // 创建背景圆角矩形（与盘子颜色匹配）
                const background = this.add.graphics();
                background.fillStyle(plateType.color, 0.8);
                background.fillRoundedRect(-35, -35, 70, 70, 15);
                background.lineStyle(2, 0x2C3E50, 1);
                background.strokeRoundedRect(-35, -35, 70, 70, 15);

                // 创建水果emoji
                const fruitEmoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: '40px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 将背景和emoji添加到容器
                fruitContainer.add([background, fruitEmoji]);
                fruitContainer.setData('typeIndex', typeIndex);
                fruitContainer.setData('isAnimating', false); // 初始化动画状态

                return fruitContainer;
            }

            onPlateClick(plate) {
                if (this.isAnimating || this.gameOver) return;

                // 找到空的暂存槽位（跳过锁住的槽位）
                const emptySlot = this.slots.find(slot => slot.tile === null && !slot.isLocked);
                if (!emptySlot) {
                    // 暂存区已满，游戏结束
                    this.gameOver = true;
                    this.showGameOver();
                    return;
                }

                // 将盘子放入暂存区
                this.isAnimating = true;

                // 创建盘子的副本放入暂存区
                const plateClone = this.add.graphics();
                plateClone.fillStyle(PLATE_TYPES[plate.typeIndex].color, 0.8);
                plateClone.fillCircle(0, 0, 30);
                plateClone.lineStyle(2, 0x2C3E50, 1);
                plateClone.strokeCircle(0, 0, 30);
                plateClone.x = plate.x;
                plateClone.y = plate.y;

                // 让原盘子消失
                plate.background.setVisible(false);

                // 动画移动到暂存区
                this.tweens.add({
                    targets: plateClone,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 将盘子放入槽位
                        emptySlot.tile = plateClone;
                        emptySlot.plateType = plate.typeIndex;
                        emptySlot.fruits = []; // 初始化水果数组

                        // 从上方掉落对应颜色的水果到暂存区的盘子内
                        this.dropFruitFromTop(plate.typeIndex, plateClone);

                        // 让该列的盘子向上移动，并在底部补充新盘子
                        this.movePlateColumnUp(plate);

                        this.isAnimating = false;
                    }
                });
            }

            dropFruitFromTop(plateTypeIndex, targetPlateInSlot) {
                // 从上方水果区域找到对应颜色的水果，从最下方开始查找
                if (!this.topFruits) return;

                // 找到对应的暂存槽位
                const targetSlot = this.slots.find(slot => slot.tile === targetPlateInSlot);
                if (!targetSlot) return;

                // 检查盘子是否已满
                const currentFruitCount = targetSlot.fruits ? targetSlot.fruits.length : 0;
                if (currentFruitCount >= 4) return;

                // 查找第一排（最下方）的所有匹配水果
                const bottomRow = this.topFruits.length - 1;
                const matchingFruits = [];

                for (let col = 0; col < this.topFruits[bottomRow].length; col++) {
                    const fruit = this.topFruits[bottomRow][col];
                    if (fruit && fruit.getData('typeIndex') === plateTypeIndex) {
                        matchingFruits.push({ fruit, row: bottomRow, col });
                    }
                }

                // 让所有匹配的水果飞入盘子（最多4个）
                const maxFruits = Math.min(matchingFruits.length, 4 - currentFruitCount);
                for (let i = 0; i < maxFruits; i++) {
                    const { fruit, row, col } = matchingFruits[i];
                    // 延迟不同时间，形成连续效果，并传递位置索引
                    this.time.delayedCall(i * 100, () => {
                        this.animateFruitToPlate(fruit, row, col, plateTypeIndex, targetPlateInSlot, currentFruitCount + i);
                    });
                }
            }

            animateFruitToPlate(fruit, row, col, plateTypeIndex, targetPlate, positionIndex = null) {
                // 水果移动到暂存区盘子内的动画

                // 找到对应的暂存槽位
                const targetSlot = this.slots.find(slot => slot.tile === targetPlate);
                if (!targetSlot) return;

                // 检查目标槽位是否正在被消除
                if (targetSlot.isBeingEliminated) {
                    return;
                }

                // 计算水果在盘子内的位置（2x2布局）
                const fruitCount = positionIndex !== null ? positionIndex : (targetSlot.fruits ? targetSlot.fruits.length : 0);
                const offsetX = (fruitCount % 2) * 25 - 12.5; // 调整间距
                const offsetY = Math.floor(fruitCount / 2) * 25 - 12.5; // 调整间距

                // 安全地获取水果类型和emoji
                let fruitEmoji = '🍎'; // 默认emoji
                try {
                    const fruitTypeIndex = fruit.getData('typeIndex');
                    if (fruitTypeIndex !== undefined && TILE_TYPES[fruitTypeIndex]) {
                        fruitEmoji = TILE_TYPES[fruitTypeIndex].emoji;
                    }
                } catch (error) {
                    console.warn('获取水果类型失败，使用默认emoji');
                }

                if (this.topFruits && this.topFruits[row] && this.topFruits[row][col]) {
                    this.topFruits[row][col] = null;
                }

                // 创建新的纯文本水果（无背景）用于飞行动画
                const flyingFruit = this.add.text(fruit.x, fruit.y, fruitEmoji, {
                    fontSize: '40px', // 保持原始大小
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                flyingFruit.setDepth(1001); // 确保在盘子上方

                // 销毁原来的容器（带背景的水果）
                if (fruit && fruit.destroy) {
                    fruit.destroy();
                }

                this.tweens.add({
                    targets: flyingFruit,
                    x: targetPlate.x + offsetX,
                    y: targetPlate.y + offsetY,
                    scaleX: 0.4, // 缩小到盘子内
                    scaleY: 0.4,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        // 再次检查目标槽位是否正在被消除
                        if (targetSlot.isBeingEliminated) {
                            flyingFruit.destroy();
                            return;
                        }

                        // 创建最终的小水果（无背景）
                        const finalFruit = this.add.text(flyingFruit.x, flyingFruit.y, fruitEmoji, {
                            fontSize: '16px', // 更小的字体
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        finalFruit.setDepth(1001); // 确保在盘子上方

                        // 销毁飞行的水果
                        flyingFruit.destroy();

                        // 将新的纯文本水果添加到盘子内
                        if (!targetSlot.fruits) targetSlot.fruits = [];
                        targetSlot.fruits.push(finalFruit);

                        // 延迟调用，确保动画完成后再处理下落
                        this.time.delayedCall(100, () => {
                            this.moveFruitColumnDown(col);
                        });

                        // 检查是否有盘子收集满4个水果
                        this.checkPlateCompletion(plateTypeIndex);
                    }
                });
            }

            checkPlateCompletion(plateTypeIndex) {
                // 检查暂存区中是否有装满4个水果的盘子
                this.slots.forEach(slot => {
                    if (slot.tile && slot.plateType === plateTypeIndex && slot.fruits && slot.fruits.length >= 4) {
                        // 找到装满4个水果的盘子，消除它
                        this.completePlate(slot);
                    }
                });
            }

            completePlate(slot) {
                // 消除装满4个水果的盘子
                slot.isBeingEliminated = true;

                // 先消除盘子内的所有水果
                if (slot.fruits && Array.isArray(slot.fruits)) {
                    // 创建水果数组的副本，避免在遍历过程中修改原数组
                    const fruitsToDestroy = [...slot.fruits];

                    fruitsToDestroy.forEach((fruit, index) => {
                        if (fruit && fruit.destroy) {
                            this.tweens.add({
                                targets: fruit,
                                scaleX: 0,
                                scaleY: 0,
                                alpha: 0,
                                duration: 200,
                                ease: 'Power2',
                                delay: index * 50, // 错开消除时间
                                onComplete: () => {
                                    if (fruit && fruit.destroy) {
                                        try {
                                            fruit.destroy();
                                        } catch (error) {
                                            console.warn('水果销毁失败:', error);
                                        }
                                    }
                                }
                            });
                        }
                    });

                    // 立即清空水果数组，防止重复引用
                    slot.fruits = [];
                }

                // 然后消除盘子
                if (slot.tile && slot.tile.destroy) {
                    this.tweens.add({
                        targets: slot.tile,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2',
                        onComplete: () => {
                            if (slot.tile && slot.tile.destroy) {
                                try {
                                    slot.tile.destroy();
                                } catch (error) {
                                    console.warn('盘子销毁失败:', error);
                                }
                            }
                            // 彻底清理槽位数据
                            slot.tile = null;
                            slot.plateType = null;
                            slot.fruits = [];
                        slot.isBeingEliminated = false; // <-- 添加此行以重置标记
                        }
                    });
                } else {
                    // 如果盘子已经不存在，直接清理数据
                    slot.tile = null;
                    slot.plateType = null;
                    slot.fruits = [];
                    slot.isBeingEliminated = false; // <-- 添加此行以重置标记
                }

                // 增加分数
                this.score += 100;
                this.scoreText.setText(`🪙 ${this.score}`);
                this.showCoinFlyAnimation(slot.x, slot.y, 100);
            }

            moveFruitColumnDown(col) {
                // 让该列的水果向下移动，类似重力效果
                const rows = this.topFruits.length;
                const fruitSpacing = 80;
                const totalWidth = (3 - 1) * fruitSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const startY = 160;

                // 找到被移除的水果位置
                let removedRow = -1;
                for (let row = 0; row < rows; row++) {
                    if (this.topFruits[row][col] === null) {
                        removedRow = row;
                        break;
                    }
                }

                if (removedRow === -1) {
                    return;
                }

                // 记录需要移动的水果，避免在循环中修改数组
                const fruitsToMove = [];
                for (let row = removedRow; row > 0; row--) {
                    const upperFruit = this.topFruits[row - 1][col];
                    if (upperFruit && upperFruit.active && upperFruit.visible && !upperFruit.getData('isAnimating')) {
                        fruitsToMove.push({
                            fruit: upperFruit,
                            fromRow: row - 1,
                            toRow: row
                        });
                    }
                }

                // 如果没有水果需要移动，直接生成新水果
                if (fruitsToMove.length === 0) {
                    this.generateNewFruitAtTop(col);
                    return;
                }

                // 执行移动动画
                let completedAnimations = 0;
                const totalAnimations = fruitsToMove.length;

                fruitsToMove.forEach(({ fruit, fromRow, toRow }, index) => {
                    // 计算目标位置
                    const targetX = startX + col * fruitSpacing;
                    const targetY = startY + toRow * fruitSpacing;

                    // 标记水果正在动画中
                    fruit.setData('isAnimating', true);

                    // 移动水果到目标位置
                    this.tweens.add({
                        targets: fruit,
                        x: targetX,
                        y: targetY,
                        duration: 400 + index * 100, // 增加基础时间，让动画更自然
                        ease: 'Bounce.easeOut', // 使用弹跳效果，更像重力
                        delay: index * 60, // 增加错开时间
                        onComplete: () => {
                            // 更新水果的位置信息
                            fruit.x = targetX;
                            fruit.y = targetY;
                            fruit.setData('isAnimating', false);
                            
                            // 更新网格状态
                            this.topFruits[toRow][col] = fruit;
                            this.topFruits[fromRow][col] = null;
                            
                            completedAnimations++;
                            
                            // 所有动画完成后生成新水果
                            if (completedAnimations === totalAnimations) {
                                this.generateNewFruitAtTop(col);
                            }
                        }
                    });
                });
            }

            generateNewFruitAtTop(col) {
                // 在顶部生成新水果
                const fruitSpacing = 80;
                const totalWidth = (3 - 1) * fruitSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const startY = 160;

                const newFruitX = startX + col * fruitSpacing;
                const newFruitY = startY;
                const fruitType = this.getRandomFruitTypeForLevel();
                const newFruit = this.createTopFruit(newFruitX, newFruitY, fruitType);
                
                // 设置初始缩放为0，然后动画出现
                newFruit.setScale(0);
                newFruit.setData('isAnimating', true);
                
                this.tweens.add({
                    targets: newFruit,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 400,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        newFruit.setData('isAnimating', false);
                        // 延迟检查第一排是否有匹配的水果可以自动飞入盘子
                        this.time.delayedCall(300, () => {
                            this.checkBottomRowAutoFly();
                        });
                    }
                });

                this.topFruits[0][col] = newFruit;
            }

            checkBottomRowAutoFly() {
                // 检查第一排（最下方）是否有水果可以自动飞入暂存区的盘子
                if (!this.topFruits || this.topFruits.length === 0) return;

                const bottomRow = this.topFruits.length - 1;
                if (!this.topFruits[bottomRow]) return;

                // 检查是否有水果正在动画中（包括下落和生成动画）
                let hasAnimatingFruits = false;
                for (let col = 0; col < this.topFruits[bottomRow].length; col++) {
                    const fruit = this.topFruits[bottomRow][col];
                    if (fruit && fruit.active && fruit.visible) {
                        // 检查水果是否正在移动或正在生成
                        if (this.tweens.isTweening(fruit) || fruit.getData('isAnimating')) {
                            hasAnimatingFruits = true;
                            break;
                        }
                    }
                }

                // 如果有水果正在动画中，延迟检查
                if (hasAnimatingFruits) {
                    this.time.delayedCall(300, () => {
                        this.checkBottomRowAutoFly();
                    });
                    return;
                }

                // 遍历暂存区的盘子
                this.slots.forEach(slot => {
                    if (slot.tile && slot.plateType !== undefined && !slot.isBeingEliminated) {
                        const currentFruitCount = slot.fruits ? slot.fruits.length : 0;
                        if (currentFruitCount < 4) {
                            // 查找匹配的水果
                            for (let col = 0; col < this.topFruits[bottomRow].length; col++) {
                                const fruit = this.topFruits[bottomRow][col];
                                if (fruit && fruit.active && fruit.visible && 
                                    fruit.getData && fruit.getData('typeIndex') === slot.plateType &&
                                    !fruit.getData('isAnimating') && !this.tweens.isTweening(fruit)) {
                                    // 找到匹配的水果，让它飞入盘子
                                    this.animateFruitToPlate(fruit, bottomRow, col, slot.plateType, slot.tile);
                                    return; // 一次只飞一个水果
                                }
                            }
                        }
                    }
                });
            }

            refillTopFruit(row, col) {
                // 在指定位置补充新的水果
                const fruitSpacing = 80;
                const totalWidth = (3 - 1) * fruitSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const startY = 160;

                const fruitX = startX + col * fruitSpacing;
                const fruitY = startY + row * fruitSpacing;

                const fruitType = this.getRandomFruitTypeForLevel();
                const fruit = this.createTopFruit(fruitX, fruitY, fruitType);
                this.topFruits[row][col] = fruit;
            }

            movePlateColumnUp(clickedPlate) {
                // 让该列的盘子向上移动，类似重力效果
                const col = clickedPlate.col;
                const clickedRow = clickedPlate.row;
                const rows = this.plateGrid.length;

                // 计算坐标参数（与createPlateArea保持一致）
                const plateSpacing = 90; // 与createPlateArea保持一致
                const totalWidth = (5 - 1) * plateSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;
                const plateAreaY = this.plateArea.y + 50;
                const startY = plateAreaY;

                // 销毁被点击的盘子
                clickedPlate.background.destroy();

                // 将该列下方的盘子向上移动（重新计算正确的目标位置）

                for (let row = clickedRow; row < rows - 1; row++) {
                    const lowerPlate = this.plateGrid[row + 1][col];

                    if (lowerPlate) {
                        // 计算目标位置（应该移动到的位置）
                        const targetX = startX + col * plateSpacing;
                        const targetY = startY + row * plateSpacing;



                        // 移动下方盘子到目标位置
                        this.tweens.add({
                            targets: lowerPlate.background,
                            x: targetX,
                            y: targetY,
                            duration: 200,
                            ease: 'Power2'
                        });

                        // 更新盘子对象的位置信息
                        lowerPlate.x = targetX;
                        lowerPlate.y = targetY;
                        lowerPlate.row = row;

                        // 更新网格
                        this.plateGrid[row][col] = lowerPlate;
                    }
                }

                // 在最底行生成新盘子（使用已声明的变量）
                const newPlateX = startX + col * plateSpacing;
                const newPlateY = startY + (rows - 1) * plateSpacing; // 最底行：row = 2



                const plateTypeIndex = this.getPlateTypeForLevel();
                const plateType = PLATE_TYPES[plateTypeIndex];

                // 创建新盘子（使用相对坐标）
                const newPlateBg = this.add.graphics();
                newPlateBg.fillStyle(plateType.color, 0.8);
                newPlateBg.fillCircle(0, 0, 45); // 使用与原盘子相同的大小(90/2=45)
                newPlateBg.lineStyle(3, 0x2C3E50, 1);
                newPlateBg.strokeCircle(0, 0, 45); // 使用与原盘子相同的大小

                // 设置Graphics对象的位置
                newPlateBg.x = newPlateX;
                newPlateBg.y = newPlateY;

                newPlateBg.setInteractive(new Phaser.Geom.Circle(0, 0, 45), Phaser.Geom.Circle.Contains);

                const newPlate = {
                    x: newPlateX,
                    y: newPlateY,
                    background: newPlateBg,
                    typeIndex: plateTypeIndex,
                    row: rows - 1,
                    col: col,
                    fruits: [],
                    maxFruits: 4
                };

                // 添加点击事件
                newPlateBg.on('pointerdown', () => {
                    this.onPlateClick(newPlate);
                });

                // 更新网格
                this.plateGrid[rows - 1][col] = newPlate;

                // 更新plates数组
                const plateIndex = this.plates.findIndex(p => p.row === clickedRow && p.col === col);
                if (plateIndex !== -1) {
                    this.plates[plateIndex] = newPlate;
                }

                // 添加出现动画
                newPlateBg.setScale(0);
                this.tweens.add({
                    targets: newPlateBg,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 200,
                    ease: 'Back.easeOut'
                });
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#2C3E50',
            scene: GameScene,
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false,
                transparent: false,
                clearBeforeRender: true,
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
                powerPreference: "high-performance"
            },
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>

