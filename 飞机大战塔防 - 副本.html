<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji小兵塔防</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#24c061',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let gameScene;
    let gridSize = 6;
    let cellSize = 80;
    let playerGrid = [];
    let enemyGrid = [];
    let playerUnits = [];
    let enemyUnits = [];
    let gameStarted = false;
    let turnTimer = 0;
    let maxTurnTime = 3000; // 3秒一回合
    let playerHealth = 100;
    let enemyHealth = 100;
    let selectedCell = null;
    let draggedUnit = null;
    let dragStartPos = null;
    let isDragging = false;
    let flyingUnits = []; // 正在飞行的单位

    // 单位类型
    const UNIT_TYPES = {
        FIGHTER: { emoji: '✈️', health: 30, attack: 15, range: 2, cost: 50, level: 1, attackType: 'fly' },
        BOMBER: { emoji: '🛩️', health: 50, attack: 25, range: 1, cost: 80, level: 1, attackType: 'fly' },
        MISSILE: { emoji: '🚎', health: 20, attack: 30, range: 3, cost: 60, level: 1, attackType: 'area', areaSize: 1, needsTarget: true },
        ANTIAIR: { emoji: '🎡', health: 40, attack: 8, range: 2, cost: 40, level: 1, attackType: 'continuous' },
        BASE: { emoji: '🏭', health: 100, attack: 0, range: 0, cost: 0, level: 1, attackType: 'none' },
        // 升级版本
        FIGHTER_2: { emoji: '🛸', health: 50, attack: 25, range: 3, cost: 100, level: 2, attackType: 'fly' },
        BOMBER_2: { emoji: '🚁', health: 80, attack: 40, range: 2, cost: 150, level: 2, attackType: 'fly' },
        MISSILE_2: { emoji: '💥', health: 35, attack: 50, range: 4, cost: 120, level: 2, attackType: 'area', areaSize: 2, needsTarget: true },
        ANTIAIR_2: { emoji: '⚡', health: 60, attack: 12, range: 3, cost: 80, level: 2, attackType: 'continuous' }
    };

    // Phaser场景函数
    function preload() {
        gameScene = this;
        // 创建简单的颜色纹理
        this.add.graphics()
            .fillStyle(0x4a90e2)
            .fillRect(0, 0, 1, 1)
            .generateTexture('blue', 1, 1);

        this.add.graphics()
            .fillStyle(0xe74c3c)
            .fillRect(0, 0, 1, 1)
            .generateTexture('red', 1, 1);

        this.add.graphics()
            .fillStyle(0x2ecc71)
            .fillRect(0, 0, 1, 1)
            .generateTexture('green', 1, 1);
    }

    function create() {
        // 初始化游戏
        initializeGame();
        createUI();
        startGame();
    }

    function update() {
        if (!gameStarted) return;

        // 更新回合计时器
        turnTimer += gameScene.game.loop.delta;
        if (turnTimer >= maxTurnTime) {
            processTurn();
            turnTimer = 0;
        }
    }

    // 初始化游戏
    function initializeGame() {
        // 初始化网格
        for (let i = 0; i < gridSize; i++) {
            playerGrid[i] = [];
            enemyGrid[i] = [];
            for (let j = 0; j < gridSize; j++) {
                playerGrid[i][j] = null;
                enemyGrid[i][j] = null;
            }
        }

        // 创建基地 - 只有一个
        playerGrid[5][2] = createUnit(UNIT_TYPES.BASE, true);
        enemyGrid[0][2] = createUnit(UNIT_TYPES.BASE, false);

        // 添加初始单位
        addInitialUnits();
    }

    // 添加全局变量来跟踪持续攻击
    let continuousAttacks = [];

    // 创建单位
    function createUnit(type, isPlayer) {
        return {
            type: type,
            health: type.health,
            maxHealth: type.health,
            attack: type.attack,
            range: type.range,
            isPlayer: isPlayer,
            cooldown: 0,
            level: type.level || 1,
            attackType: type.attackType || 'normal',
            areaSize: type.areaSize || 0,
            isFlying: false
        };
    }

    // 添加初始单位
    function addInitialUnits() {
        // 玩家初始单位
        playerGrid[4][1] = createUnit(UNIT_TYPES.FIGHTER, true);
        playerGrid[4][4] = createUnit(UNIT_TYPES.FIGHTER, true);
        playerGrid[3][1] = createUnit(UNIT_TYPES.ANTIAIR, true);
        playerGrid[3][4] = createUnit(UNIT_TYPES.ANTIAIR, true);

        // 敌方初始单位
        enemyGrid[1][1] = createUnit(UNIT_TYPES.FIGHTER, false);
        enemyGrid[1][4] = createUnit(UNIT_TYPES.FIGHTER, false);
        enemyGrid[2][2] = createUnit(UNIT_TYPES.BOMBER, false);
        enemyGrid[2][3] = createUnit(UNIT_TYPES.BOMBER, false);
    }


    // 创建UI
    function createUI() {
        const centerX = config.width / 2;

        // 标题
        gameScene.add.text(centerX, 50, '飞机大战塔防', {
            fontSize: '32px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 生命值显示
        gameScene.add.text(50, 100, '敌方基地', {
            fontSize: '20px',
            fill: '#ff4444'
        });

        gameScene.add.text(50, 1200, '我方基地', {
            fontSize: '20px',
            fill: '#4444ff'
        });

        // 绘制游戏网格
        drawGrid();

        // 添加拖拽和点击事件
        gameScene.input.on('pointerdown', handlePointerDown);
        gameScene.input.on('pointermove', handlePointerMove);
        gameScene.input.on('pointerup', handlePointerUp);
    }

    // 绘制网格
    function drawGrid() {
        const startX = (config.width - gridSize * cellSize) / 2;
        const enemyStartY = 150;
        const playerStartY = 800;
        const gridWidth = gridSize * cellSize;
        const gridHeight = gridSize * cellSize;

        // 绘制敌方网格大背景
        const enemyBg = gameScene.add.graphics();
        enemyBg.fillStyle(0x2a2a2a, 0.8);
        enemyBg.fillRoundedRect(startX - 10, enemyStartY - 10, gridWidth + 20, gridHeight + 20, 15);
        enemyBg.lineStyle(3, 0x666666, 0.8);
        enemyBg.strokeRoundedRect(startX - 10, enemyStartY - 10, gridWidth + 20, gridHeight + 20, 15);

        // 绘制玩家网格大背景
        const playerBg = gameScene.add.graphics();
        playerBg.fillStyle(0x3a3a3a, 0.8);
        playerBg.fillRoundedRect(startX - 10, playerStartY - 10, gridWidth + 20, gridHeight + 20, 15);
        playerBg.lineStyle(3, 0x888888, 0.8);
        playerBg.strokeRoundedRect(startX - 10, playerStartY - 10, gridWidth + 20, gridHeight + 20, 15);

        // 绘制敌方网格
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const x = startX + j * cellSize;
                const y = enemyStartY + i * cellSize;

                // 创建圆角网格背景
                const cellBg = gameScene.add.graphics();
                cellBg.fillStyle(0x333333, 0.9);
                cellBg.fillRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);
                cellBg.lineStyle(2, 0x666666, 0.6);
                cellBg.strokeRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);

                // 创建透明的交互区域
                const cell = gameScene.add.rectangle(x + cellSize/2, y + cellSize/2, cellSize, cellSize, 0x000000, 0)
                    .setInteractive()
                    .setData('gridType', 'enemy')
                    .setData('row', i)
                    .setData('col', j);
            }
        }

        // 绘制玩家网格
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const x = startX + j * cellSize;
                const y = playerStartY + i * cellSize;

                // 创建圆角网格背景
                const cellBg = gameScene.add.graphics();
                cellBg.fillStyle(0x444444, 0.9);
                cellBg.fillRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);
                cellBg.lineStyle(2, 0x888888, 0.6);
                cellBg.strokeRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);

                // 创建透明的交互区域
                const cell = gameScene.add.rectangle(x + cellSize/2, y + cellSize/2, cellSize, cellSize, 0x000000, 0)
                    .setInteractive()
                    .setData('gridType', 'player')
                    .setData('row', i)
                    .setData('col', j);
            }
        }

        // 绘制单位
        updateDisplay();
    }

    // 更新显示
    function updateDisplay() {
        // 清除之前的单位显示
        if (gameScene.unitTexts) {
            gameScene.unitTexts.forEach(text => text.destroy());
        }
        gameScene.unitTexts = [];

        const startX = (config.width - gridSize * cellSize) / 2;
        const enemyStartY = 150;
        const playerStartY = 800;

        // 显示敌方单位
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const unit = enemyGrid[i][j];
                if (unit) {
                    const x = startX + j * cellSize + cellSize / 2;
                    const y = enemyStartY + i * cellSize + cellSize / 2;

                    const unitText = gameScene.add.text(x, y, unit.type.emoji, {
                        fontSize: '40px'
                    }).setOrigin(0.5);

                    // 为飞机类型单位显示血条
                    if (unit.type.attackType === 'fly') {
                        const healthBarBg = gameScene.add.rectangle(x, y - 35, 40, 6, 0x333333).setOrigin(0.5);
                        const healthPercent = unit.health / unit.maxHealth;
                        const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
                        const healthBar = gameScene.add.rectangle(x, y - 35, 40 * healthPercent, 6, healthBarColor).setOrigin(0.5);
                        gameScene.unitTexts.push(healthBarBg, healthBar);
                    } else {
                        // 非飞机单位显示数字生命值
                        const healthText = gameScene.add.text(x, y + 25, `${unit.health}/${unit.maxHealth}`, {
                            fontSize: '12px',
                            fill: unit.health > unit.maxHealth * 0.5 ? '#00ff00' : '#ff0000'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(healthText);
                    }

                    // 显示等级
                    if (unit.level > 1) {
                        const levelText = gameScene.add.text(x + 25, y - 25, `★${unit.level}`, {
                            fontSize: '14px',
                            fill: '#ffff00'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(levelText);
                    }

                    // 显示冷却状态
                    if (unit.cooldown > 0) {
                        const cooldownText = gameScene.add.text(x - 25, y - 25, `${unit.cooldown}`, {
                            fontSize: '14px',
                            fill: '#ff8800'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(cooldownText);
                    }

                    gameScene.unitTexts.push(unitText);
                }
            }
        }

        // 显示玩家单位
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const unit = playerGrid[i][j];
                if (unit) {
                    const x = startX + j * cellSize + cellSize / 2;
                    const y = playerStartY + i * cellSize + cellSize / 2;

                    const unitText = gameScene.add.text(x, y, unit.type.emoji, {
                        fontSize: '40px'
                    }).setOrigin(0.5);

                    // 为飞机类型单位显示血条
                    if (unit.type.attackType === 'fly') {
                        const healthBarBg = gameScene.add.rectangle(x, y - 35, 40, 6, 0x333333).setOrigin(0.5);
                        const healthPercent = unit.health / unit.maxHealth;
                        const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
                        const healthBar = gameScene.add.rectangle(x, y - 35, 40 * healthPercent, 6, healthBarColor).setOrigin(0.5);
                        gameScene.unitTexts.push(healthBarBg, healthBar);
                    } else {
                        // 非飞机单位显示数字生命值
                        const healthText = gameScene.add.text(x, y + 25, `${unit.health}/${unit.maxHealth}`, {
                            fontSize: '12px',
                            fill: unit.health > unit.maxHealth * 0.5 ? '#00ff00' : '#ff0000'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(healthText);
                    }

                    // 显示等级
                    if (unit.level > 1) {
                        const levelText = gameScene.add.text(x + 25, y - 25, `★${unit.level}`, {
                            fontSize: '14px',
                            fill: '#ffff00'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(levelText);
                    }

                    // 显示冷却状态
                    if (unit.cooldown > 0) {
                        const cooldownText = gameScene.add.text(x - 25, y - 25, `${unit.cooldown}`, {
                            fontSize: '14px',
                            fill: '#ff8800'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(cooldownText);
                    }

                    gameScene.unitTexts.push(unitText);
                }
            }
        }
    }

    // 处理指针按下事件
    function handlePointerDown(pointer) {
        const clickedObject = gameScene.input.hitTestPointer(pointer)[0];
        if (!clickedObject || !clickedObject.getData) return;

        const gridType = clickedObject.getData('gridType');
        const row = clickedObject.getData('row');
        const col = clickedObject.getData('col');

        if (gridType === 'player') {
            if (playerGrid[row][col] !== null) {
                // 开始拖拽
                draggedUnit = playerGrid[row][col];
                dragStartPos = { row, col };
                isDragging = true;
                selectedCell = { row, col };

                // 高亮选中的格子
                highlightCell(row, col, true);
            } else {
                // 空格子，放置新单位
                showUnitMenu(row, col);
            }
        }
    }

    // 处理指针移动事件
    function handlePointerMove(pointer) {
        if (!isDragging) return;

        // 可以在这里添加拖拽预览效果
    }

    // 处理指针释放事件
    function handlePointerUp(pointer) {
        if (!isDragging) return;

        const clickedObject = gameScene.input.hitTestPointer(pointer)[0];
        if (clickedObject && clickedObject.getData) {
            const gridType = clickedObject.getData('gridType');
            const row = clickedObject.getData('row');
            const col = clickedObject.getData('col');

            if (gridType === 'player' && dragStartPos) {
                if (row !== dragStartPos.row || col !== dragStartPos.col) {
                    // 尝试移动或合成
                    handleUnitDrop(dragStartPos.row, dragStartPos.col, row, col);
                }
            }
        }

        // 清除拖拽状态
        isDragging = false;
        draggedUnit = null;
        dragStartPos = null;
        clearHighlights();
    }

    // 处理单位放置
    function handleUnitDrop(fromRow, fromCol, toRow, toCol) {
        const fromUnit = playerGrid[fromRow][fromCol];
        const toUnit = playerGrid[toRow][toCol];

        if (!fromUnit) return;

        if (toUnit === null) {
            // 移动到空格子
            playerGrid[toRow][toCol] = fromUnit;
            playerGrid[fromRow][fromCol] = null;
            updateDisplay();
        } else if (canMergeUnits(fromUnit, toUnit)) {
            // 合成升级
            const mergedUnit = mergeUnits(fromUnit, toUnit);
            if (mergedUnit) {
                playerGrid[toRow][toCol] = mergedUnit;
                playerGrid[fromRow][fromCol] = null;

                // 显示合成特效
                showMergeEffect(toRow, toCol);
                updateDisplay();
            }
        } else {
            // 交换位置
            playerGrid[fromRow][fromCol] = toUnit;
            playerGrid[toRow][toCol] = fromUnit;
            updateDisplay();
        }
    }

    // 检查是否可以合成
    function canMergeUnits(unit1, unit2) {
        return unit1.type === unit2.type && unit1.level === unit2.level && unit1.level < 2;
    }

    // 合成单位
    function mergeUnits(unit1, unit2) {
        if (!canMergeUnits(unit1, unit2)) return null;

        let upgradedType = null;
        if (unit1.type === UNIT_TYPES.FIGHTER) {
            upgradedType = UNIT_TYPES.FIGHTER_2;
        } else if (unit1.type === UNIT_TYPES.BOMBER) {
            upgradedType = UNIT_TYPES.BOMBER_2;
        } else if (unit1.type === UNIT_TYPES.MISSILE) {
            upgradedType = UNIT_TYPES.MISSILE_2;
        } else if (unit1.type === UNIT_TYPES.ANTIAIR) {
            upgradedType = UNIT_TYPES.ANTIAIR_2;
        }

        if (upgradedType) {
            return createUnit(upgradedType, true);
        }

        return null;
    }

    // 显示合成特效
    function showMergeEffect(row, col) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 800;
        const x = startX + col * cellSize + cellSize / 2;
        const y = playerStartY + row * cellSize + cellSize / 2;

        // 创建升级特效
        const upgradeEffect = gameScene.add.text(x, y - 30, '⬆️ 升级！', {
            fontSize: '20px',
            fill: '#ffff00'
        }).setOrigin(0.5);

        gameScene.tweens.add({
            targets: upgradeEffect,
            y: y - 60,
            alpha: 0,
            duration: 1000,
            onComplete: () => upgradeEffect.destroy()
        });
    }

    // 高亮格子
    function highlightCell(row, col, isPlayer) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isPlayer ? 800 : 150;
        const x = startX + col * cellSize;
        const y = startY + row * cellSize;

        // 创建圆角高亮
        const highlight = gameScene.add.graphics();
        highlight.lineStyle(4, 0xffff00, 1);
        highlight.strokeRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);
        highlight.fillStyle(0xffff00, 0.2);
        highlight.fillRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);

        // 保存高亮引用以便清除
        if (!gameScene.highlights) gameScene.highlights = [];
        gameScene.highlights.push(highlight);
    }

    // 清除高亮
    function clearHighlights() {
        if (gameScene.highlights) {
            gameScene.highlights.forEach(highlight => highlight.destroy());
            gameScene.highlights = [];
        }
    }

    // 显示单位菜单
    function showUnitMenu(row, col) {
        // 随机放置不同类型的单位
        const random = Math.random();
        if (random > 0.66) {
            playerGrid[row][col] = createUnit(UNIT_TYPES.FIGHTER, true);
        } else if (random > 0.33) {
            playerGrid[row][col] = createUnit(UNIT_TYPES.ANTIAIR, true);
        } else {
            playerGrid[row][col] = createUnit(UNIT_TYPES.MISSILE, true);
        }
        updateDisplay();
    }

    // 显示单位信息
    function showUnitInfo(unit, row, col) {
        console.log(`单位信息: ${unit.type.emoji} 生命值: ${unit.health}/${unit.maxHealth} 攻击力: ${unit.attack}`);
    }

    // 开始游戏
    function startGame() {
        gameStarted = true;
        console.log('游戏开始！');
    }

    // 处理回合
    function processTurn() {
        // 玩家单位攻击
        processAttacks(playerGrid, enemyGrid, false);

        // 敌方单位攻击
        processAttacks(enemyGrid, playerGrid, true);

        // 敌方AI行动
        enemyAI();

        // 更新显示
        updateDisplay();

        // 检查游戏结束条件
        checkGameEnd();
    }

    // 处理攻击
    function processAttacks(attackerGrid, defenderGrid, isEnemyAttacking) {
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const attacker = attackerGrid[i][j];
                if (attacker && attacker.attack > 0 && attacker.cooldown <= 0 && !attacker.isFlying) {
                    // 寻找攻击目标
                    const target = findTarget(i, j, attacker, defenderGrid, isEnemyAttacking);
                    if (target) {
                        attacker.cooldown = 2; // 冷却2回合

                        // 根据攻击类型执行不同的攻击
                        if (attacker.attackType === 'fly') {
                            // 飞机起飞攻击
                            executeFlightAttack(i, j, target, attacker, attackerGrid, defenderGrid, isEnemyAttacking);
                        } else if (attacker.attackType === 'area') {
                            // 导弹范围攻击
                            executeMissileAttack(i, j, target, attacker, defenderGrid, isEnemyAttacking);
                        } else if (attacker.attackType === 'continuous') {
                            // 防空炮持续攻击
                            executeContinuousAttack(i, j, target, attacker, defenderGrid, isEnemyAttacking);
                        } else {
                            // 普通攻击
                            executeNormalAttack(i, j, target, attacker, defenderGrid, isEnemyAttacking);
                        }
                    }
                }

                // 减少冷却时间
                if (attacker && attacker.cooldown > 0) {
                    attacker.cooldown--;
                }
            }
        }
    }

    // 飞机起飞攻击
    function executeFlightAttack(attackerRow, attackerCol, target, attacker, attackerGrid, defenderGrid, isEnemyAttacking) {
        // 标记飞机为飞行状态
        attacker.isFlying = true;
        attackerGrid[attackerRow][attackerCol] = null; // 暂时移除飞机

        // 创建飞行动画
        createFlightAttack(attackerRow, attackerCol, target.row, target.col, attacker, isEnemyAttacking, () => {
            // 攻击完成后的回调
            target.unit.health -= attacker.attack;

            // 检查目标是否死亡
            if (target.unit.health <= 0) {
                defenderGrid[target.row][target.col] = null;
            }

            // 飞机返回
            attacker.isFlying = false;
            attackerGrid[attackerRow][attackerCol] = attacker;
            updateDisplay();
        });
    }

    // 导弹范围攻击
    function executeMissileAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        // 创建导弹攻击特效
        createMissileAttack(attackerRow, attackerCol, target.row, target.col, isEnemyAttacking, () => {
            // 范围伤害
            const areaTargets = getAreaTargets(target.row, target.col, attacker.areaSize, defenderGrid);
            areaTargets.forEach(areaTarget => {
                areaTarget.unit.health -= attacker.attack;
                if (areaTarget.unit.health <= 0) {
                    defenderGrid[areaTarget.row][areaTarget.col] = null;
                }
            });
            updateDisplay();
        });
    }

    // 防空炮持续攻击
    function executeContinuousAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        // 只攻击飞机类型的单位
        if (target.unit.type.attackType !== 'fly') return;

        // 创建持续攻击效果
        createContinuousAttack(attackerRow, attackerCol, target.row, target.col, attacker, isEnemyAttacking, () => {
            target.unit.health -= attacker.attack;
            if (target.unit.health <= 0) {
                defenderGrid[target.row][target.col] = null;
            }
            updateDisplay();
        });
    }

    // 普通攻击
    function executeNormalAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        target.unit.health -= attacker.attack;
        createAttackEffect(attackerRow, attackerCol, target.row, target.col, isEnemyAttacking);

        if (target.unit.health <= 0) {
            defenderGrid[target.row][target.col] = null;
        }
    }

    // 获取范围内的目标 - 导弹攻击9个格子（3x3区域）
    function getAreaTargets(centerRow, centerCol, areaSize, defenderGrid) {
        const targets = [];
        // 固定为3x3区域（9个格子）
        const range = 1; // 中心点周围1格的距离

        for (let i = Math.max(0, centerRow - range); i <= Math.min(gridSize - 1, centerRow + range); i++) {
            for (let j = Math.max(0, centerCol - range); j <= Math.min(gridSize - 1, centerCol + range); j++) {
                if (defenderGrid[i][j]) {
                    targets.push({ unit: defenderGrid[i][j], row: i, col: j });
                }
            }
        }
        return targets;
    }

    // 寻找攻击目标
    function findTarget(attackerRow, attackerCol, attacker, defenderGrid, isEnemyAttacking) {
        let closestTarget = null;
        let closestDistance = Infinity;

        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const defender = defenderGrid[i][j];
                if (defender) {
                    const distance = Math.abs(attackerRow - i) + Math.abs(attackerCol - j);

                    // 导弹只能攻击飞机类型的单位
                    if (attacker.type.needsTarget && defender.type.attackType !== 'fly') {
                        continue;
                    }

                    if (distance <= attacker.range && distance < closestDistance) {
                        closestDistance = distance;
                        closestTarget = { unit: defender, row: i, col: j };
                    }
                }
            }
        }

        return closestTarget;
    }

    // 创建飞行攻击特效
    function createFlightAttack(fromRow, fromCol, toRow, toCol, attacker, isEnemyAttacking, onComplete) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 800;
        const endY = isEnemyAttacking ? 800 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 绘制简化的飞行路径
        drawSimpleFlightPath(fromX, fromY, toX, toY);

        // 创建飞机图标
        const plane = gameScene.add.text(fromX, fromY, attacker.type.emoji, {
            fontSize: '30px'
        }).setOrigin(0.5);

        // 创建血条背景
        const healthBarBg = gameScene.add.rectangle(fromX, fromY - 40, 40, 6, 0x333333).setOrigin(0.5);
        // 创建血条
        const healthPercent = attacker.health / attacker.maxHealth;
        const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
        const healthBar = gameScene.add.rectangle(fromX, fromY - 40, 40 * healthPercent, 6, healthBarColor).setOrigin(0.5);

        // 计算中间点（弧形路径）
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 60;

        // 飞机飞向目标（使用贝塞尔曲线）
        gameScene.tweens.add({
            targets: [plane, healthBarBg, healthBar],
            x: toX,
            y: [toY, toY - 40, toY - 40],
            duration: 1200,
            ease: 'Power2',
            onUpdate: (tween) => {
                const progress = tween.progress;
                // 贝塞尔曲线计算
                const currentX = (1 - progress) * (1 - progress) * fromX + 2 * (1 - progress) * progress * midX + progress * progress * toX;
                const currentY = (1 - progress) * (1 - progress) * fromY + 2 * (1 - progress) * progress * midY + progress * progress * toY;

                plane.setPosition(currentX, currentY);
                healthBarBg.setPosition(currentX, currentY - 40);
                healthBar.setPosition(currentX, currentY - 40);
            },
            onComplete: () => {
                // 投下炸弹
                const bomb = gameScene.add.text(toX, toY, '💣', {
                    fontSize: '20px'
                }).setOrigin(0.5);

                // 炸弹下落动画
                gameScene.tweens.add({
                    targets: bomb,
                    y: toY + 30,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        bomb.destroy();

                        // 爆炸特效
                        const explosion = gameScene.add.circle(toX, toY + 30, 25, 0xff4444, 0.8);
                        gameScene.tweens.add({
                            targets: explosion,
                            scaleX: 2,
                            scaleY: 2,
                            alpha: 0,
                            duration: 400,
                            onComplete: () => explosion.destroy()
                        });

                        // 执行攻击回调
                        onComplete();

                        // 飞机返回
                        gameScene.tweens.add({
                            targets: [plane, healthBarBg, healthBar],
                            x: fromX,
                            y: [fromY, fromY - 40, fromY - 40],
                            duration: 1000,
                            ease: 'Power2',
                            onUpdate: (tween) => {
                                const progress = tween.progress;
                                // 返回路径
                                const currentX = (1 - progress) * (1 - progress) * toX + 2 * (1 - progress) * progress * midX + progress * progress * fromX;
                                const currentY = (1 - progress) * (1 - progress) * toY + 2 * (1 - progress) * progress * midY + progress * progress * fromY;

                                plane.setPosition(currentX, currentY);
                                healthBarBg.setPosition(currentX, currentY - 40);
                                healthBar.setPosition(currentX, currentY - 40);
                            },
                            onComplete: () => {
                                plane.destroy();
                                healthBarBg.destroy();
                                healthBar.destroy();
                            }
                        });
                    }
                });
            }
        });
    }

    // 绘制简化的飞行路径
    function drawSimpleFlightPath(fromX, fromY, toX, toY) {
        const pathGraphics = gameScene.add.graphics();
        pathGraphics.lineStyle(2, 0xffff00, 0.4);

        // 计算弧形路径的中间点
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 60;

        // 使用Phaser的路径API绘制贝塞尔曲线
        const path = new Phaser.Curves.Path(fromX, fromY);
        path.quadraticBezierTo(midX, midY, toX, toY);

        // 绘制路径
        path.draw(pathGraphics);

        // 路径会在2秒后消失
        gameScene.time.delayedCall(2000, () => {
            if (pathGraphics && pathGraphics.destroy) {
                pathGraphics.destroy();
            }
        });
    }

    // 创建持续攻击特效（防空炮）
    function createContinuousAttack(fromRow, fromCol, toRow, toCol, attacker, isEnemyAttacking, onHit) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 800;
        const endY = isEnemyAttacking ? 800 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建多发子弹连续射击
        let bulletCount = 0;
        const maxBullets = 5;

        const fireBullet = () => {
            if (bulletCount >= maxBullets) {
                onHit();
                return;
            }

            // 创建子弹
            const bullet = gameScene.add.circle(fromX, fromY, 2, 0xffff00);

            // 子弹飞行动画
            gameScene.tweens.add({
                targets: bullet,
                x: toX + (Math.random() - 0.5) * 20, // 添加一些随机偏移
                y: toY + (Math.random() - 0.5) * 20,
                duration: 200,
                ease: 'Linear',
                onComplete: () => {
                    bullet.destroy();

                    // 小爆炸特效
                    const explosion = gameScene.add.circle(
                        toX + (Math.random() - 0.5) * 20,
                        toY + (Math.random() - 0.5) * 20,
                        8,
                        0xff8800,
                        0.8
                    );

                    gameScene.tweens.add({
                        targets: explosion,
                        scaleX: 2,
                        scaleY: 2,
                        alpha: 0,
                        duration: 200,
                        onComplete: () => explosion.destroy()
                    });
                }
            });

            bulletCount++;

            // 继续发射下一发子弹
            if (bulletCount < maxBullets) {
                gameScene.time.delayedCall(100, fireBullet);
            } else {
                // 最后一发子弹后执行攻击
                gameScene.time.delayedCall(200, onHit);
            }
        };

        // 开始连续射击
        fireBullet();
    }

    // 创建导弹攻击特效
    function createMissileAttack(fromRow, fromCol, toRow, toCol, isEnemyAttacking, onComplete) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 800;
        const endY = isEnemyAttacking ? 800 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建导弹
        const missile = gameScene.add.text(fromX, fromY, '🚀', {
            fontSize: '25px'
        }).setOrigin(0.5);

        // 导弹飞行轨迹（抛物线）
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 100;

        gameScene.tweens.add({
            targets: missile,
            x: toX,
            y: toY,
            duration: 1200,
            ease: 'Power2',
            onUpdate: (tween) => {
                const progress = tween.progress;
                const currentX = fromX + (toX - fromX) * progress;
                const currentY = fromY + (toY - fromY) * progress - 100 * Math.sin(progress * Math.PI);
                missile.setPosition(currentX, currentY);
            },
            onComplete: () => {
                missile.destroy();

                // 大范围爆炸特效
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        const explosion = gameScene.add.circle(
                            toX + (Math.random() - 0.5) * 60,
                            toY + (Math.random() - 0.5) * 60,
                            30 + i * 10,
                            0xff4444,
                            0.8 - i * 0.2
                        );

                        gameScene.tweens.add({
                            targets: explosion,
                            scaleX: 2 + i * 0.5,
                            scaleY: 2 + i * 0.5,
                            alpha: 0,
                            duration: 500 + i * 100,
                            onComplete: () => explosion.destroy()
                        });
                    }, i * 100);
                }

                setTimeout(onComplete, 300);
            }
        });
    }

    // 创建普通攻击特效
    function createAttackEffect(fromRow, fromCol, toRow, toCol, isEnemyAttacking) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 800;
        const endY = isEnemyAttacking ? 800 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建子弹
        const bullet = gameScene.add.circle(fromX, fromY, 3, 0xffff00);

        // 子弹飞行动画
        gameScene.tweens.add({
            targets: bullet,
            x: toX,
            y: toY,
            duration: 500,
            onComplete: () => {
                bullet.destroy();
                // 爆炸特效
                const explosion = gameScene.add.circle(toX, toY, 20, 0xff4444, 0.7);
                gameScene.tweens.add({
                    targets: explosion,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => explosion.destroy()
                });
            }
        });
    }

    // 敌方AI
    function enemyAI() {
        // 随机在空位置生成新单位
        const emptyPositions = [];
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                if (enemyGrid[i][j] === null) {
                    emptyPositions.push({ row: i, col: j });
                }
            }
        }

        // 30%概率生成新单位
        if (emptyPositions.length > 0 && Math.random() < 0.3) {
            const randomPos = emptyPositions[Math.floor(Math.random() * emptyPositions.length)];
            const unitTypes = [UNIT_TYPES.FIGHTER, UNIT_TYPES.BOMBER, UNIT_TYPES.MISSILE];
            const randomType = unitTypes[Math.floor(Math.random() * unitTypes.length)];
            enemyGrid[randomPos.row][randomPos.col] = createUnit(randomType, false);
        }
    }

    // 检查游戏结束
    function checkGameEnd() {
        // 检查基地是否被摧毁
        let playerBaseCount = 0;
        let enemyBaseCount = 0;

        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                if (playerGrid[i][j] && playerGrid[i][j].type === UNIT_TYPES.BASE) {
                    playerBaseCount++;
                }
                if (enemyGrid[i][j] && enemyGrid[i][j].type === UNIT_TYPES.BASE) {
                    enemyBaseCount++;
                }
            }
        }

        if (playerBaseCount === 0) {
            gameStarted = false;
            gameScene.add.text(config.width / 2, config.height / 2, '游戏结束\n敌方获胜！', {
                fontSize: '48px',
                fill: '#ff0000',
                align: 'center'
            }).setOrigin(0.5);
        } else if (enemyBaseCount === 0) {
            gameStarted = false;
            gameScene.add.text(config.width / 2, config.height / 2, '恭喜！\n你获胜了！', {
                fontSize: '48px',
                fill: '#00ff00',
                align: 'center'
            }).setOrigin(0.5);
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
