<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Sort Puzzle 物理版</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.80.1/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #333;
            overflow: hidden;
        }
        canvas {
            border: 2px solid #555;
            display: block;
        }
    </style>
</head>
<body>

<script>
// --- 游戏配置 ---
const config = {
    type: Phaser.AUTO,
    width: 900,
    height: 600,
    backgroundColor: '#4488AA',
    parent: 'game-container',
    physics: {
        default: 'matter',
        matter: {
            gravity: { y: 1 },
            debug: true
        }
    },
    scene: {
        preload: preload,
        create: create,
        update: update
    }
};

// --- 全局变量 ---
let game;
let tubeList = [];
let waterBlocks = [];
let tubeWaterMap = []; // 新增：记录每个试管的水层刚体数组
let tubeBlockConstraints = []; // 记录每个试管与小方块的约束数组
let selectedTubeIndex = null;
const numTubes = 6;
const TUBE_CAPACITY = 5;
const WATER_LAYER_ROWS = 4;
const WATER_LAYER_COLS = 10;
const WATER_BLOCKS_PER_LAYER = WATER_LAYER_ROWS * WATER_LAYER_COLS; // 40

const tubeWidth = 80;
const tubeHeight = 240;
const wallThickness = 10;
const bottomThickness = 8; // 杯底更薄
const waterWidth = tubeWidth - wallThickness * 2;
const layerHeight = (tubeHeight * 0.8) / TUBE_CAPACITY * 0.95;
const totalLayerHeight = (tubeHeight * 0.8) / TUBE_CAPACITY;
const blockHeight = layerHeight / WATER_LAYER_ROWS;
const blockWidth = waterWidth / WATER_LAYER_COLS;
const waterBallRadius = 5; // 固定水球半径

// 定义颜色十六进制值
const COLORS = {
    red: 0xFF0000,
    blue: 0x0000FF,
    green: 0x00FF00,
    yellow: 0xFFFF00,
    purple: 0x800080,
    orange: 0xFFA500,
    cyan: 0x00FFFF
};
const COLOR_NAMES = Object.keys(COLORS);

function preload() {
    // 无需加载图片
}

function create() {
    // --- 初始关卡设置 ---
    const numColors = 5;
    const COLORS_ARR = Phaser.Utils.Array.Shuffle(COLOR_NAMES).slice(0, numColors);
    const levelData = [];
    for (let i = 0; i < numTubes; i++) {
        const tubeColors = [];
        for (let j = 0; j < TUBE_CAPACITY; j++) {
            tubeColors.push(COLORS_ARR[(i + j) % COLORS_ARR.length]);
        }
        levelData.push(tubeColors);
    }
    // 试管参数
    const tubeSpacing = 120;
    const startX = (config.width - (numTubes * tubeSpacing)) / 2 + (tubeSpacing / 2);
    const tubeY = config.height / 2 + 50;
    // 不要再声明 tubeWidth 等参数

    tubeBlockConstraints = [];
    let cupSeperators = [];
    // 创建试管（物理刚体，静态）
    for (let i = 0; i < numTubes; i++) {
        const x = startX + (i * tubeSpacing);
        const y = tubeY;
        // 杯底
        const bottom = this.matter.add.rectangle(x, y + tubeHeight/2 - bottomThickness/2, tubeWidth, bottomThickness, { isStatic: true });
        cupSeperators[i] = [bottom]; // 杯底作为最底层隔板
        // 左壁
        const left = this.matter.add.rectangle(x - tubeWidth/2 + wallThickness/2, y, wallThickness, tubeHeight - wallThickness, { isStatic: true });
        // 右壁
        const right = this.matter.add.rectangle(x + tubeWidth/2 - wallThickness/2, y, wallThickness, tubeHeight - wallThickness, { isStatic: true });
        // 杯子分层隔板（4个，等距分布）
        const layerGap = (tubeHeight - bottomThickness) / TUBE_CAPACITY;
        for (let l = 1; l < TUBE_CAPACITY; l++) {
            const sepY = y + tubeHeight/2 - bottomThickness - l * layerGap;
            const separator = this.matter.add.rectangle(
                x,
                sepY,
                waterWidth,
                6, // 隔板厚度
                { isStatic: true }
            );
            cupSeperators[i].push(separator);
        }
        // 杯子可视化（已去除）
        // const g = this.add.graphics();
        // g.lineStyle(6, 0x888888, 1);
        // g.strokeRoundedRect(x - tubeWidth/2, y - tubeHeight/2, tubeWidth, tubeHeight, 15);
        // tubeList.push({ body: bottom, _highlight: g, index: i });
        tubeList.push({ body: bottom, _highlight: null, index: i });
        bottom.tubeIndex = i;
        bottom.isTube = true;
        tubeBlockConstraints[i] = [];
        tubeWaterMap[i] = [];
        // 点击水杯移除最下面的隔板
        const hitZone = this.add.zone(x, y, tubeWidth, tubeHeight).setOrigin(0.5).setInteractive();
        hitZone.on('pointerdown', () => {
            if (cupSeperators[i] && cupSeperators[i].length > 0) {
                const scene = this;
                const sep = cupSeperators[i].shift(); // 移除最下面的
                scene.matter.world.remove(sep);
            }
        });
    }
    console.log('tubeList.length', tubeList.length, 'tubeWaterMap.length', tubeWaterMap.length);
    // 创建水层（每层4行10列共40个小方块，全部为动态刚体）
    for (let i = 0; i < numTubes; i++) {
        const tubeColors = levelData[i];
        for (let j = 0; j < tubeColors.length; j++) { // j: 层号
            const x = startX + (i * tubeSpacing); // 修正：补充x定义
            const colorName = tubeColors[j];
            // const colorValue = COLORS[colorName]; // 不再需要
            // 计算该层中心Y坐标
            const layerGap = (tubeHeight - bottomThickness) / TUBE_CAPACITY;
            const layerCenterY = tubeY + tubeHeight/2 - bottomThickness - layerGap/2 - j * layerGap;
            // 记录这一层的40个小方块
            const blockArr = [];
            for (let row = 0; row < WATER_LAYER_ROWS; row++) {
                for (let col = 0; col < WATER_LAYER_COLS; col++) {
                    const bx = x - waterWidth/2 + blockWidth/2 + col * blockWidth;
                    const by = layerCenterY; // 全部在该层中心
                    const block = this.matter.add.circle(bx, by, waterBallRadius, {
                        isStatic: false,
                        friction: 0.01,
                        frictionStatic: 0.01,
                        restitution: 0.1,
                        frictionAir: 0.01
                    });
                    waterBlocks.push(block);
                    blockArr.push(block);
                    // 可视化（已去除）
                    // 属性
                    block.isWater = true;
                    block.colorName = colorName;
                    block.tubeIndex = i;
                    block.layerIndex = j;
                    block.blockRow = row;
                    block.blockCol = col;
                }
            }
            tubeWaterMap[i].push(blockArr);
        }
    }

    // 监听物理碰撞事件
    // 已去除所有物理约束和吸收逻辑，只保留刚体物理效果
}

function update() {
    // 物理世界自动更新
}

// --- 随机关卡生成器 ---
function generateRandomLevel(numTubes, numColors, layersPerColor, emptyTubes) {
    let allWater = [];
    const availableColors = Phaser.Utils.Array.Shuffle(COLOR_NAMES).slice(0, numColors);
    availableColors.forEach(color => {
        for (let i = 0; i < layersPerColor; i++) {
            allWater.push(color);
        }
    });
    let filledTubeCount = numTubes - emptyTubes;
    if (allWater.length % filledTubeCount !== 0) {
        while (filledTubeCount > 0 && allWater.length % filledTubeCount !== 0) {
            filledTubeCount--;
        }
        if (filledTubeCount === 0) {
            return Array(numTubes).fill([]);
        }
    }
    const layersPerFilledTube = allWater.length / filledTubeCount;
    Phaser.Utils.Array.Shuffle(allWater);
    const level = [];
    let waterIndex = 0;
    for (let i = 0; i < filledTubeCount; i++) {
        const tubeColors = [];
        for (let j = 0; j < layersPerFilledTube; j++) {
            tubeColors.push(allWater[waterIndex++]);
        }
        level.push(tubeColors);
    }
    for (let i = 0; i < emptyTubes; i++) {
        level.push([]);
    }
    return Phaser.Utils.Array.Shuffle(level);
}

// 试管倾倒并让顶部水层掉落

// 移除 handleTubeClick 和 pourTopLayer 函数定义

window.onload = function() {
    if (typeof Phaser === 'undefined') {
        document.body.innerHTML = '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h2>Error Loading Game</h2><p>Phaser library failed to load. Please check your internet connection and refresh the page.</p></div>';
        return;
    }
    try {
        game = new Phaser.Game(config);
    } catch (error) {
        document.body.innerHTML = '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h2>Game Error</h2><p>Failed to start the game. Please refresh the page and try again.</p></div>';
    }
};
</script>

</body>
</html>