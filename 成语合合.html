<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼成语游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #1e2a78 0%, #3a6edb 100%);
            min-height: 100vh;
            font-family: 'Baloo 2', 'STKaiti', 'KaiTi', 'FZKai-Z03', '楷体', 'SimHei', Arial, sans-serif;
            overflow: hidden;
            position: relative;
        }
        .cloud {
            position: absolute;
            z-index: 1;
            opacity: 0.10;
            color: #fff;
        }
        .cloud1 { left: 5vw; top: 8vh; width: 120px; }
        .cloud2 { right: 8vw; top: 12vh; width: 90px; }
        .cloud3 { left: 20vw; bottom: 10vh; width: 80px; }
        .cloud4 { right: 12vw; bottom: 8vh; width: 100px; }
        #game-container {
            text-align: center;
            position: relative;
            width: 720px;
            height: 800px;
            margin: 0 auto;
            z-index: 2;
        }
        #score {
            color: #fff;
            font-size: 32px;
            font-weight: bold;
            position: absolute;
            top: 48px;
            left: 50%;
            transform: translateX(-50%);
            background: none;
            border-radius: 32px;
            box-shadow: none;
            padding: 6px 24px 6px 18px;
            text-shadow: 2px 2px 12px #1e2a78, 0 0 8px #fff;
            z-index: 10;
            text-align: center;
            display: block;
            border: none;
            transition: all 0.3s cubic-bezier(.68,-0.55,.27,1.55);
            min-width: unset;
            min-height: 40px;
            width: auto;
            max-width: 90vw;
            overflow: visible;
            line-height: 1.2;
            white-space: nowrap;
        }
        #block-preview {
            position: absolute;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: 100px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 10;
        }
        .preview-block {
            background: #e3f0ff;
     
        }
        .preview-block:hover {
            background: #e3f0ff;
            box-shadow: 0 10px 32px 0 #3a6edb44;
        }
        .preview-block .idiom-chars {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 100%;
            gap: 10px;
        }
        .preview-block .idiom-char {
            flex: 1 1 0;
            text-align: center;
            font-size: 26px;
      
        }
        .preview-block .idiom-char:hover {
            background: #ffe066;
            color: #ff9800;
            transform: scale(1.18) rotate(-8deg);
            box-shadow: 0 4px 16px 0 #ffe06644;
        }
        /* 按钮美化（如有） */
        .btn {
            display: inline-block;
            padding: 10px 32px;
            font-size: 22px;
            font-family: 'Baloo 2', Arial, sans-serif;
            font-weight: bold;
            color: #fff;
            background: linear-gradient(90deg, #3a6edb 60%, #43d17a 100%);
            border: none;
            border-radius: 24px;
            box-shadow: 0 4px 16px 0 #1e2a7840;
            margin: 12px 16px;
            cursor: pointer;
            transition: background 0.2s, transform 0.18s;
        }
        .btn:hover {
            background: linear-gradient(90deg, #43d17a 60%, #3a6edb 100%);
            color: #fff;
            transform: scale(1.08) rotate(-4deg);
        }
        .block-shape {
            display: grid;
            gap: 2px;
            z-index: 11;
        }
        .mini-block {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div class="cloud cloud1">☁️</div>
    <div class="cloud cloud2">☁️</div>
    <div class="cloud cloud3">☁️</div>
    <div class="cloud cloud4">☁️</div>
    <div id="game-container">
        <div id="score">分数: 0</div>
        <div id="block-preview">
            <div class="preview-block" id="preview-1"></div>
            <div class="preview-block" id="preview-2"></div>
            <div class="preview-block" id="preview-3"></div>
            <div class="preview-block" id="preview-4"></div>
        </div>
        <div id="game"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        // 成语库（可扩展）
        const IDIOMS = [
            '画蛇添足','亡羊补牢','杯弓蛇影','狐假虎威','滥竽充数','掩耳盗铃','自相矛盾','守株待兔',
            '刻舟求剑','指鹿为马','盲人摸象','井底之蛙','纸上谈兵','对牛弹琴','一箭双雕','四海为家',
            '九牛一毛','一心一意','一见如故','一鸣惊人','一帆风顺','一成不变','一举两得','一清二楚',
            '一丝不苟','一暴十寒','一针见血','一劳永逸','一波三折','一日千里','一模一样','一窍不通',
            '一无所有','一目了然','一意孤行','一知半解','一触即发','一筹莫展','一马当先','一败涂地',
            '一呼百应','一叶知秋','一石二鸟','一网打尽','一箭双雕','一举成名','一拍即合','一见钟情'
        ];
        // 所有可用汉字
        const IDIOM_CHARS = Array.from(new Set(IDIOMS.join('').split('')));

        class IdiomGame extends Phaser.Scene {
            constructor() {
                super({ key: 'IdiomGame' });
                this.GRID_WIDTH = 8;
                this.GRID_HEIGHT = 8;
                this.TILE_SIZE = 53;
                this.grid = [];
                this.score = 0;
                this.isProcessing = false;
                this.isDragging = false;
                this.dragStartX = 0;
                this.dragStartY = 0;
                this.dragDirection = null;
                this.dragTargetIndex = -1;
                this.dragOffset = 0;
                this.previewIdioms = [];
                this.usedIdioms = [];
            }

            preload() {}

            create() {
                this.initGrid();
                this.createIdiomPreviews();
                this.createBoardWithIdioms();
                this.input.on('gameobjectdown', this.onTileDown, this);
                this.input.on('gameobjectup', this.onTileUp, this);
                this.input.on('pointermove', this.onPointerMove, this);
                // this.fillEmptySpaces();
            }

            initGrid() {
                this.grid = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        this.grid[row][col] = null;
                    }
                }
            }

            createIdiomPreviews() {
                // 随机抽取4个成语
                this.previewIdioms = Phaser.Utils.Array.Shuffle(IDIOMS).slice(0, 4);
                this.usedIdioms = [false, false, false, false];
                for (let i = 0; i < 4; i++) {
                    const previewElement = document.getElementById(`preview-${i + 1}`);
                    previewElement.innerHTML = '';
                    const idiomDiv = document.createElement('div');
                    idiomDiv.className = 'idiom-chars';
                    for (let c = 0; c < 4; c++) {
                        const span = document.createElement('span');
                        span.className = 'idiom-char';
                        span.textContent = this.previewIdioms[i][c];
                        idiomDiv.appendChild(span);
                    }
                    previewElement.appendChild(idiomDiv);
                }
            }

            // 新增：初始化盘面，保证4个成语的字都在盘面且不连在一起，且初始无可消成语
            createBoardWithIdioms() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                let board;
                let maxTry = 100;
                do {
                    board = [];
                    // 1. 生成空盘面
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        board[row] = [];
                        for (let col = 0; col < this.GRID_WIDTH; col++) {
                            board[row][col] = null;
                        }
                    }
                    // 2. 取4个成语，拆成16个字
                    const idiomChars = this.previewIdioms.map(idiom => idiom.split(''));
                    // 3. 随机分布到盘面16个不同位置，且不连在一起
                    let used = new Set();
                    for (let i = 0; i < 4; i++) {
                        for (let j = 0; j < 4; j++) {
                            let placed = false;
                            let tryCount = 0;
                            while (!placed && tryCount < 100) {
                                const row = Math.floor(Math.random() * this.GRID_HEIGHT);
                                const col = Math.floor(Math.random() * this.GRID_WIDTH);
                                const key = row + '-' + col;
                                // 不能重复，不能和同一成语的其他字相邻
                                if (!used.has(key) && !this.isAdjacentToSameIdiom(board, row, col, idiomChars[i])) {
                                    board[row][col] = { char: idiomChars[i][j], idiomIdx: i };
                                    used.add(key);
                                    placed = true;
                                }
                                tryCount++;
                            }
                        }
                    }
                    // 4. 其余格子用随机汉字补齐
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        for (let col = 0; col < this.GRID_WIDTH; col++) {
                            if (!board[row][col]) {
                                board[row][col] = { char: this.getRandomChar(), idiomIdx: -1 };
                            }
                        }
                    }
                    // 5. 检查盘面，若有横/纵连续4格正好组成预览成语，则重来
                    maxTry--;
                } while (!this.isValidInitialBoard(board) && maxTry > 0);
                // 6. 渲染到界面
                this.grid = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const char = board[row][col].char;
                        const tile = this.add.text(x, y, char, {
                            fontSize: '32px',
                            color: '#fff',
                            fontFamily: 'SimHei',
                            backgroundColor: 'rgba(0,0,0,0.15)',
                            padding: { left: 8, right: 8, top: 4, bottom: 4 },
                            align: 'center',
                        }).setOrigin(0.5);
                        tile.setInteractive();
                        tile.setData('row', row);
                        tile.setData('col', col);
                        tile.setData('char', char);
                        this.grid[row][col] = tile;
                    }
                }
            }
            // 判断某格是否与同一成语的其他字相邻
            isAdjacentToSameIdiom(board, row, col, idiomArr) {
                const dirs = [
                    [0, 1], [1, 0], [0, -1], [-1, 0],
                    [1, 1], [1, -1], [-1, 1], [-1, -1]
                ];
                for (const [dr, dc] of dirs) {
                    const nr = row + dr, nc = col + dc;
                    if (nr >= 0 && nr < this.GRID_HEIGHT && nc >= 0 && nc < this.GRID_WIDTH) {
                        if (board[nr][nc] && idiomArr.includes(board[nr][nc].char)) {
                            return true;
                        }
                    }
                }
                return false;
            }
            // 检查盘面无可消成语
            isValidInitialBoard(board) {
                // 横向
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col <= this.GRID_WIDTH - 4; col++) {
                        const chars = [];
                        for (let k = 0; k < 4; k++) {
                            chars.push(board[row][col + k].char);
                        }
                        if (this.previewIdioms.includes(chars.join(''))) return false;
                    }
                }
                // 纵向
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    for (let row = 0; row <= this.GRID_HEIGHT - 4; row++) {
                        const chars = [];
                        for (let k = 0; k < 4; k++) {
                            chars.push(board[row + k][col].char);
                        }
                        if (this.previewIdioms.includes(chars.join(''))) return false;
                    }
                }
                return true;
            }

            getRandomChar() {
                return IDIOM_CHARS[Math.floor(Math.random() * IDIOM_CHARS.length)];
            }

            // 拖动相关方法
            onTileDown(pointer, tile) {
                if (this.isProcessing) return;
                this.isDragging = true;
                this.dragStartX = pointer.x;
                this.dragStartY = pointer.y;
                this.dragStartTile = tile;
                this.dragDirection = null;
                this.dragTargetIndex = -1;
                this.dragOffset = 0;
            }
            onTileUp(pointer, tile) {
                if (!this.isDragging) return;
                if (this.dragDirection === 'row') {
                    this.resetRowVisual(this.dragTargetIndex);
                    const steps = Math.round(this.dragOffset / this.TILE_SIZE);
                    if (Math.abs(steps) >= 1) {
                        if (steps > 0) {
                            this.moveRowSteps(this.dragTargetIndex, 'right', Math.abs(steps));
                        } else {
                            this.moveRowSteps(this.dragTargetIndex, 'left', Math.abs(steps));
                        }
                    }
                } else if (this.dragDirection === 'column') {
                    this.resetColumnVisual(this.dragTargetIndex);
                    const steps = Math.round(this.dragOffset / this.TILE_SIZE);
                    if (Math.abs(steps) >= 1) {
                        if (steps > 0) {
                            this.moveColumnSteps(this.dragTargetIndex, 'down', Math.abs(steps));
                        } else {
                            this.moveColumnSteps(this.dragTargetIndex, 'up', Math.abs(steps));
                        }
                    }
                }
                this.isDragging = false;
                this.dragStartTile = null;
                this.dragDirection = null;
                this.dragTargetIndex = -1;
                this.dragOffset = 0;
            }
            onPointerMove(pointer) {
                if (!this.isDragging || this.isProcessing) return;
                const deltaX = pointer.x - this.dragStartX;
                const deltaY = pointer.y - this.dragStartY;
                const absDeltaX = Math.abs(deltaX);
                const absDeltaY = Math.abs(deltaY);
                if (this.dragDirection === null && (absDeltaX > 30 || absDeltaY > 30)) {
                    if (absDeltaX > absDeltaY) {
                        this.dragDirection = 'row';
                        this.dragTargetIndex = this.dragStartTile.getData('row');
                    } else {
                        this.dragDirection = 'column';
                        this.dragTargetIndex = this.dragStartTile.getData('col');
                    }
                }
                if (this.dragDirection === 'row') {
                    this.dragOffset = deltaX;
                    this.updateRowDragVisual(this.dragTargetIndex, deltaX);
                } else if (this.dragDirection === 'column') {
                    this.dragOffset = deltaY;
                    this.updateColumnDragVisual(this.dragTargetIndex, deltaY);
                }
            }
            updateRowDragVisual(row, offsetX) {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        let newX = originalX + offsetX;
                        const totalWidth = this.GRID_WIDTH * this.TILE_SIZE;
                        const leftBound = startX - this.TILE_SIZE / 2;
                        const rightBound = startX + totalWidth - this.TILE_SIZE / 2;
                        if (newX > rightBound) newX = newX - totalWidth;
                        else if (newX < leftBound) newX = newX + totalWidth;
                        tile.x = newX;
                    }
                }
            }
            updateColumnDragVisual(col, offsetY) {
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        let newY = originalY + offsetY;
                        const totalHeight = this.GRID_HEIGHT * this.TILE_SIZE;
                        const topBound = startY - this.TILE_SIZE / 2;
                        const bottomBound = startY + totalHeight - this.TILE_SIZE / 2;
                        if (newY > bottomBound) newY = newY - totalHeight;
                        else if (newY < topBound) newY = newY + totalHeight;
                        tile.y = newY;
                    }
                }
            }
            resetRowVisual(row) {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        tile.x = originalX;
                    }
                }
            }
            resetColumnVisual(col) {
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        tile.y = originalY;
                    }
                }
            }
            moveRowSteps(row, direction, steps) {
                if (this.isProcessing) return;
                this.isProcessing = true;
                steps = Math.max(1, Math.min(steps, this.GRID_WIDTH));
                const originalRowData = [];
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    originalRowData[col] = tile ? tile.getData('char') : null;
                }
                const newChars = [];
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let sourceCol;
                    if (direction === 'right') {
                        sourceCol = (col - steps + this.GRID_WIDTH) % this.GRID_WIDTH;
                    } else {
                        sourceCol = (col + steps) % this.GRID_WIDTH;
                    }
                    newChars[col] = originalRowData[sourceCol];
                }
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile && newChars[col]) {
                        tile.setText(newChars[col]);
                        tile.setData('char', newChars[col]);
                    }
                }
                this.time.delayedCall(200, () => {
                    this.checkIdiomMatches();
                });
            }
            moveColumnSteps(col, direction, steps) {
                if (this.isProcessing) return;
                this.isProcessing = true;
                steps = Math.max(1, Math.min(steps, this.GRID_HEIGHT));
                const originalColData = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    originalColData[row] = tile ? tile.getData('char') : null;
                }
                const newChars = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    let sourceRow;
                    if (direction === 'down') {
                        sourceRow = (row - steps + this.GRID_HEIGHT) % this.GRID_HEIGHT;
                    } else {
                        sourceRow = (row + steps) % this.GRID_HEIGHT;
                    }
                    newChars[row] = originalColData[sourceRow];
                }
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile && newChars[row]) {
                        tile.setText(newChars[row]);
                        tile.setData('char', newChars[row]);
                    }
                }
                this.time.delayedCall(200, () => {
                    this.checkIdiomMatches();
                });
            }
            // 检查盘面所有横/纵连续4格是否组成预览成语
            checkIdiomMatches() {
                const matches = [];
                // 横向
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col <= this.GRID_WIDTH - 4; col++) {
                        const chars = [];
                        for (let k = 0; k < 4; k++) {
                            chars.push(this.grid[row][col + k].getData('char'));
                        }
                        const idiomIdx = this.previewIdioms.findIndex((idiom, idx) => !this.usedIdioms[idx] && idiom === chars.join(''));
                        if (idiomIdx !== -1) {
                            matches.push({ type: 'row', row, col, idiomIdx });
                        }
                    }
                }
                // 纵向
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    for (let row = 0; row <= this.GRID_HEIGHT - 4; row++) {
                        const chars = [];
                        for (let k = 0; k < 4; k++) {
                            chars.push(this.grid[row + k][col].getData('char'));
                        }
                        const idiomIdx = this.previewIdioms.findIndex((idiom, idx) => !this.usedIdioms[idx] && idiom === chars.join(''));
                        if (idiomIdx !== -1) {
                            matches.push({ type: 'col', row, col, idiomIdx });
                        }
                    }
                }
                if (matches.length > 0) {
                    this.handleIdiomMatch(matches);
                } else {
                    this.isProcessing = false;
                }
            }
            // 下落补充新字（与方块合合一致，物理移动tile对象+新建tile对象）
            async dropAndFillTiles() {
                return new Promise(resolve => {
                    const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                    const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                    let animationsToComplete = 0;
                    let completedAnimations = 0;
                    // 1. 下落动画，物理移动tile对象
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        let writeRow = this.GRID_HEIGHT - 1;
                        for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                            const tile = this.grid[row][col];
                            if (tile && tile.getData('char')) {
                                if (writeRow !== row) {
                                    this.grid[writeRow][col] = tile;
                                    this.grid[row][col] = null;
                                    tile.setData('row', writeRow);
                                    const endY = startY + writeRow * this.TILE_SIZE + this.TILE_SIZE / 2;
                                    animationsToComplete++;
                                    this.tweens.add({
                                        targets: tile,
                                        y: endY,
                                        duration: 300,
                                        ease: 'Power2.easeOut',
                                        onComplete: () => {
                                            completedAnimations++;
                                            if (completedAnimations === animationsToComplete) {
                                                this._fillNewTilesPhysical(startX, startY, resolve);
                                            }
                                        }
                                    });
                                } else {
                                    this.grid[writeRow][col] = tile;
                                }
                                writeRow--;
                            }
                        }
                        // 上方空格设为null
                        for (let r = writeRow; r >= 0; r--) {
                            this.grid[r][col] = null;
                        }
                    }
                    if (animationsToComplete === 0) {
                        this._fillNewTilesPhysical(startX, startY, resolve);
                    }
                });
            }
            // 新字补充动画（新建tile对象，动画下落，grid赋值）
            _fillNewTilesPhysical(startX, startY, resolve) {
                let animationsToComplete = 0;
                let completedAnimations = 0;
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    // 收集这一列的空位（从上到下）
                    const emptyRows = [];
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        if (!this.grid[row][col]) {
                            emptyRows.push(row);
                        }
                    }
                    emptyRows.forEach((targetRow, index) => {
                        const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const finalY = startY + targetRow * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const char = this.getRandomChar();
                        // 新tile初始y在上方
                        const startYPos = startY - (emptyRows.length - index + 1) * this.TILE_SIZE;
                        const tile = this.add.text(x, startYPos, char, {
                            fontSize: '32px',
                            color: '#fff',
                            fontFamily: 'SimHei',
                            backgroundColor: 'rgba(0,0,0,0.15)',
                            padding: { left: 8, right: 8, top: 4, bottom: 4 },
                            align: 'center',
                        }).setOrigin(0.5);
                        tile.setData('row', targetRow);
                        tile.setData('col', col);
                        tile.setData('char', char);
                        tile.setInteractive();
                        this.grid[targetRow][col] = tile;
                        animationsToComplete++;
                        this.tweens.add({
                            targets: tile,
                            y: finalY,
                            duration: 500 + (index * 80),
                            ease: 'Bounce.easeOut',
                            onComplete: () => {
                                completedAnimations++;
                                if (completedAnimations === animationsToComplete) {
                                    this._validateGridPositionsPhysical(startX, startY);
                                    resolve();
                                }
                            }
                        });
                    });
                }
                if (animationsToComplete === 0) {
                    this._validateGridPositionsPhysical(startX, startY);
                    resolve();
                }
            }
            // 校正所有tile位置和数据
            _validateGridPositionsPhysical(startX, startY) {
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const tile = this.grid[row][col];
                        if (tile) {
                            tile.x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                            tile.y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                            tile.setData('row', row);
                            tile.setData('col', col);
                        }
                    }
                }
            }
            // 修改消除逻辑：tile设为null
            handleIdiomMatch(matches) {
                const toRemove = [];
                matches.forEach(match => {
                    this.usedIdioms[match.idiomIdx] = true;
                    const previewElement = document.getElementById(`preview-${match.idiomIdx + 1}`);
                    previewElement.style.opacity = '0.3';
                    previewElement.style.filter = 'grayscale(100%)';
                    if (match.type === 'row') {
                        for (let k = 0; k < 4; k++) {
                            toRemove.push(this.grid[match.row][match.col + k]);
                        }
                    } else {
                        for (let k = 0; k < 4; k++) {
                            toRemove.push(this.grid[match.row + k][match.col]);
                        }
                    }
                });
                toRemove.forEach(tile => {
                    this.tweens.add({
                        targets: tile,
                        scaleX: 1.3,
                        scaleY: 1.3,
                        alpha: 0,
                        duration: 200,
                        onComplete: () => {
                            if (tile) {
                                const row = tile.getData('row');
                                const col = tile.getData('col');
                                tile.destroy();
                                this.grid[row][col] = null;
                            }
                        }
                    });
                });
                this.updateScore(matches.length * 100);
                this.time.delayedCall(300, () => {
                    this.dropAndFillTiles().then(() => {
                        if (this.usedIdioms.every(x => x)) {
                            this.createIdiomPreviews();
                        }
                        this.isProcessing = false;
                    });
                });
            }
            updateScore(delta) {
                this.score += delta;
                const scoreElement = document.getElementById('score');
                scoreElement.textContent = `分数: ${this.score}`;
                scoreElement.style.transform = 'translateX(-50%) scale(1.35) rotate(-6deg)';
                scoreElement.style.color = '#fff';
                setTimeout(() => {
                    scoreElement.style.transform = 'translateX(-50%) scale(1) rotate(0)';
                    scoreElement.style.color = '#fff';
                }, 350);
            }
        }

        // 游戏配置 - 竖屏720x800
        const config = {
            type: Phaser.AUTO,
            width: 720,
            height: 800,
            parent: 'game',
            backgroundColor: '#2c3e50',
            scene: IdiomGame,
            scale: {
                mode: Phaser.Scale.NONE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };
        const game = new Phaser.Game(config);
    </script>
</body>
</html>
