<!DOCTYPE html>
<html lang="zh">
<head>
  <meta charset="UTF-8">
  <title>拼汉字游戏</title>
  <script src="https://cdn.jsdelivr.net/npm/phaser@3/dist/phaser.js"></script>
  <style>
    body { margin: 0; background: #f0f0f0; }
    #game-container { margin: 0 auto; display: block; }
  </style>
</head>
<body>
  <div id="game-container"></div>
  <script>
    // 示例题库
    const questions = [
      {
        image: 'https://img2.baidu.com/it/u=1234567890,1234567890&fm=253&fmt=auto&app=138&f=JPEG?w=300&h=300', // 换成你自己的图片
        answer: '苹果'
      },
      {
        image: 'https://img2.baidu.com/it/u=987654321,987654321&fm=253&fmt=auto&app=138&f=JPEG?w=300&h=300',
        answer: '香蕉'
      }
    ];

    let question, answerChars, options;
    let slots = [];
    let optionTexts = [];

    const config = {
      type: Phaser.AUTO,
      width: 400,
      height: 600,
      backgroundColor: '#fff',
      parent: 'game-container',
      scene: {
        preload,
        create
      }
    };

    function preload() {
      // 随机选一道题
      question = Phaser.Utils.Array.GetRandom(questions);
      answerChars = question.answer.split('');
      // 生成选项（打乱顺序，取6个）
      let tempOptions = [...answerChars, ...Phaser.Utils.Array.Shuffle(answerChars)];
      options = Phaser.Utils.Array.Shuffle(tempOptions).slice(0, 6);

      this.load.image('pic', question.image);
    }

    function create() {
      // 上方图片
      this.add.image(200, 100, 'pic').setDisplaySize(150, 150);

      // 中间字区域（待填空）
      slots = [];
      for (let i = 0; i < answerChars.length; i++) {
        let slot = this.add.rectangle(120 + i * 60, 300, 50, 50, 0xffffff)
          .setStrokeStyle(2, 0x000000);
        let text = this.add.text(120 + i * 60, 300, '', { fontSize: '32px', color: '#000' })
          .setOrigin(0.5);
        slots.push({ slot, text, char: '', index: i });
      }

      // 下方字备选区域
      optionTexts = [];
      for (let i = 0; i < options.length; i++) {
        let optText = this.add.text(70 + i * 50, 450, options[i], { fontSize: '32px', backgroundColor: '#eee', color: '#000' })
          .setPadding(10)
          .setOrigin(0.5)
          .setInteractive({ useHandCursor: true })
          .on('pointerdown', () => selectOption(i, this));
        optionTexts.push(optText);
      }
    }

    function selectOption(optIdx, scene) {
      // 找到第一个空的 slot
      let emptySlot = slots.find(s => s.char === '');
      if (!emptySlot) return;
      emptySlot.text.setText(optionTexts[optIdx].text);
      emptySlot.char = optionTexts[optIdx].text;
      optionTexts[optIdx].setVisible(false);

      // 检查是否完成
      if (slots.every(s => s.char !== '')) {
        const userAnswer = slots.map(s => s.char).join('');
        if (userAnswer === question.answer) {
          alert('答对了！');
        } else {
          alert('答错了，请重试！');
          // 重置
          slots.forEach(s => { s.text.setText(''); s.char = ''; });
          optionTexts.forEach(t => t.setVisible(true));
        }
      }
    }

    // 启动游戏
    new Phaser.Game(config);
  </script>
</body>
</html>
