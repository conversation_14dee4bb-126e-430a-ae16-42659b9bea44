<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>华容道</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.60.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            background-color: #000000;
        }

        #game-container {
            width: 100vw;
            height: 100vh;
        }
         canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>

<body>
    <div id="game-container"></div>
    <script id="gameLogic">
        window.onload = function () {
            // 游戏尺寸设置
            const gameWidth = 750  // 最大宽度750px
            const gameHeight = 1334 // 最大高度1334px

            // 战斗系统变量
            let monsters = [], allyBlocks = [];
            let castleHealth = 500, maxCastleHealth = 500;
            let currentBattleLevel = 1, currentWave = 1;
            let battleGrid = [];
            let battleState = 'playing';
            let battleTimer = 0;
            let waveTimer = 0;
            let monstersKilled = 0;
            let totalMonstersInWave = 3;

            // 3选1系统变量
            let playerStats = {
                attackDamage: 15,
                attackSpeed: 2000,
                maxHealth: 100,
                critChance: 0,
                lifeSteal: 0,
                multiShot: 1
            };
            let choiceUI = null;
            let choiceOptions = [];
            let isChoosing = false;
            let choiceCounter = 0;
            // 华容道关卡设计
            const levels = [
                // 关卡1：简单入门 - 只有2个小方块
                {
                    grid: [
                        [1, 2, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 1, height: 1 },  // 红色方块 (blockId=1 -> colorIndex=0 -> 红色)
                        2: { width: 1, height: 1 }   // 蓝色方块 (blockId=2 -> colorIndex=1 -> 蓝色)
                    },
                    exits: [
                        { side: 'bottom', col: 0, width: 1, color: 0xff6b6b, blockId: 1 }, // 红色出口
                        { side: 'bottom', col: 1, width: 1, color: 0x4a6fff, blockId: 2 }  // 蓝色出口
                    ]
                },
                // 关卡2：增加难度 - 4个小方块
                {
                    grid: [
                        [0, 0, 0, 0],
                        [0, 0, 0, 0],
                        [1, 2, 3, 4],
                        [0, 0, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 1, height: 1 },  // 红色方块 (blockId=1 -> 红色)
                        2: { width: 1, height: 1 },  // 蓝色方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 }   // 黄色方块 (blockId=4 -> 黄色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 1, color: 0xff6b6b, blockId: 1 },    // 红色出口
                        { side: 'top', col: 1, width: 1, color: 0x4a6fff, blockId: 2 },    // 蓝色出口
                        { side: 'bottom', col: 2, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口
                        { side: 'bottom', col: 3, width: 1, color: 0xfcc419, blockId: 4 }  // 黄色出口
                    ]
                },
                // 关卡3：混合大小 - 大方块和小方块
                {
                    grid: [
                        [0, 0, 0, 0],
                        [0, 0, 2, 0],
                        [1, 1, 3, 0],
                        [1, 1, 0, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 1, height: 1 },  // 蓝色小方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 }   // 绿色小方块 (blockId=3 -> 绿色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'top', col: 2, width: 1, color: 0x4a6fff, blockId: 2 }, // 蓝色出口
                        { side: 'bottom', col: 2, width: 1, color: 0x51cf66, blockId: 3 }  // 绿色出口
                    ]
                },
                // 关卡4：复杂布局 - 多个方块需要协调移动
                {
                    grid: [
                        [0, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [3, 4, 5, 0],
                        [0, 0, 0, 0]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 2, height: 2 },  // 蓝色大方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色小方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 },  // 黄色小方块 (blockId=4 -> 黄色)
                        5: { width: 1, height: 1 }   // 红色小方块 (blockId=5 -> 红色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'top', col: 2, width: 2, color: 0x4a6fff, blockId: 2 },    // 蓝色大出口
                        { side: 'bottom', col: 0, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口
                        { side: 'bottom', col: 1, width: 1, color: 0xfcc419, blockId: 4 }, // 黄色出口
                        { side: 'bottom', col: 2, width: 1, color: 0xff6b6b, blockId: 5 }  // 红色出口
                    ]
                },
                // 关卡5：终极挑战 - 最复杂的布局
                {
                    grid: [
                        [7, 0, 0, 0],
                        [1, 1, 2, 2],
                        [1, 1, 2, 2],
                        [0, 4, 0, 0],
                        [3, 0, 5, 6]
                    ],
                    blockSizes: {
                        1: { width: 2, height: 2 },  // 红色大方块 (blockId=1 -> 红色)
                        2: { width: 2, height: 2 },  // 蓝色大方块 (blockId=2 -> 蓝色)
                        3: { width: 1, height: 1 },  // 绿色小方块 (blockId=3 -> 绿色)
                        4: { width: 1, height: 1 },  // 黄色小方块 (blockId=4 -> 黄色)
                        5: { width: 1, height: 1 },  // 红色小方块 (blockId=5 -> 红色)
                        6: { width: 1, height: 1 },  // 蓝色小方块 (blockId=6 -> 蓝色)
                        7: { width: 1, height: 1 }   // 绿色小方块 (blockId=7 -> 绿色)
                    },
                    exits: [
                        { side: 'top', col: 0, width: 2, color: 0xff6b6b, blockId: 1 },    // 红色大出口
                        { side: 'top', col: 2, width: 2, color: 0x4a6fff, blockId: 2 },    // 蓝色大出口
                        { side: 'top', col: 0, width: 1, color: 0x51cf66, blockId: 3 }, // 绿色出口 (方块3)
                        { side: 'bottom', col: 1, width: 1, color: 0xfcc419, blockId: 4 }, // 黄色出口 (方块4)
                        { side: 'bottom', col: 2, width: 1, color: 0xff6b6b, blockId: 5 }, // 红色出口 (方块5)
                        { side: 'bottom', col: 3, width: 1, color: 0x4a6fff, blockId: 6 }, // 蓝色出口 (方块6)
                        { side: 'bottom', col: 0, width: 1, color: 0x51cf66, blockId: 7 }     // 绿色出口 (方块7)
                    ]
                }
            ];
            // 方块颜色（只使用4种颜色）
            const blockColors = [
                0xff6b6b, // 红色
                0x4a6fff, // 蓝色
                0x51cf66, // 绿色
                0xfcc419  // 黄色
            ];

            // 根据方块ID获取颜色的函数
            function getBlockColor(blockId) {
                const colorIndex = (blockId - 1) % blockColors.length;
                return blockColors[colorIndex];
            }
            // 游戏状态变量
            let currentLevel = 0;
            let steps = 0;
            let maxUnlockedLevel = 0;
            // 尝试从本地存储加载最大解锁关卡
            try {
                const savedMaxLevel = localStorage.getItem('huarongdao_maxLevel');
                if (savedMaxLevel !== null) {
                    maxUnlockedLevel = parseInt(savedMaxLevel, 10);
                }
            } catch (e) {
                console.error('无法访问本地存储:', e);
            }
            // 创建Phaser游戏配置
            const config = {
                type: Phaser.AUTO,
                width: 750,
                height: 1334,
                parent: 'game-container',
                backgroundColor: '#1a1a2e',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };
            // 创建游戏实例
            const game = new Phaser.Game(config);
            // 游戏变量
            let gameScene;
            let blocks = [];
            let exits = [];
            let cellSize;
            let boardX;
            let boardY;
            let boardWidth;
            let boardHeight;
            let grid;
            let currentBlockSizes;
            // 删除了stepText和levelText变量，简化UI
            let selectedBlock = null;
            let startDragPosition = null;

            function preload() {
                // 不需要预加载纹理，使用emoji和文字
            }
            function create() {
                gameScene = this;

                // 创建战斗系统（上半部分）
                createBattleSystem();

                // 设置华容道游戏布局（下半部分）
                setupGameLayout();
                // 加载当前关卡
                loadLevel(currentLevel);
                // 创建UI元素
                createUI();
            }
            function update(time, delta) {
                // 更新战斗系统
                updateBattleSystem(time, delta);
                // 华容道游戏更新逻辑
            }

            function createBattleSystem() {
                // 添加战斗背景
                const battleAreaHeight = gameHeight * 0.4;
                const battleBg = gameScene.add.rectangle(gameWidth / 2, battleAreaHeight / 2, gameWidth, battleAreaHeight, 0x2c3e50);

                // 创建防御墙 - 满画布宽度，中间有大门
                const defenseWallY = battleAreaHeight - 20;
                const gateWidth = 100; // 大门宽度

                // 左侧防御墙 - 从左边缘到大门左侧
                const leftWallWidth = (gameWidth - gateWidth) / 2;
                gameScene.defenseWallLeft = gameScene.add.rectangle(leftWallWidth / 2, defenseWallY, leftWallWidth, 30, 0x4A6FFF);
                gameScene.defenseWallLeft.setStrokeStyle(4, 0x2c3e50);

                // 右侧防御墙 - 从大门右侧到右边缘
                gameScene.defenseWallRight = gameScene.add.rectangle(gameWidth - leftWallWidth / 2, defenseWallY, leftWallWidth, 30, 0x4A6FFF);
                gameScene.defenseWallRight.setStrokeStyle(4, 0x2c3e50);

                // 大门
                gameScene.gate = gameScene.add.rectangle(gameWidth / 2, defenseWallY, gateWidth, 30, 0x8B4513);
                gameScene.gate.setStrokeStyle(4, 0x654321);

                // 大门标识
                gameScene.add.text(gameWidth / 2, defenseWallY, '🚪', {
                    fontSize: '24px'
                }).setOrigin(0.5);

                // 存储防御墙信息供其他函数使用
                gameScene.defenseWallY = defenseWallY;
                gameScene.gateX = gameWidth / 2;
                gameScene.gateWidth = gateWidth;

                // 创建战斗UI
                createBattleUI();

                // 创建怪物
                createBattleWave();
            }

            function createBattleUI() {
                const battleAreaHeight = gameHeight * 0.4;

                // 防御墙血条背景
                gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x333333);
                // 防御墙血条
                gameScene.defenseWallHealthBar = gameScene.add.rectangle(gameWidth / 2, 50, 300, 20, 0x4A6FFF);
                gameScene.defenseWallHealthBar.setOrigin(0.5, 0.5);

                // UI文本
                gameScene.battleLevelText = gameScene.add.text(50, 80, `战斗关卡: ${currentBattleLevel}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.battleWaveText = gameScene.add.text(200, 80, `波次: ${currentWave}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.defenseWallHealthText = gameScene.add.text(350, 80, `防御墙: ${castleHealth}/${maxCastleHealth}`, {
                    fontSize: '16px',
                    fill: '#ffffff'
                });
                gameScene.allyCountText = gameScene.add.text(550, 80, `守军: 0`, {
                    fontSize: '16px',
                    fill: '#2ecc71'
                });

                // 怪物血条容器
                gameScene.monsterHealthBars = [];
            }

            function createBattleWave() {
                // 清除现有怪物（但保留我方方块）
                monsters.forEach(monster => monster.destroy());
                monsters = [];

                // 创建新怪物 - 从上方生成
                for (let i = 0; i < totalMonstersInWave; i++) {
                    const xPos = 100 + i * 120; // 水平分布
                    const yPos = 30; // 从顶部生成

                    // 随机选择怪物emoji
                    const monsterEmojis = ['👹', '👺', '🧌', '👻', '💀', '🧟', '🧛', '🐺', '🦇', '🕷️'];
                    const monsterEmoji = monsterEmojis[Math.floor(Math.random() * monsterEmojis.length)];

                    let monster = gameScene.add.text(xPos, yPos, monsterEmoji, {
                        fontSize: '32px'
                    });
                    monster.setOrigin(0.5, 1);

                    monster.health = 30 + currentBattleLevel * 10;
                    monster.maxHealth = monster.health;
                    monster.lastAttack = 0;
                    monster.isRanged = false;
                    monster.isMoving = false;
                    monster.originalX = xPos;
                    monster.originalY = yPos;
                    monster.jumpTween = null;
                    monster.isNewlySpawned = true; // 标记为新生成
                    monsters.push(monster);

                    // 怪物生成闪动效果
                    monster.setAlpha(0);
                    gameScene.tweens.add({
                        targets: monster,
                        alpha: 1,
                        duration: 300,
                        ease: 'Power2',
                        repeat: 2,
                        yoyo: true,
                        onComplete: () => {
                            monster.setAlpha(1);
                            // 闪动完成后，允许被攻击
                            setTimeout(() => {
                                monster.isNewlySpawned = false;
                            }, 500);
                        }
                    });
                }

                monstersKilled = 0;

                // 重置所有我方方块的目标，让它们重新寻找新的敌人
                allyBlocks.forEach(allyBlock => {
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    // 确保已出城的士兵能够攻击新怪物
                    if (allyBlock.movementPhase === 'defending' || allyBlock.movementPhase === 'combat') {
                        // 重置攻击冷却，让士兵能立即攻击新怪物
                        allyBlock.lastAttack = 0;
                    }
                });
            }

            function updateBattleSystem(time, delta) {
                if (battleState === 'playing') {
                    // 更新战斗UI
                    updateBattleUI();

                    // 怪物AI
                    updateMonsters(time, delta);

                    // 更新我方方块
                    updateAllyBlocks(time, delta);

                    // 检查波次完成
                    checkBattleWaveComplete();

                    // 检查游戏结束
                    if (castleHealth <= 0) {
                        battleState = 'gameOver';
                        gameScene.add.text(gameWidth / 2, gameHeight * 0.2, '防御墙被摧毁！', {
                            fontSize: '32px',
                            fill: '#e74c3c',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);
                    }
                }
            }

            // 删除了playerAttack函数，现在只有我方方块攻击

            // 在方块创建时就生成士兵
            function createSoldiersOnBlock(blockContainer, blockId, color, blockX, blockY, blockWidth, blockHeight) {
                // 找到这个方块对应的出口
                const currentLevelData = levels[currentLevel];
                const exitSide = currentLevelData.exits.find(exit => exit.blockId === blockId)?.side || 'bottom';

                // 在方块中心生成4个士兵
                const centerX = blockX + blockWidth / 2;
                const centerY = blockY + blockHeight / 2;

                for (let i = 0; i < 4; i++) {
                    // 2x2排列，更紧密
                    const x = centerX + (i % 2) * 20 - 10;
                    const y = centerY + Math.floor(i / 2) * 20 - 10;

                    // 创建我方方块
                    const allyBlock = gameScene.add.rectangle(x, y, 30, 30, color);
                    allyBlock.setStrokeStyle(2, 0xffffff);

                    // 添加士兵emoji
                    const soldierEmojis = ['⚔️', '🛡️', '🏹', '🗡️'];
                    const soldierEmoji = soldierEmojis[i % soldierEmojis.length];
                    const soldierText = gameScene.add.text(x, y, soldierEmoji, {
                        fontSize: '20px'
                    });
                    soldierText.setOrigin(0.5, 0.5);

                    // 将emoji绑定到方块
                    allyBlock.soldierText = soldierText;

                    // 设置我方方块属性
                    allyBlock.health = 50;
                    allyBlock.maxHealth = 50;
                    allyBlock.attackDamage = 15;
                    allyBlock.lastAttack = 0;
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    allyBlock.originalColor = color;
                    allyBlock.exitSide = exitSide; // 出城方向
                    allyBlock.hasExitedGate = false; // 是否已经出城门
                    allyBlock.hasReachedGate = false; // 是否已经到达城门
                    allyBlock.movementPhase = 'onBlock'; // 在方块上
                    allyBlock.waitingForBlockExit = true; // 等待方块出去
                    allyBlock.onBlockId = blockId; // 记录站在哪个方块上
                    allyBlock.blockContainer = blockContainer; // 记录方块容器

                    // 添加到我方方块数组
                    allyBlocks.push(allyBlock);

                    // 创建入场动画
                    allyBlock.setScale(0);
                    soldierText.setScale(0);
                    gameScene.tweens.add({
                        targets: [allyBlock, soldierText],
                        scaleX: 1,
                        scaleY: 1,
                        duration: 500,
                        ease: 'Back.easeOut'
                    });
                }
            }

            function createAllyBlocksOnBlock(color, count, exitSide, spawnX, spawnY, blockId) {
                for (let i = 0; i < count; i++) {
                    // 在华容道方块上生成（稍微分散避免重叠）
                    const x = spawnX + (i % 2) * 20 - 10; // 2x2排列，更紧密
                    const y = spawnY + Math.floor(i / 2) * 20 - 10;

                    // 创建我方方块
                    const allyBlock = gameScene.add.rectangle(x, y, 30, 30, color);
                    allyBlock.setStrokeStyle(2, 0xffffff);

                    // 添加士兵emoji
                    const soldierEmojis = ['⚔️', '🛡️', '🏹', '🗡️'];
                    const soldierEmoji = soldierEmojis[i % soldierEmojis.length];
                    const soldierText = gameScene.add.text(x, y, soldierEmoji, {
                        fontSize: '20px'
                    });
                    soldierText.setOrigin(0.5, 0.5);

                    // 将emoji绑定到方块
                    allyBlock.soldierText = soldierText;

                    // 设置我方方块属性
                    allyBlock.health = 50;
                    allyBlock.maxHealth = 50;
                    allyBlock.attackDamage = 15;
                    allyBlock.lastAttack = 0;
                    allyBlock.targetMonster = null;
                    allyBlock.isMoving = false;
                    allyBlock.originalColor = color;
                    allyBlock.exitSide = exitSide; // 出城方向
                    allyBlock.hasExitedGate = false; // 是否已经出城门
                    allyBlock.hasReachedGate = false; // 是否已经到达城门
                    allyBlock.movementPhase = 'waiting'; // 等待方块出去后开始移动
                    allyBlock.waitingForBlockExit = true; // 等待方块出去
                    allyBlock.onBlockId = blockId; // 记录站在哪个方块上
                    allyBlock.exitSide = exitSide; // 记录方块的出口方向

                    // 添加到我方方块数组
                    allyBlocks.push(allyBlock);

                    // 创建入场动画
                    allyBlock.setScale(0);
                    soldierText.setScale(0);
                    gameScene.tweens.add({
                        targets: [allyBlock, soldierText],
                        scaleX: 1,
                        scaleY: 1,
                        duration: 500,
                        ease: 'Back.easeOut'
                    });

                    // 显示创建提示
                    const createText = gameScene.add.text(x, y - 25, '+1 守军', {
                        fontSize: '10px',
                        fill: '#2ecc71',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    gameScene.tweens.add({
                        targets: createText,
                        alpha: 0,
                        y: createText.y - 15,
                        duration: 1500,
                        onComplete: () => createText.destroy()
                    });
                }
            }

            function updateMonsters(time, delta) {
                monsters.forEach((monster, index) => {
                    // 新生成的怪物在闪动期间不行动
                    if (monster.isNewlySpawned) return;

                    const defenseWallY = gameScene.defenseWallY;
                    // 计算到最近防御墙的距离
                    const leftWallX = (gameWidth - gameScene.gateWidth) / 4;
                    const rightWallX = gameWidth - leftWallX;
                    const distanceToLeftWall = Phaser.Math.Distance.Between(monster.x, monster.y, leftWallX, defenseWallY);
                    const distanceToRightWall = Phaser.Math.Distance.Between(monster.x, monster.y, rightWallX, defenseWallY);
                    const distanceToWall = Math.min(distanceToLeftWall, distanceToRightWall);

                    // 向下移动攻击防御墙
                    if (monster.y < defenseWallY - 50 && !monster.isMoving) {
                        monster.isMoving = true;

                        // 向防御墙移动
                        const targetX = monster.x + Math.random() * 60 - 30; // 稍微偏移
                        const targetY = defenseWallY - 40; // 移动到防御墙附近

                        gameScene.tweens.add({
                            targets: monster,
                            x: targetX,
                            y: targetY,
                            duration: 4000,
                            ease: 'Power2',
                            onComplete: () => {
                                monster.isMoving = false;
                            }
                        });
                    }

                    // 更积极地攻击防御墙，扩大攻击范围和频率
                    if (distanceToWall <= 120 && time - monster.lastAttack > 2000) {
                        let damage = 12 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            rotation: 0.4,
                            duration: 150,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 防御墙受击效果
                        gameScene.tweens.add({
                            targets: [gameScene.defenseWallLeft, gameScene.defenseWallRight],
                            fillColor: 0xff0000,
                            duration: 100,
                            yoyo: true,
                            onComplete: () => {
                                gameScene.defenseWallLeft.setFillStyle(0x4A6FFF);
                                gameScene.defenseWallRight.setFillStyle(0x4A6FFF);
                            }
                        });
                    }

                    // 如果怪物到达防御墙附近但没有在攻击，强制开始攻击
                    else if (monster.y >= defenseWallY - 60 && time - monster.lastAttack > 3000) {
                        let damage = 8 + currentBattleLevel;
                        castleHealth -= damage;
                        monster.lastAttack = time;

                        // 简单攻击动画
                        gameScene.tweens.add({
                            targets: monster,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 100,
                            yoyo: true
                        });
                    }
                });
            }

            function updateAllyBlocks(time, delta) {
                allyBlocks.forEach((allyBlock, index) => {
                    // 跳过在方块上的士兵
                    if (allyBlock.movementPhase === 'onBlock' || allyBlock.movementPhase === 'waiting') {
                        return;
                    }

                    // 调试信息：显示所有士兵的状态
                    if (allyBlock.movementPhase === 'toGate' && !allyBlock.isMoving && !allyBlock.waitingForBlockExit) {
                        console.log('准备移动的士兵:', {
                            phase: allyBlock.movementPhase,
                            isMoving: allyBlock.isMoving,
                            waitingForBlockExit: allyBlock.waitingForBlockExit,
                            exitSide: allyBlock.exitSide,
                            x: allyBlock.x,
                            y: allyBlock.y
                        });
                    }

                    // 更积极地寻找目标，确保不漏怪
                    if (!allyBlock.targetMonster ||
                        allyBlock.targetMonster.health <= 0 ||
                        !monsters.includes(allyBlock.targetMonster)) {

                        let nearestMonster = null;
                        let nearestDistance = Infinity;

                        monsters.forEach(monster => {
                            // 不攻击正在闪动的新生成怪物
                            if (monster.isNewlySpawned) return;

                            const distance = Phaser.Math.Distance.Between(
                                allyBlock.x, allyBlock.y, monster.x, monster.y
                            );
                            // 扩大搜索范围，确保不漏怪
                            if (distance < nearestDistance && distance < 500) {
                                nearestDistance = distance;
                                nearestMonster = monster;
                            }
                        });

                        allyBlock.targetMonster = nearestMonster;
                    }

                    // 定期重新评估目标，防止士兵固守一个目标而忽略其他怪物
                    if (time % 3000 < 50) { // 每3秒重新评估一次目标
                        let nearestMonster = null;
                        let nearestDistance = Infinity;

                        monsters.forEach(monster => {
                            if (monster.isNewlySpawned) return;

                            const distance = Phaser.Math.Distance.Between(
                                allyBlock.x, allyBlock.y, monster.x, monster.y
                            );
                            if (distance < nearestDistance && distance < 500) {
                                nearestDistance = distance;
                                nearestMonster = monster;
                            }
                        });

                        // 如果找到更近的目标，或者当前目标太远，切换目标
                        if (nearestMonster && (!allyBlock.targetMonster || nearestDistance < 200)) {
                            allyBlock.targetMonster = nearestMonster;
                        }
                    }

                    const defenseWallY = gameScene.defenseWallY;
                    const gateX = gameScene.gateX;
                    const gateWidth = gameScene.gateWidth;

                    // 新的移动逻辑：沿着马路走到大门口（匀速运动）
                    // 第一阶段：从马路位置移动到大门口
                    if (allyBlock.movementPhase === 'toGate' && !allyBlock.isMoving && !allyBlock.waitingForBlockExit) {
                        console.log('进入移动逻辑，士兵出口方向:', allyBlock.exitSide);
                        allyBlock.isMoving = true;

                        // 计算到大门的目标位置
                        const targetX = gateX + (Math.random() - 0.5) * (gateWidth - 20); // 大门范围内
                        const targetY = defenseWallY + 5; // 大门位置

                        // 获取马路信息
                        const roadInfo = gameScene.roadInfo;
                        const soldierSpeed = 80; // 像素/秒，匀速移动

                        // 判断士兵当前在哪条马路上，然后规划路径到大门
                        let currentX = allyBlock.x;
                        let currentY = allyBlock.y;

                        console.log('士兵开始移动到大门:', currentX, currentY, 'exitSide:', allyBlock.exitSide);

                        // 根据士兵的出口方向来决定移动路径，更可靠
                        if (allyBlock.exitSide === 'bottom') {
                            // 底部出口的士兵，在底部马路上
                            console.log('底部出口士兵开始移动:', currentX, currentY);
                            let useLeftRoad = currentX < gateX;
                            let intermediateX = useLeftRoad ? roadInfo.leftRoadX : roadInfo.rightRoadX;

                            // 第一步：沿底部马路到左/右马路交叉点
                            const distance1 = Math.abs(currentX - intermediateX);
                            const duration1 = (distance1 / soldierSpeed) * 1000;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                x: intermediateX,
                                duration: duration1,
                                ease: 'Linear',
                                onComplete: () => {
                                    // 第二步：沿左/右马路向上到防御墙高度
                                    const distance2 = Math.abs(roadInfo.bottomRoadY - (defenseWallY + 30));
                                    const duration2 = (distance2 / soldierSpeed) * 1000;

                                    gameScene.tweens.add({
                                        targets: [allyBlock, allyBlock.soldierText],
                                        y: defenseWallY + 30,
                                        duration: duration2,
                                        ease: 'Linear',
                                        onComplete: () => {
                                            // 第三步：横向移动到大门
                                            const distance3 = Math.abs(intermediateX - targetX);
                                            const duration3 = (distance3 / soldierSpeed) * 1000;

                                            gameScene.tweens.add({
                                                targets: [allyBlock, allyBlock.soldierText],
                                                x: targetX,
                                                y: targetY,
                                                duration: duration3,
                                                ease: 'Linear',
                                                onComplete: () => {
                                                    allyBlock.movementPhase = 'atGate';
                                                    allyBlock.hasReachedGate = true;
                                                    allyBlock.isMoving = false;
                                                }
                                            });
                                        }
                                    });
                                }
                            });
                        } else if (allyBlock.exitSide === 'top') {
                            // 顶部出口的士兵，优先走城门连接段
                            const distance1 = Math.abs(currentX - roadInfo.gateConnectorX);
                            const duration1 = (distance1 / soldierSpeed) * 1000;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                x: roadInfo.gateConnectorX,
                                duration: duration1,
                                ease: 'Linear',
                                onComplete: () => {
                                    // 第二步：沿连接段向下到大门
                                    const distance2 = Math.abs(roadInfo.topRoadY - targetY);
                                    const duration2 = (distance2 / soldierSpeed) * 1000;

                                    gameScene.tweens.add({
                                        targets: [allyBlock, allyBlock.soldierText],
                                        x: targetX,
                                        y: targetY,
                                        duration: duration2,
                                        ease: 'Linear',
                                        onComplete: () => {
                                            allyBlock.movementPhase = 'atGate';
                                            allyBlock.hasReachedGate = true;
                                            allyBlock.isMoving = false;
                                        }
                                    });
                                }
                            });
                        } else if (allyBlock.exitSide === 'left') {
                            // 左侧出口的士兵，在左侧马路上
                            const distance1 = Math.abs(currentY - (defenseWallY + 30));
                            const duration1 = (distance1 / soldierSpeed) * 1000;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                y: defenseWallY + 30,
                                duration: duration1,
                                ease: 'Linear',
                                onComplete: () => {
                                    // 然后横向移动到大门
                                    const distance2 = Math.abs(roadInfo.leftRoadX - targetX);
                                    const duration2 = (distance2 / soldierSpeed) * 1000;

                                    gameScene.tweens.add({
                                        targets: [allyBlock, allyBlock.soldierText],
                                        x: targetX,
                                        y: targetY,
                                        duration: duration2,
                                        ease: 'Linear',
                                        onComplete: () => {
                                            allyBlock.movementPhase = 'atGate';
                                            allyBlock.hasReachedGate = true;
                                            allyBlock.isMoving = false;
                                        }
                                    });
                                }
                            });
                        } else if (allyBlock.exitSide === 'right') {
                            // 右侧出口的士兵，在右侧马路上
                            const distance1 = Math.abs(currentY - (defenseWallY + 30));
                            const duration1 = (distance1 / soldierSpeed) * 1000;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                y: defenseWallY + 30,
                                duration: duration1,
                                ease: 'Linear',
                                onComplete: () => {
                                    // 然后横向移动到大门
                                    const distance2 = Math.abs(roadInfo.rightRoadX - targetX);
                                    const duration2 = (distance2 / soldierSpeed) * 1000;

                                    gameScene.tweens.add({
                                        targets: [allyBlock, allyBlock.soldierText],
                                        x: targetX,
                                        y: targetY,
                                        duration: duration2,
                                        ease: 'Linear',
                                        onComplete: () => {
                                            allyBlock.movementPhase = 'atGate';
                                            allyBlock.hasReachedGate = true;
                                            allyBlock.isMoving = false;
                                        }
                                    });
                                }
                            });
                        } else {
                            // 其他情况，直接移动到大门（备用逻辑）
                            console.log('使用备用移动逻辑:', allyBlock.exitSide);
                            const distance = Phaser.Math.Distance.Between(currentX, currentY, targetX, targetY);
                            const duration = (distance / soldierSpeed) * 1000;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                x: targetX,
                                y: targetY,
                                duration: duration,
                                ease: 'Linear',
                                onComplete: () => {
                                    allyBlock.movementPhase = 'atGate';
                                    allyBlock.hasReachedGate = true;
                                    allyBlock.isMoving = false;
                                }
                            });
                        }
                    }

                    if (allyBlock.targetMonster && monsters.includes(allyBlock.targetMonster)) {
                        const target = allyBlock.targetMonster;
                        const distanceToTarget = Phaser.Math.Distance.Between(
                            allyBlock.x, allyBlock.y, target.x, target.y
                        );

                        // 第二阶段：通过大门到城墙前面防守位置
                        if (allyBlock.movementPhase === 'atGate' && !allyBlock.hasExitedGate && !allyBlock.isMoving) {
                            allyBlock.isMoving = true;

                            // 出城后移动到城墙前面的防守位置，形成防线
                            const defenseLineY = defenseWallY - 60; // 在城墙前面60像素处形成防线
                            const defensePositions = [
                                gateX - 80, gateX - 40, gateX + 40, gateX + 80
                            ];

                            // 为每个士兵分配防守位置
                            const positionIndex = allyBlocks.filter(ab => ab.hasExitedGate).length % defensePositions.length;
                            const targetX = defensePositions[positionIndex] + Math.random() * 20 - 10; // 稍微随机化
                            const targetY = defenseLineY + Math.random() * 20 - 10;

                            gameScene.tweens.add({
                                targets: [allyBlock, allyBlock.soldierText],
                                x: targetX,
                                y: targetY,
                                duration: 3500, // 调慢速度
                                ease: 'Power2',
                                onComplete: () => {
                                    allyBlock.movementPhase = 'defending';
                                    allyBlock.hasExitedGate = true;
                                    allyBlock.isMoving = false;
                                    allyBlock.defenseX = targetX; // 记录防守位置
                                    allyBlock.defenseY = targetY;
                                }
                            });
                        }
                        // 第三阶段：在城墙前面防守，只在必要时移动
                        else if (allyBlock.movementPhase === 'defending' && target && !allyBlock.isMoving) {
                            // 只有当敌人非常接近或士兵离防守位置太远时才移动
                            const distanceToDefensePosition = Phaser.Math.Distance.Between(
                                allyBlock.x, allyBlock.y, allyBlock.defenseX, allyBlock.defenseY
                            );

                            // 如果敌人很近且士兵不在攻击范围内，向敌人移动一点
                            if (distanceToTarget < 150 && distanceToTarget > 90) {
                                allyBlock.isMoving = true;

                                // 向敌人方向移动一小段距离，但不离防守位置太远
                                const moveDistance = 30;
                                const angle = Phaser.Math.Angle.Between(allyBlock.x, allyBlock.y, target.x, target.y);
                                const targetX = allyBlock.x + Math.cos(angle) * moveDistance;
                                const targetY = Math.max(allyBlock.y + Math.sin(angle) * moveDistance, defenseWallY - 120); // 不要太靠近城墙

                                gameScene.tweens.add({
                                    targets: [allyBlock, allyBlock.soldierText],
                                    x: targetX,
                                    y: targetY,
                                    duration: 2000,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        allyBlock.isMoving = false;
                                    }
                                });
                            }
                            // 如果士兵离防守位置太远，回到防守位置
                            else if (distanceToDefensePosition > 80 && distanceToTarget > 150) {
                                allyBlock.isMoving = true;

                                gameScene.tweens.add({
                                    targets: [allyBlock, allyBlock.soldierText],
                                    x: allyBlock.defenseX,
                                    y: allyBlock.defenseY,
                                    duration: 2500,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        allyBlock.isMoving = false;
                                    }
                                });
                            }
                        }

                        // 如果在攻击范围内且已经出城，进行攻击
                        if ((allyBlock.movementPhase === 'defending' || allyBlock.movementPhase === 'combat') && distanceToTarget <= 120 && time - allyBlock.lastAttack > 1200) {
                            // 攻击目标
                            target.health -= allyBlock.attackDamage;
                            allyBlock.lastAttack = time;

                            // 攻击动画
                            gameScene.tweens.add({
                                targets: allyBlock,
                                scaleX: 1.3,
                                scaleY: 1.3,
                                duration: 100,
                                yoyo: true
                            });

                            // 目标受击效果
                            gameScene.tweens.add({
                                targets: target,
                                tint: 0xffffff,
                                duration: 100,
                                yoyo: true,
                                onComplete: () => {
                                    if (target.health > 0) {
                                        target.clearTint();
                                    }
                                }
                            });

                            // 检查目标是否死亡
                            if (target.health <= 0) {
                                target.destroy();
                                const monsterIndex = monsters.indexOf(target);
                                if (monsterIndex > -1) {
                                    monsters.splice(monsterIndex, 1);
                                    monstersKilled++;
                                }
                                allyBlock.targetMonster = null;

                                // 立即寻找新目标，防止漏怪
                                setTimeout(() => {
                                    let nearestMonster = null;
                                    let nearestDistance = Infinity;

                                    monsters.forEach(monster => {
                                        if (monster.isNewlySpawned) return;
                                        const distance = Phaser.Math.Distance.Between(
                                            allyBlock.x, allyBlock.y, monster.x, monster.y
                                        );
                                        if (distance < nearestDistance && distance < 600) { // 进一步扩大搜索范围
                                            nearestDistance = distance;
                                            nearestMonster = monster;
                                        }
                                    });

                                    allyBlock.targetMonster = nearestMonster;

                                    // 如果找到新目标，立即开始攻击
                                    if (nearestMonster) {
                                        allyBlock.lastAttack = 0; // 重置攻击冷却
                                    }
                                }, 50); // 减少延迟时间
                            }
                        }
                    }
                });

                // 强化防漏怪机制，特别关注边缘怪物
                monsters.forEach(monster => {
                    if (monster.isNewlySpawned) return;

                    // 检查是否有士兵正在攻击这个怪物
                    const attackingSoldiers = allyBlocks.filter(soldier =>
                        soldier.targetMonster === monster &&
                        (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat')
                    );

                    // 如果没有士兵攻击这个怪物，强制分配士兵
                    if (attackingSoldiers.length === 0) {
                        let bestSoldier = null;
                        let bestScore = Infinity;

                        allyBlocks.forEach(soldier => {
                            if (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat') {
                                const distance = Phaser.Math.Distance.Between(
                                    soldier.x, soldier.y, monster.x, monster.y
                                );

                                // 计算分配优先级（距离越近，当前目标越远，优先级越高）
                                let score = distance;

                                // 如果士兵没有目标，优先级最高
                                if (!soldier.targetMonster || !monsters.includes(soldier.targetMonster)) {
                                    score = distance * 0.1; // 大幅降低分数（提高优先级）
                                } else {
                                    // 如果士兵有目标，但目标距离很远，也可以重新分配
                                    const currentTargetDistance = Phaser.Math.Distance.Between(
                                        soldier.x, soldier.y, soldier.targetMonster.x, soldier.targetMonster.y
                                    );
                                    if (currentTargetDistance > distance * 1.5) {
                                        score = distance * 0.5; // 适度降低分数
                                    }
                                }

                                if (score < bestScore) {
                                    bestScore = score;
                                    bestSoldier = soldier;
                                }
                            }
                        });

                        if (bestSoldier) {
                            console.log('为怪物分配士兵:', monster.x, monster.y, '士兵:', bestSoldier.x, bestSoldier.y);
                            bestSoldier.targetMonster = monster;
                            bestSoldier.lastAttack = 0; // 重置攻击冷却
                        }
                    }
                });

                // 额外检查：确保左边缘的怪物不被遗漏
                const leftEdgeMonsters = monsters.filter(monster =>
                    !monster.isNewlySpawned && monster.x < gameWidth * 0.3
                );

                leftEdgeMonsters.forEach(monster => {
                    const attackingSoldiers = allyBlocks.filter(soldier =>
                        soldier.targetMonster === monster &&
                        (soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat')
                    );

                    if (attackingSoldiers.length === 0) {
                        // 强制为左边缘怪物分配士兵
                        const availableSoldiers = allyBlocks.filter(soldier =>
                            soldier.movementPhase === 'defending' || soldier.movementPhase === 'combat'
                        );

                        if (availableSoldiers.length > 0) {
                            const randomSoldier = availableSoldiers[Math.floor(Math.random() * availableSoldiers.length)];
                            console.log('强制为左边缘怪物分配士兵:', monster.x, monster.y);
                            randomSoldier.targetMonster = monster;
                            randomSoldier.lastAttack = 0;
                        }
                    }
                });

                // 清理死亡的我方方块（如果需要的话）
                allyBlocks = allyBlocks.filter(allyBlock => {
                    if (allyBlock.health <= 0) {
                        if (allyBlock.soldierText) {
                            allyBlock.soldierText.destroy();
                        }
                        allyBlock.destroy();
                        return false;
                    }
                    return true;
                });
            }

            function updateBattleUI() {
                // 更新防御墙血条
                const healthPercent = castleHealth / maxCastleHealth;
                gameScene.defenseWallHealthBar.scaleX = healthPercent;
                gameScene.defenseWallHealthBar.x = (gameWidth / 2) - 150 + (300 * healthPercent) / 2;
                gameScene.defenseWallHealthBar.fillColor = healthPercent > 0.5 ? 0x4A6FFF :
                                                    healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;

                // 更新文本
                gameScene.battleLevelText.setText(`战斗关卡: ${currentBattleLevel}`);
                gameScene.battleWaveText.setText(`波次: ${currentWave}`);
                gameScene.defenseWallHealthText.setText(`防御墙: ${Math.max(0, castleHealth)}/${maxCastleHealth}`);
                gameScene.allyCountText.setText(`守军: ${allyBlocks.length}`);

                // 更新怪物血条
                gameScene.monsterHealthBars.forEach(bar => bar.destroy());
                gameScene.monsterHealthBars = [];

                monsters.forEach((monster, index) => {
                    const healthPercent = monster.health / monster.maxHealth;

                    // 血条背景
                    const barBg = gameScene.add.rectangle(
                        monster.x,
                        monster.y - 40,
                        30,
                        4,
                        0x333333
                    );
                    gameScene.monsterHealthBars.push(barBg);

                    // 血条前景
                    const bar = gameScene.add.rectangle(
                        monster.x - 15 + (30 * healthPercent) / 2,
                        monster.y - 40,
                        30 * healthPercent,
                        4,
                        healthPercent > 0.5 ? 0x27ae60 : 0xe74c3c
                    );
                    gameScene.monsterHealthBars.push(bar);
                });

                // 显示我方方块血条
                allyBlocks.forEach((allyBlock, index) => {
                    const healthPercent = allyBlock.health / allyBlock.maxHealth;

                    // 血条背景
                    const barBg = gameScene.add.rectangle(
                        allyBlock.x,
                        allyBlock.y - 20,
                        20,
                        3,
                        0x333333
                    );
                    gameScene.monsterHealthBars.push(barBg);

                    // 血条前景
                    const bar = gameScene.add.rectangle(
                        allyBlock.x - 10 + (20 * healthPercent) / 2,
                        allyBlock.y - 20,
                        20 * healthPercent,
                        3,
                        0x2ecc71 // 绿色表示我方
                    );
                    gameScene.monsterHealthBars.push(bar);
                });
            }

            function checkBattleWaveComplete() {
                if (monsters.length === 0 && !isChoosing) {
                    currentWave++;

                    if (currentWave > 3) {
                        currentBattleLevel++;
                        currentWave = 1;
                        totalMonstersInWave = Math.min(6, 3 + Math.floor(currentBattleLevel / 2));
                    }

                    // 创建新波次
                    setTimeout(() => {
                        createBattleWave();
                    }, 1000);
                }
            }
            function setupGameLayout() {
                // 华容道区域在下半部分，战斗系统占用上半部分
                const battleAreaHeight = gameHeight * 0.4; // 战斗系统占40%
                const headerHeight = gameHeight * 0.05; // 华容道标题区域
                const footerHeight = gameHeight * 0.05; // 底部空间
                boardHeight = gameHeight - battleAreaHeight - headerHeight - footerHeight;
                // 华容道是4列5行的网格
                const cols = 4;
                const rows = 5;
                // 计算单元格大小，确保游戏板能完全适应屏幕，增加利用率
                cellSize = Math.min(gameWidth / cols, boardHeight / rows) * 0.90;
                // 计算游戏板的宽度和位置，使其居中
                boardWidth = cellSize * cols;
                boardX = (gameWidth - boardWidth) / 2;
                boardY = battleAreaHeight + headerHeight + (boardHeight - cellSize * rows) / 2;

                // 绘制华容道游戏板背景
                gameScene.add.rectangle(gameWidth / 2, battleAreaHeight + headerHeight + boardHeight / 2, boardWidth + cellSize * 0.1, cellSize * rows + cellSize * 0.1, 0xEEEEEE)
                   .setOrigin(0.5, 0.5)
                   .setStrokeStyle(2, 0xDDDDDD);

                // 绘制华容道城外的马路
                drawRoadsAroundCity();
            }

            function drawRoadsAroundCity() {
                const roadWidth = 40; // 马路宽度
                const roadColor = 0x8B4513; // 马路颜色（土色/棕色）

                // 华容道区域的边界
                const cityLeft = boardX - 20;
                const cityRight = boardX + boardWidth + 20;
                const cityTop = boardY - 20;
                const cityBottom = boardY + cellSize * 5 + 20;

                // 防御墙位置
                const defenseWallY = gameScene.defenseWallY;
                const gateX = gameScene.gateX;

                // 城市四周的环形马路

                // 左侧马路（垂直）
                const leftRoad = gameScene.add.rectangle(
                    cityLeft - roadWidth / 2,
                    (cityTop + cityBottom) / 2,
                    roadWidth,
                    cityBottom - cityTop + roadWidth,
                    roadColor
                );

                // 右侧马路（垂直）
                const rightRoad = gameScene.add.rectangle(
                    cityRight + roadWidth / 2,
                    (cityTop + cityBottom) / 2,
                    roadWidth,
                    cityBottom - cityTop + roadWidth,
                    roadColor
                );

                // 顶部马路（水平）- 完整的环形
                const topRoad = gameScene.add.rectangle(
                    (cityLeft + cityRight) / 2,
                    cityTop - roadWidth / 2,
                    cityRight - cityLeft + roadWidth * 2,
                    roadWidth,
                    roadColor
                );

                // 底部马路（水平）- 完整的环形
                const bottomRoad = gameScene.add.rectangle(
                    (cityLeft + cityRight) / 2,
                    cityBottom + roadWidth / 2,
                    cityRight - cityLeft + roadWidth * 2,
                    roadWidth,
                    roadColor
                );

                // 从上方中间位置延伸到城门的马路段
                const gateConnectorY = (cityTop - roadWidth / 2 + defenseWallY) / 2;
                const gateConnector = gameScene.add.rectangle(
                    gateX,
                    gateConnectorY,
                    roadWidth,
                    defenseWallY - (cityTop - roadWidth / 2),
                    roadColor
                );

                // 不再添加马路中线标记

                // 存储马路信息供士兵移动使用
                gameScene.roadInfo = {
                    leftRoadX: cityLeft - roadWidth / 2,
                    rightRoadX: cityRight + roadWidth / 2,
                    topRoadY: cityTop - roadWidth / 2,
                    bottomRoadY: cityBottom + roadWidth / 2,
                    gateConnectorX: gateX,
                    cityLeft: cityLeft,
                    cityRight: cityRight,
                    cityTop: cityTop,
                    cityBottom: cityBottom,
                    defenseWallY: defenseWallY,
                    gateX: gateX,
                    roadWidth: roadWidth
                };
            }
            function loadLevel(levelIndex) {
                // 清除现有方块和出口
                blocks.forEach(block => block.destroy());
                blocks = [];
                exits.forEach(exit => exit.graphics.destroy());
                exits = [];
                // 重置步数
                steps = 0;
                // 获取当前关卡数据
                const levelData = levels[levelIndex];
                grid = JSON.parse(JSON.stringify(levelData.grid)); // 深拷贝网格
                currentBlockSizes = levelData.blockSizes;
                // 创建出口
                createExits(levelData.exits);
                // 创建方块
                createBlocks();
                // UI已简化，不更新华容道相关文本
            }
            function createExits(exitData) {
                exitData.forEach(exit => {
                    const { side, col, row, width, color } = exit;
                    let x, y, exitWidth, exitHeight;

                    if (side === 'top') {
                        x = boardX + col * cellSize;
                        y = boardY - cellSize * 0.3; // 出口更窄
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'bottom') {
                        x = boardX + col * cellSize;
                        y = boardY + 5 * cellSize;
                        exitWidth = width * cellSize;
                        exitHeight = cellSize * 0.3;
                    } else if (side === 'left') {
                        x = boardX - cellSize * 0.3;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize; // 这里width表示高度
                    } else if (side === 'right') {
                        x = boardX + 4 * cellSize;
                        y = boardY + row * cellSize;
                        exitWidth = cellSize * 0.3;
                        exitHeight = width * cellSize;
                    }

                    const exitBlock = gameScene.add.graphics();
                    exitBlock.fillStyle(color, 1);
                    exitBlock.fillRect(x, y, exitWidth, exitHeight);

                    exits.push({
                        graphics: exitBlock,
                        side,
                        col: col || 0,
                        row: row || 0,
                        width: width || 1,
                        color,
                        blockId: exit.blockId
                    });
                });
            }
            function createBlocks() {
                // 创建方块对象
                const blockIds = new Set();
                // 首先找出所有方块ID
                for (let row = 0; row < grid.length; row++) {
                    for (let col = 0; col < grid[row].length; col++) {
                        const blockId = grid[row][col];
                        if (blockId > 0) {
                            blockIds.add(blockId);
                        }
                    }
                }
                // 为每个方块ID创建方块
                blockIds.forEach(blockId => {
                    // 找到方块的起始位置
                    let startRow = -1, startCol = -1;
                    outerLoop: for (let row = 0; row < grid.length; row++) {
                        for (let col = 0; col < grid[row].length; col++) {
                            if (grid[row][col] === blockId) {
                                startRow = row;
                                startCol = col;
                                break outerLoop;
                            }
                        }
                    }
                    if (startRow >= 0 && startCol >= 0) {
                        const blockSize = currentBlockSizes[blockId];
                        const width = blockSize.width;
                        const height = blockSize.height;
                        // 计算方块的像素位置和大小
                        const x = boardX + startCol * cellSize;
                        const y = boardY + startRow * cellSize;
                        const blockWidth = width * cellSize;
                        const blockHeight = height * cellSize;
                        // 选择方块颜色
                        const colorIndex = (blockId - 1) % blockColors.length;
                        const color = blockColors[colorIndex];
                        // 创建方块图形
                        const block = gameScene.add.graphics();
                        // 绘制方块
                        block.fillStyle(color, 1);
                        block.lineStyle(2, 0xffffff, 1);
                        block.fillRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        block.strokeRoundedRect(0, 0, blockWidth - 4, blockHeight - 4, 10);
                        // 创建方块容器
                        const blockContainer = gameScene.add.container(x + 2, y + 2);
                        blockContainer.add(block);
                        // 存储方块数据
                        blockContainer.setData('id', blockId);
                        blockContainer.setData('row', startRow);
                        blockContainer.setData('col', startCol);
                        blockContainer.setData('width', width);
                        blockContainer.setData('height', height);
                        blockContainer.setData('color', color);
                        // 使方块可交互
                        blockContainer.setInteractive(new Phaser.Geom.Rectangle(0, 0, blockWidth - 4, blockHeight - 4), Phaser.Geom.Rectangle.Contains);
                        // 添加拖动事件
                        gameScene.input.setDraggable(blockContainer);
                        // 将方块添加到数组
                        blocks.push(blockContainer);

                        // 在方块创建时就生成士兵
                        createSoldiersOnBlock(blockContainer, blockId, color, x, y, blockWidth, blockHeight);
                    }
                });
                // 设置拖动事件
                gameScene.input.on('dragstart', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                    startDragPosition = { x: pointer.x, y: pointer.y };
                });
                gameScene.input.on('drag', function (pointer, gameObject, dragX, dragY) {
                    // 不直接更新位置，只记录拖动方向
                });
                gameScene.input.on('dragend', function (pointer, gameObject) {
                    if (startDragPosition) {
                        const dx = pointer.x - startDragPosition.x;
                        const dy = pointer.y - startDragPosition.y;
                        // 确定主要拖动方向
                        if (Math.abs(dx) > Math.abs(dy)) {
                            // 水平拖动
                            if (dx > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'right');
                            } else if (dx < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'left');
                            }
                        } else {
                            // 垂直拖动
                            if (dy > cellSize / 3) {
                                moveBlockToEnd(gameObject, 'down');
                            } else if (dy < -cellSize / 3) {
                                moveBlockToEnd(gameObject, 'up');
                            }
                        }
                        startDragPosition = null;
                    }
                    selectedBlock = null;
                });
                // 添加点击事件
                gameScene.input.on('gameobjectdown', function (pointer, gameObject) {
                    selectedBlock = gameObject;
                });
            }
            function moveBlockToEnd(block, direction) {
                const blockId = block.getData('id');
                const startRow = block.getData('row');
                const startCol = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');

                let newRow = startRow;
                let newCol = startCol;

                // 找到该方向上能移动的最远位置
                let canMove = true;
                while (canMove) {
                    let testRow = newRow;
                    let testCol = newCol;

                    // 根据方向计算下一个测试位置
                    switch (direction) {
                        case 'up':
                            testRow--;
                            break;
                        case 'down':
                            testRow++;
                            break;
                        case 'left':
                            testCol--;
                            break;
                        case 'right':
                            testCol++;
                            break;
                    }

                    // 检查这个位置是否有效
                    if (isValidMove(blockId, startRow, startCol, testRow, testCol, width, height)) {
                        newRow = testRow;
                        newCol = testCol;
                    } else {
                        canMove = false;
                    }
                }

                // 如果找到了新位置，执行移动
                if (newRow !== startRow || newCol !== startCol) {
                    // 更新网格
                    updateGrid(blockId, startRow, startCol, newRow, newCol, width, height);
                    // 更新方块位置
                    block.setData('row', newRow);
                    block.setData('col', newCol);

                    // 移动方块上的士兵
                    moveBlockSoldiers(block, startRow, startCol, newRow, newCol);

                    // 动画移动方块
                    gameScene.tweens.add({
                        targets: block,
                        x: boardX + newCol * cellSize + 2,
                        y: boardY + newRow * cellSize + 2,
                        duration: 300, // 稍微长一点的动画时间
                        ease: 'Power2',
                        onComplete: function() {
                            if (!gameScene || !gameScene.scene || !gameScene.scene.isActive()) return;
                            // 检查方块是否到达出口
                            checkExitCondition(block);
                            // 检查是否过关
                            checkWinCondition();
                        }
                    });
                    // 增加步数（不显示）
                    steps++;
                } else {
                    // 无效移动，播放轻微震动动画
                    gameScene.tweens.add({
                        targets: block,
                        x: block.x + (direction === 'right' ? 5 : (direction === 'left' ? -5 : 0)),
                        y: block.y + (direction === 'down' ? 5 : (direction === 'up' ? -5 : 0)),
                        duration: 50,
                        yoyo: true,
                        repeat: 1
                    });
                }
            }

            // 移动方块上的士兵
            function moveBlockSoldiers(block, startRow, startCol, newRow, newCol) {
                const blockId = block.getData('id');
                const deltaX = (newCol - startCol) * cellSize;
                const deltaY = (newRow - startRow) * cellSize;

                // 找到站在这个方块上的士兵
                allyBlocks.forEach(soldier => {
                    if ((soldier.movementPhase === 'onBlock' || soldier.waitingForBlockExit) && soldier.onBlockId === blockId) {
                        // 移动士兵跟随方块
                        gameScene.tweens.add({
                            targets: [soldier, soldier.soldierText],
                            x: soldier.x + deltaX,
                            y: soldier.y + deltaY,
                            duration: 300,
                            ease: 'Power2'
                        });
                    }
                });
            }

            // 让士兵从方块下车到对应出口的马路上
            function dropSoldiersFromBlock(blockId) {
                const roadInfo = gameScene.roadInfo;

                allyBlocks.forEach(soldier => {
                    if ((soldier.movementPhase === 'onBlock' || soldier.waitingForBlockExit) && soldier.onBlockId === blockId) {
                        // 根据方块的出口方向决定停在哪条马路上
                        let targetX, targetY;

                        // 根据方块出口方向决定马路位置
                        switch (soldier.exitSide) {
                            case 'top':
                                // 顶部出口，停在顶部马路
                                targetX = soldier.x;
                                targetY = roadInfo.topRoadY;
                                break;
                            case 'bottom':
                                // 底部出口，停在底部马路
                                targetX = soldier.x;
                                targetY = roadInfo.bottomRoadY;
                                console.log('底部出口士兵下车:', soldier.x, soldier.y, '目标:', targetX, targetY);
                                break;
                            case 'left':
                                // 左侧出口，停在左侧马路
                                targetX = roadInfo.leftRoadX;
                                targetY = soldier.y;
                                break;
                            case 'right':
                                // 右侧出口，停在右侧马路
                                targetX = roadInfo.rightRoadX;
                                targetY = soldier.y;
                                break;
                            default:
                                // 默认情况，根据位置判断
                                if (soldier.y < roadInfo.cityTop) {
                                    targetX = soldier.x;
                                    targetY = roadInfo.topRoadY;
                                } else if (soldier.y > roadInfo.cityBottom) {
                                    targetX = soldier.x;
                                    targetY = roadInfo.bottomRoadY;
                                } else if (soldier.x < roadInfo.gateX) {
                                    targetX = roadInfo.leftRoadX;
                                    targetY = soldier.y;
                                } else {
                                    targetX = roadInfo.rightRoadX;
                                    targetY = soldier.y;
                                }
                                break;
                        }

                        // 缓慢移动到马路上
                        gameScene.tweens.add({
                            targets: [soldier, soldier.soldierText],
                            x: targetX,
                            y: targetY,
                            duration: 1200, // 1.2秒的缓动时间
                            ease: 'Power2.easeOut',
                            onComplete: () => {
                                // 到达马路后，开始移动到大门
                                soldier.movementPhase = 'toGate';
                                soldier.waitingForBlockExit = false;
                                soldier.onBlockId = null;
                                soldier.isMoving = false; // 确保重置移动状态
                                console.log('士兵下车完成，状态更新:', {
                                    phase: soldier.movementPhase,
                                    exitSide: soldier.exitSide,
                                    x: soldier.x,
                                    y: soldier.y,
                                    isMoving: soldier.isMoving,
                                    waitingForBlockExit: soldier.waitingForBlockExit
                                });
                            }
                        });

                        // 添加下车特效
                        const moveText = gameScene.add.text(soldier.x, soldier.y - 20, '🚶', {
                            fontSize: '16px'
                        }).setOrigin(0.5);

                        gameScene.tweens.add({
                            targets: moveText,
                            alpha: 0,
                            y: moveText.y - 30,
                            duration: 1200,
                            onComplete: () => moveText.destroy()
                        });

                        // 士兵轻微缩放效果表示下车动作
                        gameScene.tweens.add({
                            targets: [soldier, soldier.soldierText],
                            scaleX: 1.1,
                            scaleY: 1.1,
                            duration: 300,
                            yoyo: true,
                            ease: 'Power2.easeInOut'
                        });
                    }
                });
            }
            function isValidMove(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 检查是否超出边界
                if (newRow < 0 || newCol < 0 || newRow + height > grid.length || newCol + width > grid[0].length) {
                    return false;
                }
                // 检查新位置是否被占用
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        const currentCell = grid[startRow + r][startCol + c];
                        const newCell = grid[newRow + r][newCol + c];
                        // 如果新位置不是当前方块或空位，则无法移动
                        if (newCell !== 0 && newCell !== blockId) {
                            return false;
                        }
                    }
                }
                return true;
            }
            function updateGrid(blockId, startRow, startCol, newRow, newCol, width, height) {
                // 清除原位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        if (grid[startRow + r][startCol + c] === blockId) {
                            grid[startRow + r][startCol + c] = 0;
                        }
                    }
                }
                // 设置新位置
                for (let r = 0; r < height; r++) {
                    for (let c = 0; c < width; c++) {
                        grid[newRow + r][newCol + c] = blockId;
                    }
                }
            }
            function checkExitCondition(block) {
                const blockId = block.getData('id');
                const row = block.getData('row');
                const col = block.getData('col');
                const width = block.getData('width');
                const height = block.getData('height');
                const blockColor = block.getData('color');
                
                levels[currentLevel].exits.forEach(exit => {
                    const { side, col: exitCol, width: exitWidth, color, blockId: exitBlockId } = exit;

                    // 检查方块是否到达出口位置
                    if (side === 'top' && row === 0) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    } else if (side === 'bottom' && row + height === 5) {
                        // 检查方块是否在出口范围内，且方块宽度不超过出口宽度
                        if (col >= exitCol && col + width <= exitCol + exitWidth && width <= exitWidth) {
                            // 检查方块颜色是否与出口匹配
                            if (blockColor === color || blockId === exitBlockId) {
                                // 执行出门动画
                                executeExitAnimation(block, side, exitCol);
                                return;
                            } else {
                                // 颜色不匹配，播放错误动画
                                gameScene.tweens.add({
                                    targets: block,
                                    alpha: 0.5,
                                    duration: 200,
                                    yoyo: true,
                                    repeat: 1
                                });
                                return;
                            }
                        }
                    }
                });
            }
            
            function executeExitAnimation(block, side, exitCol) {
                const blockId = block.getData('id');
                const width = block.getData('width');
                const height = block.getData('height');
                
                // 首先播放闪烁效果
                gameScene.tweens.add({
                    targets: block,
                    alpha: 0.5,
                    duration: 300,
                    yoyo: true,
                    repeat: 2,
                    onComplete: function() {
                        // 闪烁结束后，执行淡出动画
                        // 获取方块位置信息（在销毁前获取）
                        const row = block.getData('row');
                        const col = block.getData('col');

                        const exitCompleteCallback = function() {
                            // 获取方块ID
                            const blockId = block.getData('id');

                            // 延迟一段时间后，让方块消失，士兵下车
                            setTimeout(() => {
                                // 让站在这个方块上的士兵下车到马路
                                dropSoldiersFromBlock(blockId);

                                // 从blocks数组中移除方块
                                blocks = blocks.filter(b => b !== block);
                                // 更新网格（在销毁前更新）
                                for (let i = 0; i < height; i++) {
                                    for (let j = 0; j < width; j++) {
                                        if (grid[row + i] && grid[row + i][col + j] !== undefined) {
                                            grid[row + i][col + j] = 0;
                                        }
                                    }
                                }
                                // 销毁方块
                                block.destroy();
                                // 检查是否过关
                                checkWinCondition();
                            }, 1000); // 1秒后方块消失，士兵下车
                        };

                        // 方块直接消失，不需要飞出去
                        gameScene.tweens.add({
                            targets: block,
                            alpha: 0,
                            scaleX: 0.8,
                            scaleY: 0.8,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: exitCompleteCallback
                        });
                    }
                });
            }
            
            function checkWinCondition() {
                // 检查是否所有方块都已经出去
                if (blocks.length === 0) {
                    // 所有方块都已消失，游戏胜利
                    // 解锁下一关
                    if (currentLevel === maxUnlockedLevel && currentLevel < levels.length - 1) {
                        maxUnlockedLevel = currentLevel + 1;
                        try {
                            localStorage.setItem('huarongdao_maxLevel', maxUnlockedLevel);
                        } catch (e) {
                            console.error('无法访问本地存储:', e);
                        }
                        // 新关卡已解锁，模态框会在下次打开时自动更新
                    }
                    // 延迟一点显示胜利对话框，确保动画完成
                    setTimeout(() => {
                        showWinModal();
                    }, 100);
                }
            }
            // 游戏内UI变量
            let levelSelectModal = null;
            let winModal = null;

            function createUI() {
                // 华容道UI已简化，不显示关卡和步数
                // 所有底部按钮和功能已完全移除
            }

            // 删除了createButton函数，不再需要底部按钮
            // 删除了undoLastMove函数，简化游戏操作

            // 删除了showLevelSelectModal函数，简化游戏界面

            function showWinModal() {
                // 简化过关处理：直接进入下一关或重新开始
                if (currentLevel < levels.length - 1) {
                    // 自动进入下一关
                    setTimeout(() => {
                        currentLevel++;
                        loadLevel(currentLevel);
                    }, 1000); // 1秒后自动进入下一关
                } else {
                    // 最后一关完成，重新开始第一关
                    setTimeout(() => {
                        currentLevel = 0;
                        loadLevel(currentLevel);
                    }, 1000);
                }
            }

            // 删除了createModalButton函数，不再需要模态框按钮

            // 删除了showGameMenu函数，简化游戏界面
            // 所有UI现在都在Phaser游戏引擎内
        };
    </script>
</body>

</html>
