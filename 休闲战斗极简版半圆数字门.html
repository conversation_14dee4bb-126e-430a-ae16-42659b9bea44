<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>竖版战斗游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let blueBalls = [], redBalls = [], blackBalls = [];
    let king; // 蓝方国王
    let playerHealth = 100, enemyHealth = 100;
    let maxPlayerHealth = 100, maxEnemyHealth = 100;
    let gameStarted = false;
    let currentLevel = 1, currentWave = 1;

    // 新增游戏阶段控制
    let gamePhase = 'placement'; // 'placement' 摆放阶段, 'battle' 战斗阶段
    let rings = []; // 环形圈数组
    let effectRings = []; // 效果环形数组 (x2爆炸, x4冰冻)
    let selectedEffectRing = null; // 当前选中的效果环形
    let placedEffectRings = []; // 已摆放的效果环形
    let startBattleButton = null; // 开始战斗按钮

    // 游戏控制变量
    // 移除小球相关变量，因为现在直接创建小兵

    // 现代混战管理系统
    class BattleManager {
        constructor() {
            this.units = new Map(); // 所有战斗单位的统一管理
            this.pendingActions = []; // 待处理的行动队列
            this.lastUpdateTime = 0;
            this.updateInterval = 50; // 每50ms更新一次，更流畅
            this.isProcessing = false; // 防止重入
            this.gameScene = null; // 保存游戏场景引用，用于动画
        }

        // 注册战斗单位
        registerUnit(unit, team) {
            if (!unit || !unit.x) return;
            const id = this.generateUnitId();
            unit.battleId = id;
            unit.team = team;
            unit.isAlive = true;
            unit.lastActionTime = 0;
            this.units.set(id, unit);
            return id;
        }

        // 移除战斗单位
        removeUnit(unitId) {
            if (this.units.has(unitId)) {
                const unit = this.units.get(unitId);
                unit.isAlive = false;
                this.units.delete(unitId);
            }
        }

        // 获取存活的敌方单位
        getEnemies(team) {
            const enemies = [];
            this.units.forEach(unit => {
                if (unit.team !== team && unit.isAlive && unit.health > 0) {
                    enemies.push(unit);
                }
            });
            return enemies;
        }

        // 获取存活的友方单位
        getAllies(team) {
            const allies = [];
            this.units.forEach(unit => {
                if (unit.team === team && unit.isAlive && unit.health > 0) {
                    allies.push(unit);
                }
            });
            return allies;
        }

        // 生成唯一ID
        generateUnitId() {
            return 'unit_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
        }

        // 安全的战斗更新
        update(currentTime) {
            if (this.isProcessing || currentTime - this.lastUpdateTime < this.updateInterval) {
                return;
            }

            this.isProcessing = true;
            this.lastUpdateTime = currentTime;

            try {
                // 清理死亡单位
                this.cleanupDeadUnits();

                // 更新血条位置
                this.updateHealthBars();

                // 处理战斗逻辑
                this.processBattleActions(currentTime);
            } catch (error) {
                console.warn('Error in battle update:', error);
            } finally {
                this.isProcessing = false;
            }
        }

        // 更新所有单位的血条
        updateHealthBars() {
            this.units.forEach(unit => {
                if (!unit.isAlive || !unit.healthBar || !unit.healthBarBg) return;

                try {
                    // 根据单位类型设置血条大小
                    let barWidth, barHeight, offsetY;

                    if (unit.maxHealth >= 20) {
                        // Boss血条（血量20+）
                        barWidth = 50;
                        barHeight = 8;
                        offsetY = -45; // 更新Boss血条偏移量
                    } else if (unit.maxHealth >= 15) {
                        // 国王血条
                        barWidth = 50;
                        barHeight = 6;
                        offsetY = -50; // 更新国王血条偏移量
                    } else {
                        // 普通小兵血条
                        barWidth = 24;
                        barHeight = 4;
                        offsetY = -28; // 更新小兵血条偏移量
                    }

                    // 清除并重绘血条背景
                    unit.healthBarBg.clear();
                    unit.healthBarBg.fillStyle(0x000000);
                    unit.healthBarBg.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth, barHeight);

                    // 清除并重绘血条
                    unit.healthBar.clear();
                    const healthPercent = unit.health / unit.maxHealth;
                    const healthColor = unit.team === 'blue' ? 0x00ff00 : 0xff0000;
                    unit.healthBar.fillStyle(healthColor);
                    unit.healthBar.fillRect(unit.x - barWidth/2, unit.y + offsetY, barWidth * healthPercent, barHeight);
                } catch (error) {
                    console.warn('Error updating health bar:', error);
                }
            });
        }

        // 清理死亡单位
        cleanupDeadUnits() {
            const deadUnits = [];
            this.units.forEach((unit, id) => {
                if (!unit.isAlive || unit.health <= 0 || !unit.active) {
                    deadUnits.push(id);
                }
            });
            deadUnits.forEach(id => this.removeUnit(id));
        }

        // 处理战斗行动
        processBattleActions(currentTime) {
            this.units.forEach(unit => {
                if (!unit.isAlive || unit.health <= 0 || unit.isDying) return;

                // 检查攻击冷却
                if (currentTime - unit.lastActionTime < (unit.attackCooldown || 1000)) return;

                // 寻找目标
                const enemies = this.getEnemies(unit.team);
                if (enemies.length === 0) return;

                const target = this.findNearestTarget(unit, enemies);
                if (!target) return;

                const distance = this.getDistance(unit, target);

                const attackRange = unit.attackRange || 50;

                if (distance <= attackRange) {
                    // 攻击
                    this.executeAttack(unit, target);
                    unit.lastActionTime = currentTime;
                } else {
                    // 移动（避免身体重叠）
                    this.moveTowardsTargetWithCollisionAvoidance(unit, target);
                }
            });
        }

        // 寻找最近目标
        findNearestTarget(unit, enemies) {
            let nearest = null;
            let minDistance = Infinity;

            enemies.forEach(enemy => {
                if (!enemy.isAlive || enemy.health <= 0 || enemy.isDying) return;
                const distance = this.getDistance(unit, enemy);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearest = enemy;
                }
            });

            return nearest;
        }

        // 计算距离
        getDistance(unit1, unit2) {
            if (!unit1 || !unit2 || unit1.x === undefined || unit2.x === undefined) return Infinity;
            return Phaser.Math.Distance.Between(unit1.x, unit1.y, unit2.x, unit2.y);
        }

        // 执行攻击
        executeAttack(attacker, target) {
            if (!attacker || !target || target.health <= 0 || target.isDying) return;

            // 标记目标正在被攻击，防止重复攻击
            if (target.isBeingAttacked) return;
            target.isBeingAttacked = true;

            // 检查是否为魔法攻击者（国王）
            if (attacker.isMagicUser) {
                this.playMagicAttackAnimation(attacker, target);
            } else {
                // 执行普通武器挥动动画
                this.playAttackAnimation(attacker, target);
            }

            // 延迟造成伤害，配合动画
            setTimeout(() => {
                if (target.isAlive && target.health > 0) {
                    // 魔法攻击需要等待魔法弹完成并收集效果
                    if (attacker.isMagicUser) {
                        // 魔法攻击的伤害将在魔法弹击中时处理
                        attacker.pendingMagicTarget = target;
                    } else {
                        this.dealDamage(target, attacker.attackPower || 1);
                    }
                }
                target.isBeingAttacked = false;
            }, attacker.isMagicUser ? 100 : 300); // 魔法攻击只需要等待法杖动画
        }

        // 播放攻击动画
        playAttackAnimation(attacker, target) {
            if (!attacker.weapon || !this.gameScene) return;

            // 标记正在攻击
            attacker.isAttacking = true;

            // 计算攻击方向
            const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

            // 武器攻击动画：更明显的摆动幅度
            const baseAngle = angle;
            const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
            const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

            // 人物抖动动画
            const originalX = attacker.x;
            const originalY = attacker.y;

            // 第一段动画：反向预备动作
            this.gameScene.tweens.add({
                targets: attacker.weapon,
                rotation: swingAngle1,
                duration: 100,
                ease: 'Power2',
                onComplete: () => {
                    // 第二段动画：快速攻击到60度 + 人物抖动
                    this.gameScene.tweens.add({
                        targets: attacker.weapon,
                        rotation: swingAngle2,
                        duration: 120,
                        ease: 'Back.easeOut',
                        onComplete: () => {
                            // 第三段动画：回到中间位置
                            this.gameScene.tweens.add({
                                targets: attacker.weapon,
                                rotation: baseAngle + Phaser.Math.DegToRad(20),
                                duration: 100,
                                ease: 'Power2',
                                onComplete: () => {
                                    // 最后回到初始角度
                                    this.gameScene.tweens.add({
                                        targets: attacker.weapon,
                                        rotation: 0,
                                        duration: 150,
                                        ease: 'Power1',
                                        onComplete: () => {
                                            attacker.isAttacking = false;
                                        }
                                    });
                                }
                            });
                        }
                    });

                    // 人物抖动效果
                    this.gameScene.tweens.add({
                        targets: attacker,
                        x: originalX + Phaser.Math.Between(-3, 3),
                        y: originalY + Phaser.Math.Between(-3, 3),
                        duration: 50,
                        yoyo: true,
                        repeat: 2,
                        onComplete: () => {
                            // 恢复原位置
                            attacker.setPosition(originalX, originalY);
                        }
                    });
                }
            });
        }

        // 播放魔法攻击动画（国王专用）
        playMagicAttackAnimation(attacker, target) {
            if (!attacker.weapon || !this.gameScene) return;

            // 标记正在攻击
            attacker.isAttacking = true;

            // 计算攻击方向
            const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

            // 法杖举起动画
            const originalRotation = attacker.weapon.rotation;
            const raiseAngle = -Math.PI / 4; // 举起法杖

            // 第一段：举起法杖
            this.gameScene.tweens.add({
                targets: attacker.weapon,
                rotation: raiseAngle,
                duration: 200,
                ease: 'Power2',
                onComplete: () => {
                    // 创建魔法弹
                    this.createMagicProjectile(attacker, target);

                    // 第二段：法杖发光效果（稍微抖动）
                    this.gameScene.tweens.add({
                        targets: attacker.weapon,
                        rotation: raiseAngle + 0.1,
                        duration: 100,
                        yoyo: true,
                        repeat: 3,
                        onComplete: () => {
                            // 第三段：法杖回到原位
                            this.gameScene.tweens.add({
                                targets: attacker.weapon,
                                rotation: originalRotation,
                                duration: 300,
                                ease: 'Power2',
                                onComplete: () => {
                                    attacker.isAttacking = false;
                                }
                            });
                        }
                    });
                }
            });
        }

        // 创建魔法弹
        createMagicProjectile(attacker, target) {
            if (!this.gameScene) return;

            try {
                // 创建魔法弹（使用圆形图形）
                const magicBall = this.gameScene.add.graphics();
                magicBall.fillStyle(0x00ffff, 0.8); // 青色魔法弹
                magicBall.fillCircle(0, 0, 8);
                magicBall.lineStyle(2, 0xffffff, 1);
                magicBall.strokeCircle(0, 0, 8);

                // 设置初始位置
                magicBall.setPosition(attacker.x, attacker.y - 20);
                magicBall.setDepth(15);

                // 添加发光效果
                const glowEffect = this.gameScene.add.graphics();
                glowEffect.fillStyle(0x00ffff, 0.3);
                glowEffect.fillCircle(0, 0, 15);
                glowEffect.setPosition(attacker.x, attacker.y - 20);
                glowEffect.setDepth(14);

                // 魔法弹属性
                magicBall.startX = attacker.x;
                magicBall.startY = attacker.y - 20;
                magicBall.targetX = target.x;
                magicBall.targetY = target.y;
                magicBall.effects = []; // 收集到的效果
                magicBall.passedRings = new Set(); // 已穿过的环形

                // 魔法弹飞行动画
                this.gameScene.tweens.add({
                    targets: [magicBall, glowEffect],
                    x: target.x,
                    y: target.y,
                    duration: 600,
                    ease: 'Power2',
                    onUpdate: () => {
                        // 检查是否穿过环形
                        this.checkMagicBallRingCollision(magicBall);
                    },
                    onComplete: () => {
                        // 根据收集到的效果创建爆炸并造成伤害
                        this.createEnhancedMagicExplosion(target.x, target.y, magicBall.effects, target, attacker);

                        // 销毁魔法弹
                        magicBall.destroy();
                        glowEffect.destroy();
                    }
                });

                // 魔法弹旋转效果
                this.gameScene.tweens.add({
                    targets: magicBall,
                    rotation: Math.PI * 4,
                    duration: 600,
                    ease: 'Linear'
                });

            } catch (error) {
                console.warn('Error creating magic projectile:', error);
            }
        }

        // 检查魔法弹是否穿过环形的有效区域
        checkMagicBallRingCollision(magicBall) {
            if (gamePhase !== 'battle' || !rings) return;

            rings.forEach(ring => {
                if (!ring.hasEffect || magicBall.passedRings.has(ring)) return;

                const effectRing = ring.effectRing;
                if (!effectRing || !effectRing.isPlaced) return;

                // 计算魔法弹到环形中心的距离
                const distance = Phaser.Math.Distance.Between(
                    magicBall.x, magicBall.y,
                    ring.centerX, ring.centerY
                );

                // 检查是否在环形半径范围内（允许一定误差）
                if (Math.abs(distance - ring.radius) < 15) {
                    // 计算魔法弹相对于环形中心的角度
                    const angle = Math.atan2(
                        magicBall.y - ring.centerY,
                        magicBall.x - ring.centerX
                    );

                    // 检查角度是否在四分之一有效区域内
                    if (this.isAngleInEffectRange(angle, effectRing)) {
                        // 魔法弹穿过了有效区域
                        magicBall.passedRings.add(ring);
                        magicBall.effects.push({
                            type: ring.effectType,
                            multiplier: ring.multiplier
                        });

                        // 创建穿过效果
                        this.createRingPassEffect(ring);

                        console.log(`魔法弹穿过${ring.effectType}有效区域，获得${ring.multiplier}效果`);
                    }
                }
            });
        }

        // 检查角度是否在效果环形的有效范围内
        isAngleInEffectRange(angle, effectRing) {
            if (!effectRing.currentStartAngle || !effectRing.currentEndAngle) return false;

            // 标准化角度到 [-π, π] 范围
            const normalizeAngle = (a) => {
                while (a > Math.PI) a -= 2 * Math.PI;
                while (a < -Math.PI) a += 2 * Math.PI;
                return a;
            };

            const normalizedAngle = normalizeAngle(angle);
            const startAngle = normalizeAngle(effectRing.currentStartAngle);
            const endAngle = normalizeAngle(effectRing.currentEndAngle);

            // 处理跨越 -π/π 边界的情况
            if (startAngle <= endAngle) {
                return normalizedAngle >= startAngle && normalizedAngle <= endAngle;
            } else {
                return normalizedAngle >= startAngle || normalizedAngle <= endAngle;
            }
        }

        // 创建穿过环形的效果
        createRingPassEffect(ring) {
            if (!this.gameScene) return;

            try {
                const effectRing = ring.effectRing;
                if (!effectRing) return;

                // 在效果环形的位置创建闪光效果
                const centerX = ring.centerX;
                const centerY = ring.centerY;
                const radius = ring.radius;

                // 创建环形闪光效果
                const flash = this.gameScene.add.graphics();
                flash.lineStyle(12, ring.effectType === 'explosion' ? 0xff4444 : 0x4444ff, 1);

                // 绘制四分之一圆弧闪光
                const currentAngle = effectRing.currentStartAngle || 0;
                flash.beginPath();
                flash.arc(centerX, centerY, radius, currentAngle, currentAngle + Math.PI/2, false);
                flash.strokePath();
                flash.setDepth(25);

                // 闪光动画
                this.gameScene.tweens.add({
                    targets: flash,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2',
                    onComplete: () => flash.destroy()
                });

                // 创建文字提示（在魔法弹位置附近）
                const effectText = this.gameScene.add.text(
                    centerX, centerY - 50,
                    `${ring.multiplier} ${ring.effectType === 'explosion' ? '爆炸' : '冰冻'}!`,
                    {
                        fontSize: '18px',
                        fill: ring.effectType === 'explosion' ? '#ff4444' : '#4444ff',
                        fontWeight: 'bold',
                        stroke: '#000000',
                        strokeThickness: 2
                    }
                ).setOrigin(0.5);
                effectText.setDepth(26);

                // 文字动画
                this.gameScene.tweens.add({
                    targets: effectText,
                    y: centerY - 80,
                    alpha: 0,
                    scale: 1.2,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => effectText.destroy()
                });

                // 创建粒子爆发效果
                for (let i = 0; i < 6; i++) {
                    const particle = this.gameScene.add.graphics();
                    particle.fillStyle(ring.effectType === 'explosion' ? 0xff4444 : 0x4444ff, 0.8);
                    particle.fillCircle(0, 0, 4);
                    particle.setPosition(centerX, centerY);
                    particle.setDepth(24);

                    const angle = (Math.PI/2) * Math.random() + currentAngle;
                    const distance = 30 + Math.random() * 20;

                    this.gameScene.tweens.add({
                        targets: particle,
                        x: centerX + Math.cos(angle) * distance,
                        y: centerY + Math.sin(angle) * distance,
                        alpha: 0,
                        duration: 600,
                        ease: 'Power2',
                        onComplete: () => particle.destroy()
                    });
                }

            } catch (error) {
                console.warn('Error creating ring pass effect:', error);
            }
        }

        // 创建增强魔法爆炸效果
        createEnhancedMagicExplosion(x, y, effects, target = null, attacker = null) {
            if (!this.gameScene) return;

            try {
                // 计算总伤害倍数
                let totalMultiplier = 1;
                let hasExplosion = false;
                let hasFreeze = false;

                effects.forEach(effect => {
                    if (effect.type === 'explosion') {
                        totalMultiplier *= parseInt(effect.multiplier.replace('x', ''));
                        hasExplosion = true;
                    } else if (effect.type === 'freeze') {
                        totalMultiplier *= parseInt(effect.multiplier.replace('x', ''));
                        hasFreeze = true;
                    }
                });

                // 对目标造成伤害
                if (target && attacker && target.isAlive && target.health > 0) {
                    const baseDamage = attacker.attackPower || 3;
                    this.dealDamage(target, baseDamage, effects);
                }

                // 创建基础爆炸效果
                const explosion = this.gameScene.add.graphics();
                explosion.lineStyle(4, 0x00ffff, 1);
                explosion.strokeCircle(0, 0, 5);
                explosion.setPosition(x, y);
                explosion.setDepth(16);

                // 根据效果调整爆炸大小和颜色
                let explosionSize = 3;
                let explosionColor = 0x00ffff;

                if (hasExplosion) {
                    explosionSize *= 2;
                    explosionColor = 0xff4444;
                }

                if (hasFreeze) {
                    explosionColor = hasExplosion ? 0xff44ff : 0x4444ff; // 混合色或纯冰冻色
                }

                // 爆炸扩散动画
                this.gameScene.tweens.add({
                    targets: explosion,
                    scaleX: explosionSize,
                    scaleY: explosionSize,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => explosion.destroy()
                });

                // 创建增强粒子效果
                const particleCount = Math.min(16, 8 + effects.length * 4);
                for (let i = 0; i < particleCount; i++) {
                    const particle = this.gameScene.add.graphics();
                    particle.fillStyle(explosionColor, 0.8);
                    particle.fillCircle(0, 0, 3);
                    particle.setPosition(x, y);
                    particle.setDepth(17);

                    const angle = (i / particleCount) * Math.PI * 2;
                    const distance = 40 * explosionSize;

                    this.gameScene.tweens.add({
                        targets: particle,
                        x: x + Math.cos(angle) * distance,
                        y: y + Math.sin(angle) * distance,
                        alpha: 0,
                        duration: 400,
                        ease: 'Power2',
                        onComplete: () => particle.destroy()
                    });
                }

                // 显示伤害倍数
                if (totalMultiplier > 1) {
                    const multiplierText = this.gameScene.add.text(x, y - 50, `x${totalMultiplier}`, {
                        fontSize: '24px',
                        fill: '#ffff00',
                        fontWeight: 'bold',
                        stroke: '#000000',
                        strokeThickness: 2
                    }).setOrigin(0.5);
                    multiplierText.setDepth(22);

                    this.gameScene.tweens.add({
                        targets: multiplierText,
                        y: y - 80,
                        alpha: 0,
                        scale: 1.5,
                        duration: 1000,
                        onComplete: () => multiplierText.destroy()
                    });
                }

                console.log(`增强爆炸效果：倍数x${totalMultiplier}，效果：${effects.map(e => e.type).join(', ')}`);

            } catch (error) {
                console.warn('Error creating enhanced magic explosion:', error);
            }
        }

        // 创建魔法爆炸效果
        createMagicExplosion(x, y) {
            if (!this.gameScene) return;

            try {
                // 创建爆炸圆圈
                const explosion = this.gameScene.add.graphics();
                explosion.lineStyle(4, 0x00ffff, 1);
                explosion.strokeCircle(0, 0, 5);
                explosion.setPosition(x, y);
                explosion.setDepth(16);

                // 爆炸扩散动画
                this.gameScene.tweens.add({
                    targets: explosion,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => explosion.destroy()
                });

                // 创建魔法粒子效果
                for (let i = 0; i < 8; i++) {
                    const particle = this.gameScene.add.graphics();
                    particle.fillStyle(0x00ffff, 0.8);
                    particle.fillCircle(0, 0, 3);
                    particle.setPosition(x, y);
                    particle.setDepth(17);

                    const angle = (i / 8) * Math.PI * 2;
                    const distance = 40;

                    this.gameScene.tweens.add({
                        targets: particle,
                        x: x + Math.cos(angle) * distance,
                        y: y + Math.sin(angle) * distance,
                        alpha: 0,
                        duration: 400,
                        ease: 'Power2',
                        onComplete: () => particle.destroy()
                    });
                }

            } catch (error) {
                console.warn('Error creating magic explosion:', error);
            }
        }

        // 造成伤害
        dealDamage(target, damage, effects = []) {
            if (!target || target.health <= 0 || target.isDying) return;

            // 计算增强伤害
            let finalDamage = damage;
            let hasFreeze = false;

            if (effects && effects.length > 0) {
                effects.forEach(effect => {
                    if (effect.type === 'explosion') {
                        const multiplier = parseInt(effect.multiplier.replace('x', ''));
                        finalDamage *= multiplier;
                    } else if (effect.type === 'freeze') {
                        const multiplier = parseInt(effect.multiplier.replace('x', ''));
                        finalDamage *= multiplier;
                        hasFreeze = true;
                    }
                });
            }

            target.health = Math.max(0, target.health - finalDamage);

            // 创建伤害特效
            this.createDamageEffect(target, finalDamage, effects);

            // 应用冰冻效果
            if (hasFreeze) {
                this.applyFreezeEffect(target);
            }

            if (target.health <= 0 && !target.isDying) {
                target.isDying = true;
                target.isAlive = false;

                // 创建击杀特效
                this.createKillEffect(target);

                // 延迟销毁，避免立即删除
                setTimeout(() => {
                    this.destroyUnit(target);

                    // 单位死亡后检查战斗结果
                    if (this.gameScene && typeof checkBattleResult === 'function') {
                        checkBattleResult.call(this.gameScene);
                    }
                }, 500);
            }
        }

        // 应用冰冻效果
        applyFreezeEffect(target) {
            if (!target || !this.gameScene) return;

            try {
                // 创建冰冻视觉效果
                const freezeEffect = this.gameScene.add.graphics();
                freezeEffect.fillStyle(0x4444ff, 0.3);
                freezeEffect.fillCircle(target.x, target.y, 25);
                freezeEffect.setDepth(18);

                // 减慢目标移动速度
                target.isFrozen = true;
                target.originalMoveSpeed = target.moveSpeed || 1;
                target.moveSpeed = target.originalMoveSpeed * 0.3; // 减速到30%

                // 冰冻效果持续3秒
                setTimeout(() => {
                    if (target && target.active) {
                        target.isFrozen = false;
                        target.moveSpeed = target.originalMoveSpeed;
                    }
                    if (freezeEffect && freezeEffect.active) {
                        freezeEffect.destroy();
                    }
                }, 3000);

                console.log('应用冰冻效果');

            } catch (error) {
                console.warn('Error applying freeze effect:', error);
            }
        }

        // 创建伤害数字特效
        createDamageEffect(target, damage, effects = []) {
            if (!this.gameScene || !target) return;

            try {
                // 根据效果调整文字颜色和大小
                let textColor = '#ff0000';
                let fontSize = '12px';
                let hasEffects = effects && effects.length > 0;

                if (hasEffects) {
                    fontSize = '16px';
                    // 如果有多种效果，使用紫色
                    if (effects.some(e => e.type === 'explosion') && effects.some(e => e.type === 'freeze')) {
                        textColor = '#ff44ff';
                    } else if (effects.some(e => e.type === 'explosion')) {
                        textColor = '#ff4444';
                    } else if (effects.some(e => e.type === 'freeze')) {
                        textColor = '#4444ff';
                    }
                }

                const damageText = this.gameScene.add.text(target.x, target.y - 30, `-${damage}`, {
                    fontSize: fontSize,
                    fill: textColor,
                    fontWeight: 'bold',
                    stroke: '#000000',
                    strokeThickness: hasEffects ? 2 : 1
                }).setOrigin(0.5);

                // 如果有效果，添加额外的动画
                if (hasEffects) {
                    damageText.setScale(1.2);
                    this.gameScene.tweens.add({
                        targets: damageText,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 200,
                        ease: 'Back.easeOut'
                    });
                }

                this.gameScene.tweens.add({
                    targets: damageText,
                    y: target.y - 50,
                    alpha: 0,
                    duration: 800,
                    onComplete: () => damageText.destroy()
                });
            } catch (error) {
                console.warn('Error creating damage effect:', error);
            }
        }

        // 创建击杀特效
        createKillEffect(target) {
            if (!this.gameScene || !target) return;

            try {
                const killText = this.gameScene.add.text(target.x, target.y, 'KILL!', {
                    fontSize: '16px',
                    fill: '#ffff00',
                    fontWeight: 'bold',
                    stroke: '#000000',
                    strokeThickness: 2
                }).setOrigin(0.5);

                this.gameScene.tweens.add({
                    targets: killText,
                    y: target.y - 60,
                    alpha: 0,
                    scale: 1.5,
                    duration: 1000,
                    onComplete: () => killText.destroy()
                });
            } catch (error) {
                console.warn('Error creating kill effect:', error);
            }
        }

        // 销毁单位
        destroyUnit(unit) {
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 从数组中安全移除
            this.removeFromArrays(unit);

            // 销毁游戏对象
            if (unit.weapon) unit.weapon.destroy();
            if (unit.healthBar) unit.healthBar.destroy();
            if (unit.healthBarBg) unit.healthBarBg.destroy();
            if (unit.active) unit.destroy();
        }

        // 从数组中移除单位
        removeFromArrays(unit) {
            try {


                // 安全地从数组中移除
                const arrays = [blueBalls, redBalls, blackBalls];
                arrays.forEach(arr => {
                    if (Array.isArray(arr)) {
                        const index = arr.indexOf(unit);
                        if (index > -1) {
                            arr.splice(index, 1);
                        }
                    }
                });
            } catch (error) {
                console.warn('Error removing unit from arrays:', error);
            }
        }



        // 移动向目标 - 防止身体重叠
        moveTowardsTargetWithCollisionAvoidance(unit, target) {
            if (!unit || !target || unit.isAttacking) return;

            // 国王不移动，保持在摆放位置
            if (unit.label === 'king' || unit.isMagicUser) return;

            const dx = target.x - unit.x;
            const dy = target.y - unit.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 0) {
                let moveX = (dx / distance);
                let moveY = (dy / distance);

                // 检查与其他单位的碰撞
                const avoidanceVector = this.calculateAvoidanceVector(unit);
                moveX += avoidanceVector.x;
                moveY += avoidanceVector.y;

                // 标准化移动向量
                const moveLength = Math.sqrt(moveX * moveX + moveY * moveY);
                if (moveLength > 0) {
                    moveX /= moveLength;
                    moveY /= moveLength;
                }

                const moveSpeed = 2.0;
                const newX = unit.x + moveX * moveSpeed;
                const newY = unit.y + moveY * moveSpeed;

                // 边界检查 - 限制在战斗区域（画面上1/3）
                const boundedX = Math.max(30, Math.min(720, newX));  // 左右边界
                const boundedY = Math.max(150, Math.min(430, newY)); // 上1/3区域边界

                // 平滑移动：使用插值而不是瞬间移动
                this.smoothSetPosition(unit, boundedX, boundedY);
            }
        }

        // 平滑设置位置 - 保持原有速度的平滑移动
        smoothSetPosition(unit, targetX, targetY) {
            if (!unit || !this.gameScene) {
                unit.setPosition(targetX, targetY);
                return;
            }

            // 计算移动距离
            const dx = targetX - unit.x;
            const dy = targetY - unit.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            // 如果距离很小，直接设置位置
            if (distance < 0.5) {
                unit.setPosition(targetX, targetY);
                return;
            }

            // 停止之前的移动动画
            if (unit.smoothTween) {
                unit.smoothTween.stop();
            }

            // 使用短时间的补间动画保持移动速度
            const moveTime = Math.min(100, distance * 20); // 根据距离调整时间，最多100ms

            unit.smoothTween = this.gameScene.tweens.add({
                targets: unit,
                x: targetX,
                y: targetY,
                duration: moveTime,
                ease: 'Linear',
                onComplete: () => {
                    unit.smoothTween = null;
                }
            });
        }

        // 计算避让向量，防止单位重叠
        calculateAvoidanceVector(unit) {
            let avoidX = 0;
            let avoidY = 0;
            const avoidanceRadius = 40; // 避让半径

            this.units.forEach(otherUnit => {
                if (otherUnit === unit || !otherUnit.isAlive || otherUnit.health <= 0) return;

                const dx = unit.x - otherUnit.x;
                const dy = unit.y - otherUnit.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < avoidanceRadius && distance > 0) {
                    // 计算避让力度，距离越近力度越大
                    const avoidanceStrength = (avoidanceRadius - distance) / avoidanceRadius;
                    avoidX += (dx / distance) * avoidanceStrength * 0.5;
                    avoidY += (dy / distance) * avoidanceStrength * 0.5;
                }
            });

            return { x: avoidX, y: avoidY };
        }

        // 获取战斗统计
        getBattleStats() {
            const stats = { blue: 0, red: 0 };
            this.units.forEach(unit => {
                if (unit.isAlive && unit.health > 0) {
                    if (unit.team === 'blue') {
                        stats.blue++;
                    } else if (unit.team === 'red') {
                        stats.red++;
                    }
                }
            });
            return stats;
        }
    }

    // 创建全局战斗管理器
    let battleManager = new BattleManager();

    // 确保数组安全初始化的函数
    function ensureArraysInitialized() {
        if (!Array.isArray(blueBalls)) blueBalls = [];
        if (!Array.isArray(redBalls)) redBalls = [];
        if (!Array.isArray(blackBalls)) blackBalls = [];
    }

    // 游戏变量
    let battleMode = false; // 战斗模式



    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        physics: {
            default: 'matter',
            matter: {
                gravity: { y: 0 },
                debug: false
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载大背景图片
        this.load.image('gameBackground', 'images/rpg/background9.png');

        // 创建带高光效果的球纹理
        createBallTexture.call(this, 'blueBall', 0x3498db);
        createBallTexture.call(this, 'redBall', 0xe74c3c);
        createBallTexture.call(this, 'blackBall', 0x000000);

        // 加载小兵图片
        this.load.image('blueSoldier', 'images/rpg/蓝色小兵.png');
        this.load.image('redSoldier', 'images/rpg/红色小兵.png');
        this.load.image('weapon', 'images/rpg/小兵的武器.png');

        // 加载特殊角色图片
        this.load.image('king', 'images/rpg/国王.png');
        this.load.image('kingWeapon', 'images/rpg/国王的武器.png');
        this.load.image('redLeader', 'images/rpg/红色将领.png');
    }

    // 创建球的纹理（带高光效果）
    function createBallTexture(textureKey, color) {
        const ballGraphic = this.add.graphics();

        // 主球体
        ballGraphic.fillStyle(color, 1);
        ballGraphic.fillCircle(16, 16, 15);

        // 添加高光效果
        ballGraphic.fillStyle(0xffffff, 0.4);
        ballGraphic.fillCircle(11, 11, 5); // 主高光

        // 添加次高光
        ballGraphic.fillStyle(0xffffff, 0.2);
        ballGraphic.fillCircle(9, 9, 2);

        // 添加阴影效果
        ballGraphic.fillStyle(0x000000, 0.2);
        ballGraphic.fillCircle(20, 20, 3);

        ballGraphic.generateTexture(textureKey, 32, 32);
        ballGraphic.destroy(); // 销毁图形对象，避免显示在画布上
    }





    // 创建游戏场景
    function create() {
        // 添加大背景图片
        const background = this.add.image(375, 667, 'gameBackground');
        background.setDisplaySize(750, 1334); // 设置为游戏窗口大小
        background.setDepth(-10); // 设置为最底层

        // 确保数组正确初始化
        ensureArraysInitialized();

        // 创建游戏边界
        createGameBounds.call(this);

        // 创建摆放阶段的UI和元素
        createPlacementPhase.call(this);

        // 创建UI
        createUI.call(this);

        // 设置输入事件
        setupInput.call(this);

        gameStarted = true;
        gamePhase = 'placement'; // 开始时是摆放阶段

        console.log('游戏开始 - 摆放阶段');
    }

    function update() {
        if (gameStarted) {
            // 确保数组安全
            ensureArraysInitialized();

            // 更新环形旋转（战斗阶段）
            if (gamePhase === 'battle') {
                updateRingRotation.call(this);
            }

            updateHealthBars.call(this);
            checkGameState.call(this);

            // 不再需要处理小球销毁，因为直接创建小兵

            // 在战斗模式下，使用新的战斗管理器
            if (battleMode) {
                try {
                    // 使用战斗管理器进行更新，而不是每帧执行复杂逻辑
                    battleManager.update(this.time.now);
                    updateWeaponPositions.call(this);
                } catch (error) {
                    console.warn('Error in battle mode update:', error);
                }
            }
        }
    }

    // 清理数组中的null和无效值
    function cleanupArrays() {
        try {
            // 确保数组存在，如果不存在则初始化为空数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

            const originalBlueLength = blueBalls.length;
            const originalRedLength = redBalls.length;
            const originalBlackLength = blackBalls.length;

            blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && unit.active);
            blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && unit.active);

            // 如果有清理，输出日志
            if (originalBlueLength !== blueBalls.length) {
                console.log(`清理蓝球数组: ${originalBlueLength} -> ${blueBalls.length}`);
            }
            if (originalRedLength !== redBalls.length) {
                console.log(`清理红球数组: ${originalRedLength} -> ${redBalls.length}`);
            }
            if (originalBlackLength !== blackBalls.length) {
                console.log(`清理黑球数组: ${originalBlackLength} -> ${blackBalls.length}`);
            }
        } catch (error) {
            console.warn('Error cleaning arrays:', error);
            // 如果出错，重新初始化数组
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];
        }
    }

    // 创建游戏边界
    function createGameBounds() {
        // 创建战斗区域边界（画面上1/3区域）
        // 左边界
        this.matter.add.rectangle(15, 290, 30, 280, { isStatic: true });
        // 右边界
        this.matter.add.rectangle(735, 290, 30, 280, { isStatic: true });
        // 上边界
        this.matter.add.rectangle(375, 135, 750, 30, { isStatic: true });
        // 下边界（战斗区域下方）
        this.matter.add.rectangle(375, 445, 750, 30, { isStatic: true });
    }

    // 创建摆放阶段
    function createPlacementPhase() {
        // 创建国王在画布中间
        createKingInCenter.call(this);

        // 创建环形圈
        createRings.call(this);

        // 创建效果环形（x2爆炸，x4冰冻）
        createEffectRings.call(this);

        // 创建开始战斗按钮
        createStartBattleButton.call(this);
    }

    // 创建国王在中心位置
    function createKingInCenter() {
        const centerX = 375;
        const centerY = 750; // 下移200px (400 + 200)

        king = this.add.image(centerX, centerY, 'king');
        king.setScale(0.6);
        king.setDepth(5);
        king.setRotation(0);
        king.isPhysicsEnabled = false;

        // 国王的魔法法杖
        const magicStaff = this.add.image(centerX + 15, centerY, 'kingWeapon');
        magicStaff.setScale(0.6);
        magicStaff.setDepth(6);
        magicStaff.setOrigin(0.3, 0.7);
        magicStaff.setRotation(0);

        // 添加血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(centerX - 25, centerY - 50, 50, 6);
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00);
        healthBar.fillRect(centerX - 25, centerY - 50, 50, 6);
        healthBar.setDepth(11);

        king.weapon = magicStaff;
        king.healthBarBg = healthBarBg;
        king.healthBar = healthBar;
        king.maxHealth = 15;
        king.health = 15;
        king.team = 'blue';
        king.attackPower = 3;
        king.attackRange = 200;
        king.lastAttackTime = 0;
        king.attackCooldown = 2000;
        king.isAttacking = false;
        king.isMagicUser = true;
        king.label = 'king';

        console.log('国王创建在中心位置');
    }

    // 创建环形圈
    function createRings() {
        const centerX = 375;
        const centerY = 750;
        const ringCount = 5; // 5层环形

        rings = [];

        for (let i = 1; i <= ringCount; i++) {
            const radius = 80 + i * 40; // 从80像素开始，每层增加40像素
            const ring = this.add.graphics();
            ring.lineStyle(6, 0xffffff, 0.8); // 增加线段粗细从3到6
            ring.strokeCircle(centerX, centerY, radius);
            ring.setDepth(1);

            // 添加环形属性
            ring.centerX = centerX;
            ring.centerY = centerY;
            ring.radius = radius;
            ring.rotation = 0;
            ring.rotationSpeed = 0; // 摆放阶段不旋转
            ring.hasEffect = false;
            ring.effectType = null;

            rings.push(ring);
        }

        console.log(`创建了${ringCount}个环形圈`);
    }

    // 创建效果环形（x2爆炸，x4冰冻）
    function createEffectRings() {
        const bottomY = 1200; // 屏幕底部位置
        const spacing = 150;
        const startX = 150;

        effectRings = [];

        // 创建2个x2爆炸效果环形
        for (let i = 0; i < 2; i++) {
            const x = startX + i * spacing;
            const effectRing = createEffectRing.call(this, x, bottomY, 'explosion', 'x2');
            effectRings.push(effectRing);
        }

        // 创建2个x4冰冻效果环形
        for (let i = 0; i < 2; i++) {
            const x = startX + (i + 2) * spacing;
            const effectRing = createEffectRing.call(this, x, bottomY, 'freeze', 'x4');
            effectRings.push(effectRing);
        }

        console.log('创建了4个效果环形');
    }

    // 创建单个效果环形
    function createEffectRing(x, y, effectType, multiplier) {
        // 创建四分之一环形
        const ring = this.add.graphics();

        if (effectType === 'explosion') {
            ring.lineStyle(10, 0xff4444, 1); // 红色爆炸效果，加粗到10px
        } else if (effectType === 'freeze') {
            ring.lineStyle(10, 0x4444ff, 1); // 蓝色冰冻效果，加粗到10px
        }

        // 绘制四分之一圆弧
        const radius = 40;
        ring.beginPath();
        ring.arc(x, y, radius, -Math.PI/2, 0, false); // 右上四分之一
        ring.strokePath();
        ring.setDepth(2);

        // 添加效果文字
        const text = this.add.text(x, y - 20, multiplier, {
            fontSize: '20px',
            fill: effectType === 'explosion' ? '#ff4444' : '#4444ff',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        text.setDepth(3);

        // 添加效果图标
        const icon = this.add.text(x, y + 20, effectType === 'explosion' ? '💥' : '❄️', {
            fontSize: '24px'
        }).setOrigin(0.5);
        icon.setDepth(3);

        // 设置交互
        ring.setInteractive(new Phaser.Geom.Circle(x, y, radius), Phaser.Geom.Circle.Contains);

        // 添加属性
        ring.effectType = effectType;
        ring.multiplier = multiplier;
        ring.originalX = x;
        ring.originalY = y;
        ring.radius = radius;
        ring.text = text;
        ring.icon = icon;
        ring.isPlaced = false;
        ring.targetRing = null;

        // 点击事件
        ring.on('pointerdown', () => {
            if (gamePhase === 'placement' && !ring.isPlaced) {
                selectEffectRing.call(this, ring);
            }
        });

        // 悬停效果
        ring.on('pointerover', () => {
            if (gamePhase === 'placement' && !ring.isPlaced) {
                ring.setAlpha(0.8);
            }
        });

        ring.on('pointerout', () => {
            if (gamePhase === 'placement' && !ring.isPlaced) {
                ring.setAlpha(1);
            }
        });

        return ring;
    }

    // 选择效果环形
    function selectEffectRing(ring) {
        // 取消之前的选择
        if (selectedEffectRing) {
            selectedEffectRing.setAlpha(1);
        }

        selectedEffectRing = ring;
        ring.setAlpha(0.6);

        // 高亮可放置的环形圈
        rings.forEach(targetRing => {
            if (!targetRing.hasEffect) {
                targetRing.clear();
                targetRing.lineStyle(3, 0x00ff00, 1); // 绿色高亮
                targetRing.strokeCircle(targetRing.centerX, targetRing.centerY, targetRing.radius);

                // 添加点击事件
                targetRing.setInteractive(new Phaser.Geom.Circle(targetRing.centerX, targetRing.centerY, targetRing.radius), Phaser.Geom.Circle.Contains);
                targetRing.on('pointerdown', () => {
                    placeEffectRing.call(this, selectedEffectRing, targetRing);
                });
            }
        });

        console.log(`选择了${ring.effectType}效果环形`);
    }

    // 放置效果环形到目标环形圈
    function placeEffectRing(effectRing, targetRing) {
        if (!effectRing || !targetRing || targetRing.hasEffect) return;

        // 计算放置位置（在目标环形圈上，保持四分之一形状）
        const centerX = targetRing.centerX;
        const centerY = targetRing.centerY;
        const radius = targetRing.radius;

        // 移动效果环形到目标环形圈上（保持四分之一形状）
        effectRing.clear();
        if (effectRing.effectType === 'explosion') {
            effectRing.lineStyle(10, 0xff4444, 0.8); // 保持10px粗细
        } else {
            effectRing.lineStyle(10, 0x4444ff, 0.8); // 保持10px粗细
        }

        // 绘制四分之一圆弧在目标环形圈上（右上角）
        effectRing.beginPath();
        effectRing.arc(centerX, centerY, radius, -Math.PI/2, 0, false);
        effectRing.strokePath();

        // 更新文字和图标位置
        effectRing.text.x = centerX + radius * 0.7;
        effectRing.text.y = centerY - radius * 0.7;
        effectRing.icon.x = centerX + radius * 0.5;
        effectRing.icon.y = centerY - radius * 0.5;

        // 设置状态
        effectRing.isPlaced = true;
        effectRing.targetRing = targetRing;
        effectRing.placedCenterX = centerX;
        effectRing.placedCenterY = centerY;
        effectRing.placedRadius = radius;
        effectRing.currentAngle = 0; // 当前旋转角度

        targetRing.hasEffect = true;
        targetRing.effectType = effectRing.effectType;
        targetRing.multiplier = effectRing.multiplier;
        targetRing.effectRing = effectRing;

        // 添加到已放置列表
        placedEffectRings.push(effectRing);

        // 重置选择状态
        selectedEffectRing = null;

        // 恢复所有环形圈的原始样式
        rings.forEach(ring => {
            ring.removeAllListeners('pointerdown');
            ring.disableInteractive();
            if (!ring.hasEffect) {
                ring.clear();
                ring.lineStyle(6, 0xffffff, 0.8); // 保持粗线条
                ring.strokeCircle(ring.centerX, ring.centerY, ring.radius);
            }
        });

        console.log(`放置${effectRing.effectType}效果到环形圈（四分之一区域）`);

        // 检查是否可以开始战斗
        checkCanStartBattle.call(this);
    }

    // 创建开始战斗按钮
    function createStartBattleButton() {
        const buttonX = 375;
        const buttonY = 1100;

        // 按钮背景
        const buttonBg = this.add.graphics();
        buttonBg.fillStyle(0x00aa00, 1);
        buttonBg.fillRoundedRect(buttonX - 100, buttonY - 30, 200, 60, 15);
        buttonBg.lineStyle(3, 0x000000, 1);
        buttonBg.strokeRoundedRect(buttonX - 100, buttonY - 30, 200, 60, 15);
        buttonBg.setDepth(10);
        buttonBg.setAlpha(0.5); // 初始状态为半透明（不可点击）

        // 按钮文字
        const buttonText = this.add.text(buttonX, buttonY, '开始战斗', {
            fontSize: '28px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        buttonText.setDepth(11);
        buttonText.setAlpha(0.5);

        // 设置交互（初始不可点击）
        buttonBg.setInteractive(new Phaser.Geom.Rectangle(buttonX - 100, buttonY - 30, 200, 60), Phaser.Geom.Rectangle.Contains);
        buttonBg.disableInteractive();

        startBattleButton = {
            bg: buttonBg,
            text: buttonText,
            enabled: false
        };

        console.log('创建开始战斗按钮（初始禁用）');
    }

    // 检查是否可以开始战斗
    function checkCanStartBattle() {
        // 至少放置一个效果环形才能开始战斗
        if (placedEffectRings.length > 0 && startBattleButton) {
            enableStartBattleButton.call(this);
        }
    }

    // 启用开始战斗按钮
    function enableStartBattleButton() {
        if (!startBattleButton || startBattleButton.enabled) return;

        startBattleButton.enabled = true;
        startBattleButton.bg.setAlpha(1);
        startBattleButton.text.setAlpha(1);
        startBattleButton.bg.setInteractive();

        // 点击事件
        startBattleButton.bg.on('pointerdown', () => {
            if (gamePhase === 'placement') {
                startBattlePhase.call(this);
            }
        });

        // 悬停效果
        startBattleButton.bg.on('pointerover', () => {
            startBattleButton.bg.clear();
            startBattleButton.bg.fillStyle(0x00cc00, 1);
            startBattleButton.bg.fillRoundedRect(275, 1070, 200, 60, 15);
            startBattleButton.bg.lineStyle(3, 0x000000, 1);
            startBattleButton.bg.strokeRoundedRect(275, 1070, 200, 60, 15);
        });

        startBattleButton.bg.on('pointerout', () => {
            startBattleButton.bg.clear();
            startBattleButton.bg.fillStyle(0x00aa00, 1);
            startBattleButton.bg.fillRoundedRect(275, 1070, 200, 60, 15);
            startBattleButton.bg.lineStyle(3, 0x000000, 1);
            startBattleButton.bg.strokeRoundedRect(275, 1070, 200, 60, 15);
        });

        console.log('开始战斗按钮已启用');
    }

    // 开始战斗阶段
    function startBattlePhase() {
        gamePhase = 'battle';

        // 隐藏摆放阶段的UI
        hidePlacementUI.call(this);

        // 创建敌人
        createEnemies.call(this);

        // 开始环形旋转
        startRingRotation.call(this);

        // 开始战斗模式
        startBattleMode.call(this);

        console.log('战斗阶段开始！');
    }

    // 隐藏摆放阶段的UI
    function hidePlacementUI() {
        // 隐藏未放置的效果环形
        effectRings.forEach(ring => {
            if (!ring.isPlaced) {
                ring.setVisible(false);
                ring.text.setVisible(false);
                ring.icon.setVisible(false);
            }
        });

        // 隐藏所有白色环形圈
        rings.forEach(ring => {
            ring.setVisible(false);
        });

        // 隐藏开始战斗按钮
        if (startBattleButton) {
            startBattleButton.bg.setVisible(false);
            startBattleButton.text.setVisible(false);
        }
    }

    // 创建敌人
    function createEnemies() {
        // 创建红方小兵
        const redSoldierPositions = [
            { x: 600, y: 200 },
            { x: 600, y: 260 },
            { x: 600, y: 320 },
            { x: 600, y: 380 },
            { x: 650, y: 230 },
            { x: 650, y: 290 },
            { x: 650, y: 350 }
        ];

        redSoldierPositions.forEach((pos, index) => {
            if (index < 5) {
                const soldier = createRedSoldier.call(this, pos.x, pos.y);
                redBalls.push(soldier);
            }
        });

        // 创建红方将领
        const leader = createRedLeader.call(this, 680, 290);
        blackBalls.push(leader);

        console.log(`创建敌人：红方小兵${redBalls.length}个，红方将领${blackBalls.length}个`);
    }

    // 开始环形旋转
    function startRingRotation() {
        rings.forEach((ring, index) => {
            // 设置不同的旋转速度
            ring.rotationSpeed = (index % 2 === 0 ? 1 : -1) * (0.5 + index * 0.2);
        });

        console.log('环形开始旋转');
    }

    // 更新环形旋转（在update函数中调用）
    function updateRingRotation() {
        if (gamePhase !== 'battle') return;

        rings.forEach(ring => {
            // 环形本身不需要重绘，只是记录旋转角度
            ring.rotation += ring.rotationSpeed * 0.01;

            // 如果有效果环形，更新其位置
            if (ring.hasEffect && ring.effectRing) {
                updateEffectRingRotation(ring.effectRing, ring.rotation);
            }
        });
    }

    // 更新效果环形的旋转位置
    function updateEffectRingRotation(effectRing, rotationAngle) {
        if (!effectRing.isPlaced) return;

        const centerX = effectRing.placedCenterX;
        const centerY = effectRing.placedCenterY;
        const radius = effectRing.placedRadius;

        // 计算四分之一圆弧的起始和结束角度（考虑旋转）
        const startAngle = -Math.PI/2 + rotationAngle;
        const endAngle = 0 + rotationAngle;

        // 重绘四分之一圆弧
        effectRing.clear();
        if (effectRing.effectType === 'explosion') {
            effectRing.lineStyle(6, 0xff4444, 0.8);
        } else {
            effectRing.lineStyle(6, 0x4444ff, 0.8);
        }

        effectRing.beginPath();
        effectRing.arc(centerX, centerY, radius, startAngle, endAngle, false);
        effectRing.strokePath();

        // 更新文字和图标位置（跟随旋转）
        const midAngle = (startAngle + endAngle) / 2;
        effectRing.text.x = centerX + Math.cos(midAngle) * radius * 0.7;
        effectRing.text.y = centerY + Math.sin(midAngle) * radius * 0.7;
        effectRing.icon.x = centerX + Math.cos(midAngle) * radius * 0.5;
        effectRing.icon.y = centerY + Math.sin(midAngle) * radius * 0.5;

        // 记录当前有效角度范围（用于碰撞检测）
        effectRing.currentStartAngle = startAngle;
        effectRing.currentEndAngle = endAngle;
    }



    // 这个函数在摆放阶段不需要，将在战斗阶段调用

    // 创建蓝方小兵
    function createBlueSoldier(x, y) {
        const soldier = this.add.image(x, y, 'blueSoldier');
        soldier.setScale(0.2);
        soldier.setDepth(5);
        soldier.setRotation(0);
        soldier.label = 'blueBall';
        soldier.isPhysicsEnabled = false;

        // 添加武器
        const weapon = this.add.image(x + 8, y, 'weapon');
        weapon.setScale(0.2);
        weapon.setDepth(6);
        weapon.setOrigin(0.3, 0.7); // 设置旋转中心在底部   
        weapon.setRotation(0);

        // 添加血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(x - 12, y - 28, 24, 4); // 血条向上移动10像素
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0x00ff00);
        healthBar.fillRect(x - 12, y - 28, 24, 4); // 血条向上移动10像素
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5;
        soldier.health = 5;
        soldier.team = 'blue';
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        return soldier;
    }

    // 创建红方小兵
    function createRedSoldier(x, y) {
        const soldier = this.add.image(x, y, 'redSoldier');
        soldier.setScale(0.2);
        soldier.setDepth(5);
        soldier.setRotation(0);
        soldier.label = 'redBall';
        soldier.isPhysicsEnabled = false;

        // 添加武器
        const weapon = this.add.image(x - 8, y, 'weapon');
        weapon.setScale(0.2);
        weapon.setDepth(6);
        weapon.setOrigin(0.3, 0.7); // 设置旋转中心在底部 
        weapon.setRotation(0);
        

        // 添加血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(x - 12, y - 28, 24, 4); // 血条向上移动10像素
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000);
        healthBar.fillRect(x - 12, y - 28, 24, 4); // 血条向上移动10像素
        healthBar.setDepth(11);

        soldier.weapon = weapon;
        soldier.healthBarBg = healthBarBg;
        soldier.healthBar = healthBar;
        soldier.maxHealth = 5;
        soldier.health = 5;
        soldier.team = 'red';
        soldier.attackPower = 1;
        soldier.attackRange = 40;
        soldier.lastAttackTime = 0;
        soldier.attackCooldown = 1500;
        soldier.isAttacking = false;

        return soldier;
    }

    // 创建红方将领
    function createRedLeader(x, y) {
        const leader = this.add.image(x, y, 'redLeader');
        leader.setScale(0.5);
        leader.setDepth(5);
        leader.setRotation(0);
        leader.label = 'blackBall';
        leader.isPhysicsEnabled = false;

        // 添加武器
        const weapon = this.add.image(x + 25, y, 'weapon'); // 将武器向右移动23像素
        weapon.setScale(0.5);
        weapon.setDepth(6);
        weapon.setOrigin(0.3, 0.7); // 设置旋转中心在底部
        weapon.setRotation(0);

        // 添加血条
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x000000);
        healthBarBg.fillRect(x - 25, y - 45, 50, 8); // 血条向上移动15像素
        healthBarBg.setDepth(10);

        const healthBar = this.add.graphics();
        healthBar.fillStyle(0xff0000);
        healthBar.fillRect(x - 25, y - 45, 50, 8); // 血条向上移动15像素
        healthBar.setDepth(11);

        leader.weapon = weapon;
        leader.healthBarBg = healthBarBg;
        leader.healthBar = healthBar;
        leader.maxHealth = 20;
        leader.health = 20;
        leader.team = 'red';
        leader.attackPower = 3;
        leader.attackRange = 60;
        leader.lastAttackTime = 0;
        leader.attackCooldown = 1000;
        leader.isAttacking = false;

        return leader;
    }

    // 这个函数已经在createKingInCenter中实现，不需要重复

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0x3498db); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🧙‍♂️', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 25, '玩家10986', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '👹', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#FFFFFF',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 关卡显示
        this.levelText = this.add.text(375, 120, `第 ${currentLevel} 关`, {
            fontSize: '20px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 游戏状态文字
        this.statusText = this.add.text(375, 145, '摆放阶段 - 拖拽效果环形到圆圈上', {
            fontSize: '16px',
            fill: '#ffff00',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 设置输入控制
    function setupInput() {
        // 可以在这里添加其他输入控制
    }



    // 处理碰撞
    function handleCollision(event) {
        // 简化的碰撞处理，移除台球相关逻辑
        // 可以在这里添加其他碰撞逻辑
    }



    // 开始战斗模式
    function startBattleMode() {
        battleMode = true;

        // 更新状态提示
        this.statusText.setText('战斗开始！');
        this.statusText.setStyle({ fill: '#ff0000', fontSize: '18px' });

        // 直接开始战斗（不需要变身）
        startCombat.call(this);

        console.log('战斗模式开始！');
    }







    // 启动战斗
    function startCombat() {
        this.statusText.setText('战斗开始！');
        this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });

        // 确保战斗模式已启用
        battleMode = true;

        // 重置战斗管理器并设置游戏场景引用
        battleManager = new BattleManager();
        battleManager.gameScene = this; // 设置场景引用，用于动画

        // 注册所有战斗单位到战斗管理器
        try {
            // 蓝方阵营：国王 + 蓝方小兵
            if (king && king.health > 0) {
                battleManager.registerUnit(king, 'blue');
                king.isCombatReady = true;
                console.log('注册蓝方国王');
            }

            blueBalls.forEach(unit => {
                if (unit && unit.health > 0) {
                    battleManager.registerUnit(unit, 'blue');
                    unit.isCombatReady = true;
                    unit.team = 'blue'; // 确保团队设置正确
                }
            });
            console.log(`注册蓝方小兵: ${blueBalls.length}个`);

            // 红方阵营：红方将领 + 红方小兵
            blackBalls.forEach(unit => {
                if (unit && unit.health > 0) {
                    battleManager.registerUnit(unit, 'red'); // 红方将领
                    unit.isCombatReady = true;
                    unit.team = 'red'; // 确保团队设置正确
                }
            });
            console.log(`注册红方将领: ${blackBalls.length}个`);

            redBalls.forEach(unit => {
                if (unit && unit.health > 0) {
                    battleManager.registerUnit(unit, 'red');
                    unit.isCombatReady = true;
                    unit.team = 'red'; // 确保团队设置正确
                }
            });
            console.log(`注册红方小兵: ${redBalls.length}个`);

            const stats = battleManager.getBattleStats();
            console.log(`战斗启动！蓝方（国王+蓝兵）:${stats.blue}个单位, 红方（将领+红兵）:${stats.red}个单位`);
            console.log(`战斗模式状态: ${battleMode}`);

            // 检查单方是否没有单位，直接判定胜负
            if (stats.blue === 0 && stats.red === 0) {
                this.statusText.setText('双方都没有单位！平局');
                this.statusText.setStyle({ fill: '#ffff00', fontSize: '20px' });
                battleMode = false;
                return;
            } else if (stats.blue === 0) {
                this.statusText.setText('蓝方没有单位！红方获胜');
                this.statusText.setStyle({ fill: '#ff0000', fontSize: '20px' });
                battleMode = false;
                return;
            } else if (stats.red === 0) {
                this.statusText.setText('红方没有单位！蓝方获胜');
                this.statusText.setStyle({ fill: '#0000ff', fontSize: '20px' });
                battleMode = false;
                return;
            }

            // 双方都有单位，正常开始战斗
            console.log('双方都有单位，战斗管理器开始工作...');

        } catch (error) {
            console.warn('Error starting combat:', error);
        }
    }

    // 旧的攻击系统已被战斗管理器替代
    // 保留一些辅助函数供战斗管理器使用

    // 寻找最近的敌人
    function findNearestEnemy(attacker, enemies) {
        let nearestEnemy = null;
        let minDistance = Infinity;

        enemies.forEach(enemy => {
            // 确保敌人存在、活跃、有位置、有血量且没有正在死亡
            if (enemy && enemy.active && enemy.x !== undefined && enemy.health > 0 && !enemy.isDying) {
                const distance = Phaser.Math.Distance.Between(attacker.x, attacker.y, enemy.x, enemy.y);
                if (distance < minDistance) {
                    minDistance = distance;
                    nearestEnemy = enemy;
                }
            }
        });

        return nearestEnemy;
    }

    // 移动向敌人 - 不使用物理效果，直接修改位置
    function moveTowardsEnemy(unit, enemy) {
        if (!enemy || unit.isAttacking || !unit.isCombatReady) return;

        // 计算移动方向
        const dx = enemy.x - unit.x;
        const dy = enemy.y - unit.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            // 标准化方向向量
            const normalizedDx = dx / distance;
            const normalizedDy = dy / distance;

            // 直接修改位置，不使用物理速度
            const moveSpeed = 1.5; // 每帧移动的像素数
            const newX = unit.x + normalizedDx * moveSpeed;
            const newY = unit.y + normalizedDy * moveSpeed;

            // 确保不移出边界
            const boundedX = Math.max(70, Math.min(680, newX));
            const boundedY = Math.max(200, Math.min(1200, newY));

            unit.setPosition(boundedX, boundedY);
        }
    }

    // 攻击敌人
    function attackEnemy(attacker, target) {
        // 检查攻击者和目标的状态
        if (!attacker || !attacker.weapon || attacker.isAttacking || attacker.health <= 0 || attacker.isDying) return;
        if (!target || target.health <= 0 || target.isDying || !target.active) return;

        // 计算攻击方向
        const angle = Phaser.Math.Angle.Between(attacker.x, attacker.y, target.x, target.y);

        // 开始攻击动画
        attacker.isAttacking = true;

        // 武器攻击动画：更明显的摆动幅度
        const baseAngle = angle;
        const swingAngle1 = baseAngle + Phaser.Math.DegToRad(-30); // 反向预备动作
        const swingAngle2 = baseAngle + Phaser.Math.DegToRad(60);  // 更大的攻击角度

        // 人物抖动动画
        const originalX = attacker.x;
        const originalY = attacker.y;

        // 第一段动画：反向预备动作
        this.tweens.add({
            targets: attacker.weapon,
            rotation: swingAngle1,
            duration: 100,
            ease: 'Power2',
            onComplete: () => {
                // 第二段动画：快速攻击到60度 + 人物抖动
                this.tweens.add({
                    targets: attacker.weapon,
                    rotation: swingAngle2,
                    duration: 120,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        // 第三段动画：回到中间位置
                        this.tweens.add({
                            targets: attacker.weapon,
                            rotation: baseAngle + Phaser.Math.DegToRad(20),
                            duration: 100,
                            ease: 'Power2',
                            onComplete: () => {
                                // 最后回到初始角度
                                this.tweens.add({
                                    targets: attacker.weapon,
                                    rotation: 0,
                                    duration: 150,
                                    ease: 'Power1',
                                    onComplete: () => {
                                        attacker.isAttacking = false;
                                    }
                                });
                            }
                        });
                    }
                });

                // 人物抖动效果
                this.tweens.add({
                    targets: attacker,
                    x: originalX + Phaser.Math.Between(-3, 3),
                    y: originalY + Phaser.Math.Between(-3, 3),
                    duration: 50,
                    yoyo: true,
                    repeat: 2,
                    onComplete: () => {
                        // 恢复原位置
                        attacker.setPosition(originalX, originalY);
                    }
                });

                // 在快速攻击时造成伤害
                dealDamageToUnit.call(this, target, attacker.attackPower);
            }
        });
    }

    // 对单位造成伤害 - 简化版，主要用于特效
    function dealDamageToUnit(target, damage) {
        if (!target || target.health <= 0 || target.isDying) return;

        // 创建伤害数字特效
        try {
            const damageText = this.add.text(target.x, target.y - 30, `-${damage}`, {
                fontSize: '12px',
                fill: '#ff0000',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: damageText,
                y: target.y - 50,
                alpha: 0,
                duration: 800,
                onComplete: () => damageText.destroy()
            });
        } catch (error) {
            console.warn('Error creating damage effect:', error);
        }

        // 实际伤害处理由战斗管理器负责
        // 这里只负责视觉效果
    }

    // 销毁单位
    function destroyUnit(unit) {
        try {
            // 防止重复销毁
            if (!unit || unit.isDestroyed) return;
            unit.isDestroyed = true;

            // 销毁武器
            if (unit.weapon && unit.weapon.active) {
                unit.weapon.destroy();
                unit.weapon = null;
            }

            // 销毁血条
            if (unit.healthBar && unit.healthBar.active) {
                unit.healthBar.destroy();
                unit.healthBar = null;
            }
            if (unit.healthBarBg && unit.healthBarBg.active) {
                unit.healthBarBg.destroy();
                unit.healthBarBg = null;
            }

            // 从数组中移除
            if (unit === king) {
                king = null;
            } else {
                // 确保数组存在
                ensureArraysInitialized();

                const blueIndex = blueBalls.indexOf(unit);
                if (blueIndex > -1) {
                    blueBalls.splice(blueIndex, 1);
                    try {
                        console.log(`移除蓝色小兵，剩余: ${blueBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging blue unit removal');
                    }
                }

                const redIndex = redBalls.indexOf(unit);
                if (redIndex > -1) {
                    redBalls.splice(redIndex, 1);
                    try {
                        console.log(`移除红色小兵，剩余: ${redBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging red unit removal');
                    }
                }

                const blackIndex = blackBalls.indexOf(unit);
                if (blackIndex > -1) {
                    blackBalls.splice(blackIndex, 1);
                    try {
                        console.log(`移除红色将领，剩余: ${blackBalls.length}`);
                    } catch (e) {
                        console.warn('Error logging black unit removal');
                    }
                }
            }

            // 清理数组中的null值
            try {
                if (Array.isArray(blueBalls)) {
                    blueBalls = blueBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(redBalls)) {
                    redBalls = redBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
                if (Array.isArray(blackBalls)) {
                    blackBalls = blackBalls.filter(unit => unit !== null && unit !== undefined && !unit.isDestroyed);
                }
            } catch (e) {
                console.warn('Error filtering arrays:', e);
            }

            // 销毁单位
            if (unit && unit.active) {
                unit.destroy();
            }
        } catch (error) {
            console.warn('Error destroying unit:', error);
        }
    }

    // 检查战斗结果
    function checkBattleResult() {
        try {
            // 使用战斗管理器获取统计信息
            const stats = battleManager.getBattleStats();
            const blueCount = stats.blue;  // 蓝方：玩家（白球国王 + 蓝球小兵）
            const redCount = stats.red;    // 红方：电脑（黑球Boss + 红球小兵）

            console.log(`当前战况 - 玩家（蓝方）:${blueCount}, 电脑（红方）:${redCount}`);

            if (redCount === 0 && blueCount > 0) {
                // 玩家获胜
                battleMode = false;
                showLevelEndScreen.call(this, true); // 显示胜利界面
                console.log('战斗结束：玩家获胜！');
            } else if (blueCount === 0 && redCount > 0) {
                // 电脑获胜
                battleMode = false;
                showLevelEndScreen.call(this, false); // 显示失败界面
                console.log('战斗结束：电脑获胜！');
            } else if (blueCount === 0 && redCount === 0) {
                // 平局（重新开始）
                battleMode = false;
                showLevelEndScreen.call(this, false); // 平局算失败
                console.log('战斗结束：平局！');
            }
            // 如果双方都还有单位，继续战斗，不做任何处理
        } catch (error) {
            console.warn('Error in checkBattleResult:', error);
        }
    }

    // 显示关卡结束界面
    function showLevelEndScreen(isVictory) {
        // 创建黑色半透明背景
        const overlay = this.add.graphics();
        overlay.fillStyle(0x000000, 0.7);
        overlay.fillRect(0, 0, 750, 1334);
        overlay.setDepth(100);

        // 创建结果面板
        const panelWidth = 400;
        const panelHeight = 300;
        const panelX = 375;
        const panelY = 667;

        const panel = this.add.graphics();
        panel.fillStyle(0xffffff, 1);
        panel.fillRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.lineStyle(4, isVictory ? 0x00ff00 : 0xff0000, 1);
        panel.strokeRoundedRect(panelX - panelWidth/2, panelY - panelHeight/2, panelWidth, panelHeight, 20);
        panel.setDepth(101);

        // 大emoji表情
        const emoji = isVictory ? '😊' : '😢';
        const emojiText = this.add.text(panelX, panelY - 80, emoji, {
            fontSize: '80px',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 成功/失败文字
        const resultText = isVictory ? '关卡成功！' : '关卡失败！';
        const resultColor = isVictory ? '#00ff00' : '#ff0000';
        const textResult = this.add.text(panelX, panelY - 10, resultText, {
            fontSize: '32px',
            fill: resultColor,
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(102);

        // 下一关按钮
        const buttonText = isVictory ? '下一关' : '重新开始';
        const button = this.add.graphics();
        button.fillStyle(isVictory ? 0x00aa00 : 0xaa0000, 1);
        button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.lineStyle(2, 0x000000, 1);
        button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        button.setDepth(102);
        button.setInteractive(new Phaser.Geom.Rectangle(panelX - 80, panelY + 50, 160, 50), Phaser.Geom.Rectangle.Contains);

        const buttonTextObj = this.add.text(panelX, panelY + 75, buttonText, {
            fontSize: '24px',
            fill: '#ffffff',
            fontWeight: 'bold',
            fontFamily: 'Arial'
        }).setOrigin(0.5).setDepth(103);

        // 按钮点击事件
        button.on('pointerdown', () => {
            if (isVictory) {
                // 下一关：增加关卡数并重新开始
                currentLevel++;
                this.scene.restart();
            } else {
                // 重新开始当前关卡
                this.scene.restart();
            }
        });

        // 按钮悬停效果
        button.on('pointerover', () => {
            button.clear();
            button.fillStyle(isVictory ? 0x00cc00 : 0xcc0000, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        button.on('pointerout', () => {
            button.clear();
            button.fillStyle(isVictory ? 0x00aa00 : 0xaa0000, 1);
            button.fillRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
            button.lineStyle(2, 0x000000, 1);
            button.strokeRoundedRect(panelX - 80, panelY + 50, 160, 50, 10);
        });

        console.log(`显示关卡结束界面: ${isVictory ? '胜利' : '失败'}`);
    }

    // 更新武器和血条位置，让它们跟随角色移动
    function updateWeaponPositions() {

        // 更新国王的武器和血条位置
        if (king && king.active && king.weapon && king.weapon.active && king.x !== undefined) {
            try {
                king.weapon.setPosition(king.x - 15, king.y+20); // 法杖向右偏移15像素
                if (king.healthBarBg && king.healthBarBg.active) {
                    king.healthBarBg.clear();
                    king.healthBarBg.fillStyle(0x000000);
                    king.healthBarBg.fillRect(king.x - 25, king.y - 70, 50, 6); // 血条向上偏移50像素
                }
                if (king.healthBar && king.healthBar.active) {
                    king.healthBar.clear();
                    king.healthBar.fillStyle(0x00ff00);
                    const healthPercent = king.health / king.maxHealth;
                    king.healthBar.fillRect(king.x - 25, king.y - 70, 50 * healthPercent, 6); // 血条向上偏移50像素
                }
            } catch (error) {
                console.warn('Error updating king position:', error);
            }
        }


        // 更新蓝色小兵的武器和血条位置（右侧中间）
        blueBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.weapon && soldier.weapon.active && soldier.x !== undefined) {
                try {
                    soldier.weapon.setPosition(soldier.x + 8, soldier.y);
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 12, soldier.y - 28, 24, 4); // 更新血条位置
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0x00ff00);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 12, soldier.y - 28, 24 * healthPercent, 4); // 更新血条位置
                    }
                } catch (error) {
                    console.warn(`Error updating blue soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色小兵的武器和血条位置（左侧中间）
        redBalls.filter(soldier => soldier !== null && soldier !== undefined).forEach((soldier, index) => {
            if (soldier && soldier.active && soldier.weapon && soldier.weapon.active && soldier.x !== undefined) {
                try {
                    soldier.weapon.setPosition(soldier.x - 8, soldier.y);
                    if (soldier.healthBarBg && soldier.healthBarBg.active) {
                        soldier.healthBarBg.clear();
                        soldier.healthBarBg.fillStyle(0x000000);
                        soldier.healthBarBg.fillRect(soldier.x - 12, soldier.y - 28, 24, 4); // 更新血条位置
                    }
                    if (soldier.healthBar && soldier.healthBar.active) {
                        soldier.healthBar.clear();
                        soldier.healthBar.fillStyle(0xff0000);
                        const healthPercent = soldier.health / soldier.maxHealth;
                        soldier.healthBar.fillRect(soldier.x - 12, soldier.y - 28, 24 * healthPercent, 4); // 更新血条位置
                    }
                } catch (error) {
                    console.warn(`Error updating red soldier ${index} position:`, error);
                }
            }
        });

        // 更新红色将领的武器和血条位置（左侧中间）
        blackBalls.filter(leader => leader !== null && leader !== undefined).forEach((leader, index) => {
            if (leader && leader.active && leader.weapon && leader.weapon.active && leader.x !== undefined) {
                try {
                    leader.weapon.setPosition(leader.x - 58, leader.y);
                    if (leader.healthBarBg && leader.healthBarBg.active) {
                        leader.healthBarBg.clear();
                        leader.healthBarBg.fillStyle(0x000000);
                        leader.healthBarBg.fillRect(leader.x - 15, leader.y - 70, 30, 4);
                    }
                    if (leader.healthBar && leader.healthBar.active) {
                        leader.healthBar.clear();
                        leader.healthBar.fillStyle(0xff0000);
                        const healthPercent = leader.health / leader.maxHealth;
                        leader.healthBar.fillRect(leader.x - 15, leader.y - 70, 30 * healthPercent, 4);
                    }
                } catch (error) {
                    console.warn(`Error updating red leader ${index} position:`, error);
                }
            }
        });
    }

    // 处理待销毁的球 - 防止连锁反应bug
    function processBallDestruction() {
        try {
            // 确保待销毁数组存在
            if (!Array.isArray(ballsToDestroy)) ballsToDestroy = [];
            if (ballsToDestroy.length === 0) return;

            // 确保球数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];
            if (!Array.isArray(blackBalls)) blackBalls = [];

        // 每帧只处理一个球，避免同时销毁多个球
        const ballToDestroy = ballsToDestroy.shift();

        if (ballToDestroy && ballToDestroy.active) {
            // 从对应数组中移除
            if (ballToDestroy.label === 'blueBall') {
                const index = blueBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blueBalls.splice(index, 1);
                    console.log('销毁蓝球，剩余蓝球数量:', blueBalls.length);
                }
            } else if (ballToDestroy.label === 'redBall') {
                const index = redBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    redBalls.splice(index, 1);
                    console.log('销毁红球，剩余红球数量:', redBalls.length);
                }
            } else if (ballToDestroy.label === 'blackBall') {
                const index = blackBalls.indexOf(ballToDestroy);
                if (index > -1) {
                    blackBalls.splice(index, 1);
                    console.log('销毁黑球，剩余黑球数量:', blackBalls.length);
                }
            }

            // 如果球有武器，也要销毁武器
            if (ballToDestroy.weapon) {
                ballToDestroy.weapon.destroy();
            }

            // 创建销毁特效
            const destroyEffect = this.add.text(ballToDestroy.x, ballToDestroy.y, '撞击!', {
                fontSize: '14px',
                fill: '#ffff00',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            this.tweens.add({
                targets: destroyEffect,
                y: ballToDestroy.y - 40,
                alpha: 0,
                duration: 800,
                onComplete: () => destroyEffect.destroy()
            });

            // 销毁球
            ballToDestroy.destroy();
        }
        } catch (error) {
            console.warn('Error in processBallDestruction:', error);
        }
    }

    // 更新血条
    function updateHealthBars() {
        // 更新玩家血条
        this.playerHealthBar.clear();
        const playerHealthPercent = playerHealth / maxPlayerHealth;
        this.playerHealthBar.fillStyle(0x3498db);
        this.playerHealthBar.fillRoundedRect(100, 45, 150 * playerHealthPercent, 15, 7);

        // 更新敌方血条
        this.enemyHealthBar.clear();
        const enemyHealthPercent = enemyHealth / maxEnemyHealth;
        this.enemyHealthBar.fillStyle(0xe74c3c);
        this.enemyHealthBar.fillRoundedRect(480, 45, 150 * enemyHealthPercent, 15, 7);

        // 更新血条数值显示
        this.playerHealthText.setText(`${playerHealth}/${maxPlayerHealth}`);
        this.enemyHealthText.setText(`${enemyHealth}/${maxEnemyHealth}`);
    }

    // 检查游戏状态
    function checkGameState() {
        try {
            // 确保数组存在
            if (!Array.isArray(blueBalls)) blueBalls = [];
            if (!Array.isArray(redBalls)) redBalls = [];

            if (redBalls.length === 0 && blueBalls.length > 0) {
                this.statusText.setText('红球全部清除 - 蓝方获胜！');
                this.statusText.setStyle({ fill: '#0000ff', fontSize: '18px' });
            } else if (blueBalls.length === 0 && redBalls.length > 0) {
                this.statusText.setText('蓝球全部清除 - 红方获胜！');
                this.statusText.setStyle({ fill: '#ff0000', fontSize: '18px' });
            } else if (redBalls.length === 0 && blueBalls.length === 0) {
                this.statusText.setText('所有球都清除了 - 平局！');
                this.statusText.setStyle({ fill: '#ffff00', fontSize: '18px' });
            }
        } catch (error) {
            console.warn('Error in checkGameState:', error);
        }
    }









    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
