<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三消游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        #game-container {
            text-align: center;
            position: relative;
            width: 1280px;
            height: 720px;
        }
        #score {
            color: white;
            font-size: 32px;
            font-weight: bold;
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            transition: all 0.2s ease;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
        }
        canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="score">分数: 0</div>
        <div id="game"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        class Match3Game extends Phaser.Scene {
            constructor() {
                super({ key: 'Match3Game' });
                this.GRID_WIDTH = 16;  // 增加宽度适应横屏
                this.GRID_HEIGHT = 9;  // 减少高度适应横屏
                this.TILE_SIZE = 60;   // 稍微增大方块尺寸
                this.COLORS = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
                this.grid = [];
                this.selectedRow = null;
                this.score = 0;
                this.isProcessing = false;
                this.isDragging = false;
                this.dragStartX = 0;
                this.dragStartY = 0;
                this.dragThreshold = 30;
            }

            preload() {
                // 创建方块纹理
                this.createTileTextures();
            }

            create() {
                // 初始化网格
                this.initGrid();
                
                // 创建游戏板
                this.createBoard();
                
                // 添加输入处理
                this.input.on('gameobjectdown', this.onTileDown, this);
                this.input.on('gameobjectup', this.onTileUp, this);
                this.input.on('pointermove', this.onPointerMove, this);
                
                // 初始检查和填充
                this.fillEmptySpaces();
            }

            createTileTextures() {
                this.COLORS.forEach(color => {
                    const graphics = this.add.graphics();

                    // 创建主体颜色填充 - 更鲜艳的颜色
                    const mainColor = this.getColorValue(color, 1.0);
                    const lightColor = this.getColorValue(color, 1.4);
                    const darkColor = this.getColorValue(color, 0.6);

                    // 绘制主体方块 - 圆角矩形，更现代的外观
                    graphics.fillStyle(mainColor, 1.0);
                    graphics.fillRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 12);

                    // 添加渐变效果 - 顶部高光
                    graphics.fillStyle(lightColor, 0.7);
                    graphics.fillRoundedRect(3, 3, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 10);

                    // 添加底部阴影
                    graphics.fillStyle(darkColor, 0.5);
                    graphics.fillRoundedRect(3, this.TILE_SIZE / 2, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 10);

                    // 添加细微的边框
                    graphics.lineStyle(2, 0xffffff, 0.3);
                    graphics.strokeRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 12);

                    // 添加内部光泽效果
                    graphics.fillStyle(0xffffff, 0.4);
                    graphics.fillRoundedRect(6, 6, this.TILE_SIZE - 12, this.TILE_SIZE / 4, 8);

                    // 添加颜色标识符号或图案
                    this.addColorPattern(graphics, color);

                    graphics.generateTexture(color, this.TILE_SIZE, this.TILE_SIZE);
                    graphics.destroy();
                });
            }

            addColorPattern(graphics, color) {
                const centerX = this.TILE_SIZE / 2;
                const centerY = this.TILE_SIZE / 2;
                const size = 12;

                // 使用更明显的白色图案，增加对比度
                graphics.fillStyle(0xffffff, 0.9);
                graphics.lineStyle(3, 0xffffff, 0.9);

                switch(color) {
                    case 'red':
                        // 绘制心形 - 更大更明显
                        graphics.fillCircle(centerX - 6, centerY - 3, 6);
                        graphics.fillCircle(centerX + 6, centerY - 3, 6);
                        graphics.fillTriangle(centerX - 10, centerY + 3, centerX + 10, centerY + 3, centerX, centerY + 14);
                        break;
                    case 'blue':
                        // 绘制钻石 - 更大更清晰
                        graphics.fillTriangle(centerX, centerY - size, centerX - size, centerY, centerX, centerY + size);
                        graphics.fillTriangle(centerX, centerY - size, centerX + size, centerY, centerX, centerY + size);
                        break;
                    case 'green':
                        // 绘制四叶草
                        graphics.fillCircle(centerX - 4, centerY - 4, 5);
                        graphics.fillCircle(centerX + 4, centerY - 4, 5);
                        graphics.fillCircle(centerX - 4, centerY + 4, 5);
                        graphics.fillCircle(centerX + 4, centerY + 4, 5);
                        graphics.fillRect(centerX - 1, centerY - 1, 2, 8);
                        break;
                    case 'yellow':
                        // 绘制星星 - 更大更明显
                        const points = [];
                        for (let i = 0; i < 5; i++) {
                            const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
                            const outerRadius = size;
                            const innerRadius = size * 0.5;

                            points.push(centerX + Math.cos(angle) * outerRadius);
                            points.push(centerY + Math.sin(angle) * outerRadius);

                            const innerAngle = angle + Math.PI / 5;
                            points.push(centerX + Math.cos(innerAngle) * innerRadius);
                            points.push(centerY + Math.sin(innerAngle) * innerRadius);
                        }
                        graphics.fillPoints(points, true);
                        break;
                    case 'purple':
                        // 绘制花朵图案
                        for (let i = 0; i < 6; i++) {
                            const angle = (i * Math.PI * 2) / 6;
                            const x = centerX + Math.cos(angle) * 6;
                            const y = centerY + Math.sin(angle) * 6;
                            graphics.fillCircle(x, y, 4);
                        }
                        graphics.fillCircle(centerX, centerY, 4);
                        break;
                    case 'orange':
                        // 绘制太阳图案
                        graphics.fillCircle(centerX, centerY, 8);
                        for (let i = 0; i < 8; i++) {
                            const angle = (i * Math.PI * 2) / 8;
                            const x1 = centerX + Math.cos(angle) * 10;
                            const y1 = centerY + Math.sin(angle) * 10;
                            const x2 = centerX + Math.cos(angle) * 16;
                            const y2 = centerY + Math.sin(angle) * 16;
                            graphics.lineStyle(3, 0xffffff, 0.9);
                            graphics.lineBetween(x1, y1, x2, y2);
                        }
                        break;
                }
            }

            getColorValue(colorName, brightness = 1) {
                const colors = {
                    red: 0xff4757,      // 现代红色
                    blue: 0x5352ed,     // 现代蓝色
                    green: 0x2ed573,    // 现代绿色
                    yellow: 0xffa502,   // 现代黄色
                    purple: 0xa55eea,   // 现代紫色
                    orange: 0xff6348    // 现代橙色
                };

                let color = colors[colorName] || 0xffffff;
                if (brightness !== 1) {
                    const r = Math.min(255, Math.max(0, Math.floor(((color >> 16) & 0xff) * brightness)));
                    const g = Math.min(255, Math.max(0, Math.floor(((color >> 8) & 0xff) * brightness)));
                    const b = Math.min(255, Math.max(0, Math.floor((color & 0xff) * brightness)));
                    color = (r << 16) | (g << 8) | b;
                }
                return color;
            }

            initGrid() {
                this.grid = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        this.grid[row][col] = null;
                    }
                }
            }

            createBoard() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;
                
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        
                        const color = this.getRandomColor();
                        const tile = this.add.image(x, y, color);
                        tile.setInteractive();
                        tile.setData('row', row);
                        tile.setData('col', col);
                        tile.setData('color', color);
                        
                        this.grid[row][col] = tile;
                    }
                }
                
                // 确保初始状态没有匹配
                this.removeInitialMatches();
            }

            getRandomColor() {
                return this.COLORS[Math.floor(Math.random() * this.COLORS.length)];
            }

            removeInitialMatches() {
                let hasMatches = true;
                while (hasMatches) {
                    hasMatches = false;
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        for (let col = 0; col < this.GRID_WIDTH; col++) {
                            if (this.hasMatchAt(row, col)) {
                                const newColor = this.getRandomColor();
                                this.grid[row][col].setTexture(newColor);
                                this.grid[row][col].setData('color', newColor);
                                hasMatches = true;
                            }
                        }
                    }
                }
            }

            onTileDown(pointer, tile) {
                if (this.isProcessing) return;

                this.isDragging = true;
                this.dragStartX = pointer.x;
                this.dragStartY = pointer.y;
                this.dragStartTile = tile;
            }

            onTileUp(pointer, tile) {
                if (!this.isDragging || this.isProcessing) return;

                const deltaX = pointer.x - this.dragStartX;
                const deltaY = pointer.y - this.dragStartY;
                const absDeltaX = Math.abs(deltaX);
                const absDeltaY = Math.abs(deltaY);

                // 检查是否达到拖拽阈值
                if (absDeltaX > this.dragThreshold || absDeltaY > this.dragThreshold) {
                    const row = this.dragStartTile.getData('row');
                    const col = this.dragStartTile.getData('col');

                    // 判断拖拽方向
                    if (absDeltaX > absDeltaY) {
                        // 水平拖拽 - 移动整行
                        if (deltaX > 0) {
                            // 向右拖拽 - 行向右移动
                            this.moveRow(row, 'right');
                        } else {
                            // 向左拖拽 - 行向左移动
                            this.moveRow(row, 'left');
                        }
                    } else {
                        // 垂直拖拽 - 移动整列
                        if (deltaY > 0) {
                            // 向下拖拽 - 列向下移动
                            this.moveColumn(col, 'down');
                        } else {
                            // 向上拖拽 - 列向上移动
                            this.moveColumn(col, 'up');
                        }
                    }
                }

                this.isDragging = false;
                this.dragStartTile = null;
            }

            onPointerMove(pointer) {
                // 可以在这里添加拖拽预览效果
            }



            moveRow(row, direction) {
                if (this.isProcessing) return;
                this.isProcessing = true;

                // 保存原始行数据
                const originalRowData = [];
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        originalRowData[col] = {
                            color: tile.getData('color'),
                            tile: tile
                        };
                    }
                }

                // 计算新的颜色排列
                const newColors = [];
                if (direction === 'right') {
                    // 向右移动：最后一个移到第一个，其他向右移一位
                    newColors[0] = originalRowData[this.GRID_WIDTH - 1].color;
                    for (let col = 1; col < this.GRID_WIDTH; col++) {
                        newColors[col] = originalRowData[col - 1].color;
                    }
                } else if (direction === 'left') {
                    // 向左移动：第一个移到最后一个，其他向左移一位
                    for (let col = 0; col < this.GRID_WIDTH - 1; col++) {
                        newColors[col] = originalRowData[col + 1].color;
                    }
                    newColors[this.GRID_WIDTH - 1] = originalRowData[0].color;
                }

                // 应用新颜色并播放动画
                this.animateRowMove(row, newColors, direction);
            }

            moveColumn(col, direction) {
                if (this.isProcessing) return;
                this.isProcessing = true;

                // 保存原始列数据
                const originalColData = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        originalColData[row] = {
                            color: tile.getData('color'),
                            tile: tile
                        };
                    }
                }

                // 计算新的颜色排列
                const newColors = [];
                if (direction === 'down') {
                    // 向下移动：最后一个移到第一个，其他向下移一位
                    newColors[0] = originalColData[this.GRID_HEIGHT - 1].color;
                    for (let row = 1; row < this.GRID_HEIGHT; row++) {
                        newColors[row] = originalColData[row - 1].color;
                    }
                } else if (direction === 'up') {
                    // 向上移动：第一个移到最后一个，其他向上移一位
                    for (let row = 0; row < this.GRID_HEIGHT - 1; row++) {
                        newColors[row] = originalColData[row + 1].color;
                    }
                    newColors[this.GRID_HEIGHT - 1] = originalColData[0].color;
                }

                // 应用新颜色并播放动画
                this.animateColumnMove(col, newColors, direction);
            }

            animateRowMove(row, newColors, direction) {
                const moveDuration = 250;
                let completedAnimations = 0;
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;

                // 先更新所有方块的颜色数据
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        tile.setTexture(newColors[col]);
                        tile.setData('color', newColors[col]);
                    }
                }

                // 然后播放移动动画（仅视觉效果）
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        let targetX;

                        if (direction === 'right') {
                            // 向右移动动画
                            targetX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2 + this.TILE_SIZE;
                        } else if (direction === 'left') {
                            // 向左移动动画
                            targetX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2 - this.TILE_SIZE;
                        }

                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;

                        // 移动动画
                        this.tweens.add({
                            targets: tile,
                            x: targetX,
                            duration: moveDuration / 2,
                            ease: 'Power2',
                            onComplete: () => {
                                // 回到正确位置
                                this.tweens.add({
                                    targets: tile,
                                    x: originalX,
                                    duration: moveDuration / 2,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        completedAnimations++;
                                        if (completedAnimations === this.GRID_WIDTH) {
                                            // 所有动画完成后再检查匹配
                                            this.time.delayedCall(100, () => {
                                                this.checkForMatches();
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }

            animateColumnMove(col, newColors, direction) {
                const moveDuration = 250;
                let completedAnimations = 0;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;

                // 先更新所有方块的颜色数据
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        tile.setTexture(newColors[row]);
                        tile.setData('color', newColors[row]);
                    }
                }

                // 然后播放移动动画（仅视觉效果）
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        let targetY;

                        if (direction === 'down') {
                            // 向下移动动画
                            targetY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2 + this.TILE_SIZE;
                        } else if (direction === 'up') {
                            // 向上移动动画
                            targetY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2 - this.TILE_SIZE;
                        }

                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;

                        // 移动动画
                        this.tweens.add({
                            targets: tile,
                            y: targetY,
                            duration: moveDuration / 2,
                            ease: 'Power2',
                            onComplete: () => {
                                // 回到正确位置
                                this.tweens.add({
                                    targets: tile,
                                    y: originalY,
                                    duration: moveDuration / 2,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        completedAnimations++;
                                        if (completedAnimations === this.GRID_HEIGHT) {
                                            // 所有动画完成后再检查匹配
                                            this.time.delayedCall(100, () => {
                                                this.checkForMatches();
                                            });
                                        }
                                    }
                                });
                            }
                        });
                    }
                }
            }

            checkForMatches() {
                // 检查是否有匹配
                const matches = this.findAllMatches();
                if (matches.length > 0) {
                    // 有匹配，开始处理匹配
                    this.time.delayedCall(100, () => {
                        this.processMatches();
                    });
                } else {
                    // 没有匹配，结束处理
                    this.isProcessing = false;
                }
            }



            hasMatchAt(row, col) {
                if (!this.grid[row] || !this.grid[row][col]) return false;

                const color = this.grid[row][col].getData('color');

                // 检查水平匹配
                let horizontalCount = 1;
                // 向左检查
                for (let c = col - 1; c >= 0; c--) {
                    if (this.grid[row][c] && this.grid[row][c].getData('color') === color) {
                        horizontalCount++;
                    } else {
                        break;
                    }
                }
                // 向右检查
                for (let c = col + 1; c < this.GRID_WIDTH; c++) {
                    if (this.grid[row][c] && this.grid[row][c].getData('color') === color) {
                        horizontalCount++;
                    } else {
                        break;
                    }
                }

                // 检查垂直匹配
                let verticalCount = 1;
                // 向上检查
                for (let r = row - 1; r >= 0; r--) {
                    if (this.grid[r][col] && this.grid[r][col].getData('color') === color) {
                        verticalCount++;
                    } else {
                        break;
                    }
                }
                // 向下检查
                for (let r = row + 1; r < this.GRID_HEIGHT; r++) {
                    if (this.grid[r][col] && this.grid[r][col].getData('color') === color) {
                        verticalCount++;
                    } else {
                        break;
                    }
                }

                return horizontalCount >= 3 || verticalCount >= 3;
            }

            processMatches() {
                this.isProcessing = true;
                const matches = this.findAllMatches();

                if (matches.length > 0) {
                    this.removeMatches(matches);
                    this.updateScore(matches.length);

                    // 计算动画总时长（考虑延迟播放的波浪效果）
                    const totalAnimationTime = 250 + (matches.length * 50);

                    // 延迟后处理下落和填充
                    this.time.delayedCall(totalAnimationTime, () => {
                        this.dropTiles();
                        this.time.delayedCall(500, () => {
                            this.fillEmptySpaces();
                            this.time.delayedCall(300, () => {
                                // 检查是否有新的匹配
                                const newMatches = this.findAllMatches();
                                if (newMatches.length > 0) {
                                    // 连锁消除提示
                                    this.showComboEffect(newMatches.length);
                                    this.processMatches();
                                } else {
                                    this.isProcessing = false;
                                }
                            });
                        });
                    });
                } else {
                    this.isProcessing = false;
                }
            }

            findAllMatches() {
                const matches = [];
                const matchedPositions = new Set();

                // 检查水平匹配
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    let count = 1;
                    let currentColor = null;
                    let matchStart = 0;

                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const tile = this.grid[row][col];
                        if (tile && tile.active) {
                            const color = tile.getData('color');
                            if (color === currentColor) {
                                count++;
                            } else {
                                if (count >= 3) {
                                    for (let c = matchStart; c < col; c++) {
                                        matchedPositions.add(`${row}-${c}`);
                                    }
                                }
                                currentColor = color;
                                count = 1;
                                matchStart = col;
                            }
                        } else {
                            if (count >= 3) {
                                for (let c = matchStart; c < col; c++) {
                                    matchedPositions.add(`${row}-${c}`);
                                }
                            }
                            currentColor = null;
                            count = 1;
                        }
                    }
                    if (count >= 3) {
                        for (let c = matchStart; c < this.GRID_WIDTH; c++) {
                            matchedPositions.add(`${row}-${c}`);
                        }
                    }
                }

                // 检查垂直匹配
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let count = 1;
                    let currentColor = null;
                    let matchStart = 0;

                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        const tile = this.grid[row][col];
                        if (tile && tile.active) {
                            const color = tile.getData('color');
                            if (color === currentColor) {
                                count++;
                            } else {
                                if (count >= 3) {
                                    for (let r = matchStart; r < row; r++) {
                                        matchedPositions.add(`${r}-${col}`);
                                    }
                                }
                                currentColor = color;
                                count = 1;
                                matchStart = row;
                            }
                        } else {
                            if (count >= 3) {
                                for (let r = matchStart; r < row; r++) {
                                    matchedPositions.add(`${r}-${col}`);
                                }
                            }
                            currentColor = null;
                            count = 1;
                        }
                    }
                    if (count >= 3) {
                        for (let r = matchStart; r < this.GRID_HEIGHT; r++) {
                            matchedPositions.add(`${r}-${col}`);
                        }
                    }
                }

                // 转换为位置数组
                matchedPositions.forEach(pos => {
                    const [row, col] = pos.split('-').map(Number);
                    matches.push({ row, col });
                });

                return matches;
            }



            removeMatches(matches) {
                // 按顺序播放消除动画，创建连锁效果
                matches.forEach((pos, index) => {
                    const tile = this.grid[pos.row][pos.col];
                    if (tile && tile.active) {
                        // 延迟播放动画，创建波浪效果
                        this.time.delayedCall(index * 50, () => {
                            this.playDestroyAnimation(tile, pos);
                        });
                    }
                });
            }

            playDestroyAnimation(tile, pos) {
                if (!tile || !tile.active) return;

                // 创建爆炸粒子效果
                this.createExplosionEffect(tile.x, tile.y, tile.getData('color'));

                // 主要消除动画 - 旋转缩放消失
                this.tweens.add({
                    targets: tile,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    rotation: Math.PI * 2,
                    alpha: 0.8,
                    duration: 150,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段 - 快速缩小消失
                        this.tweens.add({
                            targets: tile,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 100,
                            ease: 'Power3',
                            onComplete: () => {
                                if (tile && tile.active) {
                                    tile.destroy();
                                }
                                this.grid[pos.row][pos.col] = null;
                            }
                        });
                    }
                });

                // 添加闪光效果
                this.tweens.add({
                    targets: tile,
                    tint: 0xffffff,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });
            }

            createExplosionEffect(x, y, color) {
                const particleCount = 8;
                const baseColor = this.getColorValue(color, 1.0);

                for (let i = 0; i < particleCount; i++) {
                    // 创建粒子
                    const particle = this.add.graphics();
                    particle.fillStyle(baseColor, 0.8);
                    particle.fillCircle(0, 0, 3);
                    particle.x = x;
                    particle.y = y;

                    // 随机方向和速度
                    const angle = (Math.PI * 2 * i) / particleCount + (Math.random() - 0.5) * 0.5;
                    const speed = 50 + Math.random() * 30;
                    const targetX = x + Math.cos(angle) * speed;
                    const targetY = y + Math.sin(angle) * speed;

                    // 粒子飞散动画
                    this.tweens.add({
                        targets: particle,
                        x: targetX,
                        y: targetY,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300 + Math.random() * 200,
                        ease: 'Power2',
                        onComplete: () => {
                            particle.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(0, 0, 5);
                ring.x = x;
                ring.y = y;

                // 光环扩散动画
                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });
            }

            dropTiles() {
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let writeRow = this.GRID_HEIGHT - 1;

                    for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                        if (this.grid[row][col] !== null) {
                            if (row !== writeRow) {
                                // 移动方块
                                this.grid[writeRow][col] = this.grid[row][col];
                                this.grid[row][col] = null;

                                // 更新方块数据
                                this.grid[writeRow][col].setData('row', writeRow);

                                // 添加下落动画
                                const boardStartY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;
                                const startY = boardStartY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                                const endY = boardStartY + writeRow * this.TILE_SIZE + this.TILE_SIZE / 2;

                                this.tweens.add({
                                    targets: this.grid[writeRow][col],
                                    y: endY,
                                    duration: 300,
                                    ease: 'Bounce.easeOut'
                                });
                            }
                            writeRow--;
                        }
                    }
                }
            }

            fillEmptySpaces() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2;

                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                        if (this.grid[row][col] === null) {
                            const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                            const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;

                            const color = this.getRandomColor();
                            const tile = this.add.image(x, y - this.TILE_SIZE * 2, color);
                            tile.setInteractive();
                            tile.setData('row', row);
                            tile.setData('col', col);
                            tile.setData('color', color);

                            this.grid[row][col] = tile;

                            // 添加下落动画
                            this.tweens.add({
                                targets: tile,
                                y: y,
                                duration: 400,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }
                }
            }

            showComboEffect(matchCount) {
                // 创建连锁文字效果
                const comboText = this.add.text(
                    this.sys.game.config.width / 2,
                    this.sys.game.config.height / 2,
                    `COMBO x${matchCount}!`,
                    {
                        fontSize: '32px',
                        fontFamily: 'Arial',
                        color: '#ffff00',
                        stroke: '#ff0000',
                        strokeThickness: 4,
                        shadow: {
                            offsetX: 2,
                            offsetY: 2,
                            color: '#000000',
                            blur: 4,
                            fill: true
                        }
                    }
                );
                comboText.setOrigin(0.5);
                comboText.setScale(0);

                // 连锁文字动画
                this.tweens.add({
                    targets: comboText,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    duration: 200,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        this.tweens.add({
                            targets: comboText,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            y: comboText.y - 50,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                comboText.destroy();
                            }
                        });
                    }
                });

                // 屏幕震动效果
                this.cameras.main.shake(200, 0.01);
            }

            updateScore(matchCount) {
                this.score += matchCount * 10;
                document.getElementById('score').textContent = `分数: ${this.score}`;

                // 分数增加动画
                const scoreElement = document.getElementById('score');
                scoreElement.style.transform = 'scale(1.2)';
                scoreElement.style.color = '#ffff00';

                setTimeout(() => {
                    scoreElement.style.transform = 'scale(1)';
                    scoreElement.style.color = 'white';
                }, 200);
            }
        }

        // 游戏配置 - 横屏1280x720
        const config = {
            type: Phaser.AUTO,
            width: 1280,
            height: 720,
            parent: 'game',
            backgroundColor: '#2c3e50',
            scene: Match3Game,
            scale: {
                mode: Phaser.Scale.NONE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        const game = new Phaser.Game(config);
    </script>
</body>
</html>
