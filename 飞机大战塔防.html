<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Emoji小兵塔防</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#24c061',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 全局游戏变量
    let game;
    let gameScene;
    let gridSize = 6;
    let cellSize = 80;
    let playerGrid = [];
    let enemyGrid = [];
    let playerUnits = [];
    let enemyUnits = [];
    let gameStarted = false;
    let turnTimer = 0;
    let maxTurnTime = 3000; // 3秒一回合
    let playerHealth = 100;
    let enemyHealth = 100;
    let selectedCell = null;
    let draggedUnit = null;
    let dragStartPos = null;
    let isDragging = false;
    let flyingUnits = []; // 正在飞行的单位

    // 游戏状态
    let gamePhase = 'preparation'; // 'preparation', 'battle'
    let waveNumber = 1;
    let enemySpawnTimer = 0;
    let enemySpawnInterval = 2000; // 2秒生成一个敌人
    let enemiesInWave = 5;
    let enemiesSpawned = 0;

    // 建筑选择区域
    let buildingSlots = [];
    let availableBuildings = [];

    // 经济系统
    let playerMoney = 500; // 初始资金
    let moneyText;

    // 自由飞行的敌机
    let freeEnemies = [];

    // 我方起飞的战斗机
    let playerFighters = [];

    // 单位类型 - 所有飞机都是战斗机
    const UNIT_TYPES = {
        FIGHTER: { emoji: '✈️', health: 30, attack: 15, range: 2, cost: 50, level: 1, attackType: 'fighter' },
        ENEMY_FIGHTER: { emoji: '🛩️', health: 30, attack: 15, range: 2, cost: 50, level: 1, attackType: 'fighter' },
        MISSILE: { emoji: '🚎', health: 20, attack: 30, range: 3, cost: 60, level: 1, attackType: 'area', areaSize: 1, needsTarget: true },
        ANTIAIR: { emoji: '🎡', health: 40, attack: 8, range: 2, cost: 40, level: 1, attackType: 'continuous' },
        BASE: { emoji: '🏭', health: 100, attack: 0, range: 0, cost: 0, level: 1, attackType: 'none' }
    };

    // Phaser场景函数
    function preload() {
        gameScene = this;
        // 创建简单的颜色纹理
        this.add.graphics()
            .fillStyle(0x4a90e2)
            .fillRect(0, 0, 1, 1)
            .generateTexture('blue', 1, 1);

        this.add.graphics()
            .fillStyle(0xe74c3c)
            .fillRect(0, 0, 1, 1)
            .generateTexture('red', 1, 1);

        this.add.graphics()
            .fillStyle(0x2ecc71)
            .fillRect(0, 0, 1, 1)
            .generateTexture('green', 1, 1);
    }

    function create() {
        // 初始化游戏
        initializeGame();
        createUI();
        startGame();
    }

    function update() {
        if (!gameStarted) return;

        if (gamePhase === 'battle') {
            // 更新回合计时器
            turnTimer += gameScene.game.loop.delta;
            if (turnTimer >= maxTurnTime) {
                processTurn();
                turnTimer = 0;
            }

            // 更新敌人生成计时器
            enemySpawnTimer += gameScene.game.loop.delta;
            if (enemySpawnTimer >= enemySpawnInterval && enemiesSpawned < enemiesInWave) {
                spawnEnemy();
                enemySpawnTimer = 0;
            }

            // 更新自由飞行的敌机
            updateFreeEnemies();

            // 更新我方战斗机
            updatePlayerFighters();

            // 检查波次是否结束
            checkWaveComplete();
        }
    }

    // 初始化游戏
    function initializeGame() {
        // 初始化网格
        for (let i = 0; i < gridSize; i++) {
            playerGrid[i] = [];
            enemyGrid[i] = []; // 保留敌方网格以避免错误
            for (let j = 0; j < gridSize; j++) {
                playerGrid[i][j] = null;
                enemyGrid[i][j] = null; // 但保持为空
            }
        }

        // 只创建玩家基地
        playerGrid[5][2] = createUnit(UNIT_TYPES.BASE, true);

        // 初始化建筑选择区域
        initializeBuildingSlots();

        // 设置游戏为准备阶段
        gamePhase = 'preparation';

        // 初始化自由敌机数组
        freeEnemies = [];
    }

    // 添加全局变量来跟踪持续攻击
    let continuousAttacks = [];

    // 创建单位
    function createUnit(type, isPlayer) {
        return {
            type: type,
            health: type.health,
            maxHealth: type.health,
            attack: type.attack,
            range: type.range,
            isPlayer: isPlayer,
            cooldown: 0,
            level: type.level || 1,
            attackType: type.attackType || 'normal',
            areaSize: type.areaSize || 0,
            isFlying: false
        };
    }

    // 初始化建筑选择区域
    function initializeBuildingSlots() {
        // 创建4个可选建筑
        availableBuildings = [
            { type: UNIT_TYPES.FIGHTER, x: 100, y: 1200 },
            { type: UNIT_TYPES.ANTIAIR, x: 220, y: 1200 },
            { type: UNIT_TYPES.MISSILE, x: 340, y: 1200 },
            { type: UNIT_TYPES.BASE, x: 460, y: 1200 }
        ];
    }


    // 创建UI
    function createUI() {
        const centerX = config.width / 2;

        // 标题
        gameScene.add.text(centerX, 50, '飞机大战塔防', {
            fontSize: '32px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 游戏状态显示
        gameScene.phaseText = gameScene.add.text(50, 100, '准备阶段 - 拖拽建筑到格子上', {
            fontSize: '18px',
            fill: '#00ff00'
        });

        gameScene.waveText = gameScene.add.text(50, 130, '第1波', {
            fontSize: '16px',
            fill: '#ffff00'
        });

        // 资金显示
        moneyText = gameScene.add.text(50, 160, '资金: $' + playerMoney, {
            fontSize: '18px',
            fill: '#ffff00'
        });

        // 我方基地标识
        gameScene.add.text(50, 1000, '我方基地', {
            fontSize: '20px',
            fill: '#4444ff'
        });

        // 建筑商店标识
        gameScene.add.text(50, 1160, '建筑商店', {
            fontSize: '18px',
            fill: '#ffff00'
        });

        // 绘制游戏网格
        drawGrid();

        // 创建建筑选择区域
        createBuildingSelection();

        // 创建开始战斗按钮
        createStartButton();

        // 添加拖拽和点击事件
        gameScene.input.on('pointerdown', handlePointerDown);
        gameScene.input.on('pointermove', handlePointerMove);
        gameScene.input.on('pointerup', handlePointerUp);
    }

    // 绘制网格
    function drawGrid() {
        const startX = (config.width - gridSize * cellSize) / 2;
        const enemyStartY = 150;
        const playerStartY = 600; // 上移200像素
        const gridWidth = gridSize * cellSize;
        const gridHeight = gridSize * cellSize;

        // 敌方区域现在是自由飞行区域，不需要网格背景

        // 绘制玩家网格大背景
        const playerBg = gameScene.add.graphics();
        playerBg.fillStyle(0x3a3a3a, 0.8);
        playerBg.fillRoundedRect(startX - 10, playerStartY - 10, gridWidth + 20, gridHeight + 20, 15);
        playerBg.lineStyle(3, 0x888888, 0.8);
        playerBg.strokeRoundedRect(startX - 10, playerStartY - 10, gridWidth + 20, gridHeight + 20, 15);

        // 敌方区域现在是自由飞行区域，不需要网格

        // 绘制玩家网格
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const x = startX + j * cellSize;
                const y = playerStartY + i * cellSize;

                // 创建圆角网格背景
                const cellBg = gameScene.add.graphics();
                cellBg.fillStyle(0x444444, 0.9);
                cellBg.fillRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);
                cellBg.lineStyle(2, 0x888888, 0.6);
                cellBg.strokeRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);

                // 创建透明的交互区域
                const cell = gameScene.add.rectangle(x + cellSize/2, y + cellSize/2, cellSize, cellSize, 0x000000, 0)
                    .setInteractive()
                    .setData('gridType', 'player')
                    .setData('row', i)
                    .setData('col', j);
            }
        }

        // 绘制单位
        updateDisplay();
    }

    // 创建建筑选择区域
    function createBuildingSelection() {
        const startX = 50;
        const startY = 1200; // 商店区域独立位置
        const slotSize = 80;
        const spacing = 120;

        // 创建建筑选择背景
        gameScene.buildingSelectionBg = gameScene.add.graphics();
        gameScene.buildingSelectionBg.fillStyle(0x2a2a2a, 0.8);
        gameScene.buildingSelectionBg.fillRoundedRect(30, 1180, 650, 100, 10); // 商店独立区域
        gameScene.buildingSelectionBg.lineStyle(2, 0x666666, 0.8);
        gameScene.buildingSelectionBg.strokeRoundedRect(30, 1180, 650, 100, 10);

        // 存储建筑UI元素以便隐藏
        gameScene.buildingElements = [];

        // 创建4个建筑选择槽
        for (let i = 0; i < 4; i++) {
            const x = startX + i * spacing;
            const y = startY;

            // 建筑槽背景
            const slotBg = gameScene.add.graphics();
            slotBg.fillStyle(0x444444, 0.9);
            slotBg.fillRoundedRect(x, y, slotSize, slotSize, 8);
            slotBg.lineStyle(2, 0x888888, 0.6);
            slotBg.strokeRoundedRect(x, y, slotSize, slotSize, 8);

            // 建筑图标
            const building = availableBuildings[i];
            const canAfford = playerMoney >= building.type.cost;
            const buildingIcon = gameScene.add.text(x + slotSize/2, y + slotSize/2, building.type.emoji, {
                fontSize: '40px'
            }).setOrigin(0.5)
              .setInteractive({ draggable: canAfford })
              .setData('buildingType', building.type)
              .setData('isBuilding', true);

            // 如果买不起，设置灰色
            if (!canAfford) {
                buildingIcon.setTint(0x666666);
            }

            // 建筑名称
            const buildingName = getBuildingName(building.type);
            const nameText = gameScene.add.text(x + slotSize/2, y + slotSize + 15, buildingName, {
                fontSize: '12px',
                fill: canAfford ? '#ffffff' : '#666666'
            }).setOrigin(0.5);

            // 价格显示
            const priceText = gameScene.add.text(x + slotSize/2, y + slotSize + 30, '$' + building.type.cost, {
                fontSize: '14px',
                fill: canAfford ? '#ffff00' : '#ff6666',
                fontWeight: 'bold'
            }).setOrigin(0.5);

            // 存储UI元素
            gameScene.buildingElements.push(slotBg, buildingIcon, nameText, priceText);

            // 拖拽事件
            if (canAfford) {
                buildingIcon.on('dragstart', (pointer, dragX, dragY) => {
                    buildingIcon.setTint(0x888888);
                });

                buildingIcon.on('drag', (pointer, dragX, dragY) => {
                    buildingIcon.x = dragX;
                    buildingIcon.y = dragY;
                });

                buildingIcon.on('dragend', (pointer) => {
                    buildingIcon.clearTint();
                    // 检查是否拖拽到了游戏网格上
                    handleBuildingDrop(buildingIcon, pointer);
                });
            }
        }
    }

    // 获取建筑名称
    function getBuildingName(buildingType) {
        if (buildingType === UNIT_TYPES.FIGHTER) return '战斗机';
        if (buildingType === UNIT_TYPES.ANTIAIR) return '防空炮';
        if (buildingType === UNIT_TYPES.MISSILE) return '导弹';
        if (buildingType === UNIT_TYPES.BASE) return '基地';
        return '未知';
    }

    // 创建开始战斗按钮
    function createStartButton() {
        gameScene.startButtonBg = gameScene.add.graphics();
        gameScene.startButtonBg.fillStyle(0x00aa00, 0.8);
        gameScene.startButtonBg.fillRoundedRect(300, 1100, 150, 60, 15); // 居中位置，在格子和商店之间
        gameScene.startButtonBg.lineStyle(3, 0x00ff00, 1);
        gameScene.startButtonBg.strokeRoundedRect(300, 1100, 150, 60, 15);
        gameScene.startButtonBg.setDepth(1000); // 设置高层级

        gameScene.startButtonText = gameScene.add.text(375, 1130, '开始战斗', {
            fontSize: '20px',
            fill: '#ffffff',
            fontWeight: 'bold'
        }).setOrigin(0.5).setDepth(1001); // 设置高层级

        // 创建交互区域
        gameScene.startButton = gameScene.add.rectangle(375, 1130, 150, 60, 0x000000, 0)
            .setInteractive()
            .setDepth(1002) // 设置最高层级
            .on('pointerdown', startBattle);
    }

    // 更新显示
    function updateDisplay() {
        // 清除之前的单位显示
        if (gameScene.unitTexts) {
            gameScene.unitTexts.forEach(text => text.destroy());
        }
        gameScene.unitTexts = [];

        const startX = (config.width - gridSize * cellSize) / 2;
        const enemyStartY = 150;
        const playerStartY = 600; // 修正为正确的网格位置

        // 敌方单位现在是自由飞行的，不在网格中显示
        // 它们由 updateFreeEnemies() 函数管理显示

        // 显示玩家单位
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const unit = playerGrid[i][j];
                if (unit) {
                    const x = startX + j * cellSize + cellSize / 2;
                    const y = playerStartY + i * cellSize + cellSize / 2;

                    const unitText = gameScene.add.text(x, y, unit.type.emoji, {
                        fontSize: '40px'
                    }).setOrigin(0.5);

                    // 如果是战斗机，设置默认朝向（向右倾斜45度）
                    if (unit.type.attackType === 'fighter') {
                        unitText.setRotation(Math.PI / 4); // 45度
                    }

                    // 为飞机类型单位显示血条
                    if (unit.type.attackType === 'fly') {
                        const healthBarBg = gameScene.add.rectangle(x, y - 35, 40, 6, 0x333333).setOrigin(0.5);
                        const healthPercent = unit.health / unit.maxHealth;
                        const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
                        const healthBar = gameScene.add.rectangle(x, y - 35, 40 * healthPercent, 6, healthBarColor).setOrigin(0.5);
                        gameScene.unitTexts.push(healthBarBg, healthBar);
                    } else {
                        // 非飞机单位显示数字生命值
                        const healthText = gameScene.add.text(x, y + 25, `${unit.health}/${unit.maxHealth}`, {
                            fontSize: '12px',
                            fill: unit.health > unit.maxHealth * 0.5 ? '#00ff00' : '#ff0000'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(healthText);
                    }

                    // 显示等级
                    if (unit.level > 1) {
                        const levelText = gameScene.add.text(x + 25, y - 25, `★${unit.level}`, {
                            fontSize: '14px',
                            fill: '#ffff00'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(levelText);
                    }

                    // 显示冷却状态
                    if (unit.cooldown > 0) {
                        const cooldownText = gameScene.add.text(x - 25, y - 25, `${unit.cooldown}`, {
                            fontSize: '14px',
                            fill: '#ff8800'
                        }).setOrigin(0.5);
                        gameScene.unitTexts.push(cooldownText);
                    }

                    gameScene.unitTexts.push(unitText);
                }
            }
        }
    }

    // 处理指针按下事件
    function handlePointerDown(pointer) {
        const clickedObject = gameScene.input.hitTestPointer(pointer)[0];
        if (!clickedObject || !clickedObject.getData) return;

        // 检查是否点击了建筑图标
        if (clickedObject.getData('isBuilding')) {
            return; // 建筑拖拽由其自己的事件处理
        }

        const gridType = clickedObject.getData('gridType');
        const row = clickedObject.getData('row');
        const col = clickedObject.getData('col');

        if (gridType === 'player' && gamePhase === 'preparation') {
            if (playerGrid[row][col] !== null) {
                // 开始拖拽
                draggedUnit = playerGrid[row][col];
                dragStartPos = { row, col };
                isDragging = true;
                selectedCell = { row, col };

                // 高亮选中的格子
                highlightCell(row, col, true);
            }
        }
    }

    // 处理指针移动事件
    function handlePointerMove(pointer) {
        if (!isDragging) return;

        // 可以在这里添加拖拽预览效果
    }

    // 处理指针释放事件
    function handlePointerUp(pointer) {
        if (!isDragging) return;

        const clickedObject = gameScene.input.hitTestPointer(pointer)[0];
        if (clickedObject && clickedObject.getData) {
            const gridType = clickedObject.getData('gridType');
            const row = clickedObject.getData('row');
            const col = clickedObject.getData('col');

            if (gridType === 'player' && dragStartPos) {
                if (row !== dragStartPos.row || col !== dragStartPos.col) {
                    // 尝试移动或合成
                    handleUnitDrop(dragStartPos.row, dragStartPos.col, row, col);
                }
            }
        }

        // 清除拖拽状态
        isDragging = false;
        draggedUnit = null;
        dragStartPos = null;
        clearHighlights();
    }

    // 处理单位放置
    function handleUnitDrop(fromRow, fromCol, toRow, toCol) {
        const fromUnit = playerGrid[fromRow][fromCol];
        const toUnit = playerGrid[toRow][toCol];

        if (!fromUnit) return;

        if (toUnit === null) {
            // 移动到空格子
            playerGrid[toRow][toCol] = fromUnit;
            playerGrid[fromRow][fromCol] = null;
            updateDisplay();
        } else if (canMergeUnits(fromUnit, toUnit)) {
            // 合成升级
            const mergedUnit = mergeUnits(fromUnit, toUnit);
            if (mergedUnit) {
                playerGrid[toRow][toCol] = mergedUnit;
                playerGrid[fromRow][fromCol] = null;

                // 显示合成特效
                showMergeEffect(toRow, toCol);
                updateDisplay();
            }
        } else {
            // 交换位置
            playerGrid[fromRow][fromCol] = toUnit;
            playerGrid[toRow][toCol] = fromUnit;
            updateDisplay();
        }
    }

    // 检查是否可以合成
    function canMergeUnits(unit1, unit2) {
        return unit1.type === unit2.type && unit1.level === unit2.level && unit1.level < 2;
    }

    // 合成单位 - 保持机型，提升等级和属性
    function mergeUnits(unit1, unit2) {
        if (!canMergeUnits(unit1, unit2)) return null;

        // 创建升级版本，保持相同类型但提升属性
        const upgradedUnit = createUnit(unit1.type, true);
        upgradedUnit.level = unit1.level + 1;

        // 根据类型提升属性
        if (unit1.type === UNIT_TYPES.FIGHTER) {
            upgradedUnit.health = upgradedUnit.maxHealth = 50;
            upgradedUnit.attack = 25;
            upgradedUnit.range = 3;
        } else if (unit1.type === UNIT_TYPES.MISSILE) {
            upgradedUnit.health = upgradedUnit.maxHealth = 35;
            upgradedUnit.attack = 50;
            upgradedUnit.range = 4;
            upgradedUnit.areaSize = 2;
        } else if (unit1.type === UNIT_TYPES.ANTIAIR) {
            upgradedUnit.health = upgradedUnit.maxHealth = 60;
            upgradedUnit.attack = 12;
            upgradedUnit.range = 3;
        }

        return upgradedUnit;
    }

    // 显示合成特效
    function showMergeEffect(row, col) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 600; // 修正为正确的网格位置
        const x = startX + col * cellSize + cellSize / 2;
        const y = playerStartY + row * cellSize + cellSize / 2;

        // 创建升级特效
        const upgradeEffect = gameScene.add.text(x, y - 30, '⬆️ 升级！', {
            fontSize: '20px',
            fill: '#ffff00'
        }).setOrigin(0.5);

        gameScene.tweens.add({
            targets: upgradeEffect,
            y: y - 60,
            alpha: 0,
            duration: 1000,
            onComplete: () => upgradeEffect.destroy()
        });
    }

    // 高亮格子
    function highlightCell(row, col, isPlayer) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isPlayer ? 600 : 150; // 修正玩家网格位置
        const x = startX + col * cellSize;
        const y = startY + row * cellSize;

        // 创建圆角高亮
        const highlight = gameScene.add.graphics();
        highlight.lineStyle(4, 0xffff00, 1);
        highlight.strokeRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);
        highlight.fillStyle(0xffff00, 0.2);
        highlight.fillRoundedRect(x + 2, y + 2, cellSize - 4, cellSize - 4, 8);

        // 保存高亮引用以便清除
        if (!gameScene.highlights) gameScene.highlights = [];
        gameScene.highlights.push(highlight);
    }

    // 清除高亮
    function clearHighlights() {
        if (gameScene.highlights) {
            gameScene.highlights.forEach(highlight => highlight.destroy());
            gameScene.highlights = [];
        }
    }

    // 在准备阶段不需要单位菜单，使用建筑拖拽系统

    // 显示单位信息
    function showUnitInfo(unit, row, col) {
        console.log(`单位信息: ${unit.type.emoji} 生命值: ${unit.health}/${unit.maxHealth} 攻击力: ${unit.attack}`);
    }

    // 开始游戏
    function startGame() {
        gameStarted = true;
        console.log('游戏开始！');
    }

    // 开始战斗
    function startBattle() {
        if (gamePhase !== 'preparation') return;

        gamePhase = 'battle';
        enemiesSpawned = 0;
        enemySpawnTimer = 0;

        // 更新UI
        gameScene.phaseText.setText('战斗阶段 - 第' + waveNumber + '波');

        // 隐藏开始按钮和建筑商店
        hideStartButton();
        hideBuildingShop();

        // 我方战斗机起飞
        launchPlayerFighters();

        console.log('战斗开始！第' + waveNumber + '波');
    }

    // 隐藏开始按钮
    function hideStartButton() {
        if (gameScene.startButton) gameScene.startButton.setVisible(false);
        if (gameScene.startButtonBg) gameScene.startButtonBg.setVisible(false);
        if (gameScene.startButtonText) gameScene.startButtonText.setVisible(false);
    }

    // 显示开始按钮
    function showStartButton() {
        if (gameScene.startButton) gameScene.startButton.setVisible(true);
        if (gameScene.startButtonBg) gameScene.startButtonBg.setVisible(true);
        if (gameScene.startButtonText) gameScene.startButtonText.setVisible(true);
    }

    // 隐藏建筑商店
    function hideBuildingShop() {
        if (gameScene.buildingSelectionBg) {
            gameScene.buildingSelectionBg.setVisible(false);
        }
        if (gameScene.buildingElements) {
            gameScene.buildingElements.forEach(element => {
                element.setVisible(false);
            });
        }
    }

    // 显示建筑商店
    function showBuildingShop() {
        if (gameScene.buildingSelectionBg) {
            gameScene.buildingSelectionBg.setVisible(true);
        }
        if (gameScene.buildingElements) {
            gameScene.buildingElements.forEach(element => {
                element.setVisible(true);
            });
        }

        // 重新创建建筑选择区域以更新价格状态
        if (gameScene.buildingElements) {
            gameScene.buildingElements.forEach(element => {
                element.destroy();
            });
        }
        if (gameScene.buildingSelectionBg) {
            gameScene.buildingSelectionBg.destroy();
        }
        createBuildingSelection();
    }

    // 生成敌人
    function spawnEnemy() {
        // 在屏幕顶部随机位置生成自由飞行的敌机
        const spawnX = Math.random() * config.width;
        const spawnY = 50;

        // 只生成敌方战斗机
        const randomType = UNIT_TYPES.ENEMY_FIGHTER;

        // 创建自由飞行的敌机
        const enemy = {
            unit: createUnit(randomType, false),
            x: spawnX,
            y: spawnY,
            targetX: (config.width - gridSize * cellSize) / 2 + 2 * cellSize + cellSize / 2, // 玩家基地位置
            targetY: 600 + 5 * cellSize + cellSize / 2, // 调整为新的网格位置
            speed: 0.5 + Math.random() * 0.5, // 随机速度
            sprite: null
        };

        // 创建敌机精灵
        enemy.sprite = gameScene.add.text(spawnX, spawnY, randomType.emoji, {
            fontSize: '30px'
        }).setOrigin(0.5);

        // 设置敌机朝向（向下倾斜45度，朝向玩家基地）
        enemy.sprite.setRotation(Math.PI * 3 / 4); // 135度

        // 创建血条
        enemy.healthBarBg = gameScene.add.rectangle(spawnX, spawnY - 25, 30, 4, 0x333333).setOrigin(0.5);
        enemy.healthBar = gameScene.add.rectangle(spawnX, spawnY - 25, 30, 4, 0x00ff00).setOrigin(0.5);

        freeEnemies.push(enemy);
        enemiesSpawned++;

        console.log('生成自由飞行敌机：', randomType.emoji, '位置：', spawnX, spawnY);
    }

    // 更新自由飞行的敌机
    function updateFreeEnemies() {
        for (let i = freeEnemies.length - 1; i >= 0; i--) {
            const enemy = freeEnemies[i];

            // 检查敌机是否还有效
            if (!enemy || !enemy.unit) {
                destroyFreeEnemy(i);
                continue;
            }

            // 检查敌机是否死亡
            if (enemy.unit.health <= 0) {
                destroyFreeEnemy(i);
                continue;
            }

            // 移动敌机向目标
            const dx = enemy.targetX - enemy.x;
            const dy = enemy.targetY - enemy.y;
            const distance = Math.sqrt(dx * dx + dy * dy);

            if (distance > 5) {
                // 继续移动
                enemy.x += (dx / distance) * enemy.speed;
                enemy.y += (dy / distance) * enemy.speed;

                // 更新精灵位置
                if (enemy.sprite) enemy.sprite.setPosition(enemy.x, enemy.y);
                if (enemy.healthBarBg) enemy.healthBarBg.setPosition(enemy.x, enemy.y - 25);

                // 更新血条
                if (enemy.healthBar && enemy.unit.maxHealth > 0) {
                    const healthPercent = enemy.unit.health / enemy.unit.maxHealth;
                    const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
                    enemy.healthBar.setFillStyle(healthBarColor);
                    enemy.healthBar.setPosition(enemy.x, enemy.y - 25);
                    enemy.healthBar.setSize(30 * healthPercent, 4);
                }
            } else {
                // 到达目标，攻击基地
                attackPlayerBase(enemy);
                destroyFreeEnemy(i);
            }
        }
    }

    // 攻击玩家基地
    function attackPlayerBase(enemy) {
        // 找到玩家基地并造成伤害
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                if (playerGrid[i][j] && playerGrid[i][j].type === UNIT_TYPES.BASE) {
                    playerGrid[i][j].health -= enemy.unit.attack;
                    console.log('基地受到攻击！剩余血量：', playerGrid[i][j].health);

                    if (playerGrid[i][j].health <= 0) {
                        playerGrid[i][j] = null;
                    }
                    updateDisplay();
                    return;
                }
            }
        }
    }

    // 销毁自由敌机
    function destroyFreeEnemy(index) {
        if (index < 0 || index >= freeEnemies.length) return;

        const enemy = freeEnemies[index];
        if (enemy) {
            if (enemy.sprite && enemy.sprite.destroy) enemy.sprite.destroy();
            if (enemy.healthBarBg && enemy.healthBarBg.destroy) enemy.healthBarBg.destroy();
            if (enemy.healthBar && enemy.healthBar.destroy) enemy.healthBar.destroy();
        }
        freeEnemies.splice(index, 1);
    }

    // 检查波次是否完成
    function checkWaveComplete() {
        // 如果所有敌人都生成了且都被消灭了
        if (enemiesSpawned >= enemiesInWave && freeEnemies.length === 0) {
            // 准备下一波
            waveNumber++;
            enemiesInWave += 2; // 每波增加2个敌人
            gamePhase = 'preparation';
            playerMoney += 200; // 每波奖励资金

            // 更新UI
            gameScene.phaseText.setText('准备阶段 - 准备第' + waveNumber + '波');
            gameScene.waveText.setText('第' + waveNumber + '波');
            moneyText.setText('资金: $' + playerMoney);
            gameScene.startButton.setVisible(true);

            // 显示建筑商店和开始按钮
            showBuildingShop();
            showStartButton();

            // 我方战斗机降落
            landPlayerFighters();

            console.log('第' + (waveNumber-1) + '波完成！准备第' + waveNumber + '波');
        }
    }

    // 我方战斗机起飞
    function launchPlayerFighters() {
        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 600;

        // 清空之前的战斗机
        playerFighters.forEach(fighter => {
            if (fighter.sprite) fighter.sprite.destroy();
            if (fighter.healthBarBg) fighter.healthBarBg.destroy();
            if (fighter.healthBar) fighter.healthBar.destroy();
        });
        playerFighters = [];

        // 找到所有战斗机并让它们起飞
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const unit = playerGrid[i][j];
                if (unit && unit.type.attackType === 'fighter') {
                    // 创建起飞的战斗机
                    const baseX = startX + j * cellSize + cellSize / 2;
                    const baseY = playerStartY + i * cellSize + cellSize / 2;
                    const flyingY = baseY - 60; // 在基地上方60像素飞行，配合防空炮作战

                    const fighter = {
                        unit: unit,
                        baseX: baseX,
                        baseY: baseY,
                        x: baseX,
                        y: flyingY,
                        gridRow: i,
                        gridCol: j,
                        sprite: null,
                        healthBarBg: null,
                        healthBar: null
                    };

                    // 创建飞行中的战斗机精灵
                    fighter.sprite = gameScene.add.text(baseX, flyingY, unit.type.emoji, {
                        fontSize: '30px'
                    }).setOrigin(0.5);

                    // 设置战斗机朝向（向右倾斜45度）
                    fighter.sprite.setRotation(Math.PI / 4);

                    // 创建血条
                    fighter.healthBarBg = gameScene.add.rectangle(baseX, flyingY - 25, 30, 4, 0x333333).setOrigin(0.5);
                    fighter.healthBar = gameScene.add.rectangle(baseX, flyingY - 25, 30, 4, 0x00ff00).setOrigin(0.5);

                    playerFighters.push(fighter);

                    // 从网格中移除（起飞了）
                    playerGrid[i][j] = null;
                }
            }
        }

        updateDisplay();
        console.log('我方战斗机起飞！数量：', playerFighters.length);
    }

    // 我方战斗机降落
    function landPlayerFighters() {
        playerFighters.forEach(fighter => {
            // 战斗机降落回原位置
            if (fighter.gridRow !== undefined && fighter.gridCol !== undefined) {
                playerGrid[fighter.gridRow][fighter.gridCol] = fighter.unit;
            }

            // 销毁飞行精灵
            if (fighter.sprite) fighter.sprite.destroy();
            if (fighter.healthBarBg) fighter.healthBarBg.destroy();
            if (fighter.healthBar) fighter.healthBar.destroy();
        });

        playerFighters = [];
        updateDisplay();
        console.log('我方战斗机降落！');
    }

    // 更新我方战斗机
    function updatePlayerFighters() {
        for (let i = 0; i < playerFighters.length; i++) {
            const fighter = playerFighters[i];

            // 寻找最近的敌机
            let closestEnemy = null;
            let closestDistance = Infinity;

            for (let j = 0; j < freeEnemies.length; j++) {
                const enemy = freeEnemies[j];
                // 检查敌机是否还存在且有效
                if (enemy && enemy.unit && enemy.unit.health > 0) {
                    const distance = Math.sqrt(
                        Math.pow(enemy.x - fighter.x, 2) + Math.pow(enemy.y - fighter.y, 2)
                    );

                    if (distance < closestDistance) {
                        closestDistance = distance;
                        closestEnemy = enemy;
                    }
                }
            }

            // 如果找到敌机且在攻击范围内
            if (closestEnemy && closestDistance < fighter.unit.range * cellSize) {
                // 攻击敌机
                if (fighter.unit.cooldown <= 0) {
                    // 再次检查敌机是否还存在
                    if (closestEnemy.unit && closestEnemy.unit.health > 0) {
                        closestEnemy.unit.health -= fighter.unit.attack;
                        fighter.unit.cooldown = 60; // 1秒冷却（60帧）

                        // 创建攻击特效
                        createFighterAttackEffect(fighter.x, fighter.y, closestEnemy.x, closestEnemy.y);

                        console.log('我方战斗机攻击敌机！剩余血量：', closestEnemy.unit.health);
                    }
                }
            } else if (closestEnemy) {
                // 检查是否距离基地太远，限制追击距离
                const maxChaseDistance = 200; // 最大追击距离
                const distanceFromBase = Math.sqrt(
                    Math.pow(fighter.x - fighter.baseX, 2) + Math.pow(fighter.y - fighter.baseY, 2)
                );

                if (distanceFromBase < maxChaseDistance) {
                    // 移动向最近的敌机
                    const dx = closestEnemy.x - fighter.x;
                    const dy = closestEnemy.y - fighter.y;
                    const distance = Math.sqrt(dx * dx + dy * dy);

                    if (distance > 5) {
                        const speed = 1;
                        fighter.x += (dx / distance) * speed;
                        fighter.y += (dy / distance) * speed;

                        // 计算飞机朝向角度
                        const angle = Math.atan2(dy, dx);

                        // 更新精灵位置和旋转
                        fighter.sprite.setPosition(fighter.x, fighter.y);
                        fighter.sprite.setRotation(angle + Math.PI / 4); // 基础45度 + 朝向角度
                        fighter.healthBarBg.setPosition(fighter.x, fighter.y - 25);
                        fighter.healthBar.setPosition(fighter.x, fighter.y - 25);
                    }
                } else {
                    // 距离基地太远，返回基地附近
                    const returnDx = fighter.baseX - fighter.x;
                    const returnDy = (fighter.baseY - 60) - fighter.y; // 返回到基地上方60像素
                    const returnDistance = Math.sqrt(returnDx * returnDx + returnDy * returnDy);

                    if (returnDistance > 5) {
                        const speed = 1.5; // 返回时速度稍快
                        fighter.x += (returnDx / returnDistance) * speed;
                        fighter.y += (returnDy / returnDistance) * speed;

                        // 计算返回朝向角度
                        const angle = Math.atan2(returnDy, returnDx);

                        // 更新精灵位置和旋转
                        fighter.sprite.setPosition(fighter.x, fighter.y);
                        fighter.sprite.setRotation(angle + Math.PI / 4);
                        fighter.healthBarBg.setPosition(fighter.x, fighter.y - 25);
                        fighter.healthBar.setPosition(fighter.x, fighter.y - 25);
                    }
                }
            }

            // 减少冷却时间
            if (fighter.unit.cooldown > 0) {
                fighter.unit.cooldown--;
            }

            // 更新血条
            const healthPercent = fighter.unit.health / fighter.unit.maxHealth;
            const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
            fighter.healthBar.setFillStyle(healthBarColor);
            fighter.healthBar.setSize(30 * healthPercent, 4);
        }
    }

    // 创建战斗机攻击特效
    function createFighterAttackEffect(fromX, fromY, toX, toY) {
        // 创建子弹
        const bullet = gameScene.add.circle(fromX, fromY, 3, 0x00ff00);

        // 子弹飞行动画
        gameScene.tweens.add({
            targets: bullet,
            x: toX,
            y: toY,
            duration: 300,
            onComplete: () => {
                bullet.destroy();
                // 爆炸特效
                const explosion = gameScene.add.circle(toX, toY, 15, 0xff4444, 0.8);
                gameScene.tweens.add({
                    targets: explosion,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 200,
                    onComplete: () => explosion.destroy()
                });
            }
        });
    }

    // 处理建筑拖拽放置
    function handleBuildingDrop(buildingIcon, pointer) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 600; // 调整为新的网格位置

        // 检查是否在玩家网格范围内
        if (isInPlayerGrid(pointer.x, pointer.y)) {
            const gridX = Math.floor((pointer.x - startX) / cellSize);
            const gridY = Math.floor((pointer.y - playerStartY) / cellSize);

            // 确保网格坐标在有效范围内
            if (gridX >= 0 && gridX < gridSize && gridY >= 0 && gridY < gridSize) {
                if (playerGrid[gridY][gridX] === null) {
                    const buildingType = buildingIcon.getData('buildingType');

                    // 检查是否有足够资金
                    if (playerMoney >= buildingType.cost) {
                        // 扣除资金并放置建筑
                        playerMoney -= buildingType.cost;
                        playerGrid[gridY][gridX] = createUnit(buildingType, true);

                        // 更新UI
                        moneyText.setText('资金: $' + playerMoney);
                        updateDisplay();

                        // 重新创建建筑选择区域以更新可购买状态
                        showBuildingShop();

                        console.log('放置建筑：', buildingType.emoji, '位置：', gridY, gridX, '花费：$' + buildingType.cost);
                    } else {
                        console.log('资金不足！需要：$' + buildingType.cost + '，当前：$' + playerMoney);
                    }
                } else {
                    console.log('该位置已有建筑！');
                }
            }
        } else {
            console.log('拖拽位置不在网格范围内');
        }

        // 重置建筑图标位置
        const originalBuilding = availableBuildings.find(b => b.type === buildingIcon.getData('buildingType'));
        if (originalBuilding) {
            buildingIcon.setPosition(originalBuilding.x + 40, originalBuilding.y + 40);
        }
    }

    // 检查拖拽位置是否在网格内
    function isInPlayerGrid(x, y) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 600;
        const endX = startX + gridSize * cellSize;
        const endY = playerStartY + gridSize * cellSize;

        return x >= startX && x <= endX && y >= playerStartY && y <= endY;
    }

    // 处理回合
    function processTurn() {
        // 玩家单位攻击
        processAttacks(playerGrid, enemyGrid, false);

        // 敌方单位攻击
        processAttacks(enemyGrid, playerGrid, true);

        // 敌方AI行动
        enemyAI();

        // 更新显示
        updateDisplay();

        // 检查游戏结束条件
        checkGameEnd();
    }

    // 处理攻击
    function processAttacks(attackerGrid, defenderGrid, isEnemyAttacking) {
        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                const attacker = attackerGrid[i][j];
                if (attacker && attacker.attack > 0 && attacker.cooldown <= 0 && !attacker.isFlying) {
                    // 寻找攻击目标
                    const target = findTarget(i, j, attacker, defenderGrid, isEnemyAttacking);
                    if (target) {
                        attacker.cooldown = 2; // 冷却2回合

                        // 根据攻击类型执行不同的攻击
                        if (attacker.attackType === 'fly') {
                            // 飞机起飞攻击
                            executeFlightAttack(i, j, target, attacker, attackerGrid, defenderGrid, isEnemyAttacking);
                        } else if (attacker.attackType === 'area') {
                            // 导弹范围攻击
                            executeMissileAttack(i, j, target, attacker, defenderGrid, isEnemyAttacking);
                        } else if (attacker.attackType === 'continuous') {
                            // 防空炮持续攻击 - 攻击自由飞行的敌机
                            executeAntiAirAttack(i, j, attacker, isEnemyAttacking);
                        } else {
                            // 普通攻击
                            executeNormalAttack(i, j, target, attacker, defenderGrid, isEnemyAttacking);
                        }
                    }
                }

                // 减少冷却时间
                if (attacker && attacker.cooldown > 0) {
                    attacker.cooldown--;
                }
            }
        }
    }

    // 飞机起飞攻击
    function executeFlightAttack(attackerRow, attackerCol, target, attacker, attackerGrid, defenderGrid, isEnemyAttacking) {
        // 标记飞机为飞行状态
        attacker.isFlying = true;
        attackerGrid[attackerRow][attackerCol] = null; // 暂时移除飞机

        // 创建飞行动画
        createFlightAttack(attackerRow, attackerCol, target.row, target.col, attacker, isEnemyAttacking, () => {
            // 攻击完成后的回调
            target.unit.health -= attacker.attack;

            // 检查目标是否死亡
            if (target.unit.health <= 0) {
                defenderGrid[target.row][target.col] = null;
            }

            // 飞机返回
            attacker.isFlying = false;
            attackerGrid[attackerRow][attackerCol] = attacker;
            updateDisplay();
        });
    }

    // 导弹范围攻击
    function executeMissileAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        // 创建导弹攻击特效
        createMissileAttack(attackerRow, attackerCol, target.row, target.col, isEnemyAttacking, () => {
            // 范围伤害
            const areaTargets = getAreaTargets(target.row, target.col, attacker.areaSize, defenderGrid);
            areaTargets.forEach(areaTarget => {
                areaTarget.unit.health -= attacker.attack;
                if (areaTarget.unit.health <= 0) {
                    defenderGrid[areaTarget.row][areaTarget.col] = null;
                }
            });
            updateDisplay();
        });
    }

    // 防空炮持续攻击
    function executeContinuousAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        // 只攻击飞机类型的单位
        if (target.unit.type.attackType !== 'fly') return;

        // 创建持续攻击效果
        createContinuousAttack(attackerRow, attackerCol, target.row, target.col, attacker, isEnemyAttacking, () => {
            target.unit.health -= attacker.attack;
            if (target.unit.health <= 0) {
                defenderGrid[target.row][target.col] = null;
            }
            updateDisplay();
        });
    }

    // 防空炮攻击自由飞行的敌机
    function executeAntiAirAttack(attackerRow, attackerCol, attacker, isEnemyAttacking) {
        if (isEnemyAttacking) return; // 只有我方防空炮攻击

        const startX = (config.width - gridSize * cellSize) / 2;
        const playerStartY = 600;
        const attackerX = startX + attackerCol * cellSize + cellSize / 2;
        const attackerY = playerStartY + attackerRow * cellSize + cellSize / 2;

        // 寻找射程内的敌机
        let targetEnemy = null;
        let closestDistance = Infinity;

        for (let i = 0; i < freeEnemies.length; i++) {
            const enemy = freeEnemies[i];
            if (enemy && enemy.unit && enemy.unit.health > 0) {
                const distance = Math.sqrt(
                    Math.pow(enemy.x - attackerX, 2) + Math.pow(enemy.y - attackerY, 2)
                );
                const gridDistance = distance / cellSize;

                if (gridDistance <= attacker.range && distance < closestDistance) {
                    closestDistance = distance;
                    targetEnemy = enemy;
                }
            }
        }

        if (targetEnemy) {
            // 创建防空炮开火特效
            createAntiAirFireEffect(attackerX, attackerY, targetEnemy.x, targetEnemy.y, attacker, () => {
                if (targetEnemy.unit && targetEnemy.unit.health > 0) {
                    targetEnemy.unit.health -= attacker.attack;
                    console.log('防空炮击中敌机！剩余血量：', targetEnemy.unit.health);
                }
            });
        }
    }

    // 普通攻击
    function executeNormalAttack(attackerRow, attackerCol, target, attacker, defenderGrid, isEnemyAttacking) {
        target.unit.health -= attacker.attack;

        if (target.isFreeEnemy) {
            // 攻击自由敌机
            createAttackEffectToPosition(attackerRow, attackerCol, target.x, target.y, isEnemyAttacking);
        } else {
            // 攻击网格单位
            createAttackEffect(attackerRow, attackerCol, target.row, target.col, isEnemyAttacking);
            if (target.unit.health <= 0) {
                defenderGrid[target.row][target.col] = null;
            }
        }
    }

    // 获取范围内的目标 - 导弹攻击9个格子（3x3区域）
    function getAreaTargets(centerRow, centerCol, areaSize, defenderGrid) {
        const targets = [];
        // 固定为3x3区域（9个格子）
        const range = 1; // 中心点周围1格的距离

        for (let i = Math.max(0, centerRow - range); i <= Math.min(gridSize - 1, centerRow + range); i++) {
            for (let j = Math.max(0, centerCol - range); j <= Math.min(gridSize - 1, centerCol + range); j++) {
                if (defenderGrid[i][j]) {
                    targets.push({ unit: defenderGrid[i][j], row: i, col: j });
                }
            }
        }
        return targets;
    }

    // 寻找攻击目标
    function findTarget(attackerRow, attackerCol, attacker, defenderGrid, isEnemyAttacking) {
        let closestTarget = null;
        let closestDistance = Infinity;

        // 如果是玩家单位，优先攻击自由飞行的敌机
        if (!isEnemyAttacking && freeEnemies.length > 0) {
            const startX = (config.width - gridSize * cellSize) / 2;
            const playerStartY = 600; // 修正为正确的网格位置
            const attackerX = startX + attackerCol * cellSize + cellSize / 2;
            const attackerY = playerStartY + attackerRow * cellSize + cellSize / 2;

            for (let i = 0; i < freeEnemies.length; i++) {
                const enemy = freeEnemies[i];
                const distance = Math.sqrt(
                    Math.pow(enemy.x - attackerX, 2) + Math.pow(enemy.y - attackerY, 2)
                );
                const gridDistance = distance / cellSize; // 转换为格子距离

                // 检查攻击类型限制
                if (attacker.type.needsTarget && enemy.unit.type.attackType !== 'fly') {
                    continue;
                }

                if (gridDistance <= attacker.range && distance < closestDistance) {
                    closestDistance = distance;
                    closestTarget = {
                        unit: enemy.unit,
                        enemy: enemy,
                        isFreeEnemy: true,
                        x: enemy.x,
                        y: enemy.y
                    };
                }
            }
        }

        // 如果没有找到自由敌机目标，搜索网格目标
        if (!closestTarget) {
            for (let i = 0; i < gridSize; i++) {
                for (let j = 0; j < gridSize; j++) {
                    const defender = defenderGrid[i][j];
                    if (defender) {
                        const distance = Math.abs(attackerRow - i) + Math.abs(attackerCol - j);

                        // 导弹只能攻击飞机类型的单位
                        if (attacker.type.needsTarget && defender.type.attackType !== 'fly') {
                            continue;
                        }

                        if (distance <= attacker.range && distance < closestDistance) {
                            closestDistance = distance;
                            closestTarget = { unit: defender, row: i, col: j };
                        }
                    }
                }
            }
        }

        return closestTarget;
    }

    // 创建飞行攻击特效
    function createFlightAttack(fromRow, fromCol, toRow, toCol, attacker, isEnemyAttacking, onComplete) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 600; // 修正玩家网格位置
        const endY = isEnemyAttacking ? 600 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 绘制简化的飞行路径
        drawSimpleFlightPath(fromX, fromY, toX, toY);

        // 创建飞机图标
        const plane = gameScene.add.text(fromX, fromY, attacker.type.emoji, {
            fontSize: '30px'
        }).setOrigin(0.5);

        // 创建血条背景
        const healthBarBg = gameScene.add.rectangle(fromX, fromY - 40, 40, 6, 0x333333).setOrigin(0.5);
        // 创建血条
        const healthPercent = attacker.health / attacker.maxHealth;
        const healthBarColor = healthPercent > 0.6 ? 0x00ff00 : healthPercent > 0.3 ? 0xffff00 : 0xff0000;
        const healthBar = gameScene.add.rectangle(fromX, fromY - 40, 40 * healthPercent, 6, healthBarColor).setOrigin(0.5);

        // 计算中间点（弧形路径）
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 60;

        // 飞机飞向目标（使用贝塞尔曲线）
        gameScene.tweens.add({
            targets: [plane, healthBarBg, healthBar],
            x: toX,
            y: [toY, toY - 40, toY - 40],
            duration: 1200,
            ease: 'Power2',
            onUpdate: (tween) => {
                const progress = tween.progress;
                // 贝塞尔曲线计算
                const currentX = (1 - progress) * (1 - progress) * fromX + 2 * (1 - progress) * progress * midX + progress * progress * toX;
                const currentY = (1 - progress) * (1 - progress) * fromY + 2 * (1 - progress) * progress * midY + progress * progress * toY;

                plane.setPosition(currentX, currentY);
                healthBarBg.setPosition(currentX, currentY - 40);
                healthBar.setPosition(currentX, currentY - 40);
            },
            onComplete: () => {
                // 投下炸弹
                const bomb = gameScene.add.text(toX, toY, '💣', {
                    fontSize: '20px'
                }).setOrigin(0.5);

                // 炸弹下落动画
                gameScene.tweens.add({
                    targets: bomb,
                    y: toY + 30,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        bomb.destroy();

                        // 爆炸特效
                        const explosion = gameScene.add.circle(toX, toY + 30, 25, 0xff4444, 0.8);
                        gameScene.tweens.add({
                            targets: explosion,
                            scaleX: 2,
                            scaleY: 2,
                            alpha: 0,
                            duration: 400,
                            onComplete: () => explosion.destroy()
                        });

                        // 执行攻击回调
                        onComplete();

                        // 飞机返回
                        gameScene.tweens.add({
                            targets: [plane, healthBarBg, healthBar],
                            x: fromX,
                            y: [fromY, fromY - 40, fromY - 40],
                            duration: 1000,
                            ease: 'Power2',
                            onUpdate: (tween) => {
                                const progress = tween.progress;
                                // 返回路径
                                const currentX = (1 - progress) * (1 - progress) * toX + 2 * (1 - progress) * progress * midX + progress * progress * fromX;
                                const currentY = (1 - progress) * (1 - progress) * toY + 2 * (1 - progress) * progress * midY + progress * progress * fromY;

                                plane.setPosition(currentX, currentY);
                                healthBarBg.setPosition(currentX, currentY - 40);
                                healthBar.setPosition(currentX, currentY - 40);
                            },
                            onComplete: () => {
                                plane.destroy();
                                healthBarBg.destroy();
                                healthBar.destroy();
                            }
                        });
                    }
                });
            }
        });
    }

    // 绘制简化的飞行路径
    function drawSimpleFlightPath(fromX, fromY, toX, toY) {
        const pathGraphics = gameScene.add.graphics();
        pathGraphics.lineStyle(2, 0xffff00, 0.4);

        // 计算弧形路径的中间点
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 60;

        // 使用Phaser的路径API绘制贝塞尔曲线
        const path = new Phaser.Curves.Path(fromX, fromY);
        path.quadraticBezierTo(midX, midY, toX, toY);

        // 绘制路径
        path.draw(pathGraphics);

        // 路径会在2秒后消失
        gameScene.time.delayedCall(2000, () => {
            if (pathGraphics && pathGraphics.destroy) {
                pathGraphics.destroy();
            }
        });
    }

    // 创建持续攻击特效（防空炮）
    function createContinuousAttack(fromRow, fromCol, toRow, toCol, attacker, isEnemyAttacking, onHit) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 600; // 修正玩家网格位置
        const endY = isEnemyAttacking ? 600 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建多发子弹连续射击
        let bulletCount = 0;
        const maxBullets = 5;

        const fireBullet = () => {
            if (bulletCount >= maxBullets) {
                onHit();
                return;
            }

            // 创建子弹
            const bullet = gameScene.add.circle(fromX, fromY, 2, 0xffff00);

            // 子弹飞行动画
            gameScene.tweens.add({
                targets: bullet,
                x: toX + (Math.random() - 0.5) * 20, // 添加一些随机偏移
                y: toY + (Math.random() - 0.5) * 20,
                duration: 200,
                ease: 'Linear',
                onComplete: () => {
                    bullet.destroy();

                    // 小爆炸特效
                    const explosion = gameScene.add.circle(
                        toX + (Math.random() - 0.5) * 20,
                        toY + (Math.random() - 0.5) * 20,
                        8,
                        0xff8800,
                        0.8
                    );

                    gameScene.tweens.add({
                        targets: explosion,
                        scaleX: 2,
                        scaleY: 2,
                        alpha: 0,
                        duration: 200,
                        onComplete: () => explosion.destroy()
                    });
                }
            });

            bulletCount++;

            // 继续发射下一发子弹
            if (bulletCount < maxBullets) {
                gameScene.time.delayedCall(100, fireBullet);
            } else {
                // 最后一发子弹后执行攻击
                gameScene.time.delayedCall(200, onHit);
            }
        };

        // 开始连续射击
        fireBullet();
    }

    // 创建防空炮开火特效
    function createAntiAirFireEffect(fromX, fromY, toX, toY, attacker, onHit) {
        // 创建炮口火光
        const muzzleFlash = gameScene.add.circle(fromX, fromY, 15, 0xffaa00, 0.8);
        gameScene.tweens.add({
            targets: muzzleFlash,
            scaleX: 2,
            scaleY: 2,
            alpha: 0,
            duration: 200,
            onComplete: () => muzzleFlash.destroy()
        });

        // 创建连续的防空炮弹
        let bulletCount = 0;
        const maxBullets = 3;

        const fireBullet = () => {
            if (bulletCount >= maxBullets) {
                onHit();
                return;
            }

            // 创建防空炮弹
            const bullet = gameScene.add.circle(fromX, fromY, 3, 0xff6600);

            // 添加一些随机散布
            const spreadX = (Math.random() - 0.5) * 30;
            const spreadY = (Math.random() - 0.5) * 30;

            // 炮弹飞行动画
            gameScene.tweens.add({
                targets: bullet,
                x: toX + spreadX,
                y: toY + spreadY,
                duration: 300,
                ease: 'Linear',
                onComplete: () => {
                    bullet.destroy();

                    // 小爆炸特效
                    const explosion = gameScene.add.circle(
                        toX + spreadX,
                        toY + spreadY,
                        8,
                        0xff4400,
                        0.8
                    );

                    gameScene.tweens.add({
                        targets: explosion,
                        scaleX: 2,
                        scaleY: 2,
                        alpha: 0,
                        duration: 200,
                        onComplete: () => explosion.destroy()
                    });
                }
            });

            bulletCount++;

            // 继续发射下一发炮弹
            if (bulletCount < maxBullets) {
                gameScene.time.delayedCall(100, fireBullet);
            } else {
                // 最后一发炮弹后执行攻击
                gameScene.time.delayedCall(300, onHit);
            }
        };

        // 开始连续射击
        fireBullet();
    }

    // 创建攻击自由敌机的特效
    function createAttackEffectToPosition(fromRow, fromCol, toX, toY, isEnemyAttacking) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 600; // 修正玩家网格位置

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;

        // 创建子弹
        const bullet = gameScene.add.circle(fromX, fromY, 3, 0xffff00);

        // 子弹飞行动画
        gameScene.tweens.add({
            targets: bullet,
            x: toX,
            y: toY,
            duration: 500,
            onComplete: () => {
                bullet.destroy();
                // 爆炸特效
                const explosion = gameScene.add.circle(toX, toY, 20, 0xff4444, 0.7);
                gameScene.tweens.add({
                    targets: explosion,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => explosion.destroy()
                });
            }
        });
    }

    // 创建导弹攻击特效
    function createMissileAttack(fromRow, fromCol, toRow, toCol, isEnemyAttacking, onComplete) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 600; // 修正玩家网格位置
        const endY = isEnemyAttacking ? 600 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建导弹
        const missile = gameScene.add.text(fromX, fromY, '🚀', {
            fontSize: '25px'
        }).setOrigin(0.5);

        // 导弹飞行轨迹（抛物线）
        const midX = (fromX + toX) / 2;
        const midY = Math.min(fromY, toY) - 100;

        gameScene.tweens.add({
            targets: missile,
            x: toX,
            y: toY,
            duration: 1200,
            ease: 'Power2',
            onUpdate: (tween) => {
                const progress = tween.progress;
                const currentX = fromX + (toX - fromX) * progress;
                const currentY = fromY + (toY - fromY) * progress - 100 * Math.sin(progress * Math.PI);
                missile.setPosition(currentX, currentY);
            },
            onComplete: () => {
                missile.destroy();

                // 大范围爆炸特效
                for (let i = 0; i < 3; i++) {
                    setTimeout(() => {
                        const explosion = gameScene.add.circle(
                            toX + (Math.random() - 0.5) * 60,
                            toY + (Math.random() - 0.5) * 60,
                            30 + i * 10,
                            0xff4444,
                            0.8 - i * 0.2
                        );

                        gameScene.tweens.add({
                            targets: explosion,
                            scaleX: 2 + i * 0.5,
                            scaleY: 2 + i * 0.5,
                            alpha: 0,
                            duration: 500 + i * 100,
                            onComplete: () => explosion.destroy()
                        });
                    }, i * 100);
                }

                setTimeout(onComplete, 300);
            }
        });
    }

    // 创建普通攻击特效
    function createAttackEffect(fromRow, fromCol, toRow, toCol, isEnemyAttacking) {
        const startX = (config.width - gridSize * cellSize) / 2;
        const startY = isEnemyAttacking ? 150 : 600; // 修正玩家网格位置
        const endY = isEnemyAttacking ? 600 : 150;

        const fromX = startX + fromCol * cellSize + cellSize / 2;
        const fromY = startY + fromRow * cellSize + cellSize / 2;
        const toX = startX + toCol * cellSize + cellSize / 2;
        const toY = endY + toRow * cellSize + cellSize / 2;

        // 创建子弹
        const bullet = gameScene.add.circle(fromX, fromY, 3, 0xffff00);

        // 子弹飞行动画
        gameScene.tweens.add({
            targets: bullet,
            x: toX,
            y: toY,
            duration: 500,
            onComplete: () => {
                bullet.destroy();
                // 爆炸特效
                const explosion = gameScene.add.circle(toX, toY, 20, 0xff4444, 0.7);
                gameScene.tweens.add({
                    targets: explosion,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    onComplete: () => explosion.destroy()
                });
            }
        });
    }

    // 敌方AI - 现在敌人都是自由飞行的，不需要网格AI
    function enemyAI() {
        // 敌方AI现在通过自由飞行敌机系统处理，这个函数保留但不执行任何操作
        // 避免在网格中生成敌人，因为现在敌人都是自由飞行的
    }

    // 检查游戏结束
    function checkGameEnd() {
        // 检查玩家基地是否被摧毁
        let playerBaseCount = 0;

        for (let i = 0; i < gridSize; i++) {
            for (let j = 0; j < gridSize; j++) {
                if (playerGrid[i][j] && playerGrid[i][j].type === UNIT_TYPES.BASE) {
                    playerBaseCount++;
                }
            }
        }

        if (playerBaseCount === 0) {
            gameStarted = false;
            gamePhase = 'gameover';
            gameScene.add.text(config.width / 2, config.height / 2, '游戏结束\n基地被摧毁！\n坚持了' + (waveNumber-1) + '波', {
                fontSize: '36px',
                fill: '#ff0000',
                align: 'center'
            }).setOrigin(0.5);
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
