<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>颜色数独游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            overflow: hidden;
        }

        #game-canvas {
            width: 1280px;
            height: 720px;
            max-width: 100vw;
            max-height: 100vh;
            object-fit: contain;
        }
    </style>
</head>

<body>
    <div id="game-canvas"></div>
    <script>
        class MainScene extends Phaser.Scene {
            constructor() {
                super({ key: 'MainScene' });
                this.grid = [];
                this.solution = [];
                this.cellSprites = [];
                this.cellTexts = []; // 存储格子上的文本
                this.selectedCell = null;
                this.colorPalette = [];
                this.currentLevel = 1;
                this.gridSize = 4;
                this.boxSize = 2;

                // 9种对比度更高的颜色
                this.colors = [
                    0x95e1d3, // 红色
                    0xe0f9b5, // 绿色
                    0xfce38a, // 蓝色
                    0xf38181, // 黄色
                    0xf08a5d, // 品红色
                    0x6a2c70, // 青色
                    0xFFA500, // 橙色
                    0x800080, // 紫色
                    0x008000  // 深绿色
                ];
            }

            create() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;

                // 创建标题（左右结构布局）
                this.add.text(width / 2, 30, '颜色数独', {
                    fontSize: '32px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.add.text(width / 2, 65, `第${this.currentLevel}关 - ${this.gridSize}x${this.gridSize}`, {
                    fontSize: '18px',
                    color: '#ffffff'
                }).setOrigin(0.5);

                // 初始化网格
                this.initializeGrid();

                // 创建游戏网格（左侧）
                this.createGameGrid();

                // 创建右侧控制面板
                this.createRightPanel();
            }

            initializeGrid() {
                // 根据关卡设置网格大小
                if (this.currentLevel === 1) {
                    this.gridSize = 4;
                    this.boxSize = 2;
                } else if (this.currentLevel === 2) {
                    this.gridSize = 6;
                    this.boxSize = 2;
                } else {
                    this.gridSize = 9;
                    this.boxSize = 3;
                }

                // 生成解决方案
                this.generateSolution();

                // 创建题目（移除一些格子）
                this.grid = this.solution.map(row => [...row]);

                // 根据网格大小调整移除的格子数量
                const removeCount = Math.floor(this.gridSize * this.gridSize * 0.4);
                for (let i = 0; i < removeCount; i++) {
                    const row = Math.floor(Math.random() * this.gridSize);
                    const col = Math.floor(Math.random() * this.gridSize);
                    this.grid[row][col] = null;
                }
            }

            generateSolution() {
                this.solution = [];

                if (this.gridSize === 4) {
                    // 4x4数独解决方案
                    this.solution = [
                        [0, 1, 2, 3],
                        [2, 3, 0, 1],
                        [1, 0, 3, 2],
                        [3, 2, 1, 0]
                    ];
                } else if (this.gridSize === 6) {
                    // 6x6数独解决方案
                    this.solution = [
                        [0, 1, 2, 3, 4, 5],
                        [3, 4, 5, 0, 1, 2],
                        [1, 0, 3, 2, 5, 4],
                        [2, 5, 4, 1, 0, 3],
                        [4, 3, 0, 5, 2, 1],
                        [5, 2, 1, 4, 3, 0]
                    ];
                } else {
                    // 9x9数独解决方案
                    this.solution = [
                        [0, 1, 2, 3, 4, 5, 6, 7, 8],
                        [3, 4, 5, 6, 7, 8, 0, 1, 2],
                        [6, 7, 8, 0, 1, 2, 3, 4, 5],
                        [1, 2, 0, 4, 5, 3, 7, 8, 6],
                        [4, 5, 3, 7, 8, 6, 1, 2, 0],
                        [7, 8, 6, 1, 2, 0, 4, 5, 3],
                        [2, 0, 1, 5, 3, 4, 8, 6, 7],
                        [5, 3, 4, 8, 6, 7, 2, 0, 1],
                        [8, 6, 7, 2, 0, 1, 5, 3, 4]
                    ];
                }
            }

            createGameGrid() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;

                // 清除现有的格子和文本
                if (this.cellSprites.length > 0) {
                    this.cellSprites.forEach(row => {
                        row.forEach(cell => cell.destroy());
                    });
                }

                if (this.cellTexts.length > 0) {
                    this.cellTexts.forEach(row => {
                        row.forEach(text => text.destroy());
                    });
                }

                // 左右结构布局：游戏网格放在左侧
                const leftPanelWidth = width * 0.65; // 左侧占65%宽度
                const availableHeight = height - 120; // 为顶部标题预留空间
                const availableWidth = leftPanelWidth - 60; // 为边距预留空间
                const cellSize = Math.min(availableWidth / this.gridSize, availableHeight / this.gridSize) * 0.85;
                const padding = 2;
                const gridWidth = this.gridSize * (cellSize + padding) - padding;
                const gridHeight = this.gridSize * (cellSize + padding) - padding;
                const startX = (leftPanelWidth - gridWidth) / 2;
                const startY = (height - gridHeight) / 2 + 20;

                this.cellSprites = [];
                this.cellTexts = [];

                for (let row = 0; row < this.gridSize; row++) {
                    this.cellSprites[row] = [];
                    this.cellTexts[row] = [];
                    
                    for (let col = 0; col < this.gridSize; col++) {
                        const x = startX + col * (cellSize + padding);
                        const y = startY + row * (cellSize + padding);

                        // 创建格子背景
                        const cell = this.add.rectangle(x, y, cellSize, cellSize, 0x2c2c54)
                           .setStrokeStyle(2, 0x4a4a8a)
                           .setOrigin(0)
                           .setInteractive()
                           .on('pointerdown', () => this.onCellClick(row, col));

                        // 如果有颜色，设置格子颜色
                        if (this.grid[row][col] !== null) {
                            cell.setFillStyle(this.colors[this.grid[row][col]]);
                        }

                        // 添加区域边框
                        if (this.gridSize === 4) {
                            // 4x4: 2x2区域
                            if (row % 2 === 0 && row > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                            if (col % 2 === 0 && col > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                        } else if (this.gridSize === 6) {
                            // 6x6: 2x3区域
                            if (row % 2 === 0 && row > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                            if (col % 3 === 0 && col > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                        } else {
                            // 9x9: 3x3区域
                            if (row % 3 === 0 && row > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                            if (col % 3 === 0 && col > 0) {
                                cell.setStrokeStyle(3, 0x6666aa);
                            }
                        }

                        this.cellSprites[row][col] = cell;

                        // 添加数字文本
                        const textX = x + cellSize / 2;
                        const textY = y + cellSize / 2;
                        
                        // 将颜色索引(0-3)转换为显示数字(1-4)
                        const displayNumber = this.grid[row][col] !== null ? (this.grid[row][col] + 1).toString() : '';
                        
                        const text = this.add.text(textX, textY, displayNumber, {
                            fontSize: `${Math.floor(cellSize * 0.6)}px`,
                            color: '#ffffff',
                            fontStyle: 'bold',
                            align: 'center'
                        }).setOrigin(0.5);
                        
                        this.cellTexts[row][col] = text;

                        // 添加悬停效果
                        cell.on('pointerover', () => {
                            if (this.grid[row][col] === null) {
                                cell.setStrokeStyle(3, 0x8888ff);
                            }
                        });

                        cell.on('pointerout', () => {
                            if (this.grid[row][col] === null && (this.selectedCell === null || this.selectedCell.row !== row || this.selectedCell.col !== col)) {
                                cell.setStrokeStyle(2, 0x4a4a8a);
                            }
                        });
                    }
                }
                
                // 如果有选中的格子，高亮显示
                if (this.selectedCell !== null) {
                    const { row, col } = this.selectedCell;
                    if (this.grid[row][col] === null) {
                        this.cellSprites[row][col].setStrokeStyle(4, 0xffffff);
                    }
                }
            }

            createRightPanel() {
                const width = this.cameras.main.width;
                const height = this.cameras.main.height;
                const rightPanelX = width * 0.65; // 右侧面板起始位置
                const rightPanelWidth = width * 0.35; // 右侧面板宽度

                // 创建右侧面板背景
                this.add.rectangle(rightPanelX, 0, rightPanelWidth, height, 0x1a1a2e, 0.3)
                    .setOrigin(0)
                    .setStrokeStyle(2, 0x4a4a8a);

                // 创建颜色选择器
                this.createColorPalette(rightPanelX, rightPanelWidth);

                // 创建控制按钮
                this.createButtons(rightPanelX, rightPanelWidth);

                // 添加提示信息
                this.add.text(rightPanelX + rightPanelWidth / 2, height - 60, '请先选择空格子\n再选择颜色', {
                    fontSize: '14px',
                    color: '#ffffff',
                    align: 'center'
                }).setOrigin(0.5);
            }

            createColorPalette(panelX, panelWidth) {
                const height = this.cameras.main.height;

                // 清除现有的颜色板
                this.colorPalette.forEach(rect => rect.destroy());
                this.colorPalette = [];

                // 右侧面板中的颜色选择器
                const colorSize = 50;
                const spacing = 10;
                const colorsPerRow = Math.min(3, this.gridSize); // 每行最多3个颜色
                const rows = Math.ceil(this.gridSize / colorsPerRow);

                const totalWidth = colorsPerRow * colorSize + (colorsPerRow - 1) * spacing;
                const startX = panelX + (panelWidth - totalWidth) / 2;
                const startY = 200;

                this.add.text(panelX + panelWidth / 2, startY - 30, '选择颜色:', {
                    fontSize: '20px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 只显示当前关卡需要的颜色数量
                for (let i = 0; i < this.gridSize; i++) {
                    const row = Math.floor(i / colorsPerRow);
                    const col = i % colorsPerRow;
                    const x = startX + col * (colorSize + spacing);
                    const y = startY + row * (colorSize + spacing + 10);

                    const colorRect = this.add.rectangle(x, y, colorSize, colorSize, this.colors[i])
                       .setStrokeStyle(3, 0x4a4a8a)
                       .setOrigin(0)
                       .setInteractive()
                       .on('pointerdown', () => this.selectColor(i));

                    // 添加数字文本
                    const textX = x + colorSize / 2;
                    const textY = y + colorSize / 2;

                    // 将颜色索引转换为显示数字
                    const displayNumber = (i + 1).toString();

                    this.add.text(textX, textY, displayNumber, {
                        fontSize: '22px',
                        color: '#ffffff',
                        fontStyle: 'bold',
                        align: 'center'
                    }).setOrigin(0.5);

                    // 添加悬停效果
                    colorRect.on('pointerover', () => {
                        colorRect.setStrokeStyle(4, 0x8888ff);
                    });

                    colorRect.on('pointerout', () => {
                        colorRect.setStrokeStyle(3, 0x4a4a8a);
                    });

                    this.colorPalette.push(colorRect);
                }
            }

            createButtons(panelX, panelWidth) {
                const height = this.cameras.main.height;

                // 右侧面板中的按钮
                const buttonWidth = panelWidth * 0.8;
                const buttonHeight = 40;
                const buttonSpacing = 15;
                const startX = panelX + (panelWidth - buttonWidth) / 2;
                let startY = 400;

                // 重新开始按钮
                const restartBtn = this.add.rectangle(startX, startY, buttonWidth, buttonHeight, 0x4ECDC4)
                   .setStrokeStyle(2, 0x36a0a0)
                   .setOrigin(0)
                   .setInteractive()
                   .on('pointerdown', () => this.restartGame());

                this.add.text(startX + buttonWidth / 2, startY + buttonHeight / 2, '重新开始', {
                    fontSize: '16px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                startY += buttonHeight + buttonSpacing;

                // 检查答案按钮
                const checkBtn = this.add.rectangle(startX, startY, buttonWidth, buttonHeight, 0x96CEB4)
                   .setStrokeStyle(2, 0x6ba86f)
                   .setOrigin(0)
                   .setInteractive()
                   .on('pointerdown', () => this.checkSolution());

                this.add.text(startX + buttonWidth / 2, startY + buttonHeight / 2, '检查答案', {
                    fontSize: '16px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                startY += buttonHeight + buttonSpacing;

                // 下一关按钮（只在完成当前关卡后显示）
                if (this.currentLevel < 3) {
                    const nextBtn = this.add.rectangle(startX, startY, buttonWidth, buttonHeight, 0xFECA57)
                       .setStrokeStyle(2, 0xd4a843)
                       .setOrigin(0)
                       .setInteractive()
                       .on('pointerdown', () => this.nextLevel());

                    this.add.text(startX + buttonWidth / 2, startY + buttonHeight / 2, '下一关', {
                        fontSize: '16px',
                        color: '#ffffff',
                        fontStyle: 'bold'
                    }).setOrigin(0.5);

                    // 添加按钮悬停效果
                    [restartBtn, checkBtn, nextBtn].forEach(btn => {
                        btn.on('pointerover', () => {
                            btn.setScale(1.05);
                        });

                        btn.on('pointerout', () => {
                            btn.setScale(1);
                        });
                    });
                } else {
                    // 添加按钮悬停效果
                    [restartBtn, checkBtn].forEach(btn => {
                        btn.on('pointerover', () => {
                            btn.setScale(1.05);
                        });

                        btn.on('pointerout', () => {
                            btn.setScale(1);
                        });
                    });
                }

                // 添加游戏规则说明
                const rulesY = startY + 80;
                this.add.text(panelX + panelWidth / 2, rulesY, '游戏规则:', {
                    fontSize: '18px',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                const rulesText = `每行每列不能有\n重复的颜色数字`;
                this.add.text(panelX + panelWidth / 2, rulesY + 35, rulesText, {
                    fontSize: '14px',
                    color: '#cccccc',
                    align: 'center'
                }).setOrigin(0.5);
            }

            onCellClick(row, col) {
                // 如果点击的是已填充的格子，不做处理
                if (this.grid[row][col] !== null) {
                    return;
                }
                
                // 如果已有选中的格子，先重置其样式
                if (this.selectedCell !== null) {
                    const { row: prevRow, col: prevCol } = this.selectedCell;
                    if (this.grid[prevRow][prevCol] === null) {
                        this.cellSprites[prevRow][prevCol].setStrokeStyle(2, 0x4a4a8a);
                    }
                }
                
                // 设置新的选中格子
                this.selectedCell = { row, col };
                this.cellSprites[row][col].setStrokeStyle(4, 0xffffff);
                
                console.log(`选择了格子: (${row}, ${col})`);
            }

            selectColor(colorIndex) {
                // 如果没有选中的格子，显示提示
                if (this.selectedCell === null) {
                    this.showMessage('请先选择一个空格子!', 0xFF6B6B);
                    return;
                }
                
                const { row, col } = this.selectedCell;
                
                // 填入选中的颜色
                this.grid[row][col] = colorIndex;
                this.cellSprites[row][col].setFillStyle(this.colors[colorIndex]);
                this.cellSprites[row][col].setStrokeStyle(2, 0x4a4a8a);
                
                // 更新数字文本
                this.cellTexts[row][col].setText((colorIndex + 1).toString());

                // 添加放置动画
                this.tweens.add({
                    targets: this.cellSprites[row][col],
                    scaleX: 1.1,
                    scaleY: 1.1,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });

                console.log(`在 (${row}, ${col}) 放置了颜色 ${colorIndex}`);
                
                // 重置选中的格子
                this.selectedCell = null;
                
                // 检查是否完成游戏
                if (this.isGridComplete()) {
                    this.time.delayedCall(500, () => {
                        this.checkSolution();
                    });
                }
            }

            isGridComplete() {
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (this.grid[row][col] === null) {
                            return false;
                        }
                    }
                }
                return true;
            }

            checkSolution() {
                let isCorrect = true;

                // 检查所有格子是否都被填满
                if (!this.isGridComplete()) {
                    this.showMessage('请完成所有空格!', 0xFF6B6B);
                    return;
                }

                // 检查每行
                for (let row = 0; row < this.gridSize; row++) {
                    const rowColors = new Set(this.grid[row]);
                    if (rowColors.size !== this.gridSize) {
                        isCorrect = false;
                        break;
                    }
                }

                // 检查每列
                if (isCorrect) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const colColors = new Set();
                        for (let row = 0; row < this.gridSize; row++) {
                            colColors.add(this.grid[row][col]);
                        }
                        if (colColors.size !== this.gridSize) {
                            isCorrect = false;
                            break;
                        }
                    }
                }

                if (isCorrect) {
                    this.showMessage(`恭喜！第${this.currentLevel}关完成！`, 0x96CEB4);
                    this.celebrateWin();
                } else {
                    this.showMessage('答案不正确，请检查规则!', 0xFF6B6B);
                }
            }

            showMessage(text, color) {
                // 左右结构布局：消息显示在右侧面板
                const width = this.cameras.main.width;
                const rightPanelX = width * 0.65;
                const rightPanelWidth = width * 0.35;

                const messageText = this.add.text(rightPanelX + rightPanelWidth / 2, 150, text, {
                    fontSize: '16px',
                    color: `#${color.toString(16).padStart(6, '0')}`,
                    fontStyle: 'bold',
                    align: 'center'
                }).setOrigin(0.5);

                this.time.delayedCall(3000, () => {
                    messageText.destroy();
                });
            }

            celebrateWin() {
                // 添加胜利动画
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        this.time.delayedCall(row * 50 + col * 20, () => {
                            this.tweens.add({
                                targets: this.cellSprites[row][col],
                                scaleX: 1.2,
                                scaleY: 1.2,
                                duration: 300,
                                yoyo: true,
                                ease: 'Bounce.easeOut'
                            });
                        });
                    }
                }
            }

            nextLevel() {
                if (this.currentLevel < 3) {
                    this.currentLevel++;
                    this.scene.restart();
                }
            }

            restartGame() {
                // 重置选中的格子
                this.selectedCell = null;
                
                // 重新初始化当前关卡
                this.initializeGrid();

                // 重新创建游戏界面
                this.scene.restart();

                console.log(`第${this.currentLevel}关重新开始`);
            }
        }

        // 初始化游戏
        window.onload = function () {
            const config = {
                type: Phaser.AUTO,
                width: 1280,
                height: 720,
                parent: 'game-canvas',
                backgroundColor: '#1a1a2e',
                scene: [MainScene],
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH,
                    width: 1280,
                    height: 720,
                },
                physics: {
                    default: 'arcade',
                    arcade: {
                        debug: false
                    }
                }
            };

            new Phaser.Game(config);
        };
    </script>
</body>

</html>