<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>俄罗斯方块搭桥</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }

        #game-container {
            /* border: 3px solid #333; */
            border-radius: 10px;
            /* box-shadow: 0 0 20px rgba(0,0,0,0.5); */
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>

<body>
    <div id="game-container"></div>

    <script>
        // 防止多次初始化
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            // 逻辑分辨率（放大游戏区域）
            const GAME_WIDTH = 720;
            const GAME_HEIGHT = 900;

            // Phaser配置，直接用逻辑分辨率
            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000000',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let gameGrid = [];
            let currentPiece = null;
            let nextPiece = null;
            let score = 0;
            let level = 1;
            let lines = 0;
            // 记录每种方块被弹球消除的数量
            let blockCollectCount = [0, 0, 0, 0, 0, 0, 0, 0]; // 0号不用
            let dropTime = 0;
            let dropInterval = 1000;
            let gameOver = false;
            let isPaused = false;
            let fastDrop = false; // 快速下落状态
            let isClearingLines = false; // 是否正在消除行
            let clearingLines = []; // 正在消除的行
            let fallingRows = []; // 正在下落的行
            let fallAnimationStartTime = 0; // 下落动画开始时间
            let fallDropDistances = []; // 每行下落距离

            // 搭桥相关变量
            let bridgeLines = []; // 变白的桥行
            let walkingCats = []; // 正在走桥的球球
            let isBridging = false; // 是否正在搭桥过程中
            let crossedCats = []; // 已经走过桥的球球索引，不再显示在左侧

            // 动画参数
            const LINE_CLEAR_FLASH_DURATION = 150; // 行消除闪烁时长（更快）
            const LINE_CLEAR_FLASH_COUNT = 1; // 闪烁次数（2下）
            const FALL_ANIMATION_DURATION = 400; // 下落动画时长

            // 渲染优化变量
            let lastRenderState = null; // 上一帧的渲染状态

            // 游戏区域配置（放大格子并居中）
            const GRID_WIDTH = 10;
            const GRID_HEIGHT = 19;
            const CELL_SIZE = 40;
            const GRID_X = Math.floor((GAME_WIDTH - GRID_WIDTH * CELL_SIZE) / 2);
            const GRID_Y = 120;

            // 方块颜色
            const COLORS = [
                0x000000, // 空
                0xFF0000, // 红色 - I
                0x00FF00, // 绿色 - O  
                0x0000FF, // 蓝色 - T
                0xFFFF00, // 黄色 - S
                0xFF00FF, // 紫色 - Z
                0x00FFFF, // 青色 - J
                0xFFA500 // 橙色 - L
            ];

            // 方块形状定义
            const PIECES = [{
                shape: [
                    [0, 0, 0, 0],
                    [1, 1, 1, 1],
                    [0, 0, 0, 0],
                    [0, 0, 0, 0]
                ],
                color: 1
            }, {
                shape: [
                    [2, 2],
                    [2, 2]
                ],
                color: 2
            }, {
                shape: [
                    [0, 3, 0],
                    [3, 3, 3],
                    [0, 0, 0]
                ],
                color: 3
            }, {
                shape: [
                    [0, 4, 4],
                    [4, 4, 0],
                    [0, 0, 0]
                ],
                color: 4
            }, {
                shape: [
                    [5, 5, 0],
                    [0, 5, 5],
                    [0, 0, 0]
                ],
                color: 5
            }, {
                shape: [
                    [6, 0, 0],
                    [6, 6, 6],
                    [0, 0, 0]
                ],
                color: 6
            }, {
                shape: [
                    [0, 0, 7],
                    [7, 7, 7],
                    [0, 0, 0]
                ],
                color: 7
            }];

            let scene;
            let graphics;
            let parentSizer, logicSizer;

            function preload() {
                scene = this;
            }

            function create() {
                graphics = scene.add.graphics();

                parentSizer = new Phaser.Structs.Size(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer = new Phaser.Structs.Size(GAME_WIDTH, GAME_HEIGHT, Phaser.Structs.Size.FIT, parentSizer);
                parentSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);

                this.scale.on('resize', resizeGame, this);

                initGrid();
                createUI();
                spawnPiece();
                setupInput();
            }

            function resizeGame(gameSize) {
                if (!parentSizer || !logicSizer) return;
                const width = gameSize.width;
                const height = gameSize.height;
                parentSizer.setSize(width, height);
                logicSizer.setSize(width, height);
            }

            function initGrid() {
                gameGrid = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    gameGrid[y] = [];
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        gameGrid[y][x] = 0;
                    }
                }
            }

            const rightPanelX = GRID_X + GRID_WIDTH * CELL_SIZE + 20;

            function createUI() {
                scene.add.text(GAME_WIDTH * 0.5, 40, '俄罗斯方块+搭桥', {
                    fontSize: Math.floor(40) + 'px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    resolution: window.devicePixelRatio
                }).setOrigin(0.5);

                scene.scoreText = scene.add.text(60, 80, `积分: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.linesText = scene.add.text(220, 80, `行数: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#00ffff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.levelText = scene.add.text(380, 80, `等级: 1`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ff00ff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                scene.crossedText = scene.add.text(500, 80, `过河: 0/12`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#00ff00',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                scene.add.text(rightPanelX, 130, '下一个:', {
                    fontSize: Math.floor(20) + 'px',
                    fill: '#ffffff'
                });
                const ruleY = 220;
                const ruleX = rightPanelX;
                const ruleText = [
                    '球球过河:',
                    '消除行搭桥',
                    '帮助球球过河',
                    '目标: 12个全过河',
                    '',
                    '操作:',
                    '←→ 移动',
                    '↑ 旋转',
                    '↓ 加速',
                    '空格 硬降'
                ];
                for (let i = 0; i < ruleText.length; i++) {
                    scene.add.text(ruleX, ruleY + i * 22, ruleText[i], {
                        fontSize: Math.floor(i === 0 ? 16 : 14) + 'px',
                        fill: i === 0 ? '#ffff00' : '#ffffff'
                    });
                }
            }

            function setupInput() {
                scene.input.keyboard.on('keydown', (event) => {
                    if (gameOver || isPaused) return;

                    switch (event.code) {
                        case 'ArrowLeft':
                        case 'KeyA':
                            movePiece(-1, 0);
                            break;
                        case 'ArrowRight':
                        case 'KeyD':
                            movePiece(1, 0);
                            break;
                        case 'ArrowDown':
                        case 'KeyS':
                            startFastDrop();
                            break;
                        case 'ArrowUp':
                        case 'KeyW':
                            rotatePiece();
                            break;
                        case 'Space':
                            hardDrop();
                            break;
                        case 'KeyP':
                            togglePause();
                            break;
                    }
                });

                scene.input.keyboard.on('keyup', (event) => {
                    switch (event.code) {
                        case 'ArrowDown':
                        case 'KeyS':
                            stopFastDrop();
                            break;
                    }
                });
            }

            function startFastDrop() {
                if (gameOver || isPaused) return;
                fastDrop = true;
            }

            function stopFastDrop() {
                fastDrop = false;
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }

                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;

                nextPiece = createRandomPiece();

                if (checkCollision(currentPiece, 0, 0)) {
                    gameOver = true;
                    showGameOver();
                }
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                return {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0,
                    y: 0
                };
            }

            function movePiece(dx, dy) {
                if (gameOver || isPaused || !currentPiece) return false;

                if (!checkCollision(currentPiece, dx, dy)) {
                    currentPiece.x += dx;
                    currentPiece.y += dy;
                    return true;
                }
                return false;
            }

            function rotatePiece() {
                if (gameOver || isPaused || !currentPiece) return;

                const rotated = rotateMatrix(currentPiece.shape);
                const originalShape = currentPiece.shape;
                currentPiece.shape = rotated;

                if (checkCollision(currentPiece, 0, 0)) {
                    if (!checkCollision(currentPiece, -1, 0)) {
                        currentPiece.x -= 1;
                    } else if (!checkCollision(currentPiece, 1, 0)) {
                        currentPiece.x += 1;
                    } else {
                        currentPiece.shape = originalShape;
                    }
                }
            }

            function rotateMatrix(matrix) {
                const rows = matrix.length;
                const cols = matrix[0].length;
                const rotated = [];

                for (let i = 0; i < cols; i++) {
                    rotated[i] = [];
                    for (let j = 0; j < rows; j++) {
                        rotated[i][j] = matrix[rows - 1 - j][i];
                    }
                }
                return rotated;
            }

            function checkCollision(piece, dx, dy) {
                const newX = piece.x + dx;
                const newY = piece.y + dy;

                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const gridX = newX + x;
                            const gridY = newY + y;

                            if (gridX < 0 || gridX >= GRID_WIDTH || gridY >= GRID_HEIGHT) {
                                return true;
                            }

                            if (gridY >= 0 && gameGrid[gridY][gridX] !== 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            function placePiece() {
                if (!currentPiece) return;

                fastDrop = false;

                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x] !== 0) {
                            const gridX = currentPiece.x + x;
                            const gridY = currentPiece.y + y;
                            if (gridY >= 0) {
                                gameGrid[gridY][gridX] = currentPiece.color;
                            }
                        }
                    }
                }

                currentPiece = null;

                clearLinesAnimated(() => {
                    spawnPiece();
                });
            }

            function clearLinesAnimated(callback) {
                let linesToClear = [];
                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    let isFullLine = true;
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] === 0) {
                            isFullLine = false;
                            break;
                        }
                    }
                    if (isFullLine) {
                        linesToClear.push(y);
                    }
                }

                if (linesToClear.length === 0) {
                    if (callback) callback();
                    return;
                }

                isClearingLines = true;
                clearingLines = linesToClear;

                startBridging(linesToClear, callback);
                updateScore(linesToClear.length);
            }

            function startBridging(linesToClear, callback) {
                bridgeLines = [...linesToClear];

                // 先准备新的走桥球球列表，避免清空导致的闪烁
                const newWalkingCats = [];

                bridgeLines.forEach(y => {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] !== 0) {
                            gameGrid[y][x] = -1; // -1 for white bridge block
                        }
                    }
                });

                bridgeLines.forEach((y, index) => {
                    const catRowIndex = y - (GRID_HEIGHT - 12);
                    // 只有在球球还没有过河的情况下才让它过桥
                    if (catRowIndex >= 0 && catRowIndex < 12 && !crossedCats.includes(catRowIndex)) {
                        newWalkingCats.push({
                            x: GRID_X - 30,
                            y: GRID_Y + y * CELL_SIZE + CELL_SIZE / 2,
                            targetX: GRID_X + GRID_WIDTH * CELL_SIZE + 30,
                            speed: 2,
                            bridgeRow: y,
                            catRowIndex: catRowIndex,
                            colorIndex: catRowIndex,
                            delay: index * 200,
                            passedBlocks: [],
                            finished: false,
                            started: false
                        });
                    }
                });

                // 一次性更新所有状态，避免中间状态导致渲染闪烁
                walkingCats = newWalkingCats;
                isBridging = true;  // 在所有状态准备好后再设置

                const bridgeStartTime = Date.now();

                // 如果没有球球需要过桥，直接完成桥的处理
                if (walkingCats.length === 0) {
                    finishBridging(callback);
                    return;
                }

                const bridgeInterval = setInterval(() => {
                    const currentTime = Date.now();
                    let allCatsFinished = true;

                    walkingCats.forEach(cat => {
                        const elapsed = currentTime - bridgeStartTime;
                        if (elapsed > cat.delay && !cat.finished) {
                            if (!cat.started) {
                                cat.started = true;
                            }

                            if (cat.x < cat.targetX) {
                                const currentBlockX = Math.floor((cat.x - GRID_X) / CELL_SIZE);
                                if (currentBlockX >= 0 && currentBlockX < GRID_WIDTH) {
                                    const blockKey = `${currentBlockX},${cat.bridgeRow}`;
                                    if (!cat.passedBlocks.includes(blockKey) && gameGrid[cat.bridgeRow][currentBlockX] === -1) {
                                        gameGrid[cat.bridgeRow][currentBlockX] = -2;
                                        cat.passedBlocks.push(blockKey);
                                    }
                                }
                                cat.x += cat.speed;
                                allCatsFinished = false;
                            } else {
                                cat.finished = true;
                            }
                        } else if (!cat.finished) {
                            allCatsFinished = false;
                        }
                    });

                    if (allCatsFinished) {
                        clearInterval(bridgeInterval);
                        finishBridging(callback);
                    }
                }, 16);
            }

            function finishBridging(callback) {
                // 先更新过河状态，避免球球闪现
                walkingCats.forEach(cat => {
                    if (cat.finished && !crossedCats.includes(cat.catRowIndex)) {
                        crossedCats.push(cat.catRowIndex);
                    }
                });

                if (scene && scene.crossedText) {
                    scene.crossedText.setText(`过河: ${crossedCats.length}/12`);
                }

                if (crossedCats.length >= 12) {
                    showGameWin();
                    return;
                }

                // 清理桥相关状态（保持原子性，避免中间状态导致闪烁）
                isBridging = false;
                walkingCats = [];

                for (let y = 0; y < GRID_HEIGHT; y++) {
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] === -1 || gameGrid[y][x] === -2) {
                            gameGrid[y][x] = 0;
                        }
                    }
                }

                startFallAnimation(callback);
            }

            function startFallAnimation(callback) {
                fallDropDistances = new Array(GRID_HEIGHT).fill(0);
                let clearedCount = 0;
                fallingRows = [];
                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    if (clearingLines.includes(y)) {
                        clearedCount++;
                    } else {
                        fallDropDistances[y] = clearedCount;
                        if (clearedCount > 0) {
                            fallingRows.push(y);
                        }
                    }
                }

                if (fallingRows.length > 0) {
                    fallAnimationStartTime = Date.now();
                    const fallInterval = setInterval(() => {
                        const currentTime = Date.now();
                        const elapsed = currentTime - fallAnimationStartTime;
                        const progress = Math.min(elapsed / FALL_ANIMATION_DURATION, 1);

                        if (progress >= 1) {
                            clearInterval(fallInterval);
                            let newGrid = [];
                            for (let y = 0; y < GRID_HEIGHT; y++) {
                                if (!clearingLines.includes(y)) {
                                    newGrid.push([...gameGrid[y]]);
                                }
                            }
                            while (newGrid.length < GRID_HEIGHT) {
                                newGrid.unshift(new Array(GRID_WIDTH).fill(0));
                            }
                            gameGrid = newGrid;

                            fallingRows = [];
                            fallDropDistances = [];
                            isClearingLines = false;
                            clearingLines = [];
                            if (callback) callback();
                        }
                    }, 16);
                } else {
                    isClearingLines = false;
                    clearingLines = [];
                    if (callback) callback();
                }
            }

            function updateScore(linesCleared) {
                const linePoints = [0, 100, 300, 500, 800];
                score += linePoints[linesCleared] * level;
                lines += linesCleared;

                const newLevel = Math.floor(lines / 10) + 1;
                if (newLevel > level) {
                    level = newLevel;
                    dropInterval = Math.max(100, 1000 - (level - 1) * 100);
                }

                if (scene && scene.scoreText && scene.linesText && scene.levelText) {
                    scene.scoreText.setText(`积分: ${score}`);
                    scene.linesText.setText(`行数: ${lines}`);
                    scene.levelText.setText(`等级: ${level}`);
                }
            }

            function hardDrop() {
                if (gameOver || isPaused || !currentPiece) return;

                let dropDistance = 0;
                while (!checkCollision(currentPiece, 0, 1)) {
                    currentPiece.y++;
                    dropDistance++;
                }

                if (dropDistance > 0) {
                    score += dropDistance * 2;
                }

                placePiece();
            }

            function togglePause() {
                isPaused = !isPaused;
                if (isPaused) {
                    scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏暂停', {
                        fontSize: Math.floor(48) + 'px',
                        fill: '#ffff00',
                        fontStyle: 'bold'
                    }).setOrigin(0.5).setName('pauseText');
                } else {
                    const pauseText = scene.children.getByName('pauseText');
                    if (pauseText) pauseText.destroy();
                }
            }

            function showGameOver() {
                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏结束', {
                    fontSize: Math.floor(48) + 'px',
                    fill: '#ff0000',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('gameOverText');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 60, '点击重新开始', {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5).setName('restartText');

                scene.input.once('pointerdown', restartGame);
            }



            function showGameWin() {
                gameOver = true;

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 - 40, '🎉 恭喜！🎉', {
                    fontSize: Math.floor(48) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('gameWinTitle');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '所有球球都过河了！', {
                    fontSize: Math.floor(36) + 'px',
                    fill: '#00ff00',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('gameWinText');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 50, `最终得分: ${score}`, {
                    fontSize: Math.floor(28) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5).setName('finalScoreText');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 90, '点击重新开始', {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5).setName('restartText');

                scene.input.once('pointerdown', restartGame);
            }

            function restartGame() {
                score = 0;
                level = 1;
                lines = 0;
                dropTime = 0;
                dropInterval = 1000;
                gameOver = false;
                isPaused = false;

                isBridging = false;
                bridgeLines = [];
                walkingCats = [];
                crossedCats = [];
                isClearingLines = false;
                clearingLines = [];
                fallingRows = [];
                fallDropDistances = [];

                if (scene && scene.scoreText && scene.linesText && scene.levelText && scene.crossedText) {
                    scene.scoreText.setText('积分: 0');
                    scene.linesText.setText('行数: 0');
                    scene.levelText.setText('等级: 1');
                    scene.crossedText.setText('过河: 0/12');
                }

                initGrid();
                spawnPiece();

                const gameOverText = scene.children.getByName('gameOverText');
                if (gameOverText) gameOverText.destroy();
                const restartText = scene.children.getByName('restartText');
                if (restartText) restartText.destroy();

                const gameWinTitle = scene.children.getByName('gameWinTitle');
                if (gameWinTitle) gameWinTitle.destroy();
                const gameWinText = scene.children.getByName('gameWinText');
                if (gameWinText) gameWinText.destroy();
                const finalScoreText = scene.children.getByName('finalScoreText');
                if (finalScoreText) finalScoreText.destroy();
            }

            function update(time, delta) {
                if (gameOver || isPaused) return;

                dropTime += delta;

                const currentDropInterval = fastDrop ? Math.max(50, dropInterval / 10) : dropInterval;

                if (dropTime >= currentDropInterval) {
                    if (currentPiece) {
                        if (!movePiece(0, 1)) {
                            placePiece();
                            if (fastDrop) {
                                score += 1;
                            }
                        } else if (fastDrop) {
                            score += 1;
                        }
                    }
                    dropTime = 0;
                }
                render();
            }

            // =================================================================
            // START OF MODIFIED RENDER FUNCTION
            // =================================================================
            function render() {
                graphics.clear();

                // 绘制背景和边框
                if (fastDrop) {
                    graphics.fillStyle(0x444444, 0.3);
                    graphics.fillRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);
                }
                graphics.lineStyle(Math.floor(3), fastDrop ? 0x00ff00 : 0xffffff);
                graphics.strokeRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);

                // 绘制网格线
                graphics.lineStyle(Math.max(1, Math.floor(1)), 0x333333);
                graphics.beginPath();
                for (let x = 0; x <= GRID_WIDTH; x++) {
                    graphics.moveTo(GRID_X + x * CELL_SIZE, GRID_Y);
                    graphics.lineTo(GRID_X + x * CELL_SIZE, GRID_Y + GRID_HEIGHT * CELL_SIZE);
                }
                for (let y = 0; y <= GRID_HEIGHT; y++) {
                    graphics.moveTo(GRID_X, GRID_Y + y * CELL_SIZE);
                    graphics.lineTo(GRID_X + GRID_WIDTH * CELL_SIZE, GRID_Y + y * CELL_SIZE);
                }
                graphics.strokePath();

                // 核心修改点：将所有与下落动画相关的绘制逻辑整合到一起
                if (fallingRows.length > 0 && fallAnimationStartTime > 0) {
                    const currentTime = Date.now();
                    const elapsed = currentTime - fallAnimationStartTime;
                    const progress = Math.min(elapsed / FALL_ANIMATION_DURATION, 1);
                    const easeProgress = 1 - Math.pow(1 - progress, 3);

                    // 绘制所有静止的方块
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        if ((!fallingRows.includes(y)) && (!clearingLines.includes(y))) {
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]]);
                                }
                            }
                        }
                    }

                    // 绘制下落中的方块
                    fallingRows.forEach(row => {
                        const dropDistance = fallDropDistances[row];
                        const currentDrop = dropDistance * easeProgress;
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[row][x] !== 0) {
                                drawCell(x, row + currentDrop, COLORS[gameGrid[row][x]]);
                            }
                        }
                    });

                    // 【修改】在动画期间，绘制所有球球（保持固定位置）
                    // 球球不跟随方块下落，始终保持在固定的屏幕位置
                    for (let i = 0; i < 12; i++) {
                        const ballRow = GRID_HEIGHT - 12 + i;
                        // 球球位置固定，不受下落动画影响
                        let ballY = GRID_Y + ballRow * CELL_SIZE + CELL_SIZE / 2;

                        // 判断并绘制小猫
                        const hasCrossed = crossedCats.includes(i);
                        let isWalking = false;
                        if(isBridging) {
                            for (const cat of walkingCats) {
                                if (cat.catRowIndex === i && cat.started) {
                                    isWalking = true;
                                    break;
                                }
                            }
                        }

                        if (hasCrossed) {
                            // 绘制右侧已过河的小猫
                            drawSingleBall(GRID_X + GRID_WIDTH * CELL_SIZE + 30, ballY, i);
                        } else if (!isWalking) {
                             // 绘制左侧等待的小猫
                             drawSingleBall(GRID_X - 30, ballY, i);
                        }
                        // 正在走路的小猫由独立的逻辑绘制，此处不处理
                    }

                } else {
                    // 原始的非动画状态渲染逻辑保持不变
                    if (isBridging) {
                        for (let y = 0; y < GRID_HEIGHT; y++) {
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    if (gameGrid[y][x] === -1) {
                                        drawCell(x, y, 0xffffff, 0.8); // 桥
                                    } else if (gameGrid[y][x] === -2) {
                                        drawCell(x, y, 0xffffff, 0.5); // 走过的桥
                                    } else {
                                        drawCell(x, y, COLORS[gameGrid[y][x]]);
                                    }
                                }
                            }
                        }
                    } else if (isClearingLines && clearingLines.length > 0) {
                        // 绘制非消除行的方块
                        for (let y = 0; y < GRID_HEIGHT; y++) {
                            if (clearingLines.includes(y)) continue;
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]]);
                                }
                            }
                        }
                        // 消除行闪烁效果
                        const currentTime = Date.now();
                        const flashCycle = LINE_CLEAR_FLASH_DURATION * 2;
                        const timeInCycle = currentTime % flashCycle;
                        const isVisible = timeInCycle < LINE_CLEAR_FLASH_DURATION;
                        if (isVisible) {
                            clearingLines.forEach(y => {
                                for (let x = 0; x < GRID_WIDTH; x++) {
                                    if (gameGrid[y][x] !== 0) {
                                        drawCell(x, y, 0xffffff, 0.9);
                                    }
                                }
                            });
                        }
                    } else {
                        // 正常状态：绘制所有方块
                        for (let y = 0; y < GRID_HEIGHT; y++) {
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]]);
                                }
                            }
                        }
                    }
                    
                    // 【修改】仅在非下落动画状态下，才调用独立的绘制函数
                    // 这样可以避免在动画期间发生绘制冲突
                    drawBalls();
                    drawCrossedBalls();
                }

                // --- 绘制玩家方块、鬼影和下一个方块（这部分逻辑不变） ---

                if (currentPiece) {
                    const ghostPiece = { ...currentPiece,
                        y: currentPiece.y
                    };
                    while (!checkCollision(ghostPiece, 0, 1)) {
                        ghostPiece.y++;
                    }
                    for (let y = 0; y < ghostPiece.shape.length; y++) {
                        for (let x = 0; x < ghostPiece.shape[y].length; x++) {
                            if (ghostPiece.shape[y][x] !== 0) {
                                const gridX = ghostPiece.x + x;
                                const gridY = ghostPiece.y + y;
                                if (gridY >= 0 && gridY !== currentPiece.y + y) {
                                    drawGhostCell(gridX, gridY, COLORS[ghostPiece.color]);
                                }
                            }
                        }
                    }
                }

                if (currentPiece) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;
                                if (gridY >= 0) {
                                    drawCell(gridX, gridY, COLORS[currentPiece.color]);
                                }
                            }
                        }
                    }
                }

                if (nextPiece) {
                    const previewX = rightPanelX;
                    const previewY = 160;
                    const previewCellSize = Math.floor(20);
                    for (let y = 0; y < nextPiece.shape.length; y++) {
                        for (let x = 0; x < nextPiece.shape[y].length; x++) {
                            if (nextPiece.shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * previewCellSize,
                                    previewY + y * previewCellSize,
                                    previewCellSize - 1,
                                    previewCellSize - 1
                                );
                            }
                        }
                    }
                }
                
                // --- 绘制正在过桥的小猫（这部分逻辑不变） ---
                if (isBridging && walkingCats.length > 0) {
                    walkingCats.forEach(cat => {
                        if (cat.started && !cat.finished) {
                            drawSingleBall(cat.x, cat.y, cat.colorIndex);
                        }
                    });
                }
            }
            // =================================================================
            // END OF MODIFIED RENDER FUNCTION
            // =================================================================


            // 缓存颜色计算结果，避免重复计算
            const colorCache = new Map();

            function getColorVariants(color) {
                if (colorCache.has(color)) {
                    return colorCache.get(color);
                }

                const rgb = Phaser.Display.Color.IntegerToRGB(color);
                const highlightColor = Phaser.Display.Color.GetColor32(
                    Math.min(255, rgb.r + 60),
                    Math.min(255, rgb.g + 60),
                    Math.min(255, rgb.b + 60),
                    255
                );
                const shadowColor = Phaser.Display.Color.GetColor32(
                    Math.max(0, rgb.r - 60),
                    Math.max(0, rgb.g - 60),
                    Math.max(0, rgb.b - 60),
                    255
                );

                const variants = { highlight: highlightColor, shadow: shadowColor };
                colorCache.set(color, variants);
                return variants;
            }

            function drawCell(x, y, color, alpha = 1) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 主体颜色
                graphics.fillStyle(color, alpha);
                graphics.fillRect(pixelX + 1, pixelY + 1, CELL_SIZE - 2, CELL_SIZE - 2);

                // 获取缓存的颜色变体
                const { highlight, shadow } = getColorVariants(color);

                // 高光效果
                graphics.fillStyle(highlight, alpha);
                graphics.fillRect(pixelX + 1, pixelY + 1, CELL_SIZE - 2, 4);
                graphics.fillRect(pixelX + 1, pixelY + 1, 4, CELL_SIZE - 2);

                // 阴影效果
                graphics.fillStyle(shadow, alpha);
                graphics.fillRect(pixelX + CELL_SIZE - 5, pixelY + 1, 4, CELL_SIZE - 2);
                graphics.fillRect(pixelX + 1, pixelY + CELL_SIZE - 5, CELL_SIZE - 2, 4);
            }

            function drawGhostCell(x, y, color) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;
                graphics.fillStyle(color, 0.5);
                graphics.fillRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);
                graphics.lineStyle(2, color, 0.5);
                graphics.strokeRect(pixelX + 2, pixelY + 2, CELL_SIZE - 4, CELL_SIZE - 4);
            }

            function drawBalls() {
                // 这个函数现在只在非下落动画时被调用
                for (let i = 0; i < 12; i++) {
                    const ballRow = GRID_HEIGHT - 12 + i;
                    let ballY = GRID_Y + ballRow * CELL_SIZE + CELL_SIZE / 2;

                    const hasCrossed = crossedCats.includes(i);
                    let isWalking = false;

                    if (walkingCats.length > 0) {
                        for (let j = 0; j < walkingCats.length; j++) {
                            const cat = walkingCats[j];
                            if (cat.catRowIndex === i && cat.started) {
                                isWalking = true;
                                break;
                            }
                        }
                    }
                    
                    if (!isWalking && !hasCrossed) {
                        drawSingleBall(GRID_X - 30, ballY, i);
                    }
                }
            }

            function drawCrossedBalls() {
                // 这个函数现在只在非下落动画时被调用
                crossedCats.forEach(catIndex => {
                    const rightX = GRID_X + GRID_WIDTH * CELL_SIZE + 30;
                    const ballRow = GRID_HEIGHT - 12 + catIndex;
                    let rightY = GRID_Y + ballRow * CELL_SIZE + CELL_SIZE / 2;
                    drawSingleBall(rightX, rightY, catIndex);
                });
            }

            function drawSingleBall(ballCenterX, ballCenterY, ballIndex) {
                const ballColors = [
                    0xFFA500, 0xFF69B4, 0x87CEEB, 0x98FB98, 0xDDA0DD, 0xF0E68C,
                    0xFFB6C1, 0xADD8E6, 0xF5DEB3, 0xFFE4E1, 0xE0FFFF, 0xFFFACD
                ];
                const ballColor = ballColors[ballIndex % ballColors.length];

                // 绘制简单的球球
                graphics.fillStyle(ballColor);
                graphics.fillCircle(ballCenterX, ballCenterY, 8);

                // 添加高光效果让球球更立体
                const { highlight } = getColorVariants(ballColor);
                graphics.fillStyle(highlight, 0.6);
                graphics.fillCircle(ballCenterX - 2, ballCenterY - 2, 3);

                // 添加简单的眼睛
                graphics.fillStyle(0x000000);
                graphics.fillCircle(ballCenterX - 2, ballCenterY, 1);
                graphics.fillCircle(ballCenterX + 2, ballCenterY, 1);
            }


            
            const game = new Phaser.Game(config);
        });
    </script>
</body>

</html>