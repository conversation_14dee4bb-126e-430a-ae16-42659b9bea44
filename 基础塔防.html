<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, monsters = [];
    let playerHealth = 100, maxPlayerHealth = 100;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'playing';
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

      

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6); // 缩小到0.5
        player.setOrigin(0.5, 1); // 设置旋转中心在底部

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建6x6格子
        createGrid.call(this);
    }

    // 创建波次怪物
    function createWave() {
        // 清除现有怪物
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 创建新怪物 - 全部为近战怪物，初始位置在右边
        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = 600; // 初始位置在右边
            const yPos = player.y + i * 20;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
        }

        monstersKilled = 0;
    }

    // 创建UI界面
    function createUI() {
        // 玩家血条背景 - 移到顶部
        this.add.rectangle(150, 80, 200, 20, 0x333333);
        // 玩家血条 - 修复缩放中心
        this.playerHealthBar = this.add.rectangle(150, 80, 200, 20, 0x27ae60);
        this.playerHealthBar.setOrigin(0.5, 0.5); // 设置缩放中心为中心点

        // UI文本 - 移到顶部
        this.levelText = this.add.text(50, 110, `关卡: ${currentLevel}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });
        this.waveText = this.add.text(200, 110, `波次: ${currentWave}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });
        this.healthText = this.add.text(350, 110, `生命: ${playerHealth}/${maxPlayerHealth}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });

        // 怪物血条容器
        this.monsterHealthBars = [];
    }

    // 创建6x6格子
    function createGrid() {
        const cellSize = 100;
        const gridWidth = 6 * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2; // 居中计算
        const startY = 650;

        // 创建父级容器
        this.gridContainer = this.add.container(0, 0);

        for (let row = 0; row < 6; row++) {
            grid[row] = [];
            for (let col = 0; col < 6; col++) {
                // 创建圆角矩形
                const graphics = this.add.graphics();
                graphics.fillStyle(0xcfcccf); // 白色填充
                graphics.lineStyle(2, 0xe0e0e0); // 浅灰色边框
                graphics.fillRoundedRect(-45, -45, 90, 90, 15); // 圆角半径15
                graphics.strokeRoundedRect(-45, -45, 90, 90, 15);

                // 设置位置（相对于容器）
                graphics.x = startX + col * cellSize;
                graphics.y = startY + row * cellSize;

                // 添加属性
                graphics.row = row;
                graphics.col = col;

                // 将格子添加到容器中
                this.gridContainer.add(graphics);

                grid[row][col] = graphics;
            }
        }

        // 添加格子区域标题
        const gridTitle = this.add.text(375, 600, '格子区域', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
        
        // 将标题也添加到容器中
        this.gridContainer.add(gridTitle);
    }

    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            const target = monsters[0]; // 攻击第一个怪物

            // 创建攻击特效
            const projectile = this.add.circle(player.x + 30, player.y - 20, 5, 0xffff00);

            // 攻击动画
            this.tweens.add({
                targets: projectile,
                x: target.x - 30,
                y: target.y,
                duration: 500,
                onComplete: () => {
                    projectile.destroy();

                    // 造成伤害
                    target.health -= 15 + currentLevel * 2;

                    // 伤害特效 - 减小闪动幅度
                    this.tweens.add({
                        targets: target,
                        scaleX: 0.27, // 轻微放大，避免过大闪动
                        scaleY: 0.27,
                        duration: 150,
                        yoyo: true
                    });

                    // 检查怪物是否死亡
                    if (target.health <= 0) {
                        target.destroy();
                        monsters.shift();
                        monstersKilled++;
                    }
                }
            });

            // 主角攻击动画 - 向前倾斜
            this.tweens.add({
                targets: player,
                rotation: 0.3, // 向前倾斜约17度
                duration: 150,
                yoyo: true,
                ease: 'Power2'
            });

            // 同时添加轻微的缩放效果
            this.tweens.add({
                targets: player,
                scaleX: 0.55,
                scaleY: 0.55,
                duration: 150,
                yoyo: true
            });
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        if (gameState !== 'playing') return;

        // 更新战斗
        updateBattle.call(this, time);

        // 更新UI
        updateUI.call(this);

        // 检查波次完成
        checkWaveComplete.call(this);

        // 检查游戏结束
        if (playerHealth <= 0) {
            gameState = 'gameOver';
            this.add.text(375, 667, 'GAME OVER', {
                fontSize: '48px',
                fill: '#e74c3c',
                fontFamily: 'Arial'
            }).setOrigin(0.5);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角自动攻击
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > 2000 && monsters.length > 0) { // 每2秒攻击一次
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 10000, // 从2500增加到10000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.2, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                const damage = 12 + currentLevel;
                playerHealth -= damage;
                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新玩家血条 - 修复缩放问题
        const healthPercent = playerHealth / maxPlayerHealth;
        this.playerHealthBar.scaleX = healthPercent;
        // 调整血条位置，使其从左边开始缩放
        this.playerHealthBar.x = 50 + (200 * healthPercent) / 2;
        this.playerHealthBar.fillColor = healthPercent > 0.5 ? 0x27ae60 :
                                        healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;

        // 更新文本
        this.levelText.setText(`关卡: ${currentLevel}`);
        this.waveText.setText(`波次: ${currentWave}`);
        this.healthText.setText(`生命: ${Math.max(0, playerHealth)}/${maxPlayerHealth}`);

        // 更新怪物血条 - 显示在头上
        this.monsterHealthBars.forEach(bar => bar.destroy());
        this.monsterHealthBars = [];

        monsters.forEach((monster, index) => {
            const healthPercent = monster.health / monster.maxHealth;

            // 血条背景
            const barBg = this.add.rectangle(
                monster.x,
                monster.y - 60, // 调整到头部上方
                40,
                6,
                0x333333
            );
            this.monsterHealthBars.push(barBg);

            // 血条前景
            const bar = this.add.rectangle(
                monster.x - 20 + (40 * healthPercent) / 2, // 从左边开始填充
                monster.y - 60,
                40 * healthPercent,
                6,
                healthPercent > 0.5 ? 0x27ae60 : 0xe74c3c
            );
            this.monsterHealthBars.push(bar);
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        if (monsters.length === 0) {
            currentWave++;
            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
            }, 1000);
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
