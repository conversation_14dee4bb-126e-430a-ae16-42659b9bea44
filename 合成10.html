<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1-9数字拖拽消消乐</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .game-title {
            color: white;
            font-size: 2.5em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 20px;
        }
        .game-info {
            color: white;
            text-align: center;
            margin-top: 30px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        #game-container {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <h1 class="game-title">🎮合成10消消乐 </h1>
   
    <div id="game-container"></div>
    <div class="game-info">
        <p>🎯 游戏规则：拖拽选择数字，让它们相加等于10来消除！
        💡 最多同时选择5个数字 | 🌈 每个数字都有独特的颜色</p>
        <p>✨ 新玩法：双击>5的数字可以分裂成两个较小数字！</p>
    </div>
    <script>
        class NumberDragGame extends Phaser.Scene {
            constructor() {
                super({ key: 'NumberDragGame' });
                this.gridSize = 8;
                this.tileSize = 70;
                this.grid = [];
                this.selectedTiles = [];
                this.isDragging = false;
                this.score = 0;
                this.moves = 0;
                this.tilesRemaining = 64;
                this.gameStartTime = 0;
                this.gameWon = false;
                this.selectionPath = [];
                this.maxSelection = 5;
                this.lastClickTime = 0;
                this.doubleClickDelay = 300; // 双击间隔时间
                
                // 1-9数字池
                this.numberPool = [
                    { value: 1, display: '1' },
                    { value: 2, display: '2' },
                    { value: 3, display: '3' },
                    { value: 4, display: '4' },
                    { value: 5, display: '5' },
                    { value: 6, display: '6' },
                    { value: 7, display: '7' },
                    { value: 8, display: '8' },
                    { value: 9, display: '9' }
                ];

                // 每个数字对应的颜色配置（使用较低饱和度的颜色）
                this.numberColors = {
                    1: { main: 0x59fc83, border: 0x697A9A, light: 0xF0E68C }, // 浅蓝
                    2: { main: 0xfc3e8d, border: 0x6B8E6B, light: 0xF0E68C }, // 浅绿
                    3: { main: 0x0369ff, border: 0xB07AB0, light: 0xF0E68C }, // 浅紫
                    4: { main: 0xff4cff, border: 0xBFB75A, light: 0xF0E68C }, // 浅黄
                    5: { main: 0xFFA07A, border: 0xCD5C5C, light: 0xF0E68C }, // 浅橙
                    6: { main: 0x87CEEB, border: 0x5F9EA0, light: 0xF0E68C }, // 天蓝
                    7: { main: 0xF4A460, border: 0xD2691E, light: 0xF0E68C }, // 沙棕
                    8: { main: 0xDC143C, border: 0xA0001E, light: 0xF0E68C }, // 深红
                    9: { main: 0x9370DB, border: 0x6A4B93, light: 0xB191E8 }  // 中紫
                };

                // 数字分裂组合
                this.splitCombinations = {
                    6: [[1, 5], [2, 4], [3, 3]],
                    7: [[1, 6], [2, 5], [3, 4]],
                    8: [[1, 7], [2, 6], [3, 5], [4, 4]],
                    9: [[1, 8], [2, 7], [3, 6], [4, 5]]
                };
            }

            preload() {
                this.createNumberTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(450, 375, 900, 750, 0x2c3e50);
                
                // 创建信息面板背景
                this.add.rectangle(450, 60, 880, 60, 0x34495e);

                // 显示信息
                this.scoreText = this.add.text(80, 60, '分数: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                this.movesText = this.add.text(250, 60, '移动: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tilesText = this.add.text(420, 60, '剩余: 64', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.timeText = this.add.text(590, 60, '时间: 0:00', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 当前选择的数字和总和显示
                this.selectionText = this.add.text(450, 680, '当前选择: 无', {
                    fontSize: '18px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    padding: { x: 10, y: 5 }
                }).setOrigin(0.5);

                // 刷新按钮
                const refreshBtn = this.add.rectangle(770, 60, 140, 45, 0xe74c3c);
                refreshBtn.setStrokeStyle(3, 0xc0392b);
                this.add.text(770, 60, '刷新', {
                    fontSize: '18px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                refreshBtn.setInteractive();
                refreshBtn.on('pointerdown', () => this.shuffleGrid());

                // 初始化游戏
                this.initializeGrid();
                this.createGrid();
                
                // 设置输入事件
                this.input.on('pointerup', () => this.endDrag());
                
                // 开始计时
                this.gameStartTime = this.time.now;
                this.timeEvent = this.time.addEvent({
                    delay: 1000,
                    callback: this.updateTime,
                    callbackScope: this,
                    loop: true
                });
            }

            createNumberTextures() {
                const graphics = this.add.graphics();
                
                // 为每个数字创建专用纹理
                for (let num = 1; num <= 9; num++) {
                    const colors = this.numberColors[num];
                    
                    graphics.clear();
                    graphics.fillStyle(colors.main);
                    graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    graphics.lineStyle(4, colors.border);
                    graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                    graphics.fillGradientStyle(colors.light, colors.main, colors.border, colors.main, 1);
                    graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                    graphics.generateTexture(`number_${num}`, this.tileSize - 6, this.tileSize - 6);
                }

                // 创建选中效果纹理
                graphics.clear();
                graphics.fillStyle(0xf1c40f);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(6, 0xf39c12);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.generateTexture('selected', this.tileSize - 6, this.tileSize - 6);

                graphics.destroy();
            }

            initializeGrid() {
                this.grid = [];
                
                for (let row = 0; row < this.gridSize; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.gridSize; col++) {
                        const randomNumber = Phaser.Utils.Array.GetRandom(this.numberPool);
                        this.grid[row][col] = {
                            value: randomNumber.value,
                            display: randomNumber.display,
                            sprite: null,
                            text: null,
                            isEmpty: false,
                            row: row,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };
                    }
                }
            }

            createGrid() {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const tile = this.grid[row][col];
                        if (!tile.isEmpty) {
                            this.createTile(tile, startX, startY);
                        }
                    }
                }
            }

            createTile(tile, startX, startY) {
                const x = startX + tile.col * this.tileSize + this.tileSize / 2;
                const y = startY + tile.row * this.tileSize + this.tileSize / 2;

                const textureName = `number_${tile.value}`;
                const sprite = this.add.image(x, y, textureName);
                
                // 添加数字文本
                const text = this.add.text(x, y, tile.display, {
                    fontSize: '22px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    fontFamily: 'Arial, sans-serif',
                    stroke: '#333333',
                    strokeThickness: 2,
                    shadow: {
                        offsetX: 1,
                        offsetY: 1,
                        color: '#000000',
                        blur: 2,
                        fill: true
                    }
                }).setOrigin(0.5);

                // 设置交互事件
                this.setupTileInteraction(sprite, tile);
                
                tile.sprite = sprite;
                tile.text = text;
                tile.isSelected = false;
            }

            setupTileInteraction(sprite, tile) {
                if (!sprite || sprite.destroyed) return;
                
                sprite.setInteractive();
                sprite.removeAllListeners();
                
                sprite.on('pointerdown', () => {
                    if (!tile.isEmpty) {
                        this.handleTileClick(tile);
                    }
                });
                sprite.on('pointerover', () => {
                    if (this.isDragging && !tile.isEmpty) {
                        this.addToSelection(tile);
                    }
                    if (!tile.isEmpty && !tile.isSelected && sprite && !sprite.destroyed) {
                        sprite.setScale(1.05);
                    }
                });
                sprite.on('pointerout', () => {
                    if (!tile.isEmpty && !tile.isSelected && sprite && !sprite.destroyed) {
                        sprite.setScale(1);
                    }
                });
            }

            handleTileClick(tile) {
                const currentTime = this.time.now;
                const timeSinceLastClick = currentTime - tile.lastClickTime;
                
                if (timeSinceLastClick < this.doubleClickDelay) {
                    // 双击检测
                    this.handleDoubleClick(tile);
                } else {
                    // 单击 - 开始拖拽
                    this.startDrag(tile);
                }
                
                tile.lastClickTime = currentTime;
            }

            handleDoubleClick(tile) {
                // 只有大于5的数字才能分裂
                if (tile.value <= 5) {
                    this.showMessage('只有大于5的数字才能分裂！', 0xff0000);
                    return;
                }

                // 检查是否有空位置
                const emptyPositions = this.getEmptyPositions();
                if (emptyPositions.length < 2) {
                    this.showMessage('没有足够的空位置进行分裂！', 0xff0000);
                    return;
                }

                // 执行分裂
                this.splitNumber(tile, emptyPositions);
            }

            getEmptyPositions() {
                const emptyPositions = [];
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (this.grid[row][col].isEmpty) {
                            emptyPositions.push({ row, col });
                        }
                    }
                }
                return emptyPositions;
            }

            splitNumber(tile, emptyPositions) {
                const combinations = this.splitCombinations[tile.value];
                const selectedCombination = Phaser.Utils.Array.GetRandom(combinations);
                
                // 随机选择两个空位置
                const shuffledPositions = Phaser.Utils.Array.Shuffle([...emptyPositions]);
                const position1 = shuffledPositions[0];
                const position2 = shuffledPositions[1];

                // 消除原数字
                this.tweens.add({
                    targets: [tile.sprite, tile.text],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        if (tile.sprite && !tile.sprite.destroyed) {
                            tile.sprite.destroy();
                        }
                        if (tile.text && !tile.text.destroyed) {
                            tile.text.destroy();
                        }
                        tile.sprite = null;
                        tile.text = null;
                        tile.isEmpty = true;
                    }
                });

                // 延迟创建新数字
                this.time.delayedCall(400, () => {
                    this.createSplitNumbers(selectedCombination, position1, position2);
                    this.showMessage(`${tile.value} 分裂成 ${selectedCombination[0]} + ${selectedCombination[1]}！`, 0x00ff00);
                    
                    // 应用重力效果
                    this.time.delayedCall(500, () => {
                        this.applyGravity();
                    });
                });
            }

            createSplitNumbers(combination, pos1, pos2) {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;

                // 创建第一个数字
                this.grid[pos1.row][pos1.col] = {
                    value: combination[0],
                    display: combination[0].toString(),
                    sprite: null,
                    text: null,
                    isEmpty: false,
                    row: pos1.row,
                    col: pos1.col,
                    isSelected: false,
                    lastClickTime: 0
                };
                this.createTile(this.grid[pos1.row][pos1.col], startX, startY);

                // 创建第二个数字
                this.grid[pos2.row][pos2.col] = {
                    value: combination[1],
                    display: combination[1].toString(),
                    sprite: null,
                    text: null,
                    isEmpty: false,
                    row: pos2.row,
                    col: pos2.col,
                    isSelected: false,
                    lastClickTime: 0
                };
                this.createTile(this.grid[pos2.row][pos2.col], startX, startY);

                // 添加出现动画
                const newTiles = [
                    this.grid[pos1.row][pos1.col],
                    this.grid[pos2.row][pos2.col]
                ];

                newTiles.forEach(newTile => {
                    if (newTile.sprite && newTile.text) {
                        newTile.sprite.setScale(0);
                        newTile.text.setScale(0);
                        this.tweens.add({
                            targets: [newTile.sprite, newTile.text],
                            scaleX: 1,
                            scaleY: 1,
                            duration: 400,
                            ease: 'Back.easeOut'
                        });
                    }
                });

                // 更新剩余数量（分裂增加了一个数字）
                this.tilesRemaining += 1;
                this.tilesText.setText(`剩余: ${this.tilesRemaining}`);
            }

            startDrag(tile) {
                if (tile.isEmpty) return;
                
                this.isDragging = true;
                this.clearSelection();
                this.selectedTiles = [];
                this.selectionPath = [];
                this.addToSelection(tile);
            }

            addToSelection(tile) {
                if (tile.isEmpty || tile.isSelected || this.selectedTiles.length >= this.maxSelection) {
                    return;
                }

                // 检查是否相邻（只有第一个可以任意选择）
                if (this.selectedTiles.length > 0) {
                    const lastTile = this.selectedTiles[this.selectedTiles.length - 1];
                    if (!this.isAdjacent(lastTile, tile)) {
                        return;
                    }
                }

                this.selectedTiles.push(tile);
                tile.isSelected = true;
                if (tile.sprite && !tile.sprite.destroyed) {
                    tile.sprite.setTint(0xFF00FF);
                    tile.sprite.setScale(1.2);
                }
                
                this.updateSelectionDisplay();
            }

            isAdjacent(tile1, tile2) {
                const rowDiff = Math.abs(tile1.row - tile2.row);
                const colDiff = Math.abs(tile1.col - tile2.col);
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }

            endDrag() {
                if (!this.isDragging) return;
                
                this.isDragging = false;
                
                // 检查选中的数字是否相加等于10
                const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);
                
                if (sum === 10 && this.selectedTiles.length > 1) {
                    this.eliminateSelectedTiles();
                } else {
                    this.clearSelection();
                    if (this.selectedTiles.length > 1) {
                        this.showMessage(`总和不等于10 (${sum})，请重新选择！`, 0xff0000);
                    }
                }
            }

            eliminateSelectedTiles() {
                // 计算分数
                const bonus = this.selectedTiles.length * 10;
                this.score += bonus;
                
                let animationsCompleted = 0;
                const totalAnimations = this.selectedTiles.length;
                
                // 添加消除动画
                this.selectedTiles.forEach((tile, index) => {
                    if (tile.sprite && tile.text) {
                        this.tweens.add({
                            targets: [tile.sprite, tile.text],
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                try {
                                    if (tile.sprite && !tile.sprite.destroyed) {
                                        tile.sprite.destroy();
                                    }
                                    if (tile.text && !tile.text.destroyed) {
                                        tile.text.destroy();
                                    }
                                    tile.sprite = null;
                                    tile.text = null;
                                    tile.isEmpty = true;
                                    tile.isSelected = false;
                                    
                                    animationsCompleted++;
                                    if (animationsCompleted === totalAnimations) {
                                        this.onAllAnimationsComplete();
                                    }
                                } catch (error) {
                                    console.warn('Animation cleanup error:', error);
                                    animationsCompleted++;
                                    if (animationsCompleted === totalAnimations) {
                                        this.onAllAnimationsComplete();
                                    }
                                }
                            }
                        });
                    } else {
                        tile.isEmpty = true;
                        tile.sprite = null;
                        tile.text = null;
                        tile.isSelected = false;
                        animationsCompleted++;
                        if (animationsCompleted === totalAnimations) {
                            this.onAllAnimationsComplete();
                        }
                    }
                });

                // 更新统计
                this.tilesRemaining -= this.selectedTiles.length;
                this.moves++;
                this.scoreText.setText(`分数: ${this.score}`);
                this.movesText.setText(`移动: ${this.moves}`);
                this.tilesText.setText(`剩余: ${this.tilesRemaining}`);

                // 显示成功消息
                const displayValues = this.selectedTiles.map(tile => tile.display).join(' + ');
                this.showMessage(`消除成功！ ${displayValues} = 10`, 0x00ff00);

                this.clearSelection();
            }

            onAllAnimationsComplete() {
                // 检查游戏是否结束
                if (this.tilesRemaining === 0) {
                    this.gameWon = true;
                    this.showGameComplete();
                } else {
                    // 应用重力效果
                    this.applyGravity();
                }
            }

            clearSelection() {
                this.selectedTiles.forEach(tile => {
                    if (!tile.isEmpty && tile.sprite && !tile.sprite.destroyed) {
                        tile.isSelected = false;
                        tile.sprite.clearTint();
                        tile.sprite.setScale(1);
                    }
                });
                this.selectedTiles = [];
                this.updateSelectionDisplay();
            }

            updateSelectionDisplay() {
                if (this.selectedTiles.length === 0) {
                    this.selectionText.setText('当前选择: 无');
                } else {
                    const displayValues = this.selectedTiles.map(tile => tile.display);
                    const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);
                    this.selectionText.setText(`当前选择: ${displayValues.join(' + ')} = ${sum}`);
                }
            }

            applyGravity() {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;
                
                // 处理重力效果
                for (let col = 0; col < this.gridSize; col++) {
                    const column = [];
                    
                    // 收集该列的非空方块
                    for (let row = this.gridSize - 1; row >= 0; row--) {
                        if (!this.grid[row][col].isEmpty) {
                            column.push({
                                value: this.grid[row][col].value,
                                display: this.grid[row][col].display,
                                sprite: this.grid[row][col].sprite,
                                text: this.grid[row][col].text
                            });
                        }
                    }
                    
                    // 清空该列
                    for (let row = 0; row < this.gridSize; row++) {
                        this.grid[row][col] = {
                            value: 0,
                            display: '0',
                            sprite: null,
                            text: null,
                            isEmpty: true,
                            row: row,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };
                    }
                    
                    // 从底部开始填充
                    for (let i = 0; i < column.length; i++) {
                        const newRow = this.gridSize - 1 - i;
                        this.grid[newRow][col] = {
                            value: column[i].value,
                            display: column[i].display,
                            sprite: column[i].sprite,
                            text: column[i].text,
                            isEmpty: false,
                            row: newRow,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };
                        
                        // 更新精灵位置并重新设置事件
                        const newX = startX + col * this.tileSize + this.tileSize / 2;
                        const newY = startY + newRow * this.tileSize + this.tileSize / 2;
                        
                        if (column[i].sprite && column[i].text) {
                            this.setupTileInteraction(column[i].sprite, this.grid[newRow][col]);
                            
                            this.tweens.add({
                                targets: [column[i].sprite, column[i].text],
                                x: newX,
                                y: newY,
                                duration: 300,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }
                }
            }

            shuffleGrid() {
                // 首先清除选择状态
                this.clearSelection();
                
                // 对非空的方块重新随机生成数字
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            const randomNumber = Phaser.Utils.Array.GetRandom(this.numberPool);
                            this.grid[row][col].value = randomNumber.value;
                            this.grid[row][col].display = randomNumber.display;
                            const textureName = `number_${this.grid[row][col].value}`;
                            if (this.grid[row][col].sprite) {
                                this.grid[row][col].sprite.setTexture(textureName);
                            }
                            if (this.grid[row][col].text) {
                                this.grid[row][col].text.setText(this.grid[row][col].display);
                            }
                        }
                    }
                }
                
                // 显示刷新消息
                this.showMessage('重新生成数字！', 0x00ffff);
                
                // 延迟执行自动下落
                // this.time.delayedCall(500, () => {
                //     this.applyGravity();
                //     this.showMessage('自动下落！', 0xffff00);
                // });
            }

            updateTime() {
                if (!this.gameWon) {
                    const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = elapsed % 60;
                    this.timeText.setText(`时间: ${minutes}:${seconds.toString().padStart(2, '0')}`);
                }
            }

            showGameComplete() {
                const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                
                if (this.timeEvent) {
                    this.timeEvent.remove();
                }
                
                const winMessage = this.add.text(450, 400, 
                    `🎉 恭喜通关！🎉\n用时: ${minutes}:${seconds.toString().padStart(2, '0')}\n移动次数: ${this.moves}\n最终分数: ${this.score}`, 
                    {
                        fontSize: '28px',
                        fill: '#ffffff',
                        fontStyle: 'bold',
                        align: 'center',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        padding: { x: 25, y: 20 }
                    }
                ).setOrigin(0.5);
                
                this.tweens.add({
                    targets: winMessage,
                    alpha: 0.5,
                    duration: 500,
                    yoyo: true,
                    repeat: -1
                });
            }

            showMessage(text, color) {
                const colorString = `#${color.toString(16).padStart(6, '0')}`;
                const message = this.add.text(450, 180, text, {
                    fontSize: '20px',
                    fill: colorString,
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: { x: 15, y: 8 }
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: message,
                    alpha: 0,
                    y: 150,
                    duration: 2000,
                    ease: 'Power2',
                    onComplete: () => message.destroy()
                });
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 900,
            height: 700,
            resolution: window.devicePixelRatio || 1,
            render: {
                pixelArt: true,
                antialias: false
            },
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: NumberDragGame,
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 }
                }
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>