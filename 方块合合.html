<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三消游戏</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        #game-container {
            text-align: center;
            position: relative;
            width: 720px;
            height: 800px;
        }
        #score {
            color: white;
            font-size: 32px;
            font-weight: bold;
            position: absolute;
            top: 50px;
            left: 50%;
            transform: translateX(-50%);
            transition: all 0.2s ease;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            z-index: 10;
            text-align: center;
        }
        #block-preview {
            position: absolute;
            top: 120px;
            left: 50%;
            transform: translateX(-50%);
            width: 600px;
            height: 100px;
            display: flex;
            justify-content: space-around;
            align-items: center;
            z-index: 10;
        }
        .preview-block {
            width: 120px;
            height: 80px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 8px;
            background: rgba(255,255,255,0.1);
            display: flex;
            justify-content: center;
            align-items: center;
            backdrop-filter: blur(5px);
            position: relative;
        }
        .block-shape {
            display: grid;
            gap: 2px;
            z-index: 11;
        }
        .mini-block {
            width: 12px;
            height: 12px;
            border-radius: 2px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
        }
        canvas {
            border: 3px solid #fff;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.3);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div id="score">分数: 0</div>
        <div id="block-preview">
            <div class="preview-block" id="preview-1"></div>
            <div class="preview-block" id="preview-2"></div>
            <div class="preview-block" id="preview-3"></div>
            <div class="preview-block" id="preview-4"></div>
        </div>
        <div id="game"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <script>
        class Match3Game extends Phaser.Scene {
            constructor() {
                super({ key: 'Match3Game' });
                this.GRID_WIDTH = 8;   // 8x8格子
                this.GRID_HEIGHT = 8;  // 8x8格子
                this.TILE_SIZE = 53;   // 方块尺寸缩小1/3
                this.COLORS = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
                this.grid = [];
                this.selectedRow = null;
                this.score = 0;
                this.isProcessing = false;
                this.isDragging = false;
                this.dragStartX = 0;
                this.dragStartY = 0;
                this.dragThreshold = 30;
                this.dragDirection = null; // 'row' 或 'column'
                this.dragTargetIndex = -1; // 拖拽的行号或列号
                this.dragOffset = 0; // 当前拖拽偏移量

                // 方块组合预览相关
                this.blockShapes = [
                    // I形方块
                    [[1,1,1,1]],
                    // L形方块
                    [[1,0],[1,0],[1,1]],
                    // T形方块
                    [[1,1,1],[0,1,0]],
                    // 方形方块
                    [[1,1],[1,1]]
                ];
                this.previewBlocks = [];
                this.usedShapes = []; // 记录已使用的形状索引
            }

            preload() {
                // 创建方块纹理
                this.createTileTextures();
            }

            create() {
                // 初始化网格
                this.initGrid();

                // 创建方块组合预览
                this.createBlockPreviews();

                // 创建游戏板
                this.createBoard();
                
                // 添加输入处理
                this.input.on('gameobjectdown', this.onTileDown, this);
                this.input.on('gameobjectup', this.onTileUp, this);
                this.input.on('pointermove', this.onPointerMove, this);
                
                // 初始填充
                this.fillEmptySpaces();
            }

            createTileTextures() {
                this.COLORS.forEach(color => {
                    const graphics = this.add.graphics();

                    // 创建主体颜色填充 - 更鲜艳的颜色
                    const mainColor = this.getColorValue(color, 1.0);
                    const lightColor = this.getColorValue(color, 1.4);
                    const darkColor = this.getColorValue(color, 0.6);

                    // 绘制主体方块 - 圆角矩形，更现代的外观
                    graphics.fillStyle(mainColor, 1.0);
                    graphics.fillRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 12);

                    // 添加渐变效果 - 顶部高光
                    graphics.fillStyle(lightColor, 0.7);
                    graphics.fillRoundedRect(3, 3, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 10);

                    // 添加底部阴影
                    graphics.fillStyle(darkColor, 0.5);
                    graphics.fillRoundedRect(3, this.TILE_SIZE / 2, this.TILE_SIZE - 6, this.TILE_SIZE / 2 - 3, 10);

                    // 添加细微的边框
                    graphics.lineStyle(2, 0xffffff, 0.3);
                    graphics.strokeRoundedRect(1, 1, this.TILE_SIZE - 2, this.TILE_SIZE - 2, 12);

                    // 添加内部光泽效果
                    graphics.fillStyle(0xffffff, 0.4);
                    graphics.fillRoundedRect(6, 6, this.TILE_SIZE - 12, this.TILE_SIZE / 4, 8);

                    // 添加颜色标识符号或图案
                    this.addColorPattern(graphics, color);

                    graphics.generateTexture(color, this.TILE_SIZE, this.TILE_SIZE);
                    graphics.destroy();
                });
            }

            addColorPattern(graphics, color) {
                const centerX = this.TILE_SIZE / 2;
                const centerY = this.TILE_SIZE / 2;
                const size = 12;

                // 使用更明显的白色图案，增加对比度
                graphics.fillStyle(0xffffff, 0.9);
                graphics.lineStyle(3, 0xffffff, 0.9);

                switch(color) {
                    case 'red':
                        // 绘制心形 - 更大更明显
                        graphics.fillCircle(centerX - 6, centerY - 3, 6);
                        graphics.fillCircle(centerX + 6, centerY - 3, 6);
                        graphics.fillTriangle(centerX - 10, centerY + 3, centerX + 10, centerY + 3, centerX, centerY + 14);
                        break;
                    case 'blue':
                        // 绘制钻石 - 更大更清晰
                        graphics.fillTriangle(centerX, centerY - size, centerX - size, centerY, centerX, centerY + size);
                        graphics.fillTriangle(centerX, centerY - size, centerX + size, centerY, centerX, centerY + size);
                        break;
                    case 'green':
                        // 绘制四叶草
                        graphics.fillCircle(centerX - 4, centerY - 4, 5);
                        graphics.fillCircle(centerX + 4, centerY - 4, 5);
                        graphics.fillCircle(centerX - 4, centerY + 4, 5);
                        graphics.fillCircle(centerX + 4, centerY + 4, 5);
                        graphics.fillRect(centerX - 1, centerY - 1, 2, 8);
                        break;
                    case 'yellow':
                        // 绘制星星 - 更大更明显
                        const points = [];
                        for (let i = 0; i < 5; i++) {
                            const angle = (i * Math.PI * 2) / 5 - Math.PI / 2;
                            const outerRadius = size;
                            const innerRadius = size * 0.5;

                            points.push(centerX + Math.cos(angle) * outerRadius);
                            points.push(centerY + Math.sin(angle) * outerRadius);

                            const innerAngle = angle + Math.PI / 5;
                            points.push(centerX + Math.cos(innerAngle) * innerRadius);
                            points.push(centerY + Math.sin(innerAngle) * innerRadius);
                        }
                        graphics.fillPoints(points, true);
                        break;
                    case 'purple':
                        // 绘制花朵图案
                        for (let i = 0; i < 6; i++) {
                            const angle = (i * Math.PI * 2) / 6;
                            const x = centerX + Math.cos(angle) * 6;
                            const y = centerY + Math.sin(angle) * 6;
                            graphics.fillCircle(x, y, 4);
                        }
                        graphics.fillCircle(centerX, centerY, 4);
                        break;
                    case 'orange':
                        // 绘制太阳图案
                        graphics.fillCircle(centerX, centerY, 8);
                        for (let i = 0; i < 8; i++) {
                            const angle = (i * Math.PI * 2) / 8;
                            const x1 = centerX + Math.cos(angle) * 10;
                            const y1 = centerY + Math.sin(angle) * 10;
                            const x2 = centerX + Math.cos(angle) * 16;
                            const y2 = centerY + Math.sin(angle) * 16;
                            graphics.lineStyle(3, 0xffffff, 0.9);
                            graphics.lineBetween(x1, y1, x2, y2);
                        }
                        break;
                }
            }

            getColorValue(colorName, brightness = 1) {
                const colors = {
                    red: 0xff4757,      // 现代红色
                    blue: 0x5352ed,     // 现代蓝色
                    green: 0x2ed573,    // 现代绿色
                    yellow: 0xffa502,   // 现代黄色
                    purple: 0xa55eea,   // 现代紫色
                    orange: 0xff6348    // 现代橙色
                };

                let color = colors[colorName] || 0xffffff;
                if (brightness !== 1) {
                    const r = Math.min(255, Math.max(0, Math.floor(((color >> 16) & 0xff) * brightness)));
                    const g = Math.min(255, Math.max(0, Math.floor(((color >> 8) & 0xff) * brightness)));
                    const b = Math.min(255, Math.max(0, Math.floor((color & 0xff) * brightness)));
                    color = (r << 16) | (g << 8) | b;
                }
                return color;
            }

            initGrid() {
                this.grid = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        this.grid[row][col] = null;
                    }
                }
            }

            createBlockPreviews() {
                // 清除现有预览
                this.previewBlocks = [];
                this.usedShapes = [];

                // 为每个预览区域创建方块组合
                for (let i = 0; i < 4; i++) {
                    const shape = this.blockShapes[i];
                    const color = this.getRandomColor();

                    // 获取对应的HTML预览容器
                    const previewElement = document.getElementById(`preview-${i + 1}`);

                    // 创建方块组合HTML
                    this.createBlockShapeHTML(previewElement, shape, color);

                    this.previewBlocks.push({
                        element: previewElement,
                        shape: shape,
                        color: color,
                        index: i,
                        used: false // 标记是否已被使用
                    });
                }
            }

            createBlockShapeHTML(container, shape, color) {
                // 清除现有内容
                container.innerHTML = '';

                const shapeHeight = shape.length;
                const shapeWidth = shape[0].length;

                // 创建网格容器
                const shapeDiv = document.createElement('div');
                shapeDiv.className = 'block-shape';
                shapeDiv.style.gridTemplateColumns = `repeat(${shapeWidth}, 12px)`;
                shapeDiv.style.gridTemplateRows = `repeat(${shapeHeight}, 12px)`;

                // 获取颜色值
                const colorValue = this.getColorHex(color);

                for (let row = 0; row < shapeHeight; row++) {
                    for (let col = 0; col < shapeWidth; col++) {
                        if (shape[row][col] === 1) {
                            const block = document.createElement('div');
                            block.className = 'mini-block';
                            block.style.backgroundColor = colorValue;
                            block.style.gridColumn = col + 1;
                            block.style.gridRow = row + 1;
                            shapeDiv.appendChild(block);
                        }
                    }
                }

                container.appendChild(shapeDiv);
            }

            getColorHex(colorName) {
                const colors = {
                    red: '#ff4757',
                    blue: '#5352ed',
                    green: '#2ed573',
                    yellow: '#ffa502',
                    purple: '#a55eea',
                    orange: '#ff6348'
                };
                return colors[colorName] || '#ffffff';
            }

            markPreviewAsUsed(index) {
                const previewElement = this.previewBlocks[index].element;

                // 添加已使用的视觉效果
                previewElement.style.opacity = '0.3';
                previewElement.style.filter = 'grayscale(100%)';
                previewElement.style.transition = 'all 0.5s ease';

                // 添加消除动画
                previewElement.style.transform = 'scale(0.8)';

                // 可以添加一个"已使用"的标记
                if (!previewElement.querySelector('.used-mark')) {
                    const usedMark = document.createElement('div');
                    usedMark.className = 'used-mark';
                    usedMark.style.cssText = `
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: #ff0000;
                        font-weight: bold;
                        font-size: 12px;
                        z-index: 12;
                        text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
                    `;
                    usedMark.textContent = '✓';
                    previewElement.appendChild(usedMark);
                }
            }

            checkAndRegeneratePreview() {
                // 检查是否所有预览方块都已使用
                const allUsed = this.previewBlocks.every(block => block.used);

                if (allUsed) {
                    // 所有方块都已使用，重新生成
                    this.regenerateAllPreviews();
                }
            }

            regenerateAllPreviews() {
                // 不进行整体刷新，而是触发游戏板的下落和填充
                this.time.delayedCall(100, async () => {
                    try {
                        // 1. 先让现有方块下落填补空隙
                        await this.dropTiles();

                        // 2. 然后从顶部生成新方块并下落
                        await this.fillEmptySpaces();

                        // 3. 等待动画完成后重新生成预览方块
                        await new Promise(resolve => {
                            this.time.delayedCall(300, resolve);
                        });

                        // 4. 重新生成预览方块组合
                        this.regeneratePreviewBlocks();

                    } catch (error) {
                        console.error('Error in regenerate process:', error);
                    }
                });
            }

            regeneratePreviewBlocks() {
                // 只重新生成预览方块组合，不影响游戏板
                this.previewBlocks.forEach((previewBlock, index) => {
                    const element = previewBlock.element;

                    // 淡出动画
                    element.style.opacity = '0.5';
                    element.style.transform = 'scale(0.9)';
                    element.style.transition = 'all 0.2s ease';
                });

                // 延迟后重新生成
                setTimeout(() => {
                    // 重新生成所有预览方块
                    for (let i = 0; i < 4; i++) {
                        const shape = this.blockShapes[i];
                        const color = this.getRandomColor();
                        const previewElement = document.getElementById(`preview-${i + 1}`);

                        // 清除已使用标记
                        const usedMark = previewElement.querySelector('.used-mark');
                        if (usedMark) {
                            usedMark.remove();
                        }

                        // 重置样式
                        previewElement.style.opacity = '1';
                        previewElement.style.filter = 'none';
                        previewElement.style.transform = 'scale(1)';

                        // 创建新的方块组合
                        this.createBlockShapeHTML(previewElement, shape, color);

                        // 更新预览数据
                        this.previewBlocks[i] = {
                            element: previewElement,
                            shape: shape,
                            color: color,
                            index: i,
                            used: false
                        };
                    }

                    // 显示重新生成提示
                    this.showRegenerateEffect();
                }, 200);
            }

            showRegenerateEffect() {
                // 创建重新生成的提示文字
                const regenText = document.createElement('div');
                regenText.style.cssText = `
                    position: absolute;
                    top: 170px;
                    left: 50%;
                    transform: translateX(-50%);
                    color: #ffff00;
                    font-size: 24px;
                    font-weight: bold;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
                    z-index: 15;
                    pointer-events: none;
                `;
                regenText.textContent = '新组合！';
                document.getElementById('game-container').appendChild(regenText);

                // 动画效果
                regenText.style.opacity = '0';
                regenText.style.transform = 'translateX(-50%) scale(0.5)';
                regenText.style.transition = 'all 0.5s ease';

                setTimeout(() => {
                    regenText.style.opacity = '1';
                    regenText.style.transform = 'translateX(-50%) scale(1)';
                }, 50);

                // 3秒后移除
                setTimeout(() => {
                    regenText.style.opacity = '0';
                    regenText.style.transform = 'translateX(-50%) scale(0.5)';
                    setTimeout(() => {
                        if (regenText.parentNode) {
                            regenText.parentNode.removeChild(regenText);
                        }
                    }, 500);
                }, 2000);
            }



            createBoard() {
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        const y = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        
                        const color = this.getRandomColor();
                        const tile = this.add.image(x, y, color);
                        tile.setInteractive();
                        tile.setData('row', row);
                        tile.setData('col', col);
                        tile.setData('color', color);
                        
                        this.grid[row][col] = tile;
                    }
                }
                
                // 不需要移除初始匹配，因为改为点击消除
            }

            getRandomColor() {
                return this.COLORS[Math.floor(Math.random() * this.COLORS.length)];
            }



            removeInitialMatches() {
                let hasMatches = true;
                while (hasMatches) {
                    hasMatches = false;
                    for (let row = 0; row < this.GRID_HEIGHT; row++) {
                        for (let col = 0; col < this.GRID_WIDTH; col++) {
                            if (this.hasMatchAt(row, col)) {
                                const newColor = this.getRandomColor();
                                this.grid[row][col].setTexture(newColor);
                                this.grid[row][col].setData('color', newColor);
                                hasMatches = true;
                            }
                        }
                    }
                }
            }

            onTileDown(pointer, tile) {
                if (this.isProcessing) return;

                this.isDragging = true;
                this.dragStartX = pointer.x;
                this.dragStartY = pointer.y;
                this.dragStartTile = tile;
                this.dragDirection = null;
                this.dragTargetIndex = -1;
                this.dragOffset = 0;
            }

            onTileUp(pointer, tile) {
                if (!this.isDragging) return;

                // 如果有拖拽方向，计算最终位置并执行移动
                if (this.dragDirection === 'row') {
                    // 重置行的视觉位置
                    this.resetRowVisual(this.dragTargetIndex);

                    // 计算移动步数并执行
                    const steps = Math.round(this.dragOffset / this.TILE_SIZE);
                    if (Math.abs(steps) >= 1) {
                        if (steps > 0) {
                            this.moveRowSteps(this.dragTargetIndex, 'right', Math.abs(steps));
                        } else {
                            this.moveRowSteps(this.dragTargetIndex, 'left', Math.abs(steps));
                        }
                    }
                } else if (this.dragDirection === 'column') {
                    // 重置列的视觉位置
                    this.resetColumnVisual(this.dragTargetIndex);

                    // 计算移动步数并执行
                    const steps = Math.round(this.dragOffset / this.TILE_SIZE);
                    if (Math.abs(steps) >= 1) {
                        if (steps > 0) {
                            this.moveColumnSteps(this.dragTargetIndex, 'down', Math.abs(steps));
                        } else {
                            this.moveColumnSteps(this.dragTargetIndex, 'up', Math.abs(steps));
                        }
                    }
                }

                // 重置拖拽状态
                this.isDragging = false;
                this.dragStartTile = null;
                this.dragDirection = null;
                this.dragTargetIndex = -1;
                this.dragOffset = 0;
            }

            onPointerMove(pointer) {
                if (!this.isDragging || this.isProcessing) return;

                const deltaX = pointer.x - this.dragStartX;
                const deltaY = pointer.y - this.dragStartY;
                const absDeltaX = Math.abs(deltaX);
                const absDeltaY = Math.abs(deltaY);

                // 确定拖拽方向（只在第一次移动时确定）
                if (this.dragDirection === null && (absDeltaX > this.dragThreshold || absDeltaY > this.dragThreshold)) {
                    if (absDeltaX > absDeltaY) {
                        this.dragDirection = 'row';
                        this.dragTargetIndex = this.dragStartTile.getData('row');
                    } else {
                        this.dragDirection = 'column';
                        this.dragTargetIndex = this.dragStartTile.getData('col');
                    }
                }

                // 实时更新拖拽效果
                if (this.dragDirection === 'row') {
                    this.dragOffset = deltaX;
                    this.updateRowDragVisual(this.dragTargetIndex, deltaX);
                } else if (this.dragDirection === 'column') {
                    this.dragOffset = deltaY;
                    this.updateColumnDragVisual(this.dragTargetIndex, deltaY);
                }
            }



            updateRowDragVisual(row, offsetX) {
                // 实时更新整行方块的视觉位置，实现无限拖动效果
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;

                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        let newX = originalX + offsetX;

                        // 实现无限循环效果
                        const totalWidth = this.GRID_WIDTH * this.TILE_SIZE;
                        const leftBound = startX - this.TILE_SIZE / 2;
                        const rightBound = startX + totalWidth - this.TILE_SIZE / 2;

                        // 如果方块移出右边界，从左边重新出现
                        if (newX > rightBound) {
                            newX = newX - totalWidth;
                        }
                        // 如果方块移出左边界，从右边重新出现
                        else if (newX < leftBound) {
                            newX = newX + totalWidth;
                        }

                        tile.x = newX;
                    }
                }

                // 注释掉虚拟方块创建，暂时不实现无限效果
                // this.createVirtualRowTiles(row, offsetX);
            }

            updateColumnDragVisual(col, offsetY) {
                // 实时更新整列方块的视觉位置，实现无限拖动效果
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;

                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        let newY = originalY + offsetY;

                        // 实现无限循环效果
                        const totalHeight = this.GRID_HEIGHT * this.TILE_SIZE;
                        const topBound = startY - this.TILE_SIZE / 2;
                        const bottomBound = startY + totalHeight - this.TILE_SIZE / 2;

                        // 如果方块移出下边界，从上边重新出现
                        if (newY > bottomBound) {
                            newY = newY - totalHeight;
                        }
                        // 如果方块移出上边界，从下边重新出现
                        else if (newY < topBound) {
                            newY = newY + totalHeight;
                        }

                        tile.y = newY;
                    }
                }

                // 注释掉虚拟方块创建，暂时不实现无限效果
                // this.createVirtualColumnTiles(col, offsetY);
            }

            resetRowVisual(row) {
                // 重置整行方块到原始位置
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                        tile.x = originalX;
                    }
                }
            }

            resetColumnVisual(col) {
                // 重置整列方块到原始位置
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;
                        tile.y = originalY;
                    }
                }
            }

            moveRowSteps(row, direction, steps) {
                if (this.isProcessing) return;
                this.isProcessing = true;

                // 限制步数在合理范围内
                steps = Math.max(1, Math.min(steps, this.GRID_WIDTH));

                // 保存原始行数据
                const originalRowData = [];
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        originalRowData[col] = {
                            color: tile.getData('color'),
                            tile: tile
                        };
                    } else {
                        originalRowData[col] = null;
                    }
                }

                // 计算新的颜色排列（支持多步移动）
                const newColors = [];
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    let sourceCol;
                    if (direction === 'right') {
                        // 向右移动steps步
                        sourceCol = (col - steps + this.GRID_WIDTH) % this.GRID_WIDTH;
                    } else if (direction === 'left') {
                        // 向左移动steps步
                        sourceCol = (col + steps) % this.GRID_WIDTH;
                    }
                    newColors[col] = originalRowData[sourceCol] ? originalRowData[sourceCol].color : null;
                }

                // 应用新颜色并播放动画
                this.animateRowMove(row, newColors, direction, steps);
            }

            moveColumnSteps(col, direction, steps) {
                if (this.isProcessing) return;
                this.isProcessing = true;

                // 限制步数在合理范围内
                steps = Math.max(1, Math.min(steps, this.GRID_HEIGHT));

                // 保存原始列数据
                const originalColData = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        originalColData[row] = {
                            color: tile.getData('color'),
                            tile: tile
                        };
                    } else {
                        originalColData[row] = null;
                    }
                }

                // 计算新的颜色排列（支持多步移动）
                const newColors = [];
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    let sourceRow;
                    if (direction === 'down') {
                        // 向下移动steps步
                        sourceRow = (row - steps + this.GRID_HEIGHT) % this.GRID_HEIGHT;
                    } else if (direction === 'up') {
                        // 向上移动steps步
                        sourceRow = (row + steps) % this.GRID_HEIGHT;
                    }
                    newColors[row] = originalColData[sourceRow] ? originalColData[sourceRow].color : null;
                }

                // 应用新颜色并播放动画
                this.animateColumnMove(col, newColors, direction, steps);
            }







            animateRowMove(row, newColors, direction, steps = 1) {
                const moveDuration = Math.min(400, 200 + steps * 50); // 根据步数调整动画时长
                let completedAnimations = 0;
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;

                // 先更新所有方块的颜色数据
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile && newColors[col]) {
                        tile.setTexture(newColors[col]);
                        tile.setData('color', newColors[col]);
                    }
                }

                // 然后播放移动动画（仅视觉效果）
                for (let col = 0; col < this.GRID_WIDTH; col++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        let targetX;
                        const originalX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;

                        if (direction === 'right') {
                            // 向右移动动画，距离根据步数计算
                            targetX = originalX + (steps * this.TILE_SIZE);
                        } else if (direction === 'left') {
                            // 向左移动动画，距离根据步数计算
                            targetX = originalX - (steps * this.TILE_SIZE);
                        }

                        // 直接缓动到最终位置，无回弹
                        this.tweens.add({
                            targets: tile,
                            x: originalX,
                            duration: moveDuration,
                            ease: 'Power2.easeOut',
                            onComplete: () => {
                                completedAnimations++;
                                if (completedAnimations === this.GRID_WIDTH) {
                                    // 所有动画完成后检查形状匹配
                                    this.time.delayedCall(100, () => {
                                        this.checkShapeMatches();
                                    });
                                }
                            }
                        });
                    }
                }
            }

            animateColumnMove(col, newColors, direction, steps = 1) {
                const moveDuration = Math.min(400, 200 + steps * 50); // 根据步数调整动画时长
                let completedAnimations = 0;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;

                // 先更新所有方块的颜色数据
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile && newColors[row]) {
                        tile.setTexture(newColors[row]);
                        tile.setData('color', newColors[row]);
                    }
                }

                // 然后播放移动动画（仅视觉效果）
                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    const tile = this.grid[row][col];
                    if (tile) {
                        let targetY;
                        const originalY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;

                        if (direction === 'down') {
                            // 向下移动动画，距离根据步数计算
                            targetY = originalY + (steps * this.TILE_SIZE);
                        } else if (direction === 'up') {
                            // 向上移动动画，距离根据步数计算
                            targetY = originalY - (steps * this.TILE_SIZE);
                        }

                        // 直接缓动到最终位置，无回弹
                        this.tweens.add({
                            targets: tile,
                            y: originalY,
                            duration: moveDuration,
                            ease: 'Power2.easeOut',
                            onComplete: () => {
                                completedAnimations++;
                                if (completedAnimations === this.GRID_HEIGHT) {
                                    // 所有动画完成后检查形状匹配
                                    this.time.delayedCall(100, () => {
                                        this.checkShapeMatches();
                                    });
                                }
                            }
                        });
                    }
                }
            }



            checkShapeMatches() {
                // 检查游戏板上是否有与预览方块组合相匹配的形状
                const matchedShapes = [];
                const matchedPreviewIndices = [];

                // 遍历每个未使用的预览方块形状
                for (let shapeIndex = 0; shapeIndex < this.previewBlocks.length; shapeIndex++) {
                    const previewBlock = this.previewBlocks[shapeIndex];

                    // 跳过已使用的形状
                    if (previewBlock.used) continue;

                    const targetShape = previewBlock.shape;
                    const targetColor = previewBlock.color;

                    // 在游戏板上查找匹配的形状
                    const matches = this.findShapeMatches(targetShape, targetColor);
                    if (matches.length > 0) {
                        matchedShapes.push(...matches);
                        matchedPreviewIndices.push(shapeIndex);
                    }
                }

                if (matchedShapes.length > 0) {
                    // 标记匹配的预览方块为已使用
                    matchedPreviewIndices.forEach(index => {
                        this.previewBlocks[index].used = true;
                        this.markPreviewAsUsed(index);
                    });

                    // 有匹配的形状，开始消除
                    this.removeMatchedShapes(matchedShapes);
                    this.updateScore(matchedShapes.length * 10); // 形状匹配给更高分数

                    // 延迟后处理下落和填充 - 确保正确的时序
                    this.time.delayedCall(400, async () => {
                        try {
                            // 1. 先让现有方块下落填补空隙
                            await this.dropTiles();

                            // 2. 然后从顶部生成新方块并下落
                            await this.fillEmptySpaces();

                            // 3. 等待一小段时间确保所有动画完成
                            await new Promise(resolve => {
                                this.time.delayedCall(200, resolve);
                            });

                            // 4. 检查是否所有预览方块都已使用
                            this.checkAndRegeneratePreview();

                        } catch (error) {
                            console.error('Error in drop and fill process:', error);
                        } finally {
                            this.isProcessing = false;
                        }
                    });
                } else {
                    // 没有匹配，结束处理
                    this.isProcessing = false;
                }
            }

            findShapeMatches(targetShape, targetColor) {
                const matches = [];
                const shapeHeight = targetShape.length;
                const shapeWidth = targetShape[0].length;

                // 遍历游戏板上的每个可能位置
                for (let startRow = 0; startRow <= this.GRID_HEIGHT - shapeHeight; startRow++) {
                    for (let startCol = 0; startCol <= this.GRID_WIDTH - shapeWidth; startCol++) {
                        // 检查在这个位置是否能匹配目标形状
                        if (this.isShapeMatchAt(targetShape, targetColor, startRow, startCol)) {
                            // 收集匹配的方块位置
                            const shapeMatch = [];
                            for (let row = 0; row < shapeHeight; row++) {
                                for (let col = 0; col < shapeWidth; col++) {
                                    if (targetShape[row][col] === 1) {
                                        const gridRow = startRow + row;
                                        const gridCol = startCol + col;
                                        shapeMatch.push({
                                            row: gridRow,
                                            col: gridCol,
                                            tile: this.grid[gridRow][gridCol]
                                        });
                                    }
                                }
                            }
                            matches.push(shapeMatch);
                        }
                    }
                }

                return matches;
            }

            isShapeMatchAt(targetShape, targetColor, startRow, startCol) {
                const shapeHeight = targetShape.length;
                const shapeWidth = targetShape[0].length;

                for (let row = 0; row < shapeHeight; row++) {
                    for (let col = 0; col < shapeWidth; col++) {
                        const gridRow = startRow + row;
                        const gridCol = startCol + col;

                        if (targetShape[row][col] === 1) {
                            // 这个位置应该有目标颜色的方块
                            if (!this.grid[gridRow] || !this.grid[gridRow][gridCol] ||
                                this.grid[gridRow][gridCol].getData('color') !== targetColor) {
                                return false;
                            }
                        }
                    }
                }

                return true;
            }

            removeMatchedShapes(matchedShapes) {
                // 按顺序播放消除动画
                let animationIndex = 0;
                matchedShapes.forEach((shapeMatch) => {
                    shapeMatch.forEach((block) => {
                        const tile = block.tile;
                        if (tile && tile.active) {
                            // 延迟播放动画，创建波浪效果
                            this.time.delayedCall(animationIndex * 30, () => {
                                this.playDestroyAnimation(tile, block);
                            });
                            animationIndex++;
                        }
                    });
                });
            }

            playDestroyAnimation(tile, pos) {
                if (!tile || !tile.active) return;

                // 创建爆炸粒子效果
                this.createExplosionEffect(tile.x, tile.y, tile.getData('color'));

                // 主要消除动画 - 旋转缩放消失
                this.tweens.add({
                    targets: tile,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    rotation: Math.PI * 2,
                    alpha: 0.8,
                    duration: 150,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段 - 快速缩小消失
                        this.tweens.add({
                            targets: tile,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 100,
                            ease: 'Power3',
                            onComplete: () => {
                                if (tile && tile.active) {
                                    tile.destroy();
                                }
                                this.grid[pos.row][pos.col] = null;
                            }
                        });
                    }
                });

                // 添加闪光效果
                this.tweens.add({
                    targets: tile,
                    tint: 0xffffff,
                    duration: 100,
                    yoyo: true,
                    ease: 'Power2'
                });
            }

            createExplosionEffect(x, y, color) {
                const particleCount = 8;
                const baseColor = this.getColorValue(color, 1.0);

                for (let i = 0; i < particleCount; i++) {
                    // 创建粒子
                    const particle = this.add.graphics();
                    particle.fillStyle(baseColor, 0.8);
                    particle.fillCircle(0, 0, 3);
                    particle.x = x;
                    particle.y = y;

                    // 随机方向和速度
                    const angle = (Math.PI * 2 * i) / particleCount + (Math.random() - 0.5) * 0.5;
                    const speed = 50 + Math.random() * 30;
                    const targetX = x + Math.cos(angle) * speed;
                    const targetY = y + Math.sin(angle) * speed;

                    // 粒子飞散动画
                    this.tweens.add({
                        targets: particle,
                        x: targetX,
                        y: targetY,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300 + Math.random() * 200,
                        ease: 'Power2',
                        onComplete: () => {
                            particle.destroy();
                        }
                    });
                }

                // 创建光环效果
                const ring = this.add.graphics();
                ring.lineStyle(4, 0xffffff, 0.8);
                ring.strokeCircle(0, 0, 5);
                ring.x = x;
                ring.y = y;

                // 光环扩散动画
                this.tweens.add({
                    targets: ring,
                    scaleX: 3,
                    scaleY: 3,
                    alpha: 0,
                    duration: 400,
                    ease: 'Power2',
                    onComplete: () => {
                        ring.destroy();
                    }
                });
            }

            dropTiles() {
                return new Promise((resolve) => {
                    let animationsToComplete = 0;
                    let completedAnimations = 0;
                    const boardStartY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;

                    // 逐列处理下落，但不清空和重新创建方块
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        // 从下往上扫描，找到需要下落的方块
                        let writeRow = this.GRID_HEIGHT - 1;

                        // 从下往上处理每一行
                        for (let row = this.GRID_HEIGHT - 1; row >= 0; row--) {
                            if (this.grid[row][col] !== null && this.grid[row][col] !== undefined) {
                                const tile = this.grid[row][col];

                                if (row !== writeRow) {
                                    // 需要下落，移动方块到新位置
                                    this.grid[writeRow][col] = tile;
                                    this.grid[row][col] = null;

                                    // 更新方块数据
                                    tile.setData('row', writeRow);

                                    // 添加下落动画
                                    const endY = boardStartY + writeRow * this.TILE_SIZE + this.TILE_SIZE / 2;

                                    animationsToComplete++;
                                    this.tweens.add({
                                        targets: tile,
                                        y: endY,
                                        duration: 300,
                                        ease: 'Power2.easeOut',
                                        onComplete: () => {
                                            completedAnimations++;
                                            if (completedAnimations === animationsToComplete) {
                                                resolve();
                                            }
                                        }
                                    });
                                }

                                writeRow--;
                            }
                        }
                    }

                    // 如果没有需要下落的方块，直接resolve
                    if (animationsToComplete === 0) {
                        resolve();
                    }
                });
            }

            fillEmptySpaces() {
                return new Promise((resolve) => {
                    const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                    const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;

                    let animationsToComplete = 0;
                    let completedAnimations = 0;

                    // 逐列处理空位填充
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        // 收集这一列的空位（从上到下）
                        const emptyPositions = [];
                        for (let row = 0; row < this.GRID_HEIGHT; row++) {
                            if (this.grid[row][col] === null || this.grid[row][col] === undefined) {
                                emptyPositions.push(row);
                            }
                        }

                        // 为每个空位创建新方块
                        emptyPositions.forEach((targetRow, index) => {
                            const x = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                            const finalY = startY + targetRow * this.TILE_SIZE + this.TILE_SIZE / 2;

                            const color = this.getRandomColor();
                            // 新方块从屏幕顶部上方开始，按顺序排列
                            const startYPos = startY - (emptyPositions.length - index + 1) * this.TILE_SIZE;
                            const tile = this.add.image(x, startYPos, color);
                            tile.setInteractive();
                            tile.setData('row', targetRow);
                            tile.setData('col', col);
                            tile.setData('color', color);

                            // 立即放入网格
                            this.grid[targetRow][col] = tile;

                            // 添加下落动画
                            animationsToComplete++;
                            this.tweens.add({
                                targets: tile,
                                y: finalY,
                                duration: 500 + (index * 80), // 稍微延长动画时间，错开更明显
                                ease: 'Bounce.easeOut',
                                onComplete: () => {
                                    completedAnimations++;
                                    if (completedAnimations === animationsToComplete) {
                                        // 确保所有方块都在正确位置
                                        this.validateGridPositions();
                                        resolve();
                                    }
                                }
                            });
                        });
                    }

                    // 如果没有需要填充的空位，直接resolve
                    if (animationsToComplete === 0) {
                        resolve();
                    }
                });
            }

            validateGridPositions() {
                // 验证并修正所有方块的位置
                const startX = (this.sys.game.config.width - this.GRID_WIDTH * this.TILE_SIZE) / 2;
                const startY = (this.sys.game.config.height - this.GRID_HEIGHT * this.TILE_SIZE) / 2 + 80 + 53;

                for (let row = 0; row < this.GRID_HEIGHT; row++) {
                    for (let col = 0; col < this.GRID_WIDTH; col++) {
                        const tile = this.grid[row][col];
                        if (tile && tile.active) {
                            const correctX = startX + col * this.TILE_SIZE + this.TILE_SIZE / 2;
                            const correctY = startY + row * this.TILE_SIZE + this.TILE_SIZE / 2;

                            // 确保方块在正确位置
                            tile.x = correctX;
                            tile.y = correctY;

                            // 确保数据正确
                            tile.setData('row', row);
                            tile.setData('col', col);
                        }
                    }
                }
            }

            showComboEffect(matchCount) {
                // 创建连锁文字效果
                const comboText = this.add.text(
                    this.sys.game.config.width / 2,
                    this.sys.game.config.height / 2,
                    `COMBO x${matchCount}!`,
                    {
                        fontSize: '32px',
                        fontFamily: 'Arial',
                        color: '#ffff00',
                        stroke: '#ff0000',
                        strokeThickness: 4,
                        shadow: {
                            offsetX: 2,
                            offsetY: 2,
                            color: '#000000',
                            blur: 4,
                            fill: true
                        }
                    }
                );
                comboText.setOrigin(0.5);
                comboText.setScale(0);

                // 连锁文字动画
                this.tweens.add({
                    targets: comboText,
                    scaleX: 1.2,
                    scaleY: 1.2,
                    duration: 200,
                    ease: 'Back.easeOut',
                    onComplete: () => {
                        this.tweens.add({
                            targets: comboText,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            y: comboText.y - 50,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                comboText.destroy();
                            }
                        });
                    }
                });

                // 屏幕震动效果
                this.cameras.main.shake(200, 0.01);
            }

            updateScore(matchCount) {
                this.score += matchCount * 10;
                document.getElementById('score').textContent = `分数: ${this.score}`;

                // 分数增加动画
                const scoreElement = document.getElementById('score');
                scoreElement.style.transform = 'translateX(-50%) scale(1.2)';
                scoreElement.style.color = '#ffff00';

                setTimeout(() => {
                    scoreElement.style.transform = 'translateX(-50%) scale(1)';
                    scoreElement.style.color = 'white';
                }, 200);
            }
        }

        // 游戏配置 - 竖屏720x800
        const config = {
            type: Phaser.AUTO,
            width: 720,
            height: 800,
            parent: 'game',
            backgroundColor: '#2c3e50',
            scene: Match3Game,
            scale: {
                mode: Phaser.Scale.NONE,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        const game = new Phaser.Game(config);
    </script>
</body>
</html>
