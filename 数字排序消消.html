<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1-9数字拖拽消消乐</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .game-title {
            color: white;
            font-size: 2.5em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            margin-bottom: 20px;
        }

        .game-info {
            color: white;
            text-align: center;
            margin-top: 30px;
            background: rgba(255, 255, 255, 0.1);
            padding: 10px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(47, 42, 46, 1);
            overflow: hidden;
        }
    </style>
</head>

<body>


    <div id="game-container"></div>
    <div class="game-info">
        <p>🎯 游戏规则：拖拽选择数字，让它们相加等于10来消除！</p>
        <p>💡 消除的数字会移动到上方合成区域，相同颜色堆叠5个可再次消除</p>
        <p>🌈 每个数字都有独特的颜色 | 🧩 合成区域最多容纳7种颜色</p>
    </div>
    <script>
        class NumberDragGame extends Phaser.Scene {
            constructor() {
                super({ key: 'NumberDragGame' });
                this.gridSize = 8; // 修改为8行
                this.tileSize = 60;
                this.grid = [];
                this.selectedTiles = [];
                this.isDragging = false;
                this.score = 0;
                this.moves = 0;
                this.tilesRemaining = 8 * 9; // 计算新的剩余方块数量
                this.gameStartTime = 0;
                this.gameWon = false;
                this.maxSelection = 5;
                this.doubleClickDelay = 300;
                
                // 合成区域相关属性
                this.combinationZone = {
                    startX: 350 - 300,
                    startY: 120,
                    width: 600,
                    height: 220,
                    slotWidth: 80,
                    slotHeight: 200,
                    slots: [] // 每个槽位存放一种颜色的方块
                };
                this.maxColorsInCombinationZone = 7;

                this.numberPool = [1, 2, 3, 4, 5, 6, 7, 8, 9];
                // 定义不同数字对应的背景颜色
                this.numberColors = {
                    1: 0xefb438,
                    2: 0x3ca674,
                    3: 0x896493,
                    4: 0xcf212e,
                    5: 0x2d9dc5,
                    6: 0x343fb6,
                    7: 0xff8c00,
                    8: 0x800080,
                    9: 0x008000
                };
                this.highlightColor = 0xffe403;
                this.splitCombinations = {
                    6: [[1, 5], [2, 4], [3, 3]],
                    7: [[1, 6], [2, 5], [3, 4]],
                    8: [[1, 7], [2, 6], [3, 5], [4, 4]],
                    9: [[1, 8], [2, 7], [3, 6], [4, 5]]
                };
            }

            preload() {}

            create() {
                this.add.rectangle(350, 450, 700, 900, 0x2e292b); // 修改背景矩形高度
                this.add.rectangle(350, 60, 700, 60, 0x2231c1f);

                // 上方合成区域
                this.topgraphics = this.add.graphics();       
                // 填充圆角矩形
                this.topgraphics.fillStyle(0x6b70d6, 1);
                this.topgraphics.fillRoundedRect(50, 120, 600, 220, 30);
            
               
                
               

                this.scoreText = this.add.text(80, 60, '分数: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.movesText = this.add.text(230, 60, '移动: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tilesText = this.add.text(380, 60, `剩余: ${this.tilesRemaining}`, {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.timeText = this.add.text(530, 60, '时间: 0:00', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.selectionText = this.add.text(350, 120, '当前选择: 无', {
                    fontSize: '18px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0, 0, 0, 0.5)',
                    padding: { x: 10, y: 5 }
                }).setOrigin(0.5);

                const refreshBtn = this.add.rectangle(620, 60, 140, 45, 0xe74c3c);
                refreshBtn.setStrokeStyle(3, 0xc0392b);
                this.add.text(620, 60, '刷新', {
                    fontSize: '18px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                refreshBtn.setInteractive();
                refreshBtn.on('pointerdown', () => this.shuffleGrid());

                this.initializeGrid();
                this.createGrid();
                this.initializeCombinationZone();

                this.input.on('pointerup', () => this.endDrag());

                this.gameStartTime = this.time.now;
                this.timeEvent = this.time.addEvent({
                    delay: 1000,
                    callback: this.updateTime,
                    callbackScope: this,
                    loop: true
                });
            }

            initializeCombinationZone() {
                // 初始化合成区域槽位
                for (let i = 0; i < this.maxColorsInCombinationZone; i++) {
                    this.combinationZone.slots[i] = {
                        value: null,
                        count: 0,
                        blocks: []
                    };
                }
            }

            initializeGrid() {
                this.grid = [];
                for (let row = 0; row < this.gridSize; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < 9; col++) { // 修改为9列
                        const randomNumber = Phaser.Utils.Array.GetRandom(this.numberPool);
                        this.grid[row][col] = {
                            value: randomNumber,
                            sprite: null,
                            text: null,
                            isEmpty: false,
                            row: row,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };
                    }
                }
            }

            createGrid() {
                const startX = 350 - (9 * this.tileSize) / 2; // 修改为9列
                const startY = 360;

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < 9; col++) { // 修改为9列
                        const tile = this.grid[row][col];
                        if (!tile.isEmpty) {
                            this.createTile(tile, startX, startY);
                        }
                    }
                }
            }

            createTile(tile, startX, startY) {
    const x = startX + tile.col * this.tileSize + this.tileSize / 2;
    const y = startY + tile.row * this.tileSize + this.tileSize / 2;

    // 根据数字设置背景颜色
    const color = this.numberColors[tile.value];
    // 修改这里，添加 radius 参数
    const sprite = this.add.rectangle(x, y, this.tileSize - 6, this.tileSize - 6, color, 1, 30); 
    const text = this.add.text(x, y, tile.value, {
        fontSize: '26px',
        fill: '#FFFFFF',
        fontStyle: 'bold',
        fontFamily: 'Arial, sans-serif',
      
      
    }).setOrigin(0.5);

    this.setupTileInteraction(sprite, tile);

    tile.sprite = sprite;
    tile.text = text;
    tile.isSelected = false;
}

            setupTileInteraction(sprite, tile) {
                if (sprite && sprite.scene && !sprite.scene.sys.isDestroyed) {
                    sprite.setInteractive();
                    sprite.on('pointerdown', () => {
                        if (!tile.isEmpty) {
                            this.handleTileClick(tile);
                        }
                    });
                    sprite.on('pointerover', () => {
                        if (this.isDragging && !tile.isEmpty) {
                            this.addToSelection(tile);
                        }
                        if (!tile.isEmpty && !tile.isSelected) {
                            sprite.setScale(1.05);
                        }
                    });
                    sprite.on('pointerout', () => {
                        if (!tile.isEmpty && !tile.isSelected) {
                            sprite.setScale(1);
                        }
                    });
                }
            }

            handleTileClick(tile) {
                const currentTime = this.time.now;
                const timeSinceLastClick = currentTime - tile.lastClickTime;

                if (timeSinceLastClick < this.doubleClickDelay) {
                    this.handleDoubleClick(tile);
                } else {
                    this.startDrag(tile);
                }

                tile.lastClickTime = currentTime;
            }

            handleDoubleClick(tile) {
                if (tile.value <= 5) {
                    this.showMessage('只有大于5的数字才能分裂！', 0xff0000);
                    return;
                }

                const emptyPositions = this.getEmptyPositions();
                if (emptyPositions.length < 2) {
                    this.showMessage('没有足够的空位置进行分裂！', 0xff0000);
                    return;
                }

                this.splitNumber(tile, emptyPositions);
            }

            getEmptyPositions() {
                const emptyPositions = [];
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < 9; col++) { // 修改为9列
                        if (this.grid[row][col].isEmpty) {
                            emptyPositions.push({ row, col });
                        }
                    }
                }
                return emptyPositions;
            }

            splitNumber(tile, emptyPositions) {
                const combinations = this.splitCombinations[tile.value];
                const selectedCombination = Phaser.Utils.Array.GetRandom(combinations);

                const shuffledPositions = Phaser.Utils.Array.Shuffle([...emptyPositions]);
                const position1 = shuffledPositions[0];
                const position2 = shuffledPositions[1];

                this.tweens.add({
                    targets: [tile.sprite, tile.text],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        if (tile.sprite) tile.sprite.destroy();
                        if (tile.text) tile.text.destroy();
                        tile.sprite = null;
                        tile.text = null;
                        tile.isEmpty = true;
                    }
                });

                this.time.delayedCall(400, () => {
                    this.createSplitNumbers(selectedCombination, position1, position2);
                    this.showMessage(`${tile.value} 分裂成 ${selectedCombination[0]} + ${selectedCombination[1]}！`, 0x00ff00);

                    this.time.delayedCall(500, () => {
                        this.applyGravity();
                    });
                });
            }

            createSplitNumbers(combination, pos1, pos2) {
                const startX = 350 - (9 * this.tileSize) / 2; // 修改为9列
                const startY = 360;

                this.grid[pos1.row][pos1.col] = {
                    value: combination[0],
                    sprite: null,
                    text: null,
                    isEmpty: false,
                    row: pos1.row,
                    col: pos1.col,
                    isSelected: false,
                    lastClickTime: 0
                };
                this.createTile(this.grid[pos1.row][pos1.col], startX, startY,10);

                this.grid[pos2.row][pos2.col] = {
                    value: combination[1],
                    sprite: null,
                    text: null,
                    isEmpty: false,
                    row: pos2.row,
                    col: pos2.col,
                    isSelected: false,
                    lastClickTime: 0
                };
                this.createTile(this.grid[pos2.row][pos2.col], startX, startY);

                const newTiles = [
                    this.grid[pos1.row][pos1.col],
                    this.grid[pos2.row][pos2.col]
                ];

                newTiles.forEach(newTile => {
                    if (newTile.sprite && newTile.text) {
                        newTile.sprite.setScale(0);
                        newTile.text.setScale(0);
                        this.tweens.add({
                            targets: [newTile.sprite, newTile.text],
                            scaleX: 1,
                            scaleY: 1,
                            duration: 400,
                            ease: 'Back.easeOut'
                        });
                    }
                });

                this.tilesRemaining += 1;
                this.tilesText.setText(`剩余: ${this.tilesRemaining}`);
            }

            startDrag(tile) {
                if (tile.isEmpty) return;

                this.isDragging = true;
                this.clearSelection();
                this.selectedTiles = [];
                this.addToSelection(tile);
            }

            addToSelection(tile) {
                if (tile.isEmpty || tile.isSelected || this.selectedTiles.length >= this.maxSelection) {
                    return;
                }

                if (this.selectedTiles.length > 0) {
                    const lastTile = this.selectedTiles[this.selectedTiles.length - 1];
                    if (!this.isAdjacent(lastTile, tile)) {
                        return;
                    }
                }

                this.selectedTiles.push(tile);
                tile.isSelected = true;
                // tile.sprite.setFillStyle(this.highlightColor);
                tile.sprite.setScale(1.2);

                this.updateSelectionDisplay();
            }

            isAdjacent(tile1, tile2) {
                const rowDiff = Math.abs(tile1.row - tile2.row);
                const colDiff = Math.abs(tile1.col - tile2.col);
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }

            endDrag() {
                if (!this.isDragging) return;

                this.isDragging = false;

                const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);

                if (sum === 10 && this.selectedTiles.length > 1) {
                    this.moveToCombinationZone();
                } else {
                    this.clearSelection();
                    if (this.selectedTiles.length > 1) {
                        this.showMessage(`总和不等于10 (${sum})，请重新选择！`, 0xff0000);
                    }
                }
            }

            moveToCombinationZone() {
    const bonus = this.selectedTiles.length * 10;
    this.score += bonus;

    // 检查合成区域是否已满
    const filledSlots = this.combinationZone.slots.filter(slot => slot.value !== null).length;
    const newColors = [...new Set(this.selectedTiles.map(tile => tile.value))].filter(value => 
        !this.combinationZone.slots.some(slot => slot.value === value)
    ).length;
    
    if (filledSlots + newColors > this.maxColorsInCombinationZone) {
        this.showMessage('合成区域已满！无法添加更多颜色', 0xff0000);
        this.clearSelection();
        return;
    }

    let animationsCompleted = 0;
    const totalAnimations = this.selectedTiles.length;

    this.selectedTiles.forEach((tile, index) => {
        // 找到对应的槽位或空槽位
        let slotIndex = this.combinationZone.slots.findIndex(slot => slot.value === tile.value);
        if (slotIndex === -1) {
            slotIndex = this.combinationZone.slots.findIndex(slot => slot.value === null);
        }
        
        if (slotIndex === -1) {
            animationsCompleted++;
            return;
        }
        
        const slot = this.combinationZone.slots[slotIndex];
        if (slot.value === null) {
            slot.value = tile.value;
        }
        
        // 计算在合成区域中的位置
        const slotX = this.combinationZone.startX + slotIndex * this.combinationZone.slotWidth + this.combinationZone.slotWidth / 2;
        // 修改为：
        const blockHeight = this.tileSize * 0.55;
        const slotY = this.combinationZone.startY + this.combinationZone.slotHeight - blockHeight * (slot.count + 1);
        
        // 缩小原方块并移动到合成区域
        const targetScale = 0.5;
        
        // 先移除原来的交互
        if (tile.sprite) {
            tile.sprite.removeAllListeners();
        }
        
        // 移动动画
        this.tweens.add({
            targets: [tile.sprite, tile.text],
            x: slotX,
            y: slotY,
            scaleX: 1,
            scaleY: targetScale,
            duration: 600,
            ease: 'Power2',
            onComplete: () => {
                // 更新文字大小
                if (tile.text) {
                    tile.text.setFontSize('14px');
                }
                
                // 添加到槽位
                slot.count++;
                slot.blocks.push({sprite: tile.sprite, text: tile.text});
                
                // 标记原位置为空
                tile.isEmpty = true;
                
                animationsCompleted++;
                if (animationsCompleted === totalAnimations) {
                    this.onAllAnimationsComplete();
                    
                    // 检查是否达到5个
                    if (slot.count >= 5) {
                        this.time.delayedCall(500, () => {
                            this.eliminateCombinationBlocks(slotIndex);
                        });
                    }
                }
            }
        });
    });

    this.tilesRemaining -= this.selectedTiles.length;
    this.moves++;
    this.scoreText.setText(`分数: ${this.score}`);
    this.movesText.setText(`移动: ${this.moves}`);
    this.tilesText.setText(`剩余: ${this.tilesRemaining}`);

    const displayValues = this.selectedTiles.map(tile => tile.value).join(' + ');
    this.showMessage(`成功移动到合成区域！ ${displayValues} = 10`, 0x00ff00);

    this.time.delayedCall(700, () => {
    this.clearSelection();
});
}
            eliminateCombinationBlocks(slotIndex) {
                const slot = this.combinationZone.slots[slotIndex];
                
                // 添加消除动画
                slot.blocks.forEach(block => {
                    this.tweens.add({
                        targets: [block.sprite, block.text],
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 300,
                        ease: 'Power2',
                        onComplete: () => {
                            block.sprite.destroy();
                            block.text.destroy();
                        }
                    });
                });
                
                // 增加分数
                this.score += 50; // 5个方块的奖励
                this.scoreText.setText(`分数: ${this.score}`);
                this.showMessage(`合成区域 ${slot.value} 达到5个，消除成功！`, 0x00ff00);
                
                // 重置槽位
                this.time.delayedCall(400, () => {
                    slot.value = null;
                    slot.count = 0;
                    slot.blocks = [];
                });
            }

            onAllAnimationsComplete() {
                if (this.tilesRemaining === 0) {
                    this.gameWon = true;
                    this.showGameComplete();
                } else {
                    this.applyGravity();
                }
            }

            clearSelection() {
    this.selectedTiles.forEach(tile => {
        if (!tile.isEmpty && tile.sprite) {
            tile.isSelected = false;
            // 恢复原来的颜色
            const color = this.numberColors[tile.value];
            tile.sprite.setFillStyle(color);
            tile.sprite.setScale(1);
        }
    });
    this.selectedTiles = [];
    this.updateSelectionDisplay();
}

            updateSelectionDisplay() {
                if (this.selectedTiles.length === 0) {
                    this.selectionText.setText('当前选择: 无');
                } else {
                    const displayValues = this.selectedTiles.map(tile => tile.value);
                    const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);
                    this.selectionText.setText(`当前选择: ${displayValues.join(' + ')} = ${sum}`);
                }
            }

            applyGravity() {
                const startX = 350 - (9 * this.tileSize) / 2; // 修改为9列
                const startY = 360;

                for (let col = 0; col < 9; col++) { // 修改为9列
                    const column = [];

                    for (let row = this.gridSize - 1; row >= 0; row--) {
                        if (!this.grid[row][col].isEmpty) {
                            column.push({
                                value: this.grid[row][col].value,
                                sprite: this.grid[row][col].sprite,
                                text: this.grid[row][col].text
                            });
                        }
                    }

                    for (let row = 0; row < this.gridSize; row++) {
                        this.grid[row][col] = {
                            value: 0,
                            sprite: null,
                            text: null,
                            isEmpty: true,
                            row: row,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };
                    }

                    for (let i = 0; i < column.length; i++) {
                        const newRow = this.gridSize - 1 - i;
                        this.grid[newRow][col] = {
                            value: column[i].value,
                            sprite: column[i].sprite,
                            text: column[i].text,
                            isEmpty: false,
                            row: newRow,
                            col: col,
                            isSelected: false,
                            lastClickTime: 0
                        };

                        const newX = startX + col * this.tileSize + this.tileSize / 2;
                        const newY = startY + newRow * this.tileSize + this.tileSize / 2;

                        if (column[i].sprite && column[i].text) {
    // 重新设置交互前，先清除旧的交互
    column[i].sprite.removeAllListeners();
    this.setupTileInteraction(column[i].sprite, this.grid[newRow][col]);

    this.tweens.add({
        targets: [column[i].sprite, column[i].text],
        x: newX,
        y: newY,
        duration: 300,
        ease: 'Bounce.easeOut'
    });
}
                    }
                }
            }

            updateTime() {
                const elapsedSeconds = Math.floor((this.time.now - this.gameStartTime) / 1000);
                const minutes = Math.floor(elapsedSeconds / 60);
                const seconds = elapsedSeconds % 60;
                this.timeText.setText(`时间: ${minutes}:${seconds.toString().padStart(2, '0')}`);
            }

            showMessage(text, color) {
                const message = this.add.text(350, 40, text, {
                    fontSize: '18px',
                    fill: color === 0xff0000 ? '#ff0000' : '#00ff00',
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0, 0, 0, 0.7)',
                    padding: { x: 10, y: 5 }
                }).setOrigin(0.5);
                
                this.tweens.add({
                    targets: message,
                    y: 20,
                    alpha: 0,
                    duration: 2000,
                    ease: 'Power2',
                    onComplete: () => {
                        message.destroy();
                    }
                });
            }

            showGameComplete() {
                const bg = this.add.rectangle(350, 450, 400, 300, 0x000000, 0.8);
                const title = this.add.text(350, 350, '游戏完成！', {
                    fontSize: '36px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                const scoreText = this.add.text(350, 420, `最终分数: ${this.score}`, {
                    fontSize: '24px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
                
                const movesText = this.add.text(350, 460, `总移动次数: ${this.moves}`, {
                    fontSize: '24px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
                
                const timeText = this.add.text(350, 500, `用时: ${Math.floor((this.time.now - this.gameStartTime) / 60000)}分${Math.floor(((this.time.now - this.gameStartTime) % 60000) / 1000)}秒`, {
                    fontSize: '24px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
                
                const restartBtn = this.add.rectangle(350, 580, 200, 50, 0x2ecc71);
                restartBtn.setStrokeStyle(3, 0x27ae60);
                const restartText = this.add.text(350, 580, '重新开始', {
                    fontSize: '20px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                restartBtn.setInteractive();
                restartBtn.on('pointerdown', () => {
                    this.scene.restart();
                });
            }

            shuffleGrid() {
                // 保存所有非空方块
                const tiles = [];
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < 9; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            tiles.push({
                                value: this.grid[row][col].value,
                                row: row,
                                col: col
                            });
                        }
                    }
                }
                
                // 打乱顺序
                Phaser.Utils.Array.Shuffle(tiles);
                
                // 重新分配
                let index = 0;
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < 9; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            const tile = tiles[index++];
                            this.grid[row][col].value = tile.value;
                            
                            // 更新视觉效果
                            if (this.grid[row][col].sprite) {
                                this.grid[row][col].sprite.setFillStyle(this.numberColors[tile.value]);
                            }
                            if (this.grid[row][col].text) {
                                this.grid[row][col].text.setText(tile.value);
                            }
                        }
                    }
                }
                
                this.moves++;
                this.movesText.setText(`移动: ${this.moves}`);
                this.showMessage('已重新排列数字！', 0x00ffff);
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: 700,
            height: 900,
            parent: 'game-container',
            scene: [NumberDragGame],
            
        };

        const game = new Phaser.Game(config);
        // game.renderer.smooth = true; // 启用平滑渲染
    </script>
</body>

</html>