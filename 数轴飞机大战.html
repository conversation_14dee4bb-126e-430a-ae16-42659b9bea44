<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数轴飞机大战</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }

        .game-info {
            color: white;
            text-align: center;
            margin-bottom: 20px;
        }

        .game-info h1 {
            margin: 0 0 10px 0;
            color: #FFD700;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
        }

        .controls {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            margin-top: 20px;
            backdrop-filter: blur(10px);
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-bottom: 15px;
        }

        .move-btn {
            background: linear-gradient(145deg, #4CAF50, #45a049);
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
        }

        .move-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }

        .move-btn:active {
            transform: translateY(0);
        }

        .move-btn:disabled {
            background: #666;
            cursor: not-allowed;
            transform: none;
        }

        .status {
            color: white;
            text-align: center;
            margin-top: 10px;
        }

        .game-canvas {
            border: 3px solid #FFD700;
            border-radius: 10px;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.3);
        }

        .fuel-bar {
            width: 300px;
            height: 20px;
            background-color: #444;
            border-radius: 10px;
            margin: 10px auto;
            overflow: hidden;
        }

        .fuel-level {
            height: 100%;
            background: linear-gradient(90deg, #ffcc00, #ff6600);
            transition: width 0.3s ease;
            width: 100%;
        }

        .restart-btn {
            background: linear-gradient(145deg, #ff6b6b, #ee5555);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            margin-top: 15px;
            transition: all 0.3s;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            display: none;
        }

        .restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
        }
    </style>
</head>

<body>
    <div class="game-info">
        <h1>🛩️ 数轴飞机大战 ✈️</h1>
        <p>通过选择移动数值控制飞机位置，击败敌机学习数轴概念！</p>
        <p>飞机正在自动发射子弹，注意控制油量！</p>
    </div>

    <div id="game-container"></div>

    <div class="controls">
        <div class="fuel-bar">
            <div class="fuel-level" id="fuelLevel"></div>
        </div>
        <div class="status">
            <p>油量: <span id="fuelAmount">10</span> | 我方位置: <span id="playerPos">0</span> | 分数: <span id="score">0</span> | 关卡: <span id="level">1</span></p>
        </div>
        <div class="control-buttons">
            <button class="move-btn" onclick="movePlayer(-3)">-3</button>
            <button class="move-btn" onclick="movePlayer(-2)">-2</button>
            <button class="move-btn" onclick="movePlayer(-1)">-1</button>
            <button class="move-btn" onclick="movePlayer(1)">+1</button>
            <button class="move-btn" onclick="movePlayer(2)">+2</button>
            <button class="move-btn" onclick="movePlayer(3)">+3</button>
        </div>
        <button class="restart-btn" id="restartBtn" onclick="restartGame()">重新开始游戏</button>
    </div>

    <script>
        let game;
        let gameState = {
            playerPosition: 0,
            enemies: [],
            score: 0,
            level: 1,
            fuel: 10,    // 初始油量
            maxFuel: 10, // 最大油量
            gameOver: false
        };

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
            }

            preload() {
                this.createPlayerSprite();
                this.createEnemySprite();
                this.createBulletSprite();
            }

            createPlayerSprite() {
                const graphics = this.add.graphics();
                graphics.fillStyle(0x00ff00);
                graphics.lineStyle(2, 0xffffff);
                graphics.beginPath();
                graphics.moveTo(20, 5);
                graphics.lineTo(35, 30);
                graphics.lineTo(20, 25);
                graphics.lineTo(5, 30);
                graphics.closePath();
                graphics.fillPath();
                graphics.strokePath();
                graphics.generateTexture('player', 40, 40);
                graphics.destroy();
            }

            createEnemySprite() {
                const graphics = this.add.graphics();
                graphics.fillStyle(0xff4444);
                graphics.lineStyle(2, 0xffffff);
                graphics.beginPath();
                graphics.moveTo(17.5, 30);
                graphics.lineTo(32.5, 5);
                graphics.lineTo(17.5, 10);
                graphics.lineTo(2.5, 5);
                graphics.closePath();
                graphics.fillPath();
                graphics.strokePath();
                graphics.generateTexture('enemy', 35, 35);
                graphics.destroy();
            }

            createBulletSprite() {
                const graphics = this.add.graphics();
                graphics.fillStyle(0xffff00);
                graphics.lineStyle(1, 0xffffff);
                graphics.beginPath();
                graphics.moveTo(5, 0);
                graphics.lineTo(8, 10);
                graphics.lineTo(5, 8);
                graphics.lineTo(2, 10);
                graphics.closePath();
                graphics.fillPath();
                graphics.strokePath();
                graphics.generateTexture('bullet', 10, 10);
                graphics.destroy();
            }

            create() {
                this.add.rectangle(400, 300, 800, 600, 0x001122);
                this.drawNumberLine();
                this.player = this.add.image(400, 500, 'player');
                this.player.setScale(1.2);
                this.createPlayerInfo();
                
                // 创建子弹组
                this.bullets = this.add.group();
                
                // 创建敌机组
                this.enemies = this.add.group();
                this.enemyInfos = []; // 单独管理敌机信息显示
                
                this.spawnEnemies();
                
                // 启动自动发射子弹
                this.bulletTimer = this.time.addEvent({
                    delay: 500,
                    callback: this.shootBullet,
                    callbackScope: this,
                    loop: true
                });
                
                // 定时生成敌机
                this.enemySpawnTimer = this.time.addEvent({
                    delay: 5000,
                    callback: this.spawnEnemies,
                    callbackScope: this,
                    loop: true
                });
                
                this.updateFuelUI();
            }

            createPlayerInfo() {
                this.playerInfo = this.add.text(this.player.x, this.player.y - 40, `位置: ${gameState.playerPosition}`, {
                    fontSize: '16px',
                    fill: '#00FF00',
                    backgroundColor: '#00000080',
                    padding: { x: 10, y: 5 },
                    borderRadius: 5
                }).setOrigin(0.5);
            }

            drawNumberLine() {
                this.add.line(400, 350, 0, 0, 720, 0, 0xffffff).setLineWidth(3);
                for (let i = -9; i <= 9; i++) {
                    const x = 400 + i * 40;
                    this.add.line(x, 350, 0, -10, 0, 10, 0xffffff).setLineWidth(2);
                    const numText = this.add.text(x, 370, i.toString(), {
                        fontSize: '14px',
                        fill: '#ffffff',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);
                    if (i === 0) {
                        numText.setColor('#FFD700');
                        this.add.circle(x, 350, 8, 0xFFD700, 0.3);
                    } else if (i % 5 === 0) {
                        numText.setColor('#00FFFF');
                    }
                }
                this.add.text(400, 320, '数轴 (X轴)', {
                    fontSize: '16px',
                    fill: '#FFD700',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
            }

            shootBullet() {
                if (!this.player || this.player.destroyed || gameState.gameOver) return;
                
                const bullet = this.add.image(this.player.x, this.player.y - 30, 'bullet');
                bullet.setScale(1.5);
                this.bullets.add(bullet);
                
                this.tweens.add({
                    targets: bullet,
                    y: 0,
                    duration: 1000,
                    onComplete: () => {
                        if (bullet && !bullet.destroyed) {
                            this.bullets.remove(bullet);
                            bullet.destroy();
                        }
                    }
                });
            }

            update() {
                if (gameState.gameOver) return;
                
                this.checkBulletsCollision();
                this.checkGameOver();
            }

            checkGameOver() {
                if (gameState.fuel <= 0 && !gameState.gameOver) {
                    this.gameOver();
                }
            }

            gameOver() {
                gameState.gameOver = true;
                
                // 停止所有定时器
                if (this.bulletTimer) {
                    this.bulletTimer.destroy();
                }
                if (this.enemySpawnTimer) {
                    this.enemySpawnTimer.destroy();
                }
                
                // 显示游戏结束界面
                const gameOverBg = this.add.rectangle(400, 300, 600, 400, 0x000000, 0.8);
                const gameOverText = this.add.text(400, 250, '游戏结束！', {
                    fontSize: '48px',
                    fill: '#ff4444',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                const finalScoreText = this.add.text(400, 320, `最终得分: ${gameState.score}`, {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                const finalLevelText = this.add.text(400, 360, `到达关卡: ${gameState.level}`, {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                
                // 显示重新开始按钮
                document.getElementById('restartBtn').style.display = 'block';
                
                // 禁用移动按钮
                const moveButtons = document.querySelectorAll('.move-btn');
                moveButtons.forEach(btn => {
                    btn.disabled = true;
                });
            }

            checkBulletsCollision() {
                if (!this.bullets || !this.enemies) return;
                
                this.bullets.getChildren().forEach(bullet => {
                    if (!bullet || bullet.destroyed) return;
                    
                    this.enemies.getChildren().forEach(enemy => {
                        if (enemy && !enemy.destroyed && enemy.hp > 0 &&
                            Phaser.Geom.Intersects.RectangleToRectangle(
                                bullet.getBounds(), 
                                enemy.getBounds()
                            )) {
                            this.animateAttack(enemy);
                            this.bullets.remove(bullet);
                            bullet.destroy();
                        }
                    });
                });
            }

            spawnEnemies() {
                if (gameState.gameOver) return;
                
                // 完全清除当前敌机和信息
                this.clearAllEnemies();
                
                const enemyCount = Phaser.Math.Between(2, 4 + Math.floor(gameState.level / 3));
                const positions = this.getUniquePositions(enemyCount);
                
                positions.forEach(pos => {
                    const enemy = this.createEnemy(pos);
                    this.enemies.add(enemy);
                    
                    // 创建敌机信息显示
                    const infoContainer = this.createEnemyInfo(enemy);
                    enemy.infoContainer = infoContainer;
                    this.enemyInfos.push(infoContainer);
                });
            }

            clearAllEnemies() {
                // 清除所有敌机信息显示
                this.enemyInfos.forEach(info => {
                    if (info && !info.destroyed) {
                        info.destroy();
                    }
                });
                this.enemyInfos = [];
                
                // 清除所有敌机
                if (this.enemies) {
                    this.enemies.clear(true, true);
                }
                
                // 清除所有子弹
                if (this.bullets) {
                    this.bullets.clear(true, true);
                }
            }

            getUniquePositions(count) {
                const positions = [];
                while (positions.length < count) {
                    const pos = Phaser.Math.Between(-8, 8);
                    if (!positions.includes(pos)) {
                        positions.push(pos);
                    }
                }
                return positions;
            }

            createEnemy(pos) {
                const x = 400 + pos * 40;
                const enemy = this.add.image(x, 200, 'enemy');
                enemy.setScale(1.1);
                
                const baseHp = Phaser.Math.Between(1, 3);
                const levelBonus = Math.floor(gameState.level / 2);
                const hp = Math.max(1, baseHp + levelBonus);
                
                enemy.hp = hp;
                enemy.maxHp = hp;
                enemy.position = pos;
                enemy.isDestroyed = false;
                
                return enemy;
            }

            createEnemyInfo(enemy) {
                const infoContainer = this.add.container(enemy.x, 150);
                
                const posText = this.add.text(0, 0, `位置: ${enemy.position}`, {
                    fontSize: '12px',
                    fill: '#ffffff',
                    backgroundColor: '#000000',
                    padding: { x: 5, y: 2 }
                }).setOrigin(0.5);
                
                const hpBg = this.add.rectangle(0, 20, 30, 6, 0x444444);
                const hpBar = this.add.rectangle(-15 + (enemy.hp / enemy.maxHp) * 15, 20, (enemy.hp / enemy.maxHp) * 30, 6, 0xff4444);
                const hpText = this.add.text(0, 35, `HP: ${enemy.hp}`, {
                    fontSize: '10px',
                    fill: '#ffffff'
                }).setOrigin(0.5);
                
                infoContainer.add([posText, hpBg, hpBar, hpText]);
                
                // 保存引用以便更新
                enemy.hpBar = hpBar;
                enemy.hpText = hpText;
                
                return infoContainer;
            }

            updatePlayerPosition() {
                const newX = 400 + gameState.playerPosition * 40;
                this.tweens.add({
                    targets: this.player,
                    x: newX,
                    duration: 500,
                    ease: 'Power2',
                    onUpdate: () => {
                        if (this.playerInfo) {
                            this.playerInfo.setX(this.player.x);
                        }
                    }
                });
                
                if (this.playerInfo) {
                    this.playerInfo.setText(`位置: ${gameState.playerPosition}`);
                }
                
                this.updateUIElement('playerPos', gameState.playerPosition);
            }

            animateAttack(enemy) {
                if (!enemy || enemy.destroyed || enemy.isDestroyed) return;
                
                this.tweens.add({
                    targets: enemy,
                    scaleX: 1.3,
                    scaleY: 1.3,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        if (!enemy || enemy.destroyed || enemy.isDestroyed) return;
                        
                        enemy.hp--;
                        
                        // 更新血条显示
                        if (enemy.hpBar && enemy.hpText && enemy.infoContainer && !enemy.infoContainer.destroyed) {
                            const hpRatio = Math.max(0, enemy.hp / enemy.maxHp);
                            enemy.hpBar.setScale(hpRatio, 1);
                            enemy.hpBar.setX(-15 + hpRatio * 15);
                            enemy.hpText.setText(`HP: ${enemy.hp}`);
                        }
                        
                        if (enemy.hp <= 0) {
                            this.destroyEnemy(enemy);
                            gameState.score += 10;
                            gameState.fuel = Math.min(gameState.fuel + 2, gameState.maxFuel);
                            this.updateFuelUI();
                            this.updateUIElement('score', gameState.score);
                            this.checkLevelComplete();
                        }
                    }
                });
            }

            destroyEnemy(enemy) {
                if (!enemy || enemy.destroyed || enemy.isDestroyed) return;
                
                enemy.isDestroyed = true;
                
                // 创建爆炸效果
                const explosion = this.add.circle(enemy.x, enemy.y, 30, 0xff4444, 0.7);
                this.tweens.add({
                    targets: explosion,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 500,
                    onComplete: () => {
                        if (explosion && !explosion.destroyed) {
                            explosion.destroy();
                        }
                    }
                });
                
                // 销毁信息显示
                if (enemy.infoContainer && !enemy.infoContainer.destroyed) {
                    const index = this.enemyInfos.indexOf(enemy.infoContainer);
                    if (index > -1) {
                        this.enemyInfos.splice(index, 1);
                    }
                    enemy.infoContainer.destroy();
                }
                
                // 从敌机组中移除并销毁
                this.enemies.remove(enemy);
                enemy.destroy();
            }

            checkLevelComplete() {
                if (gameState.gameOver) return;
                
                const aliveEnemies = this.enemies.getChildren().filter(enemy => 
                    enemy && !enemy.destroyed && !enemy.isDestroyed && enemy.hp > 0
                );
                
                if (aliveEnemies.length === 0) {
                    gameState.level++;
                    this.updateUIElement('level', gameState.level);
                    
                    const levelText = this.add.text(400, 300, `关卡 ${gameState.level - 1} 完成！\n准备下一关...`, {
                        fontSize: '24px',
                        fill: '#FFD700',
                        fontWeight: 'bold',
                        align: 'center'
                    }).setOrigin(0.5);
                    
                    gameState.fuel = Math.min(gameState.fuel + 5, gameState.maxFuel);
                    this.updateFuelUI();
                    
                    this.time.delayedCall(2000, () => {
                        if (levelText && !levelText.destroyed) {
                            levelText.destroy();
                        }
                        this.spawnEnemies();
                    });
                }
            }

            updateUIElement(id, value) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }
            
            updateFuelUI() {
                const fuelAmount = document.getElementById('fuelAmount');
                const fuelLevel = document.getElementById('fuelLevel');
                
                if (fuelAmount) {
                    fuelAmount.textContent = gameState.fuel;
                }
                
                if (fuelLevel) {
                    const fuelPercentage = (gameState.fuel / gameState.maxFuel) * 100;
                    fuelLevel.style.width = `${fuelPercentage}%`;
                    
                    if (fuelPercentage < 20) {
                        fuelLevel.style.background = 'linear-gradient(90deg, #ff0000, #ff6600)';
                    } else if (fuelPercentage < 50) {
                        fuelLevel.style.background = 'linear-gradient(90deg, #ffcc00, #ff6600)';
                    } else {
                        fuelLevel.style.background = 'linear-gradient(90deg, #00ff00, #00cc00)';
                    }
                }
                
                const moveButtons = document.querySelectorAll('.move-btn');
                moveButtons.forEach(btn => {
                    btn.disabled = gameState.fuel <= 0 || gameState.gameOver;
                });
            }
        }

        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#001122',
            scene: GameScene,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        function initGame() {
            if (typeof Phaser !== 'undefined') {
                game = new Phaser.Game(config);
            } else {
                console.log('等待Phaser加载...');
                setTimeout(initGame, 100);
            }
        }

        window.addEventListener('load', () => {
            initGame();
            updateUIElement('playerPos', gameState.playerPosition);
            updateUIElement('score', gameState.score);
            updateUIElement('level', gameState.level);
            updateUIElement('fuelAmount', gameState.fuel);
        });

        function movePlayer(value) {
            if (gameState.gameOver) return;
            
            if (gameState.fuel <= 0) {
                const scene = game.scene.scenes[0];
                if (scene && !scene.scene.isPaused()) {
                    const warningText = scene.add.text(400, 450, '油量不足，游戏结束！', {
                        fontSize: '18px',
                        fill: '#ff4444',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);
                    scene.time.delayedCall(1500, () => {
                        if (warningText && !warningText.destroyed) {
                            warningText.destroy();
                        }
                    });
                }
                return;
            }
            
            const newPosition = gameState.playerPosition + value;
            if (newPosition >= -9 && newPosition <= 9) {
                gameState.playerPosition = newPosition;
                gameState.fuel--;
                game.scene.scenes[0].updateFuelUI();
                game.scene.scenes[0].updatePlayerPosition();
            } else {
                const scene = game.scene.scenes[0];
                const warningText = scene.add.text(400, 450, '无法移动超出数轴范围！', {
                    fontSize: '18px',
                    fill: '#ff4444',
                    fontWeight: 'bold'
                }).setOrigin(0.5);
                scene.time.delayedCall(1500, () => {
                    if (warningText && !warningText.destroyed) {
                        warningText.destroy();
                    }
                });
            }
        }

        function restartGame() {
            // 重置游戏状态
            gameState = {
                playerPosition: 0,
                enemies: [],
                score: 0,
                level: 1,
                fuel: 10,
                maxFuel: 10,
                gameOver: false
            };
            
            // 隐藏重新开始按钮
            document.getElementById('restartBtn').style.display = 'none';
            
            // 重新启动游戏
            if (game) {
                game.destroy(true);
            }
            
            setTimeout(() => {
                initGame();
                updateUIElement('playerPos', gameState.playerPosition);
                updateUIElement('score', gameState.score);
                updateUIElement('level', gameState.level);
                updateUIElement('fuelAmount', gameState.fuel);
            }, 100);
        }

        function updateUIElement(id, value) {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        }
    </script>
</body>

</html>