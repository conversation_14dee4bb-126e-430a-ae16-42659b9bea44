<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>合成</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #080808 0%, #1a3009 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .ui-text {
            font-family: 'Arial', sans-serif;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 60; // 建造格子尺寸
        // const TOWER_SLOT_COUNT = 5; // 防御塔槽位数量 - 已移除防御塔区域
        const GRID_SIZE = 7; // 建造区域大小 7x7

        // 物品类型 - 合成物品系统
        const BUILDING_TYPES = [
            { id: 0, color: 0x8B4513, emoji: '📦', name: '基地', level: 0, canMerge: false, isDefense: false },
            // 1级水果
            { id: 1, color: 0x32CD32, emoji: '🍏', name: '苹果', level: 1, canMerge: true, isDefense: false },
            { id: 2, color: 0x4169E1, emoji: '🍊', name: '橙子', level: 1, canMerge: true, isDefense: false },
            { id: 3, color: 0xFF69B4, emoji: '🍌', name: '香蕉', level: 1, canMerge: true, isDefense: false },
            { id: 4, color: 0x9370DB, emoji: '🍇', name: '葡萄', level: 1, canMerge: true, isDefense: false },
            // 2级水果
            { id: 5, color: 0xFF6347, emoji: '🍓', name: '草莓', level: 2, canMerge: true, isDefense: false },
            { id: 6, color: 0xFFD700, emoji: '🍒', name: '樱桃', level: 2, canMerge: true, isDefense: false },
            { id: 7, color: 0x8A2BE2, emoji: '🫐', name: '蓝莓', level: 2, canMerge: true, isDefense: false },
            { id: 8, color: 0xFF1493, emoji: '🍅', name: '西红柿', level: 2, canMerge: true, isDefense: false },
            // 3级水果
            { id: 9, color: 0xFF8C00, emoji: '🥭', name: '芒果', level: 3, canMerge: true, isDefense: false },
            { id: 10, color: 0x32CD32, emoji: '🥝', name: '猕猴桃', level: 3, canMerge: true, isDefense: false },
            { id: 11, color: 0xFFB6C1, emoji: '🍑', name: '桃子', level: 3, canMerge: true, isDefense: false },
            { id: 12, color: 0x9370DB, emoji: '🍈', name: '哈密瓜', level: 3, canMerge: true, isDefense: false },
            // 4级水果
            { id: 13, color: 0xFF4500, emoji: '🍍', name: '菠萝', level: 4, canMerge: true, isDefense: false },
            { id: 14, color: 0x228B22, emoji: '🥥', name: '椰子', level: 4, canMerge: true, isDefense: false },
            { id: 15, color: 0xDC143C, emoji: '🍉', name: '西瓜', level: 4, canMerge: true, isDefense: false },
            { id: 16, color: 0xFFD700, emoji: '🍋', name: '柠檬', level: 4, canMerge: true, isDefense: false }
        ];

        // 客户头像列表
        const CUSTOMER_AVATARS = ['🙍', '🙍‍♂️', '🙍‍♀️', '🙎‍♂️', '🙎‍♀️', '💁', '💁‍♂️', '👩‍⚕️', '👩‍💼', '👨‍🍳', '👩‍🍳', '👨‍🌾', '👩‍🌾'];

        // 合成规则：两个相同水果合成一个固定的新水果
        const MERGE_RULES = {
            // 1级水果合成2级水果
            1: 5,  // 两个苹果 -> 草莓
            2: 6,  // 两个橙子 -> 樱桃
            3: 7,  // 两个香蕉 -> 蓝莓
            4: 8,  // 两个葡萄 -> 红莓
            // 2级水果合成3级水果
            5: 9,  // 两个草莓 -> 芒果
            6: 10, // 两个樱桃 -> 猕猴桃
            7: 11, // 两个蓝莓 -> 桃子
            8: 12, // 两个红莓 -> 哈密瓜
            // 3级水果合成4级水果
            9: 13,  // 两个芒果 -> 菠萝
            10: 14, // 两个猕猴桃 -> 椰子
            11: 15, // 两个桃子 -> 西瓜
            12: 16  // 两个哈密瓜 -> 柠檬
        };

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.buildGrid = []; // 7x7建造网格
                this.buildings = []; // 已建造的建筑
                this.gold = 1000; // 初始金币
                this.selectedBuilding = null; // 选中的建筑
                this.basePosition = { row: 3, col: 3 }; // 基地位置（中心）
                this.baseLevel = 1; // 基地等级
                this.isGenerating = false; // 是否正在生成水果
            }

            preload() {
                // 创建建筑纹理
                this.createBuildingTextures();
            }

            create() {
                // 设置背景（草地色调）
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0xc8dbe2);

                // 创建设置按钮（左上角）
                this.createSettingsButton();

                // 创建金币显示（右上角）
                this.goldText = this.add.text(GAME_WIDTH - 30, 30, '🪙 1000', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);



                // 创建游戏标题
              
                // 建造区域（7x7网格，居中显示）
                this.buildArea = {
                    x: 40,
                    y: 200,
                    width: GAME_WIDTH - 80,
                    height: 600
                };

                // 创建订单系统
                this.createOrderSystem();

                // 创建建造网格
                this.createBuildGrid();

                // 创建控制按钮区域
                this.createControlArea();

                // 初始化游戏
                this.initializeGame();
            }

            createBuildingTextures() {
                BUILDING_TYPES.forEach((buildingType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆角矩形背景
                    // graphics.fillStyle(buildingType.color);
                    // graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);

                    // 添加边框
                  

                    graphics.generateTexture(`building_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }





            createSettingsButton() {
                // 设置按钮位置和大小
                const buttonSize = 50;
                const buttonX = 30;
                const buttonY = 30;

                // 创建设置按钮背景
                const settingsBg = this.add.graphics();
                settingsBg.fillStyle(0x34495E, 0.9);
                settingsBg.fillRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);
                settingsBg.lineStyle(2, 0x2C3E50, 1);
                settingsBg.strokeRoundedRect(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize, 10);

                // 设置按钮可交互
                settingsBg.setInteractive(new Phaser.Geom.Rectangle(buttonX - buttonSize/2, buttonY - buttonSize/2, buttonSize, buttonSize), Phaser.Geom.Rectangle.Contains);

                // 创建设置图标
                const settingsIcon = this.add.text(buttonX, buttonY, '⚙️', {
                    fontSize: '28px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加悬停效果
               

                // 添加点击事件（暂时只显示提示）
                settingsBg.on('pointerdown', () => {
                    this.showToolTip('设置', buttonX, buttonY - 35);
                });
            }

            createInstructionArea() {
                // 显示加载游戏文字，2秒后消失
                const loadingText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2, '加载游戏中...', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                loadingText.setDepth(1000);

                // 2秒后让加载文字消失
                this.time.delayedCall(2000, () => {
                    this.tweens.add({
                        targets: loadingText,
                        alpha: 0,
                        duration: 500,
                        ease: 'Power2',
                        onComplete: () => {
                            loadingText.destroy();
                        }
                    });
                });
            }

    

            createOrderSystem() {
                // 创建订单显示区域 - 再次放大
                this.orderArea = this.add.graphics();
                this.orderArea.fillStyle(0x4169E1, 0.3);
                this.orderArea.fillRoundedRect(0, 120, GAME_WIDTH, 240, 10); // 高度从160增加到200，Y从80调整到60
                this.orderArea.lineStyle(2, 0x4169E1, 0.8);
                this.orderArea.strokeRoundedRect(0, 120, GAME_WIDTH, 240, 10);

             

                // 初始化订单数据
                this.currentOrders = [];
                this.orderSlots = [];

                // 创建3个订单槽位 - 再次调整位置和大小
                for (let i = 0; i < 3; i++) {
                    const slotX = 180 + i * 200; // 间距从150增加到160，起始位置调整
                    const slotY = 280; // 保持Y位置

                    // 订单槽位背景 - 再次放大尺寸
                    const slotBg = this.add.graphics();
                  

                    const orderSlot = {
                        x: slotX,
                        y: slotY,
                        background: slotBg,
                        order: null,
                        itemIcon: null,
                        levelText: null,
                        countText: null,
                        completeButton: null,
                        avatarIcon: null
                    };

                    this.orderSlots.push(orderSlot);
                }

                // 生成初始订单
                this.generateNewOrders();
            }

            generateNewOrders() {
                // 为每个空槽位生成新订单
                this.orderSlots.forEach((slot, index) => {
                    if (!slot.order) {
                        this.generateOrderForSlot(slot);
                    }
                });
            }

            generateOrderForSlot(slot) {
                // 生成随机订单 - 现在包括所有等级的水果
                const allFruitTypes = [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16]; // 2-4级水果
                const fruitType = Phaser.Utils.Array.GetRandom(allFruitTypes);
                const count = Phaser.Math.Between(1, 2); // 需要数量1-2
                const avatar = Phaser.Utils.Array.GetRandom(CUSTOMER_AVATARS); // 随机选择客户头像

                const order = {
                    type: fruitType,
                    level: BUILDING_TYPES[fruitType].level,
                    count: count,
                    completed: 0, // 已完成数量
                    customerAvatar: avatar // 客户头像
                };

                slot.order = order;

                // 创建订单显示
                this.updateOrderSlotDisplay(slot);
            }

            updateOrderSlotDisplay(slot) {
                // 清除旧的显示
                if (slot.itemIcon) slot.itemIcon.destroy();
                if (slot.levelText) slot.levelText.destroy();
                if (slot.countText) slot.countText.destroy();
                if (slot.completeButton) slot.completeButton.destroy();
                if (slot.buttonBackground) slot.buttonBackground.destroy(); // 清除按钮背景
                if (slot.avatarIcon) slot.avatarIcon.destroy();
                if (slot.stallBackground) slot.stallBackground.destroy();

                if (!slot.order) return;

                const order = slot.order;

                // 客户头像（大尺寸，占区域高度的2/3）
                slot.avatarIcon = this.add.text(slot.x, slot.y - 30, order.customerAvatar, {
                    fontSize: '200px', // 从24px增加到48px，占高度的2/3
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建小的摊位背景（水果背景）
                slot.stallBackground = this.add.graphics();
                slot.stallBackground.fillStyle(0xFFFFFF, 1);
                slot.stallBackground.fillRoundedRect(slot.x - 85, slot.y + 25, 150, 50, 8); // 小的摊位背景
                slot.stallBackground.lineStyle(2, 0xFFFFFF, 0.6);
                slot.stallBackground.strokeRoundedRect(slot.x - 85, slot.y + 25, 150, 50, 8);

                // 物品图标（在摊位背景内）
                slot.itemIcon = this.add.text(slot.x - 60, slot.y + 50, BUILDING_TYPES[order.type].emoji, {
                    fontSize: '34px', // 适中的水果图标大小
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 等级显示（在摊位内）
              

                // 数量显示（在摊位内）
                const countColor = order.completed >= order.count ? '#00FF00' : '#000000';
                slot.countText = this.add.text(slot.x + 5, slot.y + 50, `${order.completed}/${order.count}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: countColor,
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 如果订单可以完成，显示完成按钮
                if (this.canCompleteOrder(order)) {
                    // 创建圆角绿色背景
                    const buttonBg = this.add.graphics();
                    buttonBg.fillStyle(0x00AA00, 1); // 绿色背景
                    buttonBg.fillRoundedRect(slot.x - 25, slot.y + 35, 100, 40, 10); // 圆角矩形

                    slot.completeButton = this.add.text(slot.x+30, slot.y + 55, '完成', {
                        fontSize: '24px',
                        fontFamily: 'Arial, sans-serif',
                        color: '#FFFFFF', // 白色字体
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5).setInteractive();

                    // 将背景和按钮组合
                    slot.buttonBackground = buttonBg;

                    slot.completeButton.on('pointerdown', () => {
                        this.submitOrderWithAnimation(slot);
                    });


                }
            }

            canCompleteOrder(order) {
                // 检查是否有足够的物品来完成订单
                if (!this.buildGrid || !order) return false;

                let availableCount = 0;

                // 遍历所有格子，计算可用的物品数量
                for (let row = 0; row < GRID_SIZE; row++) {
                    if (!this.buildGrid[row]) continue;
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        if (cell && cell.building &&
                            cell.building.type === order.type &&
                            !cell.isGray) { // 只计算非灰色水果
                            availableCount++;
                        }
                    }
                }

                return availableCount >= (order.count - order.completed);
            }

            submitOrderWithAnimation(slot) {
                const order = slot.order;
                const neededCount = order.count - order.completed;
                let submittedCount = 0;

                // 找到需要提交的物品
                const itemsToSubmit = [];
                for (let row = 0; row < GRID_SIZE && submittedCount < neededCount; row++) {
                    for (let col = 0; col < GRID_SIZE && submittedCount < neededCount; col++) {
                        const cell = this.buildGrid[row][col];
                        if (cell.building &&
                            cell.building.type === order.type &&
                            !cell.isGray) {
                            itemsToSubmit.push(cell);
                            submittedCount++;
                        }
                    }
                }

                // 创建飞行动画
                itemsToSubmit.forEach((cell, index) => {
                    this.time.delayedCall(index * 200, () => {
                        this.createFlyingAnimation(cell, slot);
                    });
                });
            }

            createFlyingAnimation(cell, orderSlot) {
                const building = cell.building;

                // 创建飞行的物品
                const flyingItem = this.add.text(cell.x, cell.y, BUILDING_TYPES[building.type].emoji, {
                    fontSize: '42px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建飞行动画
                this.tweens.add({
                    targets: flyingItem,
                    x: orderSlot.x,
                    y: orderSlot.y - 15,
                    scaleX: 0.5,
                    scaleY: 0.5,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        flyingItem.destroy();

                        // 更新订单进度
                        orderSlot.order.completed++;
                        this.updateOrderSlotDisplay(orderSlot);

                        // 检查订单是否完成
                        if (orderSlot.order.completed >= orderSlot.order.count) {
                            this.completeOrder(orderSlot);
                        }
                    }
                });

                // 移除原物品
                const cellSize = this.getCellSize();

                // 如果被移除的是选中的建筑，清除选中状态
                if (this.selectedBuilding && this.selectedBuilding === cell) {
                    this.deselectBuilding();
                }

                building.container.destroy();
                cell.building = null;
                cell.isEmpty = true;
                cell.background.clear();
                cell.background.fillStyle(0xFFFFFF, 0.1);
                cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                cell.background.lineStyle(1, 0x228B22, 0.5);
                cell.background.strokeRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
            }

            checkOrderCompletion(itemType, itemLevel) {
                // 只更新订单显示，不自动完成订单
                this.orderSlots.forEach(slot => {
                    if (slot.order) {
                        // 更新订单显示（可能会显示完成按钮）
                        this.updateOrderSlotDisplay(slot);
                    }
                });
            }

            completeOrder(slot) {
                const order = slot.order;
                const reward = order.level * order.count * 50; // 奖励金币

                // 给予奖励
                this.gold += reward;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 显示完成消息
                this.showMessage(`订单完成！获得 ${reward} 金币！`, '#00FF00');

                // 清除订单
                slot.order = null;
                this.updateOrderSlotDisplay(slot);

                // 延迟生成新订单
                this.time.delayedCall(1000, () => {
                    this.generateOrderForSlot(slot);
                });
            }

            createBuildGrid() {
                // 创建7x7建造网格 - 调整位置并移除间距
                this.buildGrid = [];
                const buildAreaY = 400; // 从280调整到300，为更大的订单区域留出空间
                const buildAreaHeight = 700; // 从520调整到500
                this.gridSpacing = (GAME_WIDTH - 80) / GRID_SIZE; // 格子大小，无间距，设为实例变量
                const startX = 40 + this.gridSpacing / 2;
                const startY = buildAreaY + 20 + this.gridSpacing / 2;

                // 创建网格背景
                const gridBg = this.add.graphics();
                gridBg.fillStyle(0xdce0d7, 1);
                gridBg.fillRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);
                gridBg.lineStyle(5, 0xa0c2da, 0.8);
                gridBg.strokeRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);

                for (let row = 0; row < GRID_SIZE; row++) {
                    this.buildGrid[row] = [];
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const x = startX + col * this.gridSpacing;
                        const y = startY + row * this.gridSpacing;
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);

                        // 创建网格格子
                        const gridCell = this.add.graphics();

                        // 计算棋盘格模式：(row + col) % 2 决定颜色
                        const isEvenSquare = (row + col) % 2 === 0;

                        if (isCenter) {
                            // 基地格子 - 棕色
                            gridCell.fillStyle(0x8B4513, 0.8);
                            gridCell.fillRect(x - this.gridSpacing/2, y - this.gridSpacing/2, this.gridSpacing, this.gridSpacing);
                            gridCell.lineStyle(1, 0x654321, 0.9);
                            gridCell.strokeRect(x - this.gridSpacing/2, y - this.gridSpacing/2, this.gridSpacing, this.gridSpacing);
                        } else {
                            // 棋盘格模式：灰白交叉
                            if (isEvenSquare) {
                                // 白色格子
                                gridCell.fillStyle(0xcdd0c9, 1);
                                gridCell.fillRect(x - this.gridSpacing/2, y - this.gridSpacing/2, this.gridSpacing, this.gridSpacing);
                             
                            } else {
                                // 灰色格子
                                gridCell.fillStyle(0xdce0d7, 1);
                                gridCell.fillRect(x - this.gridSpacing/2, y - this.gridSpacing/2, this.gridSpacing, this.gridSpacing);
                           
                            }
                        }

                        // 设置交互 - 使用新的格子大小
                        gridCell.setInteractive(new Phaser.Geom.Rectangle(x - this.gridSpacing/2, y - this.gridSpacing/2, this.gridSpacing, this.gridSpacing), Phaser.Geom.Rectangle.Contains);

                        const cell = {
                            x: x,
                            y: y,
                            row: row,
                            col: col,
                            background: gridCell,
                            building: null,
                            isEmpty: !isCenter,
                            isBase: isCenter
                        };

                        // 如果是基地，创建基地建筑
                        if (isCenter) {
                            this.createBaseBuilding(cell); // 创建基地建筑
                        }

                        // 添加点击事件处理
                        gridCell.on('pointerdown', () => {
                            this.onCellClick(cell);
                        });

                      

                        this.buildGrid[row][col] = cell;
                    }
                }

                // 添加灰色固定物品在网格周围
                this.createFixedGrayItems();
            }

            createFixedGrayItems() {
                // 在网格内创建固定的灰色物品（除了基地周围）
                this.fixedGrayItems = [];
                const baseRow = Math.floor(GRID_SIZE / 2);
                const baseCol = Math.floor(GRID_SIZE / 2);

                // 遍历所有网格位置
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];

                        // 跳过基地位置
                        if (cell.isBase) {
                            continue;
                        }

                        // 跳过基地周围的8个位置
                        const isAroundBase = Math.abs(row - baseRow) <= 1 && Math.abs(col - baseCol) <= 1;
                        if (isAroundBase) {
                            continue;
                        }

                        // 在这个位置创建灰色水果
                        this.createGrayFruitInCell(cell);
                    }
                }
            }

            getCellSize() {
                return this.gridSpacing || TILE_SIZE; // 回退到 TILE_SIZE 如果 gridSpacing 未定义
            }

            createGrayFruitInCell(cell) {
                // 随机选择一个水果类型
                const fruitType = Phaser.Math.Between(1, 4);

                // 保持棋盘格背景
                const cellSize = this.getCellSize();
                const isEvenSquare = (cell.row + cell.col) % 2 === 0;
                cell.background.clear();

                if (isEvenSquare) {
                    // 白色格子
                    cell.background.fillStyle(0xcdd0c9, 1);
                    cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    cell.background.fillStyle(0xdce0d7, 1);
                    cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                }

                // 创建物品容器
                const itemContainer = this.add.container(cell.x, cell.y);

                // 创建物品表情（去掉背景，增大图标，灰色调）
                const itemEmoji = this.add.text(0, 0, BUILDING_TYPES[fruitType].emoji, {
                    fontSize: '58px', // 从32px增加到48px
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                itemEmoji.setTint(0x808080); // 设置灰色调

                itemContainer.add([itemEmoji]); // 只添加表情，去掉背景

                // 创建灰色水果对象
                const grayFruit = {
                    type: fruitType,
                    level: 1,
                    canMerge: true,
                    isGray: true, // 标记为灰色水果
                    container: itemContainer
                };

                // 将灰色水果放入格子
                cell.building = grayFruit;
                cell.isEmpty = false;
                cell.isGray = true; // 标记格子包含灰色水果

                this.fixedGrayItems.push({
                    cell: cell,
                    fruit: grayFruit
                });
            }

            createBuilding(cell, buildingTypeId) {
                const buildingType = BUILDING_TYPES[buildingTypeId];

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑表情（去掉背景，增大图标）
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '58px', // 从32px增加到48px
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 只添加表情，去掉等级显示
                buildingContainer.add([buildingEmoji]);

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: buildingTypeId,
                    level: buildingType.level,
                    canMerge: buildingType.canMerge,
                    isDefense: buildingType.isDefense,
                    cell: cell // 存储格子引用
                };

                // 如果不是基地，添加建造动画
                if (buildingTypeId !== 0) {
                    buildingContainer.setScale(0);
                    this.tweens.add({
                        targets: buildingContainer,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }



                return cell.building;
            }

            createBuildingWithLevel(cell, buildingTypeId, level) {
                const buildingType = BUILDING_TYPES[buildingTypeId];

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 建筑表情（去掉背景，增大图标）
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '48px', // 从32px增加到48px
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 只添加表情，去掉等级显示
                buildingContainer.add([buildingEmoji]);

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: buildingTypeId,
                    level: level,
                    canMerge: buildingType.canMerge,
                    isDefense: buildingType.isDefense,
                    cell: cell
                };

                // 如果不是基地，添加建造动画
                if (buildingTypeId !== 0) {
                    buildingContainer.setScale(0);
                    this.tweens.add({
                        targets: buildingContainer,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 300,
                        ease: 'Back.easeOut'
                    });
                }



                // 检查基地升级
                this.checkBaseUpgrade();

                return cell.building;
            }

            createBaseBuilding(cell) {
                const buildingType = BUILDING_TYPES[0]; // 基地

                // 创建建筑容器
                const buildingContainer = this.add.container(cell.x, cell.y);

                // 基地表情（更大的图标，去掉背景）
                const buildingEmoji = this.add.text(0, 0, buildingType.emoji, {
                    fontSize: '72px', // 从48px增加到72px，让基地更大
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 只添加表情，去掉等级显示
                buildingContainer.add([buildingEmoji]);

                // 更新格子状态
                cell.isEmpty = false;
                cell.building = {
                    container: buildingContainer,
                    type: 0,
                    level: this.baseLevel,
                    canMerge: false,
                    isDefense: false,
                    cell: cell
                };

                // 清除基地格子的背景
                cell.background.clear();

                return cell.building;
            }



            checkBaseUpgrade() {
                // 检查是否需要升级基地
                let maxBuildingLevel = 1;

                // 遍历所有建筑，找到最高等级
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row] && this.buildGrid[row][col];
                        if (cell && cell.building && cell.building.type !== 0) { // 不包括基地本身
                            maxBuildingLevel = Math.max(maxBuildingLevel, cell.building.level);
                        }
                    }
                }

                // 基地等级应该等于最高建筑等级
                if (maxBuildingLevel > this.baseLevel) {
                    this.upgradeBase(maxBuildingLevel);
                }
            }

            upgradeBase(newLevel) {
                this.baseLevel = newLevel;

                // 找到基地格子
                const baseCell = this.buildGrid[this.basePosition.row][this.basePosition.col];
                if (!baseCell || !baseCell.building) return;

                // 重新创建基地建筑
                baseCell.building.container.destroy();
                this.createBaseBuilding(baseCell);

                // 显示升级消息
                this.showMessage(`基地升级到${newLevel}级！`, '#FFD700');
            }







            onCellClick(cell) {
                if (cell.isEmpty) {
                    // 点击空格子 - 如果有选中的建筑，移动到这里
                    if (this.selectedBuilding && this.selectedBuilding !== cell) {
                        this.moveBuildingToCell(this.selectedBuilding, cell);
                    } else {
                        // 点击空格子不再生成建筑
                        this.deselectBuilding();
                    }
                } else if (cell.isBase) {
                    // 点击基地，在周围生成建筑，扣除100金币
                    this.generateBuildingAroundBase(cell);
                } else if (cell.building) {
                    // 点击已有建筑（包括灰色水果）
                    if (cell.isGray) {
                        // 点击灰色水果
                        if (this.selectedBuilding && !this.selectedBuilding.isGray) {
                            // 有选中的普通建筑，尝试与灰色水果合成
                            if (this.canMergeWithGrayFruit(this.selectedBuilding, cell)) {
                                this.mergeWithGrayFruit(this.selectedBuilding, cell);
                            } else {
                                this.showMessage('无法与灰色水果合成！', '#FF4444');
                            }
                        } else {
                            // 灰色水果不能被选中
                            this.showMessage('灰色水果不能选中，只能作为合成目标！', '#FF4444');
                        }
                    } else {
                        // 点击普通建筑
                        if (this.selectedBuilding === cell) {
                            // 如果已经选中，取消选中
                            this.deselectBuilding();
                        } else if (this.selectedBuilding && this.selectedBuilding !== cell) {
                            // 有选中的建筑，尝试合成或移动
                            // 两个都是普通建筑
                            if (this.canMergeBuildings(this.selectedBuilding, cell)) {
                                this.mergeBuildings(this.selectedBuilding, cell);
                            } else {
                                this.showMessage('无法合成这两个建筑！', '#FF4444');
                            }
                        } else {
                            // 检查是否可以提交订单（双击检测）
                            if (this.lastClickedCell === cell && this.time.now - this.lastClickTime < 500) {
                                // 双击，尝试提交订单
                                this.trySubmitOrder(cell);
                            } else {
                                // 选中建筑（用于移动）
                                this.selectBuilding(cell);
                            }
                            this.lastClickedCell = cell;
                            this.lastClickTime = this.time.now;
                        }
                    }
                }
            }

            trySubmitOrder(cell) {
                // 尝试提交订单
                const building = cell.building;
                if (!building) return;

                // 灰色水果不能提交到订单
                if (cell.isGray) {
                    this.showMessage('灰色水果不能提交到订单！', '#FF4444');
                    return;
                }

                // 查找匹配的订单
                let matchingSlot = null;
                for (let slot of this.orderSlots) {
                    if (slot.order &&
                        slot.order.type === building.type &&
                        slot.order.level === building.level &&
                        slot.order.completed < slot.order.count) {
                        matchingSlot = slot;
                        break;
                    }
                }

                if (matchingSlot) {
                    // 提交物品到订单
                    this.submitItemToOrder(cell, matchingSlot);
                } else {
                    this.showMessage('没有匹配的订单！', '#FF4444');
                }
            }

            submitItemToOrder(cell, orderSlot) {
                // 创建飞行动画
                const building = cell.building;
                const flyingItem = this.add.text(cell.x, cell.y, BUILDING_TYPES[building.type].emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 飞向订单槽位
                this.tweens.add({
                    targets: flyingItem,
                    x: orderSlot.x,
                    y: orderSlot.y,
                    scaleX: 0.5,
                    scaleY: 0.5,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        flyingItem.destroy();

                        // 更新订单进度
                        orderSlot.order.completed++;
                        this.updateOrderSlotDisplay(orderSlot);

                        // 检查订单是否完成
                        if (orderSlot.order.completed >= orderSlot.order.count) {
                            this.completeOrder(orderSlot);
                        }
                    }
                });

                // 移除原物品
                const cellSize = this.getCellSize();
                building.container.destroy();
                cell.building = null;
                cell.isEmpty = true;

                // 恢复棋盘格背景
                const isEvenSquare = (cell.row + cell.col) % 2 === 0;
                cell.background.clear();

                if (isEvenSquare) {
                    // 白色格子
                    cell.background.fillStyle(0xcdd0c9, 1);
                    cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    cell.background.fillStyle(0xdce0d7, 1);
                    cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                }

                // 取消选中
                this.deselectBuilding();

                this.showMessage('物品已提交到订单！', '#00FF00');
            }

            canMergeWithGrayFruit(normalCell, grayCell) {
                // 检查普通建筑是否可以与灰色水果合成
                if (!normalCell.building || !grayCell.building) return false;
                if (!normalCell.building.canMerge || !grayCell.building.canMerge) return false;
                if (normalCell.building.type !== grayCell.building.type) return false;

                // 检查是否有对应的合成规则
                const mergeResult = MERGE_RULES[normalCell.building.type];
                if (!mergeResult) return false; // 没有合成规则

                return true;
            }

            mergeWithGrayFruit(normalCell, grayCell) {
                const buildingType = normalCell.building.type;

                // 获取合成结果
                const newBuildingType = MERGE_RULES[buildingType];
                if (!newBuildingType) {
                    this.showMessage('无法合成这两个水果！', '#FF4444');
                    return;
                }

                // 清理选中边框
                if (normalCell.building.selectionBorder) {
                    normalCell.building.selectionBorder.destroy();
                    normalCell.building.selectionBorder = null;
                }
                if (grayCell.building.selectionBorder) {
                    grayCell.building.selectionBorder.destroy();
                    grayCell.building.selectionBorder = null;
                }

                // 销毁原建筑
                normalCell.building.container.destroy();

                // 清空普通格子，恢复棋盘格背景
                const cellSize = this.getCellSize();
                normalCell.building = null;
                normalCell.isEmpty = true;

                // 恢复棋盘格背景
                const isEvenSquare = (normalCell.row + normalCell.col) % 2 === 0;
                normalCell.background.clear();

                if (isEvenSquare) {
                    // 白色格子
                    normalCell.background.fillStyle(0xcdd0c9, 1);
                    normalCell.background.fillRect(normalCell.x - cellSize/2, normalCell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    normalCell.background.fillStyle(0xdce0d7, 1);
                    normalCell.background.fillRect(normalCell.x - cellSize/2, normalCell.y - cellSize/2, cellSize, cellSize);
                }

                // 销毁灰色水果
                if (grayCell.building && grayCell.building.container) {
                    grayCell.building.container.destroy();
                }

                // 在灰色水果位置创建新的合成水果
                this.createBuildingWithLevel(grayCell, newBuildingType, BUILDING_TYPES[newBuildingType].level);
                grayCell.isGray = false; // 不再是灰色水果

                // 保持棋盘格背景
                const newBuildingTypeData = BUILDING_TYPES[newBuildingType];
                const isGrayEvenSquare = (grayCell.row + grayCell.col) % 2 === 0;
                grayCell.background.clear();

                if (isGrayEvenSquare) {
                    // 白色格子
                    grayCell.background.fillStyle(0xcdd0c9, 1);
                    grayCell.background.fillRect(grayCell.x - cellSize/2, grayCell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    grayCell.background.fillStyle(0xdce0d7, 1);
                    grayCell.background.fillRect(grayCell.x - cellSize/2, grayCell.y - cellSize/2, cellSize, cellSize);
                }

                // 取消选中
                this.selectedBuilding = null;

                // 检查订单完成
                this.checkOrderCompletion(newBuildingType, newBuildingTypeData.level);

                this.showMessage(`合成了 ${newBuildingTypeData.name}！`, '#FFD700');
            }



            canMergeBuildings(cell1, cell2) {
                // 检查两个建筑是否可以合成
                if (!cell1.building || !cell2.building) return false;
                if (!cell1.building.canMerge || !cell2.building.canMerge) return false;
                if (cell1.building.type !== cell2.building.type) return false;

                // 检查等级是否相同 - 只有相同等级的水果才能合成
                if (cell1.building.level !== cell2.building.level) return false;

                // 检查是否有对应的合成规则
                const mergeResult = MERGE_RULES[cell1.building.type];
                if (!mergeResult) return false; // 没有合成规则

                return true;
            }

            generateBuildingAroundBase(baseCell) {
                // 检查是否正在生成动画中
                if (this.isGenerating) {
                    this.showMessage('正在生成中，请稍候...', '#FFAA00');
                    return;
                }

                // 检查金币是否足够
                if (this.gold < 100) {
                    this.showMessage('金币不足！需要100金币', '#FF4444');
                    return;
                }

                // 找到基地周围更大范围的空格子（3x3范围内的所有空格子）
                const emptyNearCells = [];
                const searchRadius = 2; // 搜索半径，可以调整

                for (let rowOffset = -searchRadius; rowOffset <= searchRadius; rowOffset++) {
                    for (let colOffset = -searchRadius; colOffset <= searchRadius; colOffset++) {
                        // 跳过基地本身
                        if (rowOffset === 0 && colOffset === 0) continue;

                        const newRow = baseCell.row + rowOffset;
                        const newCol = baseCell.col + colOffset;

                        if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                            const targetCell = this.buildGrid[newRow][newCol];
                            if (targetCell.isEmpty) {
                                // 按距离排序，优先选择离基地近的位置
                                const distance = Math.abs(rowOffset) + Math.abs(colOffset);
                                emptyNearCells.push({cell: targetCell, distance: distance});
                            }
                        }
                    }
                }

                if (emptyNearCells.length === 0) {
                    this.showMessage('基地周围没有空位！', '#FF4444');
                    return;
                }

                // 扣除金币
                this.gold -= 0;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 按距离排序，优先选择最近的空格子
                emptyNearCells.sort((a, b) => a.distance - b.distance);

                // 选择最近的空格子生成建筑
                const targetCell = emptyNearCells[0].cell;
                this.generateRandomBuilding(targetCell);

                this.showMessage('免费生成水果！', '#FFD700');
            }

            selectBuilding(cell) {
                // 取消之前的选择
                this.deselectBuilding();

                // 选中当前建筑
                this.selectedBuilding = cell;

                // 添加选中效果 - 创建一个黄色边框
                const cellSize = this.getCellSize();
                const selectionBorder = this.add.graphics();
                selectionBorder.lineStyle(4, 0xFFFF00, 1);
                selectionBorder.strokeRect(cell.x - cellSize/2 - 2, cell.y - cellSize/2 - 2, cellSize + 4, cellSize + 4);

                // 将边框存储到建筑对象中
                cell.building.selectionBorder = selectionBorder;

                this.showMessage(`选中了 ${BUILDING_TYPES[cell.building.type].name}，点击空格子移动建筑`, '#FFFF44');
            }

            deselectBuilding() {
                if (this.selectedBuilding && this.selectedBuilding.building) {
                    // 移除选中效果
                    if (this.selectedBuilding.building.selectionBorder) {
                        this.selectedBuilding.building.selectionBorder.destroy();
                        this.selectedBuilding.building.selectionBorder = null;
                    }
                    this.selectedBuilding = null;
                }

            }

            isAdjacentToBuilding(cell) {
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];
                        if (!adjacentCell.isEmpty) {
                            return true;
                        }
                    }
                }
                return false;
            }

            generateRandomBuilding(cell) {
                // 设置生成状态
                this.isGenerating = true;

                // 在基地周围生成低级水果
                let availableFruits = [1, 2, 3, 4]; // 基础水果：苹果、橙子、香蕉、葡萄

                const randomFruitId = availableFruits[Phaser.Math.Between(0, availableFruits.length - 1)];
                const fruitType = BUILDING_TYPES[randomFruitId];

                // 获取基地位置
                const baseCell = this.buildGrid[this.basePosition.row][this.basePosition.col];

                // 创建飞行的水果
                const flyingFruit = this.add.text(baseCell.x, baseCell.y, fruitType.emoji, {
                    fontSize: '42px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建飞行动画
                this.tweens.add({
                    targets: flyingFruit,
                    x: cell.x,
                    y: cell.y,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        flyingFruit.destroy();
                        // 动画完成后创建建筑
                        this.createBuilding(cell, randomFruitId);

                        // 保持棋盘格背景
                        const cellSize = this.getCellSize();
                        const isRandomEvenSquare = (cell.row + cell.col) % 2 === 0;
                        cell.background.clear();

                        if (isRandomEvenSquare) {
                            // 白色格子
                            cell.background.fillStyle(0xcdd0c9, 1);
                            cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                        } else {
                            // 灰色格子
                            cell.background.fillStyle(0xdce0d7, 1);
                            cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                        }

                        this.showMessage(`生成了 ${fruitType.name}！`, '#44FF44');

                        // 清除生成状态
                        this.isGenerating = false;
                    }
                });


            }

            moveBuildingToCell(fromCell, toCell) {
                if (!fromCell.building || !toCell.isEmpty) {
                    this.showMessage('无法移动到该位置！', '#FF4444');
                    return;
                }

                // 灰色水果不能移动
                if (fromCell.isGray) {
                    this.showMessage('灰色水果不能移动！', '#FF4444');
                    return;
                }

                const building = fromCell.building;
                const buildingType = BUILDING_TYPES[building.type];
                const buildingLevel = building.level; // 保存等级信息

                // 清理选中边框
                if (building.selectionBorder) {
                    building.selectionBorder.destroy();
                    building.selectionBorder = null;
                }





                // 销毁原位置的建筑显示
                building.container.destroy();

                // 在新位置创建建筑，保持等级
                this.createBuildingWithLevel(toCell, building.type, buildingLevel);

                // 保持棋盘格背景
                const cellSize = this.getCellSize();
                const isMoveEvenSquare = (toCell.row + toCell.col) % 2 === 0;
                toCell.background.clear();

                if (isMoveEvenSquare) {
                    // 白色格子
                    toCell.background.fillStyle(0xcdd0c9, 1);
                    toCell.background.fillRect(toCell.x - cellSize/2, toCell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    toCell.background.fillStyle(0xdce0d7, 1);
                    toCell.background.fillRect(toCell.x - cellSize/2, toCell.y - cellSize/2, cellSize, cellSize);
                }

                // 清空原格子，恢复棋盘格背景
                fromCell.building = null;
                fromCell.isEmpty = true;

                // 恢复棋盘格背景
                const isEvenSquare = (fromCell.row + fromCell.col) % 2 === 0;
                fromCell.background.clear();

                if (isEvenSquare) {
                    // 白色格子
                    fromCell.background.fillStyle(0xcdd0c9, 1);
                    fromCell.background.fillRect(fromCell.x - cellSize/2, fromCell.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    fromCell.background.fillStyle(0xdce0d7, 1);
                    fromCell.background.fillRect(fromCell.x - cellSize/2, fromCell.y - cellSize/2, cellSize, cellSize);
                }

                // 取消选中
                this.selectedBuilding = null;



                this.showMessage(`${buildingType.name} 已移动！`, '#44FF44');
            }

            tryMergeBuilding(cell) {
                const building = cell.building;
                if (!building.canMerge) return;

                // 寻找相邻的相同建筑
                const directions = [
                    {row: -1, col: 0}, {row: 1, col: 0},  // 上下
                    {row: 0, col: -1}, {row: 0, col: 1}   // 左右
                ];

                for (let dir of directions) {
                    const newRow = cell.row + dir.row;
                    const newCol = cell.col + dir.col;

                    if (newRow >= 0 && newRow < GRID_SIZE && newCol >= 0 && newCol < GRID_SIZE) {
                        const adjacentCell = this.buildGrid[newRow][newCol];

                        if (!adjacentCell.isEmpty &&
                            adjacentCell.building &&
                            adjacentCell.building.type === building.type &&
                            adjacentCell.building.level === building.level &&
                            adjacentCell.building.canMerge) {

                            // 执行合成
                            this.mergeBuildings(cell, adjacentCell);
                            return;
                        }
                    }
                }

                // 防御塔区域已移除，所有建筑都可以在网格中拖动
            }

            mergeBuildings(cell1, cell2) {
                const buildingType = cell1.building.type;

                // 获取合成结果
                const newBuildingType = MERGE_RULES[buildingType];
                if (!newBuildingType) {
                    this.showMessage('无法合成这两个水果！', '#FF4444');
                    return;
                }

                // 清理选中边框
                if (cell1.building.selectionBorder) {
                    cell1.building.selectionBorder.destroy();
                    cell1.building.selectionBorder = null;
                }
                if (cell2.building.selectionBorder) {
                    cell2.building.selectionBorder.destroy();
                    cell2.building.selectionBorder = null;
                }

                // 停止计时器
                [cell1.building, cell2.building].forEach(building => {
                    if (building.productionTimer) {
                        building.productionTimer.destroy();
                    }
                });

                // 销毁两个建筑
                cell1.building.container.destroy();
                cell2.building.container.destroy();

                // 清空第一个格子，恢复棋盘格背景
                const cellSize = this.getCellSize();
                cell1.building = null;
                cell1.isEmpty = true;

                // 恢复棋盘格背景
                const isEvenSquare = (cell1.row + cell1.col) % 2 === 0;
                cell1.background.clear();

                if (isEvenSquare) {
                    // 白色格子
                    cell1.background.fillStyle(0xcdd0c9, 1);
                    cell1.background.fillRect(cell1.x - cellSize/2, cell1.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    cell1.background.fillStyle(0xdce0d7, 1);
                    cell1.background.fillRect(cell1.x - cellSize/2, cell1.y - cellSize/2, cellSize, cellSize);
                }

                // 创建新的合成水果在第二个位置
                this.createBuildingWithLevel(cell2, newBuildingType, BUILDING_TYPES[newBuildingType].level);

                // 保持棋盘格背景
                const newBuildingTypeData = BUILDING_TYPES[newBuildingType];
                const isMergeEvenSquare = (cell2.row + cell2.col) % 2 === 0;
                cell2.background.clear();

                if (isMergeEvenSquare) {
                    // 白色格子
                    cell2.background.fillStyle(0xcdd0c9, 1);
                    cell2.background.fillRect(cell2.x - cellSize/2, cell2.y - cellSize/2, cellSize, cellSize);
                } else {
                    // 灰色格子
                    cell2.background.fillStyle(0xdce0d7, 1);
                    cell2.background.fillRect(cell2.x - cellSize/2, cell2.y - cellSize/2, cellSize, cellSize);
                }

                // 取消选中
                this.selectedBuilding = null;

                this.showMessage(`合成了 ${newBuildingTypeData.name}！`, '#FFD700');

                // 检查订单完成
                this.checkOrderCompletion(newBuildingType, newBuildingTypeData.level);

                // 检查基地升级
                this.checkBaseUpgrade();
            }

            createControlArea() {
                // 控制按钮区域（在屏幕最下方）- 只保留重置按钮
                const controlAreaY = GAME_HEIGHT - 80;
                const buttonWidth = 120;
                const buttonHeight = 60;
                const startButtonX = (GAME_WIDTH - buttonWidth) / 2;

                // 创建重置按钮（居中）
                const resetButton = this.createControlButton(
                    startButtonX, controlAreaY, buttonWidth, buttonHeight,
                    '重置', 0xFF5722, () => this.resetGame()
                );

                this.controlButtons = {
                    reset: resetButton
                };
            }

            createControlButton(x, y, width, height, text, color, callback) {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(color);
                buttonBg.fillRoundedRect(x, y, width, height, 10);
                buttonBg.lineStyle(3, Phaser.Display.Color.GetColor32(color) - 0x333333);
                buttonBg.strokeRoundedRect(x, y, width, height, 10);

                // 设置按钮可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(x, y, width, height), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(x + width/2, y + height/2, text, {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加点击事件
                buttonBg.on('pointerdown', callback);

             
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            showMessage(text, color = '#FFFFFF') {
                // 显示临时消息
                const message = this.add.text(GAME_WIDTH/2, 150, text, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: color,
                    fontStyle: 'bold',
                    backgroundColor: '#000000',
                    padding: { x: 15, y: 8 },
                    resolution: 2
                }).setOrigin(0.5);
                message.setDepth(1000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (message) {
                        this.tweens.add({
                            targets: message,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                message.destroy();
                            }
                        });
                    }
                });
            }

            getFruitTypeForPlate(plateIndex, fruitIndex) {
                // 根据关卡和水果盘索引生成水果类型
                const level = this.level;

                if (level === 1) {
                    // 第1关：1个水果盘，4个相同水果
                    return 7; // 使用樱桃（最后一个水果类型）
                }

                if (level === 2) {
                    // 第2关：2个水果盘，每个盘子4个相同水果，但两个盘子不同
                    return plateIndex === 0 ? 7 : 6; // 第一个盘子樱桃，第二个盘子猕猴桃
                }

                if (level === 3) {
                    // 第3关：3个水果盘，每个盘子4个相同水果
                    const fruitTypes = [7, 6, 5]; // 樱桃、猕猴桃、草莓
                    return fruitTypes[plateIndex];
                }

                // 第4关及以后：更复杂的组合
                if (level === 4) {
                    // 第4关：4个水果盘，前两个盘子相同水果，后两个盘子混合水果
                    if (plateIndex < 2) {
                        return plateIndex === 0 ? 7 : 6; // 樱桃和猕猴桃
                    } else {
                        // 后两个盘子：2个樱桃 + 2个草莓
                        return fruitIndex < 2 ? 7 : 5;
                    }
                }

                // 第5关及以后：完全随机但保证有解
                const availableTypes = [7, 6, 5, 4]; // 使用4种水果类型
                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getRandomFruitTypeForLevel() {
                // 根据关卡返回随机的可用水果类型（前5关只有1种干扰水果）
                let availableTypes = [];

                if (this.level === 1) {
                    // 第1关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6]; // 樱桃（目标） + 猕猴桃（干扰）
                } else if (this.level === 2) {
                    // 第2关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5]; // 樱桃、猕猴桃（目标） + 草莓（干扰）
                } else if (this.level === 3) {
                    // 第3关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4]; // 樱桃、猕猴桃、草莓（目标） + 葡萄（干扰）
                } else if (this.level === 4) {
                    // 第4关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 3]; // 4种目标水果 + 香蕉（干扰）
                } else if (this.level === 5) {
                    // 第5关：目标水果 + 1种干扰水果
                    availableTypes = [7, 6, 5, 4, 2]; // 使用橙子作为干扰
                } else {
                    // 第6关及以后：使用更多干扰水果
                    availableTypes = [];
                    for (let i = 0; i < TILE_TYPES.length; i++) {
                        availableTypes.push(i);
                    }
                }

                return availableTypes[Phaser.Math.Between(0, availableTypes.length - 1)];
            }

            getGridSizeForLevel() {
                // 根据关卡返回网格大小
                if (this.level === 1) {
                    return { rows: 4, cols: 4 }; // 第1关：4x4
                } else if (this.level === 2) {
                    return { rows: 4, cols: 5 }; // 第2关：4x5
                } else if (this.level === 3) {
                    return { rows: 5, cols: 5 }; // 第3关：5x5
                } else if (this.level === 4) {
                    return { rows: 5, cols: 6 }; // 第4关：5x6
                } else {
                    return { rows: 6, cols: 6 }; // 第5关及以后：6x6
                }
            }

            getTargetFruitTypes() {
                // 获取当前关卡目标水果盘中需要的所有水果类型
                const targetTypes = new Set();

                if (this.fruitPlates) {
                    this.fruitPlates.forEach(plate => {
                        plate.fruits.forEach(fruit => {
                            targetTypes.add(fruit.getData('typeIndex'));
                        });
                    });
                }

                return Array.from(targetTypes);
            }

            createToolArea() {
                // 道具区域位置（在屏幕最下方）
                const toolAreaHeight = 100;
                const toolAreaY = GAME_HEIGHT - toolAreaHeight;
                const toolSize = 80;
                const toolSpacing = 60;
                const totalToolsWidth = 3 * toolSize + 2 * toolSpacing;
                const startToolX = (GAME_WIDTH - totalToolsWidth) / 2;

                // 创建道具背景区域
                const toolBgHeight = toolAreaHeight - 20; // 留10像素上下边距
                const toolBgY = toolAreaY + 10; // 从顶部留10像素开始

                const toolBg = this.add.graphics();
                toolBg.fillStyle(0x2C3E50, 0.8);
                toolBg.fillRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);
                toolBg.lineStyle(2, 0x34495E, 0.9);
                toolBg.strokeRoundedRect(startToolX - 30, toolBgY, totalToolsWidth + 60, toolBgHeight, 15);

                // 道具信息
                const tools = [
                    { icon: '🔨', name: '锤子' },
                    { icon: '↶', name: '撤销' },
                    { icon: '🔄', name: '交换' }
                ];

                // 创建道具按钮（在背景框内上下居中）
                tools.forEach((tool, index) => {
                    const toolX = startToolX + index * (toolSize + toolSpacing);
                    const toolY = toolBgY + toolBgHeight / 2 - toolSize / 2; // 在背景框内垂直居中

                    // 创建道具按钮背景
                    const buttonBg = this.add.graphics();
                    buttonBg.fillStyle(0x3498DB, 0.9);
                    buttonBg.fillRoundedRect(toolX, toolY, toolSize, toolSize, 12);
                    buttonBg.lineStyle(3, 0x2980B9, 1);
                    buttonBg.strokeRoundedRect(toolX, toolY, toolSize, toolSize, 12);

                    // 设置按钮可交互
                    buttonBg.setInteractive(new Phaser.Geom.Rectangle(toolX, toolY, toolSize, toolSize), Phaser.Geom.Rectangle.Contains);

                    // 创建道具图标
                    const toolIcon = this.add.text(toolX + toolSize/2, toolY + toolSize/2, tool.icon, {
                        fontSize: '48px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    // 添加点击事件（暂时只显示提示）
                    buttonBg.on('pointerdown', () => {
                        this.showToolTip(tool.name, toolX + toolSize/2, toolY - 20);
                    });
                });
            }

            showToolTip(toolName, x, y) {
                // 显示道具提示
                const tooltip = this.add.text(x, y, `${toolName}功能开发中...`, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    backgroundColor: '#2C3E50',
                    padding: { x: 10, y: 5 },
                    resolution: 2
                }).setOrigin(0.5);
                tooltip.setDepth(2000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (tooltip) {
                        this.tweens.add({
                            targets: tooltip,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                tooltip.destroy();
                            }
                        });
                    }
                });
            }





            initializeGame() {
                // 重置游戏状态
                this.gold = 1000;
                this.baseLevel = 1; // 重置基地等级

                // 清理选中状态
                this.deselectBuilding();
                this.selectedBuilding = null;
                this.selectedGrayItem = null;

                // 初始化双击检测
                this.lastClickedCell = null;
                this.lastClickTime = 0;

                // 更新UI
                this.goldText.setText(`🪙 ${this.gold}`);

                // 清理已建造的建筑
                this.buildings.forEach(building => {
                    if (building.container) building.container.destroy();
                });
                this.buildings = [];

                // 重置建造网格
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        const isCenter = (row === this.basePosition.row && col === this.basePosition.col);

                        if (cell.building && !isCenter) {
                            cell.building.container.destroy();
                            cell.building = null;
                        }

                        if (!isCenter) {
                            cell.isEmpty = true;
                            cell.isGray = false; // 重置灰色标记
                            // 重置格子背景为棋盘格模式
                            const cellSize = this.getCellSize();
                            const isEvenSquare = (cell.row + cell.col) % 2 === 0;
                            cell.background.clear();

                            if (isEvenSquare) {
                                // 白色格子
                                cell.background.fillStyle(0xcdd0c9, 1);
                                cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                            } else {
                                // 灰色格子
                                cell.background.fillStyle(0xdce0d7, 1);
                                cell.background.fillRect(cell.x - cellSize/2, cell.y - cellSize/2, cellSize, cellSize);
                            }
                        } else if (isCenter) {
                            // 重建基地
                            if (cell.building) {
                                cell.building.container.destroy();
                            }
                            this.createBaseBuilding(cell);
                        }
                    }
                }

                // 重置防御塔槽位 - 已移除防御塔区域功能
                // this.towerSlots.forEach(slot => { ... });

                // 重新创建灰色水果
                this.createFixedGrayItems();
            }






            resetGame() {
                this.initializeGame();
                this.showMessage('游戏已重置', '#FFFF44');
            }





            selectGridTile(tile, row, col) {
                this.isAnimating = true;

                // 找到空的槽位（跳过锁住的槽位）
                const emptySlot = this.slots.find(slot => slot.tile === null && !slot.isLocked);

                if (!emptySlot) {
                    // 槽位已满，游戏结束
                    this.gameOver = true;
                    this.showGameOver();
                    this.isAnimating = false;
                    return;
                }

                // 从网格中移除方块
                this.tileGrid[row][col] = null;

                // 移动方块到槽位，同时缩小以匹配暂存区格子大小
                this.tweens.add({
                    targets: tile,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    scaleX: 0.83, // 从1.2缩小到1.0 (1.0/1.2 ≈ 0.83)
                    scaleY: 0.83,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 将方块放入槽位
                        emptySlot.tile = tile;

                        // 检查是否有三个相同的方块
                        this.checkForMatches();

                        // 执行列向上移动和补充新方块
                        this.moveColumnUp(col);

                        // 检查暂存区是否已满（游戏失败条件）
                        this.checkGameOverCondition();

                        this.isAnimating = false;

                        // 检查胜利条件（目标水果盘完成）
                        // 这里暂时不检查胜利条件，因为胜利逻辑在handlePlateComplete中处理
                    }
                });
            }

            moveColumnUp(col) {
                const gridSize = this.getGridSizeForLevel();
                const rows = gridSize.rows;
                const cols = gridSize.cols;
                const tileSpacing = 102; // 使用与createStackLayout一致的间距
                const startX = this.stackArea.x + (this.stackArea.width - (cols - 1) * tileSpacing) / 2;
                const startY = this.stackArea.y + 20; // 与createStackLayout保持一致

                // 将该列的所有方块向上移动一格
                for (let row = 0; row < rows - 1; row++) {
                    if (this.tileGrid[row + 1][col]) {
                        const tile = this.tileGrid[row + 1][col];
                        this.tileGrid[row][col] = tile;
                        this.tileGrid[row + 1][col] = null;

                        // 更新方块的网格位置数据
                        tile.setData('gridRow', row);

                        // 动画移动到新位置
                        const newY = startY + row * tileSpacing;
                        this.tweens.add({
                            targets: tile,
                            y: newY,
                            duration: 200,
                            ease: 'Power2'
                        });
                    }
                }

                // 在最底部补充新方块（根据关卡限制水果种类）
                const newTileType = this.getRandomFruitTypeForLevel();
                const newX = startX + col * tileSpacing;
                const newY = startY + (rows - 1) * tileSpacing;
                const newTile = this.createGridTile(newX, newY, newTileType, rows - 1, col);
                this.tileGrid[rows - 1][col] = newTile;

                // 新方块从下方滑入的动画
                newTile.y += 100;
                newTile.setAlpha(0);
                this.tweens.add({
                    targets: newTile,
                    y: newY,
                    alpha: 1,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 更新可点击状态
                        this.updateGridClickableStates();
                    }
                });
            }



            checkForMatches() {
                // 统计暂存区每种类型的水果数量
                const typeCounts = {};

                this.slots.forEach(slot => {
                    if (slot.tile) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        typeCounts[typeIndex] = (typeCounts[typeIndex] || 0) + 1;
                    }
                });

                // 更新水果盘中小水果的状态（变暗/变亮）
                this.updateFruitPlateStates(typeCounts);

                // 检查是否有完整的水果盘可以消除
                this.checkCompletePlates(typeCounts);
            }

            updateFruitPlateStates(typeCounts) {
                // 首先重置所有剩余水果盘中小水果的匹配状态
                this.fruitPlates.forEach(plate => {
                    plate.fruits.forEach(fruit => {
                        const wasMatched = fruit.getData('isMatched');
                        fruit.setData('isMatched', false);
                        fruit.setData('matchedSlotIndex', -1);

                        if (wasMatched) {
                            // 如果之前是亮的，现在变暗
                            fruit.setAlpha(0.6);
                            fruit.setData('isDark', true);
                        }
                    });
                });

                // 为每个暂存区的水果找到对应的小水果进行一对一匹配
                this.slots.forEach((slot, slotIndex) => {
                    if (!slot.tile) return;

                    const slotFruitType = slot.tile.getData('typeIndex');

                    // 找到第一个未匹配的、类型相同的小水果
                    let foundMatch = false;
                    for (let plate of this.fruitPlates) {
                        if (foundMatch) continue;

                        for (let fruit of plate.fruits) {
                            if (foundMatch) break;

                            const fruitType = fruit.getData('typeIndex');
                            const isMatched = fruit.getData('isMatched');

                            if (fruitType === slotFruitType && !isMatched) {
                                // 找到匹配，让小水果变亮
                                fruit.setAlpha(1.0);
                                fruit.setData('isDark', false);
                                fruit.setData('isMatched', true);
                                fruit.setData('matchedSlotIndex', slotIndex);
                                foundMatch = true;
                            }
                        }
                    }
                });
            }

            checkCompletePlates(typeCounts) {
                // 检查是否有水果盘的所有小水果都被匹配了（变亮了）
                // 需要从后往前遍历，因为完成的水果盘会从数组中移除
                for (let plateIndex = this.fruitPlates.length - 1; plateIndex >= 0; plateIndex--) {
                    const plate = this.fruitPlates[plateIndex];

                    // 检查这个水果盘是否所有小水果都被匹配了（都变亮了）
                    const allMatched = plate.fruits.every(fruit => fruit.getData('isMatched'));

                    if (allMatched) {
                        // 收集这个水果盘需要的暂存区水果
                        const tilesToRemove = [];
                        plate.fruits.forEach(fruit => {
                            const slotIndex = fruit.getData('matchedSlotIndex');
                            if (slotIndex >= 0 && this.slots[slotIndex].tile) {
                                tilesToRemove.push({
                                    tile: this.slots[slotIndex].tile,
                                    slot: this.slots[slotIndex],
                                    targetFruit: fruit
                                });
                                this.slots[slotIndex].tile = null;
                            }
                        });

                        if (tilesToRemove.length === 4) { // 确保有4个水果
                            this.completeFruitPlate(plateIndex, tilesToRemove);
                        }
                    }
                }
            }

            completeFruitPlate(plateIndex, tilesToRemove) {
                const plate = this.fruitPlates[plateIndex];

                // 播放消除动画
                this.playFruitPlateCompleteAnimation(tilesToRemove, plate);
            }

            playFruitPlateCompleteAnimation(tilesToRemove, plate) {
                // 让水果飞向盘子里对应的小水果位置
                let completedCount = 0;

                tilesToRemove.forEach((item, index) => {
                    const targetFruit = item.targetFruit;

                    // 延迟不同时间让水果飞向对应的小水果位置
                    this.time.delayedCall(index * 100, () => {
                        this.tweens.add({
                            targets: item.tile,
                            x: targetFruit.x,
                            y: targetFruit.y,
                            scaleX: 0.6,
                            scaleY: 0.6,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                // 显示完成特效在小水果位置
                                this.showCompleteAnimation(targetFruit.x, targetFruit.y);

                                // 暂存区水果消失
                                item.tile.destroy();

                                completedCount++;

                                // 如果是最后一个水果，处理完成逻辑
                                if (completedCount === tilesToRemove.length) {
                                    this.time.delayedCall(300, () => {
                                        this.handlePlateComplete(plate);
                                    });
                                }
                            }
                        });
                    });
                });
            }

            handlePlateComplete(plate) {
                // 计算分数
                const points = 100;
                this.score += points;
                this.scoreText.setText(`🪙 ${this.score}`);

                // 显示金币飞行动画
                this.showCoinFlyAnimation(plate.x, plate.y, points);

                this.showScoreAnimation(points);

                // 让完成的水果盘立即消失
                this.tweens.add({
                    targets: [plate.background, ...plate.fruits],
                    scaleX: 0,
                    scaleY: 0,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => {
                        // 销毁水果盘元素
                        plate.background.destroy();
                        plate.fruits.forEach(fruit => fruit.destroy());

                        // 从水果盘数组中移除这个盘子
                        const plateIndex = this.fruitPlates.indexOf(plate);
                        if (plateIndex > -1) {
                            this.fruitPlates.splice(plateIndex, 1);
                        }

                        // 重新排列剩余水果盘居中
                        this.time.delayedCall(300, () => {
                            this.reorganizeFruitPlates();
                        });
                    }
                });

                // 整理暂存区槽位
                this.reorganizeSlots();

                // 检查是否所有水果盘都完成了
                this.time.delayedCall(600, () => {
                    if (this.fruitPlates.length === 0) {
                        // 所有水果盘都消失了，进入下一关或显示胜利
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                    }
                });
            }

            completeLevel() {
                // 关卡完成处理
                this.level++;
                this.levelText.setText(`${this.level}`);

                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '🤣', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示关卡完成文字
                const levelCompleteText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, `第${this.level - 1}关完成！`, {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                levelCompleteText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建下一关按钮
                const nextButton = this.createNextLevelButton();

                // 启动散花特效
                this.startConfettiEffect();

                // 存储界面元素以便清理
                this.levelCompleteUI = {
                    overlay,
                    bigEmoji,
                    levelCompleteText,
                    scoreText,
                    nextButton
                };
            }

            createNextLevelButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0x4CAF50);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0x45A049);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100,
                    this.level > 10 ? '游戏完成' : '下一关', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onNextLevelClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            disableGameInput() {
                // 禁用游戏输入
                this.gameInputDisabled = true;

                // 禁用所有方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.disableInteractive();
                    });
                }
            }

            enableGameInput() {
                // 启用游戏输入
                this.gameInputDisabled = false;

                // 重新启用方块的交互
                if (this.tileGroup) {
                    this.tileGroup.children.entries.forEach(tile => {
                        tile.setInteractive();
                    });
                }
            }

            onNextLevelClick() {
                // 清理关卡完成界面
                if (this.levelCompleteUI) {
                    Object.values(this.levelCompleteUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.levelCompleteUI = null;
                }

                // 停止散花特效
                this.stopConfettiEffect();

                // 重新启用游戏输入
                this.enableGameInput();

                // 检查是否达到最大关卡
                if (this.level > 10) {
                    this.showVictory();
                } else {
                    // 重新初始化游戏进入下一关
                    this.initializeGame();
                }
            }

            startConfettiEffect() {
                // 创建散花特效
                this.confettiParticles = [];
                this.confettiTimer = this.time.addEvent({
                    delay: 100,
                    callback: this.createConfettiParticle,
                    callbackScope: this,
                    loop: true
                });

                // 3秒后停止生成新的散花
                this.time.delayedCall(3000, () => {
                    if (this.confettiTimer) {
                        this.confettiTimer.destroy();
                        this.confettiTimer = null;
                    }
                });
            }

            createConfettiParticle() {
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0xFECA57, 0xFF9FF3, 0xA8E6CF];
                const shapes = ['●', '★', '♦', '▲'];

                // 随机选择颜色和形状
                const color = colors[Phaser.Math.Between(0, colors.length - 1)];
                const shape = shapes[Phaser.Math.Between(0, shapes.length - 1)];

                // 在屏幕顶部随机位置创建粒子
                const x = Phaser.Math.Between(0, GAME_WIDTH);
                const y = -20;

                const particle = this.add.text(x, y, shape, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: `#${color.toString(16).padStart(6, '0')}`,
                    resolution: 2
                }).setOrigin(0.5);
                particle.setDepth(1003);

                // 随机旋转和缩放
                particle.setRotation(Phaser.Math.Between(0, 360) * Math.PI / 180);
                particle.setScale(Phaser.Math.FloatBetween(0.5, 1.5));

                // 添加下落和旋转动画
                this.tweens.add({
                    targets: particle,
                    y: GAME_HEIGHT + 50,
                    x: x + Phaser.Math.Between(-100, 100),
                    rotation: particle.rotation + Phaser.Math.Between(2, 6) * Math.PI,
                    alpha: 0,
                    duration: Phaser.Math.Between(2000, 4000),
                    ease: 'Power2',
                    onComplete: () => {
                        particle.destroy();
                        // 从数组中移除
                        const index = this.confettiParticles.indexOf(particle);
                        if (index > -1) {
                            this.confettiParticles.splice(index, 1);
                        }
                    }
                });

                this.confettiParticles.push(particle);
            }

            stopConfettiEffect() {
                // 停止生成新的散花
                if (this.confettiTimer) {
                    this.confettiTimer.destroy();
                    this.confettiTimer = null;
                }

                // 清理现有的散花粒子
                if (this.confettiParticles) {
                    this.confettiParticles.forEach(particle => {
                        if (particle && particle.destroy) {
                            particle.destroy();
                        }
                    });
                    this.confettiParticles = [];
                }
            }

            showCoinFlyAnimation(startX, startY, points) {
                // 创建多个金币图标飞向右上角的金币显示位置
                const coinCount = Math.min(3, Math.ceil(points / 50)); // 减少金币数量，根据分数决定，最多3个

                // 获取金币显示文字的准确位置
                const scoreTextBounds = this.scoreText.getBounds();
                const targetX = scoreTextBounds.x + 18; // 金币图标在文字中的大概位置
                const targetY = scoreTextBounds.y + scoreTextBounds.height / 2; // 垂直居中

                for (let i = 0; i < coinCount; i++) {
                    // 减少延迟时间，让金币更快飞出
                    this.time.delayedCall(i * 50, () => {
                        // 创建金币图标
                        const coin = this.add.text(startX, startY, '🪙', {
                            fontSize: '32px',
                            fontFamily: 'Arial, sans-serif',
                            resolution: 2
                        }).setOrigin(0.5);
                        coin.setDepth(1000);

                        // 添加初始的轻微随机偏移
                        const randomOffsetX = Phaser.Math.Between(-15, 15);
                        const randomOffsetY = Phaser.Math.Between(-15, 15);
                        coin.x += randomOffsetX;
                        coin.y += randomOffsetY;

                        // 金币飞向目标位置的动画
                        this.tweens.add({
                            targets: coin,
                            x: targetX,
                            y: targetY,
                            scaleX: 0.9,
                            scaleY: 0.9,
                            duration: 600, // 减少飞行时间
                            ease: 'Power2',
                            onComplete: () => {
                                // 到达目标位置后的闪烁效果
                                this.tweens.add({
                                    targets: coin,
                                    scaleX: 1.3,
                                    scaleY: 1.3,
                                    alpha: 0,
                                    duration: 150,
                                    ease: 'Power2',
                                    onComplete: () => {
                                        coin.destroy();

                                        // 让金币显示文字闪烁一下
                                        if (i === coinCount - 1) { // 最后一个金币到达时
                                            this.tweens.add({
                                                targets: this.scoreText,
                                                scaleX: 1.15,
                                                scaleY: 1.15,
                                                duration: 120,
                                                ease: 'Power2',
                                                yoyo: true,
                                                repeat: 1
                                            });
                                        }
                                    }
                                });
                            }
                        });

                        // 添加旋转动画
                        this.tweens.add({
                            targets: coin,
                            rotation: Math.PI * 1.5, // 减少旋转角度
                            duration: 600,
                            ease: 'Linear'
                        });
                    });
                }
            }

            showCompleteAnimation(x, y) {
                // 显示完成特效文字
                const completeText = this.add.text(x, y - 30, '完成!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#00FF00',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                completeText.setDepth(500);

                this.tweens.add({
                    targets: completeText,
                    y: completeText.y - 30,
                    alpha: 0,
                    duration: 800,
                    ease: 'Power2',
                    onComplete: () => {
                        completeText.destroy();
                    }
                });

                // 添加光环特效
                const halo = this.add.graphics();
                halo.lineStyle(4, 0x00FF00, 0.8);
                halo.strokeCircle(x, y, 30);
                halo.setDepth(499);

                this.tweens.add({
                    targets: halo,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        halo.destroy();
                    }
                });
            }









            reorganizeFruitPlates() {
                // 重新排列剩余的水果盘，让它们居中显示
                if (this.fruitPlates.length === 0) return;

                const plateWidth = 140;
                const plateSpacing = 20;
                const remainingCount = this.fruitPlates.length;
                const totalPlatesWidth = remainingCount * plateWidth + (remainingCount - 1) * plateSpacing;
                const startPlateX = (GAME_WIDTH - totalPlatesWidth) / 2;
                const fruitPlateAreaY = 220; // 水果盘区域的Y坐标

                // 为每个剩余的水果盘计算新位置并添加移动动画
                this.fruitPlates.forEach((plate, index) => {
                    const newPlateX = startPlateX + index * (plateWidth + plateSpacing);
                    const newCenterX = newPlateX + plateWidth / 2;

                    // 重新绘制水果盘背景到新位置
                    plate.background.clear();
                    plate.background.fillStyle(0x8B4513, 0.3);
                    plate.background.fillRoundedRect(newPlateX, fruitPlateAreaY - 60, plateWidth, 120, 10);
                    plate.background.lineStyle(2, 0x8B4513, 0.8);
                    plate.background.strokeRoundedRect(newPlateX, fruitPlateAreaY - 60, plateWidth, 120, 10);

                    // 更新水果盘中心位置
                    plate.x = newCenterX;

                    // 移动水果盘中的所有小水果
                    const smallFruitSize = 50;
                    const fruitSpacing = 10;
                    const startFruitX = newPlateX + (plateWidth - (2 * smallFruitSize + fruitSpacing)) / 2;
                    const startFruitY = fruitPlateAreaY - (smallFruitSize + fruitSpacing / 2);

                    plate.fruits.forEach((fruit, fruitIndex) => {
                        const row = Math.floor(fruitIndex / 2);
                        const col = fruitIndex % 2;
                        const newFruitX = startFruitX + col * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;
                        const newFruitY = startFruitY + row * (smallFruitSize + fruitSpacing) + smallFruitSize / 2;

                        this.tweens.add({
                            targets: fruit,
                            x: newFruitX,
                            y: newFruitY,
                            duration: 400,
                            ease: 'Power2'
                        });
                    });
                });
            }

            areAllTargetsFulfilled() {
                // 检查是否所有水果盘都已消失
                return this.fruitPlates.length === 0;
            }

            calculateStars() {
                // 根据分数计算星星数量
                const baseScore = this.level * 100; // 基础分数
                const scoreRatio = this.score / baseScore;

                if (scoreRatio >= 2.0) {
                    return 3; // 3星：分数达到基础分数的200%
                } else if (scoreRatio >= 1.5) {
                    return 2; // 2星：分数达到基础分数的150%
                } else {
                    return 1; // 1星：完成关卡即可
                }
            }



            checkGameOverCondition() {
                // 检查暂存区是否已满
                const isSlotsFull = this.slots.every(slot => slot.tile !== null);

                if (isSlotsFull) {
                    // 检查是否还有剩余的水果盘
                    if (this.fruitPlates.length === 0) {
                        // 没有水果盘了，关卡完成
                        this.time.delayedCall(500, () => {
                            this.completeLevel();
                        });
                        return;
                    }

                    // 在一对一匹配的情况下，检查是否有任何剩余水果盘可以完成
                    let canCompleteAnyPlate = false;

                    // 检查每个剩余的水果盘
                    this.fruitPlates.forEach(plate => {
                        // 统计这个水果盘需要的水果类型
                        const requiredTypes = [];
                        plate.fruits.forEach(fruit => {
                            requiredTypes.push(fruit.getData('typeIndex'));
                        });

                        // 统计暂存区的水果类型
                        const availableTypes = [];
                        this.slots.forEach(slot => {
                            if (slot.tile) {
                                availableTypes.push(slot.tile.getData('typeIndex'));
                            }
                        });

                        // 检查是否能一对一匹配完成这个水果盘
                        const tempAvailable = [...availableTypes];
                        let matchCount = 0;

                        requiredTypes.forEach(requiredType => {
                            const index = tempAvailable.indexOf(requiredType);
                            if (index >= 0) {
                                tempAvailable.splice(index, 1); // 移除已匹配的
                                matchCount++;
                            }
                        });

                        if (matchCount === 4) { // 如果能匹配完整个水果盘
                            canCompleteAnyPlate = true;
                        }
                    });

                    // 如果暂存区满了且无法完成任何剩余水果盘，游戏结束
                    if (!canCompleteAnyPlate) {
                        this.gameOver = true;
                        this.time.delayedCall(500, () => {
                            this.showGameOver();
                        });
                    }
                }
            }

            reorganizeSlots() {
                // 收集所有非空的方块
                const remainingTiles = [];
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        remainingTiles.push(slot.tile);
                        slot.tile = null;
                    }
                });

                // 重新排列到左侧
                remainingTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.tile = tile;

                    this.tweens.add({
                        targets: tile,
                        x: slot.x,
                        y: slot.y,
                        duration: 200,
                        ease: 'Power2'
                    });
                });
            }

            showScoreAnimation(points) {
                const scorePopup = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 100, `+${points}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scorePopup.setDepth(500); // 设置较高深度确保分数动画可见

                this.tweens.add({
                    targets: scorePopup,
                    y: scorePopup.y - 50,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scorePopup.destroy();
                    }
                });
            }

            showVictory() {
                // 立即清空暂存区，防止显示残留动物
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 计算关卡评星
                const stars = this.calculateStars();

                // 显示胜利信息
                const victoryBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                victoryBg.setDepth(1000); // 设置最高深度确保在最上层

                const victoryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 180, '恭喜过关！', {
                    fontSize: '90px', // 150% (48 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                victoryText.setDepth(1001); // 设置最高深度确保在最上层

                // 显示星星评级
                this.showStarRating(stars);

                const nextButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, 240, 72, 0x27AE60); // 120%按钮
                nextButton.setStrokeStyle(3, 0x2ECC71);
                nextButton.setInteractive();
                nextButton.setDepth(1001); // 设置最高深度确保在最上层

                const nextText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, '下一关', {
                    fontSize: '39px', // 150% (26 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                nextText.setDepth(1002); // 设置最高深度确保在最上层

                // 在UI界面上添加撒花庆祝特效
                this.showConfettiCelebration();

                nextButton.on('pointerdown', () => {
                    victoryBg.destroy();
                    victoryText.destroy();
                    nextButton.destroy();
                    nextText.destroy();
                    this.nextLevel();
                });
            }

            showConfettiCelebration() {
                // 创建撒花庆祝特效，2次爆炸效果，共2秒
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0xA8E6CF, 0xFD79A8];
                const centerX = GAME_WIDTH / 2;
                const centerY = GAME_HEIGHT / 2 - 80; // "恭喜过关"字上方位置
                const explosionCount = 2; // 2次爆炸
                const explosionInterval = 1000; // 1秒间隔（0秒和1秒时爆炸）

                for (let explosion = 0; explosion < explosionCount; explosion++) {
                    this.time.delayedCall(explosion * explosionInterval, () => {
                        // 每次爆炸创建多个花瓣
                        const confettiPerExplosion = 25; // 增加每次爆炸的花瓣数量
                        for (let i = 0; i < confettiPerExplosion; i++) {
                            this.createConfettiExplosion(centerX, centerY, colors);
                        }
                    });
                }
            }

            createConfettiExplosion(centerX, centerY, colors) {
                // 随机选择颜色
                const color = colors[Math.floor(Math.random() * colors.length)];

                // 创建花瓣形状（更大的尺寸）
                const confetti = this.add.graphics();
                confetti.fillStyle(color);

                // 随机选择花瓣形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形花瓣
                    confetti.fillCircle(0, 0, Math.random() * 8 + 6);
                } else if (shapeType === 1) {
                    // 方形花瓣
                    const size = Math.random() * 12 + 8;
                    confetti.fillRect(-size/2, -size/2, size, size);
                } else {
                    // 三角形花瓣
                    const size = Math.random() * 10 + 8;
                    confetti.fillTriangle(0, -size, -size/2, size/2, size/2, size/2);
                }

                // 从中心点开始
                confetti.x = centerX;
                confetti.y = centerY;
                confetti.setDepth(1003); // 确保在UI界面之上

                // 随机的爆炸方向和距离
                const angle = Math.random() * Math.PI * 2; // 随机角度
                const explosionRadius = Math.random() * 150 + 100; // 爆炸半径
                const explosionX = centerX + Math.cos(angle) * explosionRadius;
                const explosionY = centerY + Math.sin(angle) * explosionRadius;

                // 第一阶段：爆炸扩散
                this.tweens.add({
                    targets: confetti,
                    x: explosionX,
                    y: explosionY,
                    rotation: (Math.random() - 0.5) * 360 * Math.PI / 180,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段：重力下落
                        const fallDistance = GAME_HEIGHT - explosionY + 50;
                        const horizontalDrift = (Math.random() - 0.5) * 100;

                        this.tweens.add({
                            targets: confetti,
                            x: explosionX + horizontalDrift,
                            y: explosionY + fallDistance,
                            rotation: confetti.rotation + (Math.random() - 0.5) * 720 * Math.PI / 180,
                            alpha: 0,
                            duration: 1300, // 调整为1300ms，总时长1.5秒
                            ease: 'Power1',
                            onComplete: () => {
                                confetti.destroy();
                            }
                        });
                    }
                });

                // 添加轻微的摆动效果
                this.tweens.add({
                    targets: confetti,
                    scaleX: 0.8,
                    scaleY: 1.2,
                    duration: 300 + Math.random() * 100,
                    yoyo: true,
                    repeat: 2, // 重复2次，总共约1.2秒
                    ease: 'Sine.easeInOut'
                });
            }

            showStarRating(stars) {
                // 显示星星评级
                const starY = GAME_HEIGHT/2 - 80;
                const starSize = 60;
                const starSpacing = 80;
                const totalWidth = 3 * starSize + 2 * starSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2 + starSize / 2;

                // 创建3个星星位置
                for (let i = 0; i < 3; i++) {
                    const starX = startX + i * (starSize + starSpacing);
                    const isLit = i < stars; // 是否点亮这颗星星

                    // 延迟显示每颗星星
                    this.time.delayedCall(500 + i * 300, () => {
                        this.createAnimatedStar(starX, starY, starSize, isLit);
                    });
                }

                // 显示评级文字
                this.time.delayedCall(1400, () => {
                    let ratingText = '';
                    let ratingColor = '';

                    switch(stars) {
                        case 3:
                            ratingText = '完美通关！';
                            ratingColor = '#FFD700';
                            break;
                        case 2:
                            ratingText = '表现优秀！';
                            ratingColor = '#C0C0C0';
                            break;
                        case 1:
                            ratingText = '成功通关！';
                            ratingColor = '#CD7F32';
                            break;
                    }

                    const rating = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 10, ratingText, {
                        fontSize: '36px',
                        fontFamily: 'Arial, sans-serif',
                        color: ratingColor,
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);
                    rating.setDepth(1003);

                    // 评级文字出现动画
                    rating.setScale(0);
                    this.tweens.add({
                        targets: rating,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 400,
                        ease: 'Back.easeOut'
                    });
                });
            }

            createAnimatedStar(x, y, size, isLit) {
                // 创建星星图形
                const star = this.add.graphics();
                star.setDepth(1003);

                // 设置星星颜色
                const fillColor = isLit ? 0xFFD700 : 0x666666; // 金色或灰色
                const strokeColor = isLit ? 0xFFA500 : 0x444444; // 橙色或深灰色

                star.fillStyle(fillColor);
                star.lineStyle(3, strokeColor);

                // 绘制五角星
                const points = [];
                const outerRadius = size / 2;
                const innerRadius = outerRadius * 0.4;

                for (let i = 0; i < 10; i++) {
                    const angle = (i * Math.PI) / 5;
                    const radius = i % 2 === 0 ? outerRadius : innerRadius;
                    const px = x + Math.cos(angle - Math.PI / 2) * radius;
                    const py = y + Math.sin(angle - Math.PI / 2) * radius;
                    points.push(px, py);
                }

                star.fillPoints(points);
                star.strokePoints(points);

                // 星星出现动画
                star.setScale(0);
                star.setRotation(0);

                this.tweens.add({
                    targets: star,
                    scaleX: 1,
                    scaleY: 1,
                    rotation: Math.PI * 2,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 如果是点亮的星星，添加闪烁效果
                if (isLit) {
                    this.time.delayedCall(600, () => {
                        this.tweens.add({
                            targets: star,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 200,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 添加光芒效果
                        const glow = this.add.graphics();
                        glow.setDepth(1002);
                        glow.fillStyle(0xFFD700, 0.3);
                        glow.fillCircle(x, y, size);
                        glow.setScale(0);

                        this.tweens.add({
                            targets: glow,
                            scaleX: 1.5,
                            scaleY: 1.5,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });
                    });
                }
            }

            showGameOver() {
                // 禁用游戏输入
                this.disableGameInput();

                // 创建半透明背景遮罩（可交互，阻止点击穿透）
                const overlay = this.add.graphics();
                overlay.fillStyle(0x000000, 0.7);
                overlay.fillRect(0, 0, GAME_WIDTH, GAME_HEIGHT);
                overlay.setDepth(999);
                overlay.setInteractive(new Phaser.Geom.Rectangle(0, 0, GAME_WIDTH, GAME_HEIGHT), Phaser.Geom.Rectangle.Contains);

                // 显示大表情（向上移动）
                const bigEmoji = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 280, '😭', {
                    fontSize: '180px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);
                bigEmoji.setDepth(1001);

                // 添加表情缩放动画
                bigEmoji.setScale(0);
                this.tweens.add({
                    targets: bigEmoji,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 显示游戏结束文字
                const gameOverText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 80, '游戏结束！', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#E74C3C',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                gameOverText.setDepth(1001);

                // 显示分数（右上角）
                const scoreText = this.add.text(GAME_WIDTH - 30, 100, `🪙 ${this.score}`, {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);
                scoreText.setDepth(1001);

                // 创建重新开始按钮
                const retryButton = this.createRetryButton();

                // 存储界面元素以便清理
                this.gameOverUI = {
                    overlay,
                    bigEmoji,
                    gameOverText,
                    scoreText,
                    retryButton
                };
            }

            createRetryButton() {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(0xE74C3C);
                buttonBg.fillRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.lineStyle(4, 0xC0392B);
                buttonBg.strokeRoundedRect(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80, 20);
                buttonBg.setDepth(1001);

                // 设置按钮背景可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(GAME_WIDTH/2 - 120, GAME_HEIGHT/2 + 60, 240, 80), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 100, '重新开始', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                buttonText.setDepth(1002);

                // 添加点击事件
                buttonBg.on('pointerdown', () => {
                    this.onRetryClick();
                });

                // 返回一个包含背景和文字的对象，方便清理
                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            onRetryClick() {
                // 清理游戏结束界面
                if (this.gameOverUI) {
                    Object.values(this.gameOverUI).forEach(element => {
                        if (element && element.destroy) {
                            element.destroy();
                        }
                    });
                    this.gameOverUI = null;
                }

                // 重新启用游戏输入
                this.enableGameInput();

                // 重新开始游戏
                this.restartGame();
            }

            nextLevel() {
                this.level++;
                this.levelText.setText(`${this.level}`);
                this.gameOver = false;
                this.isAnimating = false; // 重置动画状态

                // 强制清空暂存区（确保清空）
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }



            restartGame() {
                this.score = 0;
                this.level = 1;
                this.scoreText.setText('🪙 0');
                this.levelText.setText('1');
                this.gameOver = false;
                this.isAnimating = false;

                // 清空暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });



                this.initializeGame();
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#c7dae2',
            scene: GameScene,
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false,
                transparent: false,
                clearBeforeRender: true,
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
                powerPreference: "high-performance"
            },
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>

