
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Water Sort Puzzle</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.80.1/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background-color: #333;
            overflow: hidden;
        }
        canvas {
            border: 2px solid #555;
            display: block;
            max-width: 100vw;
            max-height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // --- 游戏配置 ---
    const GAME_WIDTH = 750;
    const GAME_HEIGHT = 1334;
    
    const config = {
        type: Phaser.AUTO,
        width: GAME_WIDTH,
        height: GAME_HEIGHT,
        backgroundColor: '#4488AA',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // --- 全局游戏变量 ---
    let game;
    let tubes = [];
    let selectedTube = null;
    let gameEnded = false; // 防止重复弹窗
    const TUBE_CAPACITY = 4;

    // 定义颜色十六进制值
    const COLORS = {
        red: 0xFF0000,
        blue: 0x0000FF,
        green: 0x00FF00,
        yellow: 0xFFFF00,
        purple: 0x800080,
        orange: 0xFFA500,
        cyan: 0x00FFFF
    };
    const COLOR_NAMES = Object.keys(COLORS);

    // --- Phaser 场景函数 ---

    function preload() {
        // 不需要加载外部图片，此函数留空
    }

    function create() {
        // 添加一个矩形作为背景
        this.add.rectangle(config.width / 2, config.height / 2, config.width, config.height, 0x4488AA);

        // 添加游戏标题
        const title = this.add.text(config.width / 2, 80, '水排序游戏', {
            fontSize: '36px',
            fontWeight: 'bold',
            fill: '#ffffff',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5);

        // --- 初始关卡设置 ---
        const numTubes = 6; // 减少试管数量
        const numColors = 4;
        const layersPerColor = TUBE_CAPACITY;
        const emptyTubes = 2;

        const levelData = generateRandomLevel(numTubes, numColors, layersPerColor, emptyTubes);

        // 创建 TestTube 实例 - 竖屏布局
        const tubeSpacing = 150; // 进一步增加水平间距
        const tubesPerRow = 3; // 每行3个试管
        const rowSpacing = 250; // 进一步增加垂直间距
        const startX = (config.width - (tubesPerRow * tubeSpacing)) / 2 + (tubeSpacing / 2);
        const startY = config.height * 0.25; // 从屏幕25%位置开始，整体下移

        levelData.forEach((colors, index) => {
            const row = Math.floor(index / tubesPerRow);
            const col = index % tubesPerRow;
            const tubeX = startX + (col * tubeSpacing);
            const tubeY = startY + (row * rowSpacing);
            
            const tube = new TestTube(this, tubeX, tubeY, TUBE_CAPACITY, colors);
            tubes.push(tube);
        });

        // 修复: 将 handleInput 绑定到当前场景上下文
        const scene = this;
        this.input.on('pointerdown', function(pointer) {
            handleInput.call(scene, pointer);
        });
    }

    function update() {
        // 每帧运行的游戏逻辑
    }

    // --- 输入处理 ---
    function handleInput(pointer) {
        // 检查是否点击了试管
        let clickedTube = null;
        for (let i = 0; i < tubes.length; i++) {
            if (tubes[i].getBounds().contains(pointer.x, pointer.y)) {
                clickedTube = tubes[i];
                break;
            }
        }

        if (clickedTube) {
            if (selectedTube === null) {
                // 第一次点击：选择源试管
                if (!clickedTube.isEmpty()) {
                    selectedTube = clickedTube;
                    selectedTube.highlight();
                }
            } else if (selectedTube === clickedTube) {
                // 再次点击同一个试管：取消选择
                selectedTube.unhighlight();
                selectedTube = null;
            } else {
                // 第二次点击：尝试移动水
                const moveSuccessful = tryMoveWater(selectedTube, clickedTube);
                if (moveSuccessful) {
                    // 如果移动成功，延迟检查游戏状态以等待动画完成
                    const scene = this;
                    scene.time.delayedCall(500, function() {
                        checkWinCondition();
                        // 只有在没有胜利的情况下才检查游戏结束
                        if (!gameEnded) {
                            checkGameOver();
                        }
                    });
                } else {
                    // 移动失败时也检查游戏是否结束
                    setTimeout(() => {
                        if (!checkGameOver()) {
                            // 如果游戏没结束，给出提示
                            console.log("无法移动到该位置");
                        }
                    }, 100);
                }
                selectedTube.unhighlight();
                selectedTube = null;
            }
        }
    }

    // --- 核心游戏逻辑 ---

    function tryMoveWater(sourceTube, targetTube) {
        if (sourceTube.isEmpty()) {
            console.log("源试管为空。");
            return false;
        }

        const { layers: topLayersToMove, color: topColorToMove } = sourceTube.getTopLayersInfo();
        const targetTopColor = targetTube.getTopColor();
        const targetEmptySpace = targetTube.getEmptySpace();

        // 规则 1: 目标试管必须为空 OR 顶部颜色匹配
        if (!targetTube.isEmpty() && targetTopColor !== topColorToMove) {
            console.log("目标试管顶部颜色不匹配或目标试管非空。");
            return false;
        }

        // 规则 2: 目标试管必须有足够的空间
        if (targetEmptySpace < topLayersToMove.length) {
            console.log("目标试管空间不足。");
            return false;
        }

        // 如果所有条件都满足，执行移动动画并更新状态
        moveWaterAnimation(sourceTube, targetTube, topLayersToMove, () => {
            // 动画完成后回调，更新实际数据
            sourceTube.removeWater(topLayersToMove.length);
            targetTube.addWater(topLayersToMove);
        });

        return true;
    }

    // --- [OPTIMIZED] ---
    function moveWaterAnimation(sourceTube, targetTube, layersToMove, onCompleteCallback) {
        // 保存源杯子的原始位置
        const originalX = sourceTube.x;
        const originalY = sourceTube.y;
        
        // --- 1. 计算对齐和倾斜所需的参数 ---
        
        // 根据目标位置确定倾斜角度。向右倾斜为正，向左为负。
        const tiltAngle = (targetTube.x >= originalX) ? 75 : -75;
        const angleInRadians = Phaser.Math.DegToRad(tiltAngle);
    
        // 获取试管尺寸
        const tubeHeight = sourceTube.tubeHeight;
    
        // 计算倾斜后，杯口相对于杯子中心点的偏移量
        // 杯口的原始相对位置是 (0, -tubeHeight / 2)
        // 旋转后，新坐标为 x' = x*cos(a) - y*sin(a), y' = x*sin(a) + y*cos(a)
        const mouthOffsetX = 0 * Math.cos(angleInRadians) - (-tubeHeight / 2) * Math.sin(angleInRadians);
        const mouthOffsetY = 0 * Math.sin(angleInRadians) + (-tubeHeight / 2) * Math.cos(angleInRadians);
    
        // 目标杯口的理想世界坐标
        const targetMouthX = targetTube.x;
        const targetMouthY = targetTube.y - tubeHeight / 2;
        
        // 计算为了让杯口对齐，源杯子的中心点需要移动到的最终位置
        const finalPourX = targetMouthX - mouthOffsetX;
        const finalPourY = targetMouthY - mouthOffsetY;
    
        // --- 2. 创建动画序列 ---
    
        // a. 移动和倾斜
        sourceTube.scene.tweens.add({
            targets: sourceTube,
            x: finalPourX,
            y: finalPourY,
            angle: tiltAngle,
            duration: 400,
            ease: 'Sine.easeInOut',
            onComplete: () => {
                // b. 水流动画
                pourWater();
            }
        });
    
        function pourWater() {
            // 创建临时水层精灵用于动画
            const tempWaterSprites = [];
            const layerHeight = sourceTube.getLayerHeight();

            // 源杯口在倾斜后的世界坐标
            const pourStartX = sourceTube.x + mouthOffsetX;
            const pourStartY = sourceTube.y + mouthOffsetY;

            for (let i = 0; i < layersToMove.length; i++) {
                const color = layersToMove[i];
                const tempSprite = sourceTube.scene.add.rectangle(
                    pourStartX, // 从倾斜的杯口开始
                    pourStartY,
                    sourceTube.getWaterWidth(),
                    layerHeight,
                    COLORS[color]
                );
                tempSprite.setStrokeStyle(2, 0x000000, 0.3);
                tempWaterSprites.push(tempSprite);
            }

            // 隐藏源试管中被倒出的水
            sourceTube.removeWaterVisuals(layersToMove.length);

            let completedAnimations = 0;
            const totalAnimations = tempWaterSprites.length;

            // 水层下落动画
            tempWaterSprites.forEach((sprite, index) => {
                const delay = index * 60;
                const targetLayerIndex = targetTube.colors.length + index;
                // 计算水落在目标试管中的最终位置
                const finalWaterX = targetTube.x;
                const finalWaterY = targetTube.y + targetTube.getTubeBaseY() - ((targetLayerIndex + 1) * targetTube.getLayerHeight());

                sourceTube.scene.tweens.add({
                    targets: sprite,
                    x: finalWaterX,
                    y: finalWaterY,
                    duration: 400,
                    delay: delay,
                    ease: 'Bounce.easeOut',
                    onComplete: () => {
                        // 不销毁水块，而是将其添加到目标试管中
                        targetTube.add(sprite);
                        // 调整水块的相对位置，使其与原始水层位置匹配
                        sprite.x = 0; // 水平居中
                        // 计算正确的Y位置：从试管底部向上堆叠
                        const correctY = targetTube.getTubeBaseY() - ((targetLayerIndex + 1) * targetTube.getLayerHeight()) + targetTube.getLayerHeight() / 2;
                        sprite.y = correctY;
                        // 将水块添加到目标试管的水层数组中
                        targetTube.waterGraphics.push(sprite);

                        completedAnimations++;
                        // 当所有水滴动画都完成时
                        if (completedAnimations === totalAnimations) {
                            returnToOrigin();
                        }
                    }
                });
            });
        }
    
        function returnToOrigin() {
            // c. 回正并回到原位
            sourceTube.scene.tweens.add({
                targets: sourceTube,
                x: originalX,
                y: originalY,
                angle: 0, // 角度回正
                duration: 400,
                ease: 'Sine.easeInOut',
                onComplete: () => {
                    // 动画全部完成后，执行回调，更新数据
                    // 水块已经直接移动到目标试管了，不需要再添加视觉效果
                    onCompleteCallback();

                    // 延迟检查消除和胜利条件
                    setTimeout(() => {
                        if (!gameEnded) {
                            checkAndEliminateFullContainers();
                        }
                    }, 100);
                }
            });
        }
    }

    function checkWinCondition() {
        if (gameEnded) return; // 如果游戏已结束，不再检查

        let allSorted = true;
        for (let i = 0; i < tubes.length; i++) {
            if (!tubes[i].isEmpty() && !tubes[i].isSortedAndFull()) {
                allSorted = false;
                break;
            }
        }

        if (allSorted) {
            gameEnded = true;
            showWinDialog();
        }
    }

    // 显示胜利弹窗
    function showWinDialog() {
        const scene = tubes[0].scene; // 获取场景引用

        // 创建半透明背景遮罩
        const overlay = scene.add.rectangle(config.width / 2, config.height / 2, config.width, config.height, 0x000000, 0.7);
        overlay.setInteractive();

        // 创建弹窗背景
        const dialogBg = scene.add.graphics();
        dialogBg.fillStyle(0xffffff, 1);
        dialogBg.lineStyle(4, 0x4CAF50, 1);
        dialogBg.fillRoundedRect(config.width / 2 - 150, config.height / 2 - 200, 300, 400, 20);
        dialogBg.strokeRoundedRect(config.width / 2 - 150, config.height / 2 - 200, 300, 400, 20);

        // 胜利标题
        const winTitle = scene.add.text(config.width / 2, config.height / 2 - 120, '🎉 恭喜！', {
            fontSize: '48px',
            fontWeight: 'bold',
            fill: '#4CAF50',
            align: 'center'
        }).setOrigin(0.5);

        // 胜利信息
        const winMessage = scene.add.text(config.width / 2, config.height / 2 - 40, '你成功分类了所有的水！', {
            fontSize: '24px',
            fill: '#333333',
            align: 'center'
        }).setOrigin(0.5);

        // 重新开始按钮
        const restartBtn = scene.add.graphics();
        restartBtn.fillStyle(0x4CAF50, 1);
        restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        restartBtn.setInteractive(new Phaser.Geom.Rectangle(config.width / 2 - 80, config.height / 2 + 80, 160, 50), Phaser.Geom.Rectangle.Contains);

        const restartText = scene.add.text(config.width / 2, config.height / 2 + 105, '重新开始', {
            fontSize: '20px',
            fontWeight: 'bold',
            fill: '#ffffff'
        }).setOrigin(0.5);

        // 按钮点击效果
        restartBtn.on('pointerover', () => {
            restartBtn.clear();
            restartBtn.fillStyle(0x45a049, 1);
            restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        });

        restartBtn.on('pointerout', () => {
            restartBtn.clear();
            restartBtn.fillStyle(0x4CAF50, 1);
            restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        });

        restartBtn.on('pointerdown', () => {
            // 销毁弹窗元素
            overlay.destroy();
            dialogBg.destroy();
            winTitle.destroy();
            winMessage.destroy();
            restartBtn.destroy();
            restartText.destroy();

            // 重新开始游戏
            restartGame();
        });

        // 弹窗出现动画
        const dialogElements = [overlay, dialogBg, winTitle, winMessage, restartBtn, restartText];
        dialogElements.forEach((element, index) => {
            element.setAlpha(0);
            element.setScale(0.8);
            scene.tweens.add({
                targets: element,
                alpha: 1,
                scaleX: 1,
                scaleY: 1,
                duration: 300,
                delay: index * 50,
                ease: 'Back.easeOut'
            });
        });
    }

    // 重新开始游戏
    function restartGame() {
        const scene = game.scene.scenes[0]; // 获取主场景

        // 重置游戏状态
        gameEnded = false;

        // 清除所有试管
        tubes.forEach(tube => tube.destroy());
        tubes = [];
        selectedTube = null;

        // 重新生成关卡
        const numTubes = 6; // 减少试管数量
        const numColors = 4;
        const layersPerColor = TUBE_CAPACITY;
        const emptyTubes = 2;

        const levelData = generateRandomLevel(numTubes, numColors, layersPerColor, emptyTubes);

        // 重新创建试管 - 竖屏布局
        const tubeSpacing = 150; // 进一步增加水平间距
        const tubesPerRow = 3; // 每行3个试管
        const rowSpacing = 250; // 进一步增加垂直间距
        const startX = (config.width - (tubesPerRow * tubeSpacing)) / 2 + (tubeSpacing / 2);
        const startY = config.height * 0.25; // 从屏幕25%位置开始，整体下移

        levelData.forEach((colors, index) => {
            const row = Math.floor(index / tubesPerRow);
            const col = index % tubesPerRow;
            const tubeX = startX + (col * tubeSpacing);
            const tubeY = startY + (row * rowSpacing);
            
            const tube = new TestTube(scene, tubeX, tubeY, TUBE_CAPACITY, colors);
            tubes.push(tube);
        });
    }

    // 显示游戏失败弹窗
    function showGameOverDialog() {
        const scene = tubes[0].scene;

        // 创建半透明背景遮罩
        const overlay = scene.add.rectangle(config.width / 2, config.height / 2, config.width, config.height, 0x000000, 0.7);
        overlay.setInteractive();

        // 创建弹窗背景
        const dialogBg = scene.add.graphics();
        dialogBg.fillStyle(0xffffff, 1);
        dialogBg.lineStyle(4, 0xF44336, 1);
        dialogBg.fillRoundedRect(config.width / 2 - 150, config.height / 2 - 200, 300, 400, 20);
        dialogBg.strokeRoundedRect(config.width / 2 - 150, config.height / 2 - 200, 300, 400, 20);

        // 失败标题
        const gameOverTitle = scene.add.text(config.width / 2, config.height / 2 - 120, '😔 游戏结束', {
            fontSize: '48px',
            fontWeight: 'bold',
            fill: '#F44336',
            align: 'center'
        }).setOrigin(0.5);

        // 失败信息
        const gameOverMessage = scene.add.text(config.width / 2, config.height / 2 - 40, '没有更多可移动的步骤了！', {
            fontSize: '24px',
            fill: '#333333',
            align: 'center'
        }).setOrigin(0.5);

        // 重新开始按钮
        const restartBtn = scene.add.graphics();
        restartBtn.fillStyle(0xF44336, 1);
        restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        restartBtn.setInteractive(new Phaser.Geom.Rectangle(config.width / 2 - 80, config.height / 2 + 80, 160, 50), Phaser.Geom.Rectangle.Contains);

        const restartText = scene.add.text(config.width / 2, config.height / 2 + 105, '重新开始', {
            fontSize: '20px',
            fontWeight: 'bold',
            fill: '#ffffff'
        }).setOrigin(0.5);

        // 按钮点击效果
        restartBtn.on('pointerover', () => {
            restartBtn.clear();
            restartBtn.fillStyle(0xe53935, 1);
            restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        });

        restartBtn.on('pointerout', () => {
            restartBtn.clear();
            restartBtn.fillStyle(0xF44336, 1);
            restartBtn.fillRoundedRect(config.width / 2 - 80, config.height / 2 + 80, 160, 50, 10);
        });

        restartBtn.on('pointerdown', () => {
            // 销毁弹窗元素
            overlay.destroy();
            dialogBg.destroy();
            gameOverTitle.destroy();
            gameOverMessage.destroy();
            restartBtn.destroy();
            restartText.destroy();

            // 重新开始游戏
            restartGame();
        });

        // 弹窗出现动画
        const dialogElements = [overlay, dialogBg, gameOverTitle, gameOverMessage, restartBtn, restartText];
        dialogElements.forEach((element, index) => {
            element.setAlpha(0);
            element.setScale(0.8);
            scene.tweens.add({
                targets: element,
                alpha: 1,
                scaleX: 1,
                scaleY: 1,
                duration: 300,
                delay: index * 50,
                ease: 'Back.easeOut'
            });
        });
    }

    // 检查是否还有可移动的步骤
    function checkGameOver() {
        if (gameEnded) return true; // 如果游戏已结束，直接返回

        // 检查是否有任何可能的移动
        for (let i = 0; i < tubes.length; i++) {
            for (let j = 0; j < tubes.length; j++) {
                if (i !== j && canMove(tubes[i], tubes[j])) {
                    return false; // 还有可移动的步骤
                }
            }
        }

        // 没有可移动的步骤，游戏结束
        gameEnded = true;
        setTimeout(() => {
            showGameOverDialog();
        }, 500);
        return true;
    }

    // 检查是否可以从源试管移动到目标试管
    function canMove(sourceTube, targetTube) {
        if (sourceTube.isEmpty()) return false;

        const { layers: topLayersToMove, color: topColorToMove } = sourceTube.getTopLayersInfo();
        const targetTopColor = targetTube.getTopColor();
        const targetEmptySpace = targetTube.getEmptySpace();

        // 检查移动规则
        if (!targetTube.isEmpty() && targetTopColor !== topColorToMove) {
            return false;
        }

        if (targetEmptySpace < topLayersToMove.length) {
            return false;
        }

        return true;
    }

    // 检查并消除满容器
    function checkAndEliminateFullContainers() {
        console.log("检查满容器...");

        for (let i = 0; i < tubes.length; i++) {
            const tube = tubes[i];
            if (tube.isSortedAndFull()) {
                console.log(`试管 ${i} 已满且颜色统一，开始消除动画`);
                eliminateFullContainer(tube);
            }
        }
    }

    // 消除满容器的动画
    function eliminateFullContainer(tube) {
        // 创建闪烁效果
        const flashCount = 3;
        let currentFlash = 0;

        const flashInterval = setInterval(() => {
            if (currentFlash < flashCount) {
                // 闪烁效果
                tube.waterGraphics.forEach(graphic => {
                    graphic.alpha = graphic.alpha === 1 ? 0.3 : 1;
                });
                currentFlash++;
            } else {
                clearInterval(flashInterval);

                // 消除动画：水层向上消失
                tube.waterGraphics.forEach((graphic, index) => {
                    tube.scene.tweens.add({
                        targets: graphic,
                        alpha: 0,
                        y: graphic.y - 50,
                        scaleX: 1.2,
                        scaleY: 1.2,
                        duration: 300,
                        delay: index * 50, // 从下往上依次消失
                        ease: 'Back.easeIn',
                        onComplete: () => {
                            graphic.destroy();
                        }
                    });
                });

                // 清空试管数据
                setTimeout(() => {
                    tube.colors = [];
                    tube.waterGraphics = [];

                    // 添加得分效果
                    showScoreEffect(tube);

                    // 检查胜利条件
                    setTimeout(() => {
                        if (!gameEnded) {
                            checkWinCondition();
                        }
                    }, 500);
                }, 500);
            }
        }, 200);
    }

    // 显示得分效果
    function showScoreEffect(tube) {
        const scoreText = tube.scene.add.text(tube.x, tube.y - 50, '+100', {
            fontSize: '32px',
            fontWeight: 'bold',
            fill: '#FFD700',
            stroke: '#000000',
            strokeThickness: 3
        }).setOrigin(0.5);

        // 得分文字动画
        tube.scene.tweens.add({
            targets: scoreText,
            y: scoreText.y - 100,
            alpha: 0,
            scaleX: 1.5,
            scaleY: 1.5,
            duration: 1000,
            ease: 'Power2',
            onComplete: () => {
                scoreText.destroy();
            }
        });

        // 添加粒子效果
        createParticleEffect(tube);
    }

    // 创建粒子效果
    function createParticleEffect(tube) {
        const particleCount = 10;

        for (let i = 0; i < particleCount; i++) {
            const particle = tube.scene.add.circle(
                tube.x + (Math.random() - 0.5) * 60,
                tube.y + (Math.random() - 0.5) * 100,
                Math.random() * 8 + 4,
                0xFFD700
            );

            tube.scene.tweens.add({
                targets: particle,
                x: particle.x + (Math.random() - 0.5) * 200,
                y: particle.y - Math.random() * 150 - 50,
                alpha: 0,
                scaleX: 0,
                scaleY: 0,
                duration: Math.random() * 800 + 600,
                ease: 'Power2',
                onComplete: () => {
                    particle.destroy();
                }
            });
        }
    }

    // --- 随机关卡生成器 ---
    function generateRandomLevel(numTubes, numColors, layersPerColor, emptyTubes) {
        let allWater = [];
        const availableColors = Phaser.Utils.Array.Shuffle(COLOR_NAMES).slice(0, numColors);

        // 生成所有水层
        availableColors.forEach(color => {
            for (let i = 0; i < layersPerColor; i++) {
                allWater.push(color);
            }
        });

        // 确保总水量能够均匀分布到非空试管中
        let filledTubeCount = numTubes - emptyTubes;
        if (allWater.length % filledTubeCount !== 0) {
            console.warn("生成的颜色数量无法均匀分布到非空试管中，正在调整非空试管数量。");
            while (filledTubeCount > 0 && allWater.length % filledTubeCount !== 0) {
                filledTubeCount--;
            }
            if (filledTubeCount === 0) {
                console.error("无法创建有效的关卡布局，所有试管都必须为空或水量无法分布。请调整关卡参数。");
                return Array(numTubes).fill([]);
            }
        }

        const layersPerFilledTube = allWater.length / filledTubeCount;
        if (layersPerFilledTube > TUBE_CAPACITY) {
            console.error("每管的水层数超过试管容量，请调整参数或试管容量。");
            return Array(numTubes).fill([]);
        }

        Phaser.Utils.Array.Shuffle(allWater);

        const level = [];
        let waterIndex = 0;

        // 分配水到部分试管
        for (let i = 0; i < filledTubeCount; i++) {
            const tubeColors = [];
            for (let j = 0; j < layersPerFilledTube; j++) {
                tubeColors.push(allWater[waterIndex++]);
            }
            level.push(tubeColors);
        }

        // 添加空试管
        for (let i = 0; i < emptyTubes; i++) {
            level.push([]);
        }

        return Phaser.Utils.Array.Shuffle(level);
    }

    // --- TestTube 类 ---
    class TestTube extends Phaser.GameObjects.Container {
        constructor(scene, x, y, capacity, initialColors) {
            super(scene, x, y);
            scene.add.existing(this);

            this.capacity = capacity;
            this.colors = initialColors || [];
            this.waterGraphics = [];

            // 绘制试管本体
            this.drawTubeBody();

            // 根据初始颜色绘制水层
            this.updateWaterVisuals();

            // 设置交互区域
            this.setSize(this.tubeWidth, this.tubeHeight);
            this.setInteractive(new Phaser.Geom.Rectangle(-this.tubeWidth/2, -this.tubeHeight/2, this.tubeWidth, this.tubeHeight), Phaser.Geom.Rectangle.Contains);
        }

        // 绘制试管本体
        drawTubeBody() {
            this.tubeWidth = 70; // 减小试管宽度
            this.tubeHeight = 200; // 减小试管高度
            this.tubeBaseY = this.tubeHeight / 2 - 10; // 调整底部位置，让水层紧贴底部
            this.waterWidth = this.tubeWidth * 0.7;

            this.graphics = this.scene.add.graphics();
            this.add(this.graphics);

            // 只绘制三条边：左、右、底
            this.graphics.lineStyle(6, 0x888888, 1);
            // 左边
            this.graphics.lineBetween(
                -this.tubeWidth / 2,
                -this.tubeHeight / 2,
                -this.tubeWidth / 2,
                this.tubeHeight / 2
            );
            // 右边
            this.graphics.lineBetween(
                this.tubeWidth / 2,
                -this.tubeHeight / 2,
                this.tubeWidth / 2,
                this.tubeHeight / 2
            );
            // 底边
            this.graphics.lineBetween(
                -this.tubeWidth / 2,
                this.tubeHeight / 2,
                this.tubeWidth / 2,
                this.tubeHeight / 2
            );
        }

        getLayerHeight() {
            const liquidAreaHeight = this.tubeHeight * 0.8;
            const baseLayerHeight = liquidAreaHeight / this.capacity;
            // 水层占满整个高度，不留间距
            return baseLayerHeight;
        }

        getLayerSpacing() {
            // 水层之间不留间距
            return 0;
        }

        getTotalLayerHeight() {
            // 水层高度，无间距
            return this.getLayerHeight();
        }

        getWaterWidth() {
            return this.waterWidth;
        }

        getTubeBaseY() {
            return this.tubeBaseY;
        }

        updateWaterVisuals() {
            // 清除现有水层图形
            this.waterGraphics.forEach(graphic => graphic.destroy());
            this.waterGraphics = [];

            const layerHeight = this.getLayerHeight();

            // 从底部向上绘制水层，紧贴在一起
            for (let i = 0; i < this.colors.length; i++) {
                const colorName = this.colors[i];
                const colorValue = COLORS[colorName];

                const waterGraphic = this.scene.add.graphics();
                waterGraphic.fillStyle(colorValue, 1);

                // 计算水层位置，紧贴在一起
                const yPosition = this.tubeBaseY - ((i + 1) * layerHeight);

                                 waterGraphic.fillRect(
                     -this.waterWidth / 2,
                     yPosition,
                     this.waterWidth,
                     layerHeight
                 );
                this.add(waterGraphic);
                this.waterGraphics.push(waterGraphic);
            }
        }

        addWaterVisuals(layersToAdd) {
            // 优化：只添加新的水层，避免重新绘制整个试管造成闪动
            const layerHeight = this.getLayerHeight();
            const startIndex = this.colors.length - layersToAdd.length;

            // 只为新添加的水层创建图形
            for (let i = 0; i < layersToAdd.length; i++) {
                const colorName = layersToAdd[i];
                const colorValue = COLORS[colorName];
                const layerIndex = startIndex + i;

                const waterGraphic = this.scene.add.graphics();
                waterGraphic.fillStyle(colorValue, 1);

                // 计算新水层的位置
                const yPosition = this.tubeBaseY - ((layerIndex + 1) * layerHeight);

                waterGraphic.fillRect(
                    -this.waterWidth / 2,
                    yPosition,
                    this.waterWidth,
                    layerHeight
                );

                this.add(waterGraphic);
                this.waterGraphics.push(waterGraphic);
            }
        }

        removeWaterVisuals(count) {
            for (let i = 0; i < count; i++) {
                if (this.waterGraphics.length > 0) {
                    this.waterGraphics.pop().destroy();
                }
            }
        }

        getTopColor() {
            if (this.colors.length === 0) return null;
            return this.colors[this.colors.length - 1];
        }

        getTopLayersInfo() {
            if (this.colors.length === 0) return { layers: [], color: null };

            const topColor = this.getTopColor();
            let count = 0;
            for (let i = this.colors.length - 1; i >= 0; i--) {
                if (this.colors[i] === topColor) {
                    count++;
                } else {
                    break;
                }
            }
            return { layers: this.colors.slice(this.colors.length - count), color: topColor };
        }

        getEmptySpace() {
            return this.capacity - this.colors.length;
        }

        isEmpty() {
            return this.colors.length === 0;
        }

        addWater(layersToAdd) {
            this.colors = this.colors.concat(layersToAdd);
        }

        removeWater(count) {
            this.colors.splice(this.colors.length - count, count);
        }

        isSortedAndFull() {
            if (this.colors.length === 0) return false;
            if (this.colors.length < this.capacity) {
                return false;
            }

            const firstColor = this.colors[0];
            return this.colors.every(color => color === firstColor);
        }

        highlight() {
            this.graphics.lineStyle(8, 0xFFD700, 1);
            this.graphics.strokeRoundedRect(
                -this.tubeWidth / 2,
                -this.tubeHeight / 2,
                this.tubeWidth,
                this.tubeHeight,
                15
            );
        }

        unhighlight() {
            this.graphics.clear();
            this.drawTubeBody();
        }

        getBounds() {
            // 返回试管的边界矩形，用于点击检测
            return new Phaser.Geom.Rectangle(
                this.x - this.tubeWidth / 2,
                this.y - this.tubeHeight / 2,
                this.tubeWidth,
                this.tubeHeight
            );
        }

        setTilt(angle, duration = 200, callback) {
            this.scene.tweens.add({
                targets: this,
                angle: angle,
                duration: duration,
                ease: 'Sine.easeInOut',
                onComplete: callback
            });
        }
    }

    // --- 启动游戏 ---
    window.onload = function() {
        // 检查 Phaser 是否已加载
        if (typeof Phaser === 'undefined') {
            console.error('Phaser library failed to load. Please check your internet connection.');
            document.body.innerHTML = '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h2>Error Loading Game</h2><p>Phaser library failed to load. Please check your internet connection and refresh the page.</p></div>';
            return;
        }
        
        try {
            game = new Phaser.Game(config);
        } catch (error) {
            console.error('Error starting game:', error);
            document.body.innerHTML = '<div style="text-align: center; padding: 50px; font-family: Arial, sans-serif;"><h2>Game Error</h2><p>Failed to start the game. Please refresh the page and try again.</p></div>';
        }
    };
</script>

</body>
</html>