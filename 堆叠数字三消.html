<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三消堆叠游戏</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
        }
        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }
        .ui-overlay {
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 1000;
            color: white;
            font-size: 18px;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
        }
    </style>
</head>
<body>
    <div id="game-container">
        <div class="ui-overlay">
            <div>剩余方块: <span id="blocks-count">0</span></div>
        </div>
    </div>

    <script>
        class MatchThreeGame extends Phaser.Scene {
            constructor() {
                super({ key: 'MatchThreeGame' });
                this.blocks = [];
                this.slots = [];
                this.slotPositions = [];
                this.maxSlots = 7;
                this.blockTypes = ['red', 'blue', 'green', 'yellow', 'purple', 'orange'];
                this.gameWidth = 800;
                this.gameHeight = 600;
            }

            preload() {
                // 创建圆角方块纹理
                this.createBlockTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(this.gameWidth/2, this.gameHeight/2, this.gameWidth, this.gameHeight, 0x2c3e50);
                
                // 初始化槽位
                this.initSlots();
                
                // 生成游戏方块
                this.generateBlocks();
                
                // 添加重新开始按钮
                this.createRestartButton();
                
                // 更新方块计数
                this.updateBlockCount();
            }

            createBlockTextures() {
                const colors = {
                    'red': 0xff4757,
                    'blue': 0x3742fa,
                    'green': 0x2ed573,
                    'yellow': 0xffa502,
                    'purple': 0x8b00ff,
                    'orange': 0xff6348
                };

                Object.keys(colors).forEach(colorName => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(colors[colorName]);
                    graphics.fillRoundedRect(0, 0, 60, 60, 12);
                    graphics.lineStyle(3, 0xffffff, 0.8);
                    graphics.strokeRoundedRect(0, 0, 60, 60, 12);
                    graphics.generateTexture(colorName, 60, 60);
                    graphics.destroy();
                });
            }

            initSlots() {
                const slotY = this.gameHeight - 80;
                const startX = (this.gameWidth - (this.maxSlots * 70)) / 2;
                
                for (let i = 0; i < this.maxSlots; i++) {
                    const x = startX + i * 70 + 35;
                    const slotBg = this.add.rectangle(x, slotY, 65, 65, 0x34495e);
                    slotBg.setStrokeStyle(2, 0x7f8c8d);
                    
                    this.slotPositions.push({ x, y: slotY });
                }
                
                this.slots = new Array(this.maxSlots).fill(null);
            }

            generateBlocks() {
                // 创建一个保证有解的关卡
                const blockData = this.createSolvableLevel();
                
                blockData.forEach((data, index) => {
                    const block = this.add.image(data.x, data.y, data.type);
                    block.setInteractive();
                    block.setScale(0.8);
                    block.blockType = data.type;
                    block.layer = data.layer;
                    block.originalIndex = index;
                    
                    // 根据层级设置透明度和深度
                    block.setAlpha(0.7 + (data.layer * 0.1));
                    block.setDepth(data.layer);
                    
                    block.on('pointerdown', () => this.onBlockClick(block));
                    block.on('pointerover', () => {
                        if (this.canSelectBlock(block)) {
                            block.setTint(0xffffff);
                            block.setScale(0.85);
                        }
                    });
                    block.on('pointerout', () => {
                        block.clearTint();
                        block.setScale(0.8);
                    });
                    
                    this.blocks.push(block);
                });
            }

            createSolvableLevel() {
                // 创建一个简单但保证有解的关卡
                const types = ['red', 'blue', 'green', 'yellow'];
                const blockData = [];
                
                // 第一层 - 底层 (4x3 网格)
                for (let row = 0; row < 3; row++) {
                    for (let col = 0; col < 4; col++) {
                        blockData.push({
                            x: 200 + col * 80,
                            y: 150 + row * 80,
                            type: types[(row + col) % types.length],
                            layer: 0
                        });
                    }
                }
                
                // 第二层 - 中层 (3x2 网格，部分重叠)
                for (let row = 0; row < 2; row++) {
                    for (let col = 0; col < 3; col++) {
                        blockData.push({
                            x: 240 + col * 80,
                            y: 190 + row * 80,
                            type: types[(row * 3 + col) % types.length],
                            layer: 1
                        });
                    }
                }
                
                // 第三层 - 顶层 (2x1 网格)
                for (let col = 0; col < 2; col++) {
                    blockData.push({
                        x: 280 + col * 80,
                        y: 230,
                        type: types[col % types.length],
                        layer: 2
                    });
                }
                
                // 确保每种类型的方块数量是3的倍数
                const typeCounts = {};
                blockData.forEach(block => {
                    typeCounts[block.type] = (typeCounts[block.type] || 0) + 1;
                });
                
                // 添加额外的方块使每种类型都是3的倍数
                Object.keys(typeCounts).forEach(type => {
                    const count = typeCounts[type];
                    const remainder = count % 3;
                    if (remainder !== 0) {
                        const needed = 3 - remainder;
                        for (let i = 0; i < needed; i++) {
                            blockData.push({
                                x: 500 + i * 70,
                                y: 200 + Math.floor(i/2) * 70,
                                type: type,
                                layer: 0
                            });
                        }
                    }
                });
                
                return blockData;
            }

            canSelectBlock(block) {
                // 检查方块是否被其他方块遮挡
                const blockBounds = block.getBounds();
                
                for (let otherBlock of this.blocks) {
                    if (otherBlock === block || !otherBlock.visible) continue;
                    if (otherBlock.layer <= block.layer) continue;
                    
                    const otherBounds = otherBlock.getBounds();
                    if (Phaser.Geom.Rectangle.Overlaps(blockBounds, otherBounds)) {
                        return false;
                    }
                }
                return true;
            }

            onBlockClick(block) {
                if (!this.canSelectBlock(block)) return;
                if (this.slots.filter(slot => slot !== null).length >= this.maxSlots) return;
                
                // 将方块移动到槽位
                this.moveBlockToSlot(block);
                
                // 隐藏原方块
                block.setVisible(false);
                
                // 检查匹配
                this.checkMatches();
                
                // 更新计数
                this.updateBlockCount();
                
                // 检查游戏状态
                this.checkGameState();
            }

            moveBlockToSlot(block) {
                // 找到第一个空槽位
                const emptySlotIndex = this.slots.findIndex(slot => slot === null);
                if (emptySlotIndex === -1) return;
                
                // 创建槽位中的方块
                const slotBlock = this.add.image(
                    this.slotPositions[emptySlotIndex].x,
                    this.slotPositions[emptySlotIndex].y,
                    block.blockType
                );
                slotBlock.setScale(0.7);
                slotBlock.blockType = block.blockType;
                slotBlock.setDepth(100);
                
                // 添加到槽位
                this.slots[emptySlotIndex] = slotBlock;
                
                // 添加移动动画
                this.tweens.add({
                    targets: slotBlock,
                    scaleX: 0.8,
                    scaleY: 0.8,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2'
                });
            }

            checkMatches() {
                const typeCount = {};
                const typePositions = {};
                
                // 统计每种类型的数量和位置
                this.slots.forEach((slot, index) => {
                    if (slot) {
                        const type = slot.blockType;
                        typeCount[type] = (typeCount[type] || 0) + 1;
                        if (!typePositions[type]) typePositions[type] = [];
                        typePositions[type].push(index);
                    }
                });
                
                // 检查是否有3个相同的
                Object.keys(typeCount).forEach(type => {
                    if (typeCount[type] >= 3) {
                        // 消除3个相同的方块
                        const positions = typePositions[type].slice(0, 3);
                        positions.forEach(pos => {
                            const block = this.slots[pos];
                            if (block) {
                                // 添加消除动画
                                this.tweens.add({
                                    targets: block,
                                    scaleX: 0,
                                    scaleY: 0,
                                    alpha: 0,
                                    duration: 300,
                                    ease: 'Power2',
                                    onComplete: () => block.destroy()
                                });
                                this.slots[pos] = null;
                            }
                        });
                        
                        // 重新排列剩余方块
                        this.rearrangeSlots();
                    }
                });
            }

            rearrangeSlots() {
                // 将所有非空方块向前移动
                const nonEmptyBlocks = this.slots.filter(slot => slot !== null);
                
                // 清空槽位
                this.slots.fill(null);
                
                // 重新放置方块
                nonEmptyBlocks.forEach((block, index) => {
                    this.slots[index] = block;
                    
                    // 动画移动到新位置
                    this.tweens.add({
                        targets: block,
                        x: this.slotPositions[index].x,
                        duration: 300,
                        ease: 'Power2'
                    });
                });
            }

            checkGameState() {
                const visibleBlocks = this.blocks.filter(block => block.visible).length;
                const occupiedSlots = this.slots.filter(slot => slot !== null).length;
                
                if (visibleBlocks === 0) {
                    // 胜利
                    this.showGameOver('恭喜通关！', 0x2ed573);
                } else if (occupiedSlots >= this.maxSlots) {
                    // 失败
                    this.showGameOver('游戏失败！', 0xff4757);
                }
            }

            showGameOver(message, color) {
                const overlay = this.add.rectangle(this.gameWidth/2, this.gameHeight/2, this.gameWidth, this.gameHeight, 0x000000, 0.7);
                overlay.setDepth(1000);
                
                const text = this.add.text(this.gameWidth/2, this.gameHeight/2 - 50, message, {
                    fontSize: '48px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                });
                text.setOrigin(0.5);
                text.setDepth(1001);
                
                const button = this.add.rectangle(this.gameWidth/2, this.gameHeight/2 + 50, 200, 60, color);
                button.setInteractive();
                button.setDepth(1001);
                
                const buttonText = this.add.text(this.gameWidth/2, this.gameHeight/2 + 50, '重新开始', {
                    fontSize: '24px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                });
                buttonText.setOrigin(0.5);
                buttonText.setDepth(1002);
                
                button.on('pointerdown', () => {
                    this.scene.restart();
                });
            }

            createRestartButton() {
                const button = this.add.rectangle(this.gameWidth - 100, 50, 120, 40, 0x3742fa);
                button.setInteractive();
                button.setStrokeStyle(2, 0xffffff);
                
                const buttonText = this.add.text(this.gameWidth - 100, 50, '重新开始', {
                    fontSize: '16px',
                    fill: '#ffffff',
                    fontWeight: 'bold'
                });
                buttonText.setOrigin(0.5);
                
                button.on('pointerdown', () => {
                    this.scene.restart();
                });
                
                button.on('pointerover', () => {
                    button.setFillStyle(0x5352ed);
                });
                
                button.on('pointerout', () => {
                    button.setFillStyle(0x3742fa);
                });
            }

            updateBlockCount() {
                const visibleBlocks = this.blocks.filter(block => block.visible).length;
                const countElement = document.getElementById('blocks-count');
                if (countElement) {
                    countElement.textContent = visibleBlocks;
                }
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 800,
            height: 600,
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: MatchThreeGame,
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 },
                    debug: false
                }
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>