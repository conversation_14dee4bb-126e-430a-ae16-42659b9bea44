<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>Tile Match</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }

        .ui-text {
            font-family: 'Arial', sans-serif;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 78; // 方块尺寸增加到120% (65 * 1.2)
        const SLOT_COUNT = 7; // 底部槽位数量
        // 斗兽棋动物类型，按战斗力从强到弱排序
        const TILE_TYPES = [
            { color: 0xFF6B6B, emoji: '🐘', name: '象', power: 8 },
            { color: 0x4ECDC4, emoji: '🦁', name: '狮', power: 7 },
            { color: 0x45B7D1, emoji: '🐯', name: '虎', power: 6 },
            { color: 0x96CEB4, emoji: '🐆', name: '豹', power: 5 },
            { color: 0xFECA57, emoji: '🐺', name: '狼', power: 4 },
            { color: 0xFF9FF3, emoji: '🐶', name: '狗', power: 3 },
            { color: 0xA8E6CF, emoji: '🐱', name: '猫', power: 2 },
            { color: 0xFD79A8, emoji: '🐭', name: '鼠', power: 1 }
        ];

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.tiles = [];
                this.slots = []; // 暂存区槽位
                this.enemySlots = []; // 敌人区域槽位
                this.score = 0;
                this.level = 1;
                this.isAnimating = false;
                this.gameOver = false;
            }

            preload() {
                // 创建方块纹理
                this.createTileTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x2C3E50);

                // 创建标题
                this.add.text(GAME_WIDTH/2, 40, '斗兽棋', {
                    fontSize: '54px', // 150% (36 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建分数和关卡显示 - 调整位置避免被遮挡
                this.scoreText = this.add.text(40, 100, '分数: 0', {
                    fontSize: '33px', // 150% (22 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    resolution: 2
                });

                this.levelText = this.add.text(GAME_WIDTH - 180, 100, '关卡: 1', {
                    fontSize: '33px', // 150% (22 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    resolution: 2
                });

                // 创建玩法说明区域
                this.createInstructionArea();

                // 创建敌人区域
                this.createEnemyArea();

                // 创建暂存区域
                this.createSlotArea();

                // 方块堆叠区域（在最下方）
                this.stackArea = {
                    x: 40,
                    y: 700,
                    width: GAME_WIDTH - 80,
                    height: GAME_HEIGHT - 740
                };

                // 方块堆叠区域（取消背景框）

                // 初始化游戏
                this.initializeGame();

                // 设置输入事件
                this.input.on('pointerdown', this.onTileClick, this);
            }

            createTileTextures() {
                TILE_TYPES.forEach((tileType, index) => {
                    const graphics = this.add.graphics();

                    // 创建圆角矩形背景
                    graphics.fillStyle(tileType.color);
                    graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);

                    // 添加内阴影效果（去掉白色边框）
                    graphics.lineStyle(2, 0x000000, 0.2);
                    graphics.strokeRoundedRect(2, 2, TILE_SIZE-4, TILE_SIZE-4, 6);

                    graphics.generateTexture(`tile_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }

            createEnemyArea() {
                // 敌人区域（取消背景框和标题）
                const enemyAreaY = 350;

                // 创建4个敌人槽位（增大格子尺寸）
                this.enemySlots = [];
                const enemySlotCount = 4;
                const enemyTileSize = 108; // 120% (90 * 1.2)
                const totalEnemyWidth = enemySlotCount * enemyTileSize + (enemySlotCount - 1) * 25;
                const startEnemyX = (GAME_WIDTH - totalEnemyWidth) / 2 + enemyTileSize / 2;

                for (let i = 0; i < enemySlotCount; i++) {
                    const slotX = startEnemyX + i * (enemyTileSize + 25);
                    const slotY = enemyAreaY;

                    // 敌人槽位（去掉边框背景）
                    this.enemySlots.push({
                        x: slotX,
                        y: slotY,
                        tile: null,
                        background: null, // 不再需要背景
                        size: enemyTileSize
                    });
                }

                // 初始化敌人
                this.spawnEnemies();
            }

            createInstructionArea() {
                // 玩法说明区域（取消背景框）
                const instructionAreaY = 200;

                // 添加玩法说明标题
                this.add.text(GAME_WIDTH/2, instructionAreaY - 45, '玩法说明', {
                    fontSize: '30px', // 150% (20 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#3498DB',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加动物战斗力说明
                const powerText = '动物等级：象 > 狮 > 虎 > 豹 > 狼 > 狗 > 猫 > 鼠';
                this.add.text(GAME_WIDTH/2, instructionAreaY - 15, powerText, {
                    fontSize: '30px', // 150% (20 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ECF0F1',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加游戏规则说明
                const ruleText = '三只相同动物可以攻击敌方动物 | 无敌人时可合成';
                this.add.text(GAME_WIDTH/2, instructionAreaY + 20, ruleText, {
                    fontSize: '27px', // 150% (18 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#BDC3C7',
                    resolution: 2
                }).setOrigin(0.5);

               
            }

            createSlotArea() {
                // 暂存区域（取消背景框）
                const slotAreaY = 550;

                // 添加暂存区标题
                this.add.text(GAME_WIDTH/2, slotAreaY - 60, '暂存区', {
                    fontSize: '33px', // 150% (22 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建暂存区槽位
                this.slots = [];
                const totalSlotWidth = SLOT_COUNT * TILE_SIZE + (SLOT_COUNT - 1) * 5;
                const startX = (GAME_WIDTH - totalSlotWidth) / 2 + TILE_SIZE / 2;

                for (let i = 0; i < SLOT_COUNT; i++) {
                    const slotX = startX + i * (TILE_SIZE + 5);
                    const slotY = slotAreaY;

                    // 槽位背景（圆角矩形，与方块样式一致）
                    const slotBg = this.add.graphics();
                    slotBg.fillStyle(0x2C3E50);
                    slotBg.fillRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);
                    slotBg.lineStyle(2, 0x7F8C8D);
                    slotBg.strokeRoundedRect(slotX - TILE_SIZE/2, slotY - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 8);

                    this.slots.push({
                        x: slotX,
                        y: slotY,
                        tile: null,
                        background: slotBg
                    });
                }
            }

            spawnEnemies() {
                // 根据关卡生成对应难度的敌人
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }

                    // 根据关卡确定敌人的动物种类范围
                    // 与堆叠区域保持一致：第1关3种，第2关4种，以此类推
                    const availableTypeCount = Math.min(3 + this.level - 1, TILE_TYPES.length);
                    const startTypeIndex = TILE_TYPES.length - availableTypeCount;

                    // 敌人稍微比玩家动物强一点，但不会超出当前关卡范围
                    const minEnemyType = Math.max(startTypeIndex, startTypeIndex + Math.floor(availableTypeCount * 0.3));
                    const maxEnemyType = Math.min(startTypeIndex + availableTypeCount - 1, TILE_TYPES.length - 1);

                    const enemyTypeIndex = Phaser.Math.Between(minEnemyType, maxEnemyType);

                    const enemy = this.createEnemyTile(slot.x, slot.y, enemyTypeIndex);
                    slot.tile = enemy;
                });
            }

            createEnemyTile(x, y, typeIndex) {
                const tileType = TILE_TYPES[typeIndex];
                const container = this.add.container(x, y);

                // 创建敌人方块背景（占满敌人格子）
                const tile = this.add.image(0, 0, `tile_${typeIndex}`);
                tile.setScale(1.4); // 占满70px的敌人格子

                // 创建emoji文字（占满敌人格子）
                const emoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: '90px', // 150% (60 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建等级数字（右上角，敌人格子更大所以位置也调整）
                const levelText = this.add.text(38, -38, (8 - typeIndex).toString(), {
                    fontSize: '30px', // 150% (20 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建动物文字容器（只有文字做动画）
                const animalTextContainer = this.add.container(0, 0);
                animalTextContainer.add([emoji]);

                // 创建血条背景（增大尺寸，远离敌人）
                const healthBarBg = this.add.graphics();
                healthBarBg.fillStyle(0x333333);
                healthBarBg.fillRoundedRect(-40, -70, 80, 8, 4); // 更长更粗的血条，距离更远

                // 创建血条前景（增大尺寸，远离敌人）
                const healthBarFg = this.add.graphics();
                healthBarFg.fillStyle(0xFF4444);
                healthBarFg.fillRoundedRect(-40, -70, 80, 8, 4); // 更长更粗的血条，距离更远

                container.add([tile, animalTextContainer, levelText, healthBarBg, healthBarFg]);
                container.setSize(108, 108); // 使用120%的容器尺寸
                container.setData('typeIndex', typeIndex);
                container.setData('isEnemy', true);
                container.setData('health', 3); // 初始生命值
                container.setData('maxHealth', 3); // 最大生命值
                container.setData('healthBarFg', healthBarFg); // 保存血条引用

                // 添加Y轴方向的循环缩放动画（只对动物文字）
                this.tweens.add({
                    targets: animalTextContainer,
                    scaleY: 1.15, // 增大缩放比例
                    duration: 1500, // 稍微快一点
                    ease: 'Sine.easeInOut',
                    yoyo: true,
                    repeat: -1 // 无限循环
                });

                return container;
            }

            initializeGame() {
                // 清空现有方块
                if (this.tileGroup) {
                    this.tileGroup.clear(true, true);
                }

                this.tileGroup = this.add.group();
                this.tileGrid = []; // 存储方块网格

                // 重置暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 重新生成敌人
                this.spawnEnemies();

                // 创建方块网格布局
                this.createStackLayout();
            }

            createStackLayout() {
                // 创建方块网格布局 - 6x6的网格，每个位置一个方块
                const rows = 6;
                const cols = 6;
                const tileSpacing = 102; // 120% (85 * 1.2)

                // 计算居中位置
                const totalWidth = (cols - 1) * tileSpacing;
                const totalHeight = (rows - 1) * tileSpacing;
                const startX = this.stackArea.x + (this.stackArea.width - totalWidth) / 2;
                const startY = this.stackArea.y + (this.stackArea.height - totalHeight) / 2;

                // 初始化网格数组
                this.tileGrid = [];
                for (let row = 0; row < rows; row++) {
                    this.tileGrid[row] = [];
                    for (let col = 0; col < cols; col++) {
                        this.tileGrid[row][col] = null;
                    }
                }

                // 生成方块类型数组，确保每种类型都是3的倍数
                const totalTiles = rows * cols;
                const tileTypes = this.generateTileTypes(totalTiles);
                let tileIndex = 0;

                // 创建方块网格
                for (let row = 0; row < rows; row++) {
                    for (let col = 0; col < cols; col++) {
                        const tileX = startX + col * tileSpacing;
                        const tileY = startY + row * tileSpacing;
                        const tileType = tileTypes[tileIndex++];

                        const tile = this.createGridTile(tileX, tileY, tileType, row, col);
                        this.tileGrid[row][col] = tile;
                    }
                }

                // 更新可点击状态（只有第一行可点击）
                this.updateGridClickableStates();
            }

            createGridTile(x, y, typeIndex, row, col) {
                const tileType = TILE_TYPES[typeIndex];

                // 创建方块容器
                const container = this.add.container(x, y);

                // 创建方块背景（占满格子）
                const tile = this.add.image(0, 0, `tile_${typeIndex}`);
                tile.setScale(1.2); // 增大到占满格子

                // 创建emoji文字（占满格子）
                const emoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: '69px', // 150% (46 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建等级数字（右上角）
                const levelText = this.add.text(28, -28, (8 - typeIndex).toString(), {
                    fontSize: '27px', // 150% (18 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                container.add([tile, emoji, levelText]);
                container.setSize(TILE_SIZE, TILE_SIZE);
                container.setInteractive();

                // 设置数据
                container.setData('typeIndex', typeIndex);
                container.setData('gridRow', row);
                container.setData('gridCol', col);
                container.setData('isClickable', false);

                // 添加悬停效果
                container.on('pointerover', () => {
                    if (container.getData('isClickable') && !this.isAnimating) {
                        container.setScale(1.1);
                    }
                });

                container.on('pointerout', () => {
                    if (!this.isAnimating) {
                        container.setScale(1.0);
                    }
                });

                this.tileGroup.add(container);

                return container;
            }

            updateGridClickableStates() {
                // 只有第一行的方块可以点击
                for (let row = 0; row < this.tileGrid.length; row++) {
                    for (let col = 0; col < this.tileGrid[row].length; col++) {
                        const tile = this.tileGrid[row][col];
                        if (tile) {
                            const isClickable = (row === 0);
                            tile.setData('isClickable', isClickable);
                            tile.setAlpha(isClickable ? 1.0 : 0.8);
                        }
                    }
                }
            }



            generateTileTypes(totalTiles) {
                const types = [];

                // 根据关卡确定可用的动物种类
                // 第1关：3种动物（猫、狗、狼）
                // 第2关：4种动物（豹、猫、狗、狼）
                // 第3关：5种动物（虎、豹、猫、狗、狼）
                // 以此类推，最多8种
                const availableTypeCount = Math.min(3 + this.level - 1, TILE_TYPES.length);
                const startTypeIndex = TILE_TYPES.length - availableTypeCount; // 从较弱的动物开始

                const availableTypes = [];
                for (let i = startTypeIndex; i < TILE_TYPES.length; i++) {
                    availableTypes.push(i);
                }

                const typesNeeded = Math.ceil(totalTiles / 3);

                // 确保每种类型都有3的倍数个
                for (let i = 0; i < typesNeeded; i++) {
                    const typeIndex = availableTypes[i % availableTypes.length];
                    types.push(typeIndex, typeIndex, typeIndex);
                }

                // 打乱数组
                for (let i = types.length - 1; i > 0; i--) {
                    const j = Math.floor(Math.random() * (i + 1));
                    [types[i], types[j]] = [types[j], types[i]];
                }

                return types.slice(0, totalTiles);
            }



            onTileClick(pointer) {
                if (this.isAnimating || this.gameOver) return;

                // 找到被点击的方块
                let clickedTile = null;
                let clickedRow = -1;
                let clickedCol = -1;

                // 遍历网格找到被点击的方块
                for (let row = 0; row < this.tileGrid.length; row++) {
                    for (let col = 0; col < this.tileGrid[row].length; col++) {
                        const tile = this.tileGrid[row][col];
                        if (tile && tile.getBounds().contains(pointer.x, pointer.y) && tile.getData('isClickable')) {
                            clickedTile = tile;
                            clickedRow = row;
                            clickedCol = col;
                            break;
                        }
                    }
                    if (clickedTile) break;
                }

                if (clickedTile) {
                    this.selectGridTile(clickedTile, clickedRow, clickedCol);
                }
            }

            selectGridTile(tile, row, col) {
                this.isAnimating = true;

                // 找到空的槽位
                const emptySlot = this.slots.find(slot => slot.tile === null);

                if (!emptySlot) {
                    // 槽位已满，游戏结束
                    this.gameOver = true;
                    this.showGameOver();
                    this.isAnimating = false;
                    return;
                }

                // 从网格中移除方块
                this.tileGrid[row][col] = null;

                // 移动方块到槽位，同时缩小以匹配暂存区格子大小
                this.tweens.add({
                    targets: tile,
                    x: emptySlot.x,
                    y: emptySlot.y,
                    scaleX: 0.83, // 从1.2缩小到1.0 (1.0/1.2 ≈ 0.83)
                    scaleY: 0.83,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 将方块放入槽位
                        emptySlot.tile = tile;

                        // 检查是否有三个相同的方块
                        this.checkForMatches();

                        // 执行列向上移动和补充新方块
                        this.moveColumnUp(col);

                        // 检查暂存区是否已满（游戏失败条件）
                        this.checkGameOverCondition();

                        this.isAnimating = false;

                        // 检查胜利条件（所有敌人被消灭）
                        if (this.areAllEnemiesDefeated()) {
                            // 胜利时立即清空暂存区
                            this.slots.forEach(slot => {
                                if (slot.tile) {
                                    slot.tile.destroy();
                                    slot.tile = null;
                                }
                            });
                            this.showVictory();
                        }
                    }
                });
            }

            moveColumnUp(col) {
                const rows = this.tileGrid.length;
                const tileSpacing = 102; // 使用与createStackLayout一致的间距
                const startX = this.stackArea.x + (this.stackArea.width - (6 - 1) * tileSpacing) / 2;
                const startY = this.stackArea.y + (this.stackArea.height - (rows - 1) * tileSpacing) / 2;

                // 将该列的所有方块向上移动一格
                for (let row = 0; row < rows - 1; row++) {
                    if (this.tileGrid[row + 1][col]) {
                        const tile = this.tileGrid[row + 1][col];
                        this.tileGrid[row][col] = tile;
                        this.tileGrid[row + 1][col] = null;

                        // 更新方块的网格位置数据
                        tile.setData('gridRow', row);

                        // 动画移动到新位置
                        const newY = startY + row * tileSpacing;
                        this.tweens.add({
                            targets: tile,
                            y: newY,
                            duration: 200,
                            ease: 'Power2'
                        });
                    }
                }

                // 在最底部补充新方块（根据关卡限制动物种类）
                const availableTypeCount = Math.min(3 + this.level - 1, TILE_TYPES.length);
                const startTypeIndex = TILE_TYPES.length - availableTypeCount;
                const newTileType = Phaser.Math.Between(startTypeIndex, TILE_TYPES.length - 1);
                const newX = startX + col * tileSpacing;
                const newY = startY + (rows - 1) * tileSpacing;
                const newTile = this.createGridTile(newX, newY, newTileType, rows - 1, col);
                this.tileGrid[rows - 1][col] = newTile;

                // 新方块从下方滑入的动画
                newTile.y += 100;
                newTile.setAlpha(0);
                this.tweens.add({
                    targets: newTile,
                    y: newY,
                    alpha: 1,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        // 更新可点击状态
                        this.updateGridClickableStates();
                    }
                });
            }



            checkForMatches() {
                // 统计暂存区每种类型的方块数量
                const typeCounts = {};

                this.slots.forEach(slot => {
                    if (slot.tile) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        typeCounts[typeIndex] = (typeCounts[typeIndex] || 0) + 1;
                    }
                });

                // 找到有3个的类型，可以进攻敌人
                const attackTypes = [];
                Object.keys(typeCounts).forEach(typeIndex => {
                    if (typeCounts[typeIndex] >= 3) {
                        attackTypes.push(parseInt(typeIndex));
                    }
                });

                // 执行进攻
                if (attackTypes.length > 0) {
                    this.attackEnemies(attackTypes);
                }
            }

            attackEnemies(attackTypes) {
                attackTypes.forEach(attackerType => {
                    // 找到可以被这种动物消灭的敌人
                    const targetEnemy = this.findTargetEnemy(attackerType);

                    if (targetEnemy) {
                        // 执行进攻
                        this.executeAttack(attackerType, targetEnemy);
                    } else {
                        // 没有可攻击的敌人，尝试合成高一级动物
                        this.synthesizeAnimal(attackerType);
                    }
                });
            }

            findTargetEnemy(attackerType) {
                // 3个动物消除1个同等级的敌人
                // 象(0) > 狮(1) > 虎(2) > 豹(3) > 狼(4) > 狗(5) > 猫(6) > 鼠(7)
                for (let slot of this.enemySlots) {
                    if (slot.tile) {
                        const enemyType = slot.tile.getData('typeIndex');

                        // 只能消除同等级的敌人
                        if (attackerType === enemyType) {
                            return slot;
                        }
                    }
                }
                return null;
            }

            executeAttack(attackerType, targetSlot) {
                // 找到暂存区的3个攻击者
                const attackerTiles = [];
                let foundCount = 0;

                this.slots.forEach(slot => {
                    if (slot.tile && foundCount < 3) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        if (typeIndex === attackerType) {
                            attackerTiles.push({
                                tile: slot.tile,
                                slot: slot
                            });
                            foundCount++;
                        }
                    }
                });

                // 让3个动物依次飞向敌人并攻击
                this.flyAndAttackSequence(attackerTiles, targetSlot, attackerType);
            }

            flyAndAttackSequence(attackerTiles, targetSlot, attackerType) {
                let attackCount = 0;
                const enemy = targetSlot.tile;

                attackerTiles.forEach((attacker, index) => {
                    // 每个攻击者延迟不同时间发起攻击
                    // 第一个立即开始，第二个在150ms后开始，第三个在300ms后开始
                    this.time.delayedCall(index * 150, () => {
                        // 清空槽位
                        attacker.slot.tile = null;

                        // 动物飞向敌人
                        this.tweens.add({
                            targets: attacker.tile,
                            x: targetSlot.x,
                            y: targetSlot.y,
                            scaleX: 0.5,
                            scaleY: 0.5,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                // 攻击命中
                                attackCount++;

                                // 显示攻击特效
                                this.showAttackHitAnimation(targetSlot.x, targetSlot.y);

                                // 敌人受击动画：抖动效果
                                this.showEnemyShakeAnimation(enemy);

                                // 减少敌人生命值并更新血条
                                const currentHealth = enemy.getData('health') - 1;
                                enemy.setData('health', currentHealth);
                                this.updateHealthBar(enemy);

                                // 攻击者消失
                                attacker.tile.destroy();

                                // 检查敌人是否被击败
                                if (currentHealth <= 0) {
                                    // 敌人被击败
                                    this.time.delayedCall(200, () => {
                                        targetSlot.tile = null;

                                        // 敌人消失动画
                                        this.tweens.add({
                                            targets: enemy,
                                            scaleX: 0,
                                            scaleY: 0,
                                            alpha: 0,
                                            duration: 400,
                                            ease: 'Power2',
                                            onComplete: () => {
                                                enemy.destroy();
                                            }
                                        });

                                        // 计算分数
                                        const points = (8 - attackerType) * 20;
                                        this.score += points;
                                        this.scoreText.setText(`分数: ${this.score}`);
                                        this.showScoreAnimation(points);

                                        // 重新排列剩余敌人
                                        this.time.delayedCall(300, () => {
                                            this.reorganizeEnemies();
                                        });

                                        // 检查胜利条件
                                        this.time.delayedCall(500, () => {
                                            if (this.areAllEnemiesDefeated()) {
                                                // 胜利时立即清空暂存区
                                                this.slots.forEach(slot => {
                                                    if (slot.tile) {
                                                        slot.tile.destroy();
                                                        slot.tile = null;
                                                    }
                                                });
                                                this.showVictory();
                                            }
                                        });
                                    });
                                }

                                // 最后一个攻击者完成后整理槽位
                                if (attackCount === 3) {
                                    this.time.delayedCall(300, () => {
                                        this.reorganizeSlots();
                                    });
                                }
                            }
                        });
                    });
                });
            }

            updateHealthBar(enemy) {
                const currentHealth = enemy.getData('health');
                const maxHealth = enemy.getData('maxHealth');
                const healthBarFg = enemy.getData('healthBarFg');

                if (healthBarFg) {
                    // 计算血条宽度
                    const healthPercentage = currentHealth / maxHealth;
                    const barWidth = 50 * healthPercentage;

                    // 清除旧的血条
                    healthBarFg.clear();

                    // 根据生命值改变颜色
                    let barColor = 0xFF4444; // 红色
                    if (healthPercentage > 0.6) {
                        barColor = 0x44FF44; // 绿色
                    } else if (healthPercentage > 0.3) {
                        barColor = 0xFFAA44; // 橙色
                    }

                    // 绘制新的血条（更大更远的位置）
                    healthBarFg.fillStyle(barColor);
                    healthBarFg.fillRoundedRect(-40, -70, barWidth, 8, 4);

                    // 血条减少动画
                    healthBarFg.setScale(1.2, 1);
                    this.tweens.add({
                        targets: healthBarFg,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 200,
                        ease: 'Power2'
                    });
                }
            }

            showAttackHitAnimation(x, y) {
                // 显示命中特效文字
                const hitText = this.add.text(x, y - 30, 'HIT!', {
                    fontSize: '16px',
                    fontFamily: 'Arial',
                    color: '#FF6666',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                hitText.setDepth(500);

                this.tweens.add({
                    targets: hitText,
                    y: hitText.y - 20,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        hitText.destroy();
                    }
                });

                // 添加冲击波效果
                const shockwave = this.add.graphics();
                shockwave.lineStyle(2, 0xFF6666, 0.8);
                shockwave.strokeCircle(x, y, 15);
                shockwave.setDepth(499);

                this.tweens.add({
                    targets: shockwave,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 300,
                    ease: 'Power2',
                    onComplete: () => {
                        shockwave.destroy();
                    }
                });

                // 添加火花效果
                for (let i = 0; i < 6; i++) {
                    const angle = (i / 6) * Math.PI * 2;
                    const spark = this.add.graphics();
                    spark.fillStyle(0xFFAA00);
                    spark.fillCircle(0, 0, 2);
                    spark.x = x;
                    spark.y = y;
                    spark.setDepth(501);

                    this.tweens.add({
                        targets: spark,
                        x: x + Math.cos(angle) * 25,
                        y: y + Math.sin(angle) * 25,
                        alpha: 0,
                        duration: 400,
                        ease: 'Power2',
                        onComplete: () => {
                            spark.destroy();
                        }
                    });
                }
            }

            showEnemyShakeAnimation(enemy) {
                // 记录敌人的原始位置
                const originalX = enemy.x;
                const originalY = enemy.y;

                // 创建抖动序列
                const shakeIntensity = 8; // 抖动强度
                const shakeDuration = 50; // 每次抖动的持续时间
                const shakeCount = 4; // 抖动次数

                let currentShake = 0;

                const performShake = () => {
                    if (currentShake >= shakeCount) {
                        // 抖动结束，回到原位
                        this.tweens.add({
                            targets: enemy,
                            x: originalX,
                            y: originalY,
                            duration: shakeDuration,
                            ease: 'Power2'
                        });
                        return;
                    }

                    // 随机抖动方向
                    const shakeX = originalX + (Math.random() - 0.5) * shakeIntensity;
                    const shakeY = originalY + (Math.random() - 0.5) * shakeIntensity;

                    this.tweens.add({
                        targets: enemy,
                        x: shakeX,
                        y: shakeY,
                        duration: shakeDuration,
                        ease: 'Power2',
                        onComplete: () => {
                            currentShake++;
                            performShake();
                        }
                    });
                };

                // 开始抖动
                performShake();
            }

            removeAttackerTiles(attackerType) {
                const tilesToRemove = [];
                let removedCount = 0;

                // 找到要移除的3个攻击者方块
                this.slots.forEach(slot => {
                    if (slot.tile && removedCount < 3) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        if (typeIndex === attackerType) {
                            tilesToRemove.push(slot.tile);
                            slot.tile = null;
                            removedCount++;
                        }
                    }
                });

                // 移除方块动画
                tilesToRemove.forEach(tile => {
                    this.tweens.add({
                        targets: tile,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 200,
                        ease: 'Power2',
                        onComplete: () => {
                            tile.destroy();
                        }
                    });
                });

                // 整理暂存区槽位
                this.time.delayedCall(250, () => {
                    this.reorganizeSlots();
                });
            }

            synthesizeAnimal(attackerType) {
                // 合成高一级动物的逻辑
                // 象(0) > 狮(1) > 虎(2) > 豹(3) > 狼(4) > 狗(5) > 猫(6) > 鼠(7)

                // 象(0)是最高级，不需要合成，直接移除
                if (attackerType === 0) {
                    this.removeAttackerTiles(attackerType);
                    return;
                }

                // 鼠(7)可以合成猫(6)
                if (attackerType === 7) {
                    // 鼠合成猫
                    const synthesizedType = 6; // 猫的索引

                    // 移除3个鼠
                    const tilesToRemove = [];
                    let removedCount = 0;

                    this.slots.forEach(slot => {
                        if (slot.tile && removedCount < 3) {
                            const typeIndex = slot.tile.getData('typeIndex');
                            if (typeIndex === attackerType) {
                                tilesToRemove.push(slot.tile);
                                slot.tile = null;
                                removedCount++;
                            }
                        }
                    });

                    // 移除动画
                    tilesToRemove.forEach(tile => {
                        this.tweens.add({
                            targets: tile,
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 200,
                            ease: 'Power2',
                            onComplete: () => {
                                tile.destroy();
                            }
                        });
                    });

                    // 延迟后创建合成的猫
                    this.time.delayedCall(300, () => {
                        const emptySlot = this.slots.find(slot => slot.tile === null);
                        if (emptySlot) {
                            const synthesizedTile = this.createSynthesizedTile(emptySlot.x, emptySlot.y, synthesizedType);
                            emptySlot.tile = synthesizedTile;
                            this.showSynthesisAnimation(emptySlot.x, emptySlot.y);

                            // 合成完成后检查是否可以消除
                            this.time.delayedCall(500, () => {
                                this.checkForMatches();
                            });
                        }

                        this.time.delayedCall(100, () => {
                            this.reorganizeSlots();
                        });
                    });
                    return;
                }

                // 其他动物可以合成高一级
                const synthesizedType = attackerType - 1; // 索引越小等级越高

                // 移除3个低级动物
                const tilesToRemove = [];
                let removedCount = 0;

                this.slots.forEach(slot => {
                    if (slot.tile && removedCount < 3) {
                        const typeIndex = slot.tile.getData('typeIndex');
                        if (typeIndex === attackerType) {
                            tilesToRemove.push(slot.tile);
                            slot.tile = null;
                            removedCount++;
                        }
                    }
                });

                // 移除动画
                tilesToRemove.forEach(tile => {
                    this.tweens.add({
                        targets: tile,
                        scaleX: 0,
                        scaleY: 0,
                        alpha: 0,
                        duration: 200,
                        ease: 'Power2',
                        onComplete: () => {
                            tile.destroy();
                        }
                    });
                });

                // 延迟后创建合成的高级动物
                this.time.delayedCall(300, () => {
                    // 找到第一个空槽位放置合成的动物
                    const emptySlot = this.slots.find(slot => slot.tile === null);
                    if (emptySlot) {
                        // 创建合成的高级动物
                        const synthesizedTile = this.createSynthesizedTile(emptySlot.x, emptySlot.y, synthesizedType);
                        emptySlot.tile = synthesizedTile;

                        // 显示合成效果
                        this.showSynthesisAnimation(emptySlot.x, emptySlot.y);

                        // 合成完成后检查是否可以消除
                        this.time.delayedCall(500, () => {
                            this.checkForMatches();
                        });
                    }

                    // 整理槽位
                    this.time.delayedCall(100, () => {
                        this.reorganizeSlots();
                    });
                });
            }

            createSynthesizedTile(x, y, typeIndex) {
                const tileType = TILE_TYPES[typeIndex];
                const container = this.add.container(x, y);

                // 创建方块背景（暂存区大小）
                const tile = this.add.image(0, 0, `tile_${typeIndex}`);
                tile.setScale(1.0); // 暂存区的标准大小

                // 创建emoji文字（暂存区大小）
                const emoji = this.add.text(0, 0, tileType.emoji, {
                    fontSize: '57px', // 150% (38 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建等级数字（暂存区大小）
                const levelText = this.add.text(23, -23, (8 - typeIndex).toString(), {
                    fontSize: '24px', // 150% (16 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                container.add([tile, emoji, levelText]);
                container.setSize(TILE_SIZE, TILE_SIZE);
                container.setData('typeIndex', typeIndex);

                // 合成动画效果
                container.setScale(0);
                this.tweens.add({
                    targets: container,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 400,
                    ease: 'Back.easeOut'
                });

                return container;
            }

            showSynthesisAnimation(x, y) {
                // 显示合成特效文字
                const synthesisText = this.add.text(x, y - 30, '合成!', {
                    fontSize: '20px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                synthesisText.setDepth(500);

                this.tweens.add({
                    targets: synthesisText,
                    y: synthesisText.y - 30,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        synthesisText.destroy();
                    }
                });

                // 添加光环特效
                const halo = this.add.graphics();
                halo.lineStyle(4, 0xFFD700, 0.8);
                halo.strokeCircle(x, y, 30);
                halo.setDepth(499);

                this.tweens.add({
                    targets: halo,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 600,
                    ease: 'Power2',
                    onComplete: () => {
                        halo.destroy();
                    }
                });

                // 添加粒子效果
                for (let i = 0; i < 8; i++) {
                    const angle = (i / 8) * Math.PI * 2;
                    const particle = this.add.graphics();
                    particle.fillStyle(0xFFD700);
                    particle.fillCircle(0, 0, 3);
                    particle.x = x;
                    particle.y = y;
                    particle.setDepth(501);

                    this.tweens.add({
                        targets: particle,
                        x: x + Math.cos(angle) * 40,
                        y: y + Math.sin(angle) * 40,
                        alpha: 0,
                        duration: 800,
                        ease: 'Power2',
                        onComplete: () => {
                            particle.destroy();
                        }
                    });
                }
            }

            areAllEnemiesDefeated() {
                return this.enemySlots.every(slot => slot.tile === null);
            }

            calculateStars() {
                // 根据分数计算星星数量
                const baseScore = this.level * 100; // 基础分数
                const scoreRatio = this.score / baseScore;

                if (scoreRatio >= 2.0) {
                    return 3; // 3星：分数达到基础分数的200%
                } else if (scoreRatio >= 1.5) {
                    return 2; // 2星：分数达到基础分数的150%
                } else {
                    return 1; // 1星：完成关卡即可
                }
            }

            reorganizeEnemies() {
                // 收集所有剩余的敌人
                const remainingEnemies = [];
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        remainingEnemies.push(slot.tile);
                        slot.tile = null;
                    }
                });

                if (remainingEnemies.length === 0) return;

                // 计算居中位置（使用正确的敌人尺寸和位置）
                const enemySlotCount = 4;
                const enemyTileSize = 108; // 120%的敌人方块尺寸
                const enemySpacing = 25; // 更新的敌人间距
                const totalEnemyWidth = remainingEnemies.length * enemyTileSize + (remainingEnemies.length - 1) * enemySpacing;
                const startEnemyX = (GAME_WIDTH - totalEnemyWidth) / 2 + enemyTileSize / 2;
                const enemyAreaY = 350; // 更新的敌人区域Y坐标

                // 重新分配敌人到居中位置
                remainingEnemies.forEach((enemy, index) => {
                    const newX = startEnemyX + index * (enemyTileSize + enemySpacing);
                    const targetSlot = this.enemySlots[Math.floor((enemySlotCount - remainingEnemies.length) / 2) + index];

                    // 将敌人分配到新槽位
                    targetSlot.tile = enemy;

                    // 动画移动到新位置
                    this.tweens.add({
                        targets: enemy,
                        x: newX,
                        y: enemyAreaY,
                        duration: 400,
                        ease: 'Power2'
                    });
                });
            }

            checkGameOverCondition() {
                // 检查暂存区是否已满
                const isSlotsFull = this.slots.every(slot => slot.tile !== null);

                if (isSlotsFull) {
                    // 检查是否还有可以消除的组合
                    const typeCounts = {};
                    this.slots.forEach(slot => {
                        if (slot.tile) {
                            const typeIndex = slot.tile.getData('typeIndex');
                            typeCounts[typeIndex] = (typeCounts[typeIndex] || 0) + 1;
                        }
                    });

                    // 检查是否有3个相同的动物可以攻击敌人
                    let canAttack = false;
                    Object.keys(typeCounts).forEach(typeIndex => {
                        if (typeCounts[typeIndex] >= 3) {
                            const attackerType = parseInt(typeIndex);
                            const targetEnemy = this.findTargetEnemy(attackerType);
                            if (targetEnemy) {
                                canAttack = true;
                            }
                        }
                    });

                    // 如果暂存区满了且没有可攻击的组合，游戏结束
                    if (!canAttack) {
                        this.gameOver = true;
                        this.time.delayedCall(500, () => {
                            this.showGameOver();
                        });
                    }
                }
            }

            reorganizeSlots() {
                // 收集所有非空的方块
                const remainingTiles = [];
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        remainingTiles.push(slot.tile);
                        slot.tile = null;
                    }
                });

                // 重新排列到左侧
                remainingTiles.forEach((tile, index) => {
                    const slot = this.slots[index];
                    slot.tile = tile;

                    this.tweens.add({
                        targets: tile,
                        x: slot.x,
                        y: slot.y,
                        duration: 200,
                        ease: 'Power2'
                    });
                });
            }

            showScoreAnimation(points) {
                const scorePopup = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 100, `+${points}`, {
                    fontSize: '24px',
                    fontFamily: 'Arial',
                    color: '#FFD700',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                scorePopup.setDepth(500); // 设置较高深度确保分数动画可见

                this.tweens.add({
                    targets: scorePopup,
                    y: scorePopup.y - 50,
                    alpha: 0,
                    duration: 1000,
                    ease: 'Power2',
                    onComplete: () => {
                        scorePopup.destroy();
                    }
                });
            }

            showVictory() {
                // 立即清空暂存区，防止显示残留动物
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 计算关卡评星
                const stars = this.calculateStars();

                // 显示胜利信息
                const victoryBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                victoryBg.setDepth(1000); // 设置最高深度确保在最上层

                const victoryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 180, '恭喜过关！', {
                    fontSize: '90px', // 150% (48 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                victoryText.setDepth(1001); // 设置最高深度确保在最上层

                // 显示星星评级
                this.showStarRating(stars);

                const nextButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, 240, 72, 0x27AE60); // 120%按钮
                nextButton.setStrokeStyle(3, 0x2ECC71);
                nextButton.setInteractive();
                nextButton.setDepth(1001); // 设置最高深度确保在最上层

                const nextText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 120, '下一关', {
                    fontSize: '39px', // 150% (26 * 1.5)
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);
                nextText.setDepth(1002); // 设置最高深度确保在最上层

                // 在UI界面上添加撒花庆祝特效
                this.showConfettiCelebration();

                nextButton.on('pointerdown', () => {
                    victoryBg.destroy();
                    victoryText.destroy();
                    nextButton.destroy();
                    nextText.destroy();
                    this.nextLevel();
                });
            }

            showConfettiCelebration() {
                // 创建撒花庆祝特效，2次爆炸效果，共2秒
                const colors = [0xFF6B6B, 0x4ECDC4, 0x45B7D1, 0x96CEB4, 0xFECA57, 0xFF9FF3, 0xA8E6CF, 0xFD79A8];
                const centerX = GAME_WIDTH / 2;
                const centerY = GAME_HEIGHT / 2 - 80; // "恭喜过关"字上方位置
                const explosionCount = 2; // 2次爆炸
                const explosionInterval = 1000; // 1秒间隔（0秒和1秒时爆炸）

                for (let explosion = 0; explosion < explosionCount; explosion++) {
                    this.time.delayedCall(explosion * explosionInterval, () => {
                        // 每次爆炸创建多个花瓣
                        const confettiPerExplosion = 25; // 增加每次爆炸的花瓣数量
                        for (let i = 0; i < confettiPerExplosion; i++) {
                            this.createConfettiExplosion(centerX, centerY, colors);
                        }
                    });
                }
            }

            createConfettiExplosion(centerX, centerY, colors) {
                // 随机选择颜色
                const color = colors[Math.floor(Math.random() * colors.length)];

                // 创建花瓣形状（更大的尺寸）
                const confetti = this.add.graphics();
                confetti.fillStyle(color);

                // 随机选择花瓣形状
                const shapeType = Math.floor(Math.random() * 3);
                if (shapeType === 0) {
                    // 圆形花瓣
                    confetti.fillCircle(0, 0, Math.random() * 8 + 6);
                } else if (shapeType === 1) {
                    // 方形花瓣
                    const size = Math.random() * 12 + 8;
                    confetti.fillRect(-size/2, -size/2, size, size);
                } else {
                    // 三角形花瓣
                    const size = Math.random() * 10 + 8;
                    confetti.fillTriangle(0, -size, -size/2, size/2, size/2, size/2);
                }

                // 从中心点开始
                confetti.x = centerX;
                confetti.y = centerY;
                confetti.setDepth(1003); // 确保在UI界面之上

                // 随机的爆炸方向和距离
                const angle = Math.random() * Math.PI * 2; // 随机角度
                const explosionRadius = Math.random() * 150 + 100; // 爆炸半径
                const explosionX = centerX + Math.cos(angle) * explosionRadius;
                const explosionY = centerY + Math.sin(angle) * explosionRadius;

                // 第一阶段：爆炸扩散
                this.tweens.add({
                    targets: confetti,
                    x: explosionX,
                    y: explosionY,
                    rotation: (Math.random() - 0.5) * 360 * Math.PI / 180,
                    duration: 200,
                    ease: 'Power2',
                    onComplete: () => {
                        // 第二阶段：重力下落
                        const fallDistance = GAME_HEIGHT - explosionY + 50;
                        const horizontalDrift = (Math.random() - 0.5) * 100;

                        this.tweens.add({
                            targets: confetti,
                            x: explosionX + horizontalDrift,
                            y: explosionY + fallDistance,
                            rotation: confetti.rotation + (Math.random() - 0.5) * 720 * Math.PI / 180,
                            alpha: 0,
                            duration: 1300, // 调整为1300ms，总时长1.5秒
                            ease: 'Power1',
                            onComplete: () => {
                                confetti.destroy();
                            }
                        });
                    }
                });

                // 添加轻微的摆动效果
                this.tweens.add({
                    targets: confetti,
                    scaleX: 0.8,
                    scaleY: 1.2,
                    duration: 300 + Math.random() * 100,
                    yoyo: true,
                    repeat: 2, // 重复2次，总共约1.2秒
                    ease: 'Sine.easeInOut'
                });
            }

            showStarRating(stars) {
                // 显示星星评级
                const starY = GAME_HEIGHT/2 - 80;
                const starSize = 60;
                const starSpacing = 80;
                const totalWidth = 3 * starSize + 2 * starSpacing;
                const startX = (GAME_WIDTH - totalWidth) / 2 + starSize / 2;

                // 创建3个星星位置
                for (let i = 0; i < 3; i++) {
                    const starX = startX + i * (starSize + starSpacing);
                    const isLit = i < stars; // 是否点亮这颗星星

                    // 延迟显示每颗星星
                    this.time.delayedCall(500 + i * 300, () => {
                        this.createAnimatedStar(starX, starY, starSize, isLit);
                    });
                }

                // 显示评级文字
                this.time.delayedCall(1400, () => {
                    let ratingText = '';
                    let ratingColor = '';

                    switch(stars) {
                        case 3:
                            ratingText = '完美通关！';
                            ratingColor = '#FFD700';
                            break;
                        case 2:
                            ratingText = '表现优秀！';
                            ratingColor = '#C0C0C0';
                            break;
                        case 1:
                            ratingText = '成功通关！';
                            ratingColor = '#CD7F32';
                            break;
                    }

                    const rating = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 10, ratingText, {
                        fontSize: '36px',
                        fontFamily: 'Arial, sans-serif',
                        color: ratingColor,
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);
                    rating.setDepth(1003);

                    // 评级文字出现动画
                    rating.setScale(0);
                    this.tweens.add({
                        targets: rating,
                        scaleX: 1,
                        scaleY: 1,
                        duration: 400,
                        ease: 'Back.easeOut'
                    });
                });
            }

            createAnimatedStar(x, y, size, isLit) {
                // 创建星星图形
                const star = this.add.graphics();
                star.setDepth(1003);

                // 设置星星颜色
                const fillColor = isLit ? 0xFFD700 : 0x666666; // 金色或灰色
                const strokeColor = isLit ? 0xFFA500 : 0x444444; // 橙色或深灰色

                star.fillStyle(fillColor);
                star.lineStyle(3, strokeColor);

                // 绘制五角星
                const points = [];
                const outerRadius = size / 2;
                const innerRadius = outerRadius * 0.4;

                for (let i = 0; i < 10; i++) {
                    const angle = (i * Math.PI) / 5;
                    const radius = i % 2 === 0 ? outerRadius : innerRadius;
                    const px = x + Math.cos(angle - Math.PI / 2) * radius;
                    const py = y + Math.sin(angle - Math.PI / 2) * radius;
                    points.push(px, py);
                }

                star.fillPoints(points);
                star.strokePoints(points);

                // 星星出现动画
                star.setScale(0);
                star.setRotation(0);

                this.tweens.add({
                    targets: star,
                    scaleX: 1,
                    scaleY: 1,
                    rotation: Math.PI * 2,
                    duration: 600,
                    ease: 'Back.easeOut'
                });

                // 如果是点亮的星星，添加闪烁效果
                if (isLit) {
                    this.time.delayedCall(600, () => {
                        this.tweens.add({
                            targets: star,
                            scaleX: 1.2,
                            scaleY: 1.2,
                            duration: 200,
                            yoyo: true,
                            ease: 'Power2'
                        });

                        // 添加光芒效果
                        const glow = this.add.graphics();
                        glow.setDepth(1002);
                        glow.fillStyle(0xFFD700, 0.3);
                        glow.fillCircle(x, y, size);
                        glow.setScale(0);

                        this.tweens.add({
                            targets: glow,
                            scaleX: 1.5,
                            scaleY: 1.5,
                            alpha: 0,
                            duration: 400,
                            ease: 'Power2',
                            onComplete: () => {
                                glow.destroy();
                            }
                        });
                    });
                }
            }

            showGameOver() {
                // 显示游戏结束信息
                const gameOverBg = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x000000, 0.7);
                gameOverBg.setDepth(1000); // 设置最高深度确保在最上层

                const gameOverText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 - 50, '游戏结束', {
                    fontSize: '32px',
                    fontFamily: 'Arial',
                    color: '#E74C3C',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                gameOverText.setDepth(1001); // 设置最高深度确保在最上层

                const retryButton = this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2 + 20, 150, 40, 0xE74C3C);
                retryButton.setStrokeStyle(2, 0xC0392B);
                retryButton.setInteractive();
                retryButton.setDepth(1001); // 设置最高深度确保在最上层

                const retryText = this.add.text(GAME_WIDTH/2, GAME_HEIGHT/2 + 20, '重新开始', {
                    fontSize: '18px',
                    fontFamily: 'Arial',
                    color: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                retryText.setDepth(1002); // 设置最高深度确保在最上层

                retryButton.on('pointerdown', () => {
                    gameOverBg.destroy();
                    gameOverText.destroy();
                    retryButton.destroy();
                    retryText.destroy();
                    this.restartGame();
                });
            }

            nextLevel() {
                this.level++;
                this.levelText.setText(`关卡: ${this.level}`);
                this.gameOver = false;
                this.isAnimating = false; // 重置动画状态

                // 强制清空暂存区（确保清空）
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }



            restartGame() {
                this.score = 0;
                this.level = 1;
                this.scoreText.setText('分数: 0');
                this.levelText.setText('关卡: 1');
                this.gameOver = false;
                this.isAnimating = false;

                // 清空暂存区槽位
                this.slots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                // 清空敌人区域
                this.enemySlots.forEach(slot => {
                    if (slot.tile) {
                        slot.tile.destroy();
                        slot.tile = null;
                    }
                });

                this.initializeGame();
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#2C3E50',
            scene: GameScene,
            render: {
                antialias: true,
                pixelArt: false,
                roundPixels: false,
                transparent: false,
                clearBeforeRender: true,
                preserveDrawingBuffer: false,
                failIfMajorPerformanceCaveat: false,
                powerPreference: "high-performance"
            },
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>

