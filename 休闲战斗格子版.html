<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let grid = [];
    let gameState = 'playing';
    let battleTimer = 0;
    let waveTimer = 0;
    let monstersKilled = 0;
    let totalMonstersInWave = 3;

    // 3选1系统变量
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 2000,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };
    let choiceUI = null;
    let choiceOptions = [];
    let isChoosing = false;
    let choiceCounter = 0; // 用于追踪选择次数

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }

        this.add.graphics()
            .fillStyle(0x95a5a6)
            .fillRect(0, 0, 100, 100)
            .generateTexture('gridCell', 100, 100);
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

      

        // 创建主角 - 左边位置，向下移动
        player = this.add.image(150, 400, 'player');
        player.setScale(0.6); // 缩小到0.5
        player.setOrigin(0.5, 1); // 设置旋转中心在底部

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建6x6格子
        createGrid.call(this);
    }

    // 创建波次怪物
    function createWave() {
        // 清除现有怪物
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 创建新怪物 - 全部为近战怪物，初始位置在右边
        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = 600; // 初始位置在右边
            const yPos = player.y + i * 20;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(
                xPos,
                yPos,
                `monster_${monsterImageIndex}`
            );
            monster.setScale(0.25); // 缩小怪物尺寸
            monster.setOrigin(0.5, 1); // 设置旋转中心在底部

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isRanged = false; // 全部为近战
            monster.isMoving = false; // 移动状态
            monster.originalX = xPos; // 记录原始位置
            monster.originalY = yPos;
            monster.jumpTween = null; // 跳跃动画引用
            monsters.push(monster);
        }

        monstersKilled = 0;
    }

    // 创建UI界面
    function createUI() {
        // 玩家血条背景 - 移到顶部
        this.add.rectangle(150, 80, 200, 20, 0x333333);
        // 玩家血条 - 修复缩放中心
        this.playerHealthBar = this.add.rectangle(150, 80, 200, 20, 0x27ae60);
        this.playerHealthBar.setOrigin(0.5, 0.5); // 设置缩放中心为中心点

        // UI文本 - 移到顶部
        this.levelText = this.add.text(50, 110, `关卡: ${currentLevel}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });
        this.waveText = this.add.text(200, 110, `波次: ${currentWave}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });
        this.healthText = this.add.text(350, 110, `生命: ${playerHealth}/${maxPlayerHealth}`, {
            fontSize: '20px',
            fill: '#ffffff'
        });
        this.shieldText = this.add.text(550, 110, `护盾: 0`, {
            fontSize: '20px',
            fill: '#3498db'
        });

        // 怪物血条容器
        this.monsterHealthBars = [];
    }

    // 创建6x6格子
    function createGrid() {
        const cellSize = 100;
        const gridWidth = 6 * cellSize;
        const startX = (750 - gridWidth) / 2 + cellSize / 2; // 居中计算
        const startY = 650;

        for (let row = 0; row < 6; row++) {
            grid[row] = [];
            for (let col = 0; col < 6; col++) {
                // 创建圆角矩形
                const graphics = this.add.graphics();
                graphics.fillStyle(0xcfcccf); // 白色填充
                graphics.lineStyle(2, 0xe0e0e0); // 浅灰色边框
                graphics.fillRoundedRect(-45, -45, 90, 90, 15); // 圆角半径15
                graphics.strokeRoundedRect(-45, -45, 90, 90, 15);

                // 设置位置
                graphics.x = startX + col * cellSize;
                graphics.y = startY + row * cellSize;

                // 添加属性
                graphics.row = row;
                graphics.col = col;

                grid[row][col] = graphics;
            }
        }

        // 添加格子区域标题
        this.add.text(375, 600, '格子区域', {
            fontSize: '24px',
            fill: '#ffffff',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 多重射击：攻击多个目标
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i];

                // 创建攻击特效
                const projectile = this.add.circle(player.x + 30, player.y - 20, 5, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y,
                    duration: 500,
                    delay: i * 100, // 多重射击时添加延迟
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 狂战士效果：生命越低攻击越高
                        if (playerStats.berserker) {
                            const healthPercent = playerHealth / maxPlayerHealth;
                            const berserkerBonus = (1 - healthPercent) * 2; // 最多200%加成
                            damage *= (1 + berserkerBonus);
                        }

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 冰冻效果
                        if (playerStats.freeze && Math.random() < playerStats.freeze) {
                            target.frozen = true;
                            target.frozenTime = 2000; // 冰冻2秒
                            target.setTint(0x87ceeb); // 浅蓝色

                            const freezeText = this.add.text(target.x, target.y - 100, 'FROZEN!', {
                                fontSize: '16px',
                                fill: '#87ceeb',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: freezeText,
                                alpha: 0,
                                y: freezeText.y - 20,
                                duration: 1000,
                                onComplete: () => freezeText.destroy()
                            });
                        }

                        // 毒性效果
                        if (playerStats.poison) {
                            if (!target.poisoned) {
                                target.poisoned = true;
                                target.poisonDamage = Math.floor(damage * 0.3);
                                target.poisonDuration = 5000; // 持续5秒
                                target.setTint(0x9acd32); // 绿色
                            }
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 爆炸攻击效果
                        if (playerStats.explosive) {
                            const explosionRadius = 80;
                            const explosionDamage = Math.floor(damage * 0.5);

                            // 爆炸特效
                            const explosion = this.add.circle(target.x, target.y, explosionRadius, 0xff6b35, 0.3);
                            this.tweens.add({
                                targets: explosion,
                                scaleX: 1.5,
                                scaleY: 1.5,
                                alpha: 0,
                                duration: 300,
                                onComplete: () => explosion.destroy()
                            });

                            // 对范围内的其他怪物造成伤害
                            monsters.forEach(otherMonster => {
                                if (otherMonster !== target) {
                                    const distance = Phaser.Math.Distance.Between(
                                        target.x, target.y, otherMonster.x, otherMonster.y
                                    );
                                    if (distance <= explosionRadius) {
                                        otherMonster.health -= explosionDamage;

                                        // 爆炸伤害特效
                                        this.tweens.add({
                                            targets: otherMonster,
                                            scaleX: 0.3,
                                            scaleY: 0.3,
                                            duration: 100,
                                            yoyo: true
                                        });
                                    }
                                }
                            });
                        }

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 向前倾斜
            this.tweens.add({
                targets: player,
                rotation: 0.3, // 向前倾斜约17度
                duration: 150,
                yoyo: true,
                ease: 'Power2'
            });

            // 同时添加轻微的缩放效果
            this.tweens.add({
                targets: player,
                scaleX: 0.55,
                scaleY: 0.55,
                duration: 150,
                yoyo: true
            });
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        if (gameState === 'playing') {
            // 更新战斗
            updateBattle.call(this, time);

            // 检查波次完成
            checkWaveComplete.call(this);

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }
    }

    // 更新战斗逻辑
    function updateBattle(time) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {
            // 处理冰冻状态
            if (monster.frozen) {
                monster.frozenTime -= delta;
                if (monster.frozenTime <= 0) {
                    monster.frozen = false;
                    monster.clearTint();
                }
                return; // 冰冻时不能行动
            }

            // 处理毒性伤害
            if (monster.poisoned) {
                monster.poisonDuration -= delta;
                if (monster.poisonDuration <= 0) {
                    monster.poisoned = false;
                    monster.clearTint();
                } else {
                    // 每秒造成毒性伤害
                    if (!monster.lastPoisonDamage) monster.lastPoisonDamage = 0;
                    if (time - monster.lastPoisonDamage > 1000) {
                        monster.health -= monster.poisonDamage;
                        monster.lastPoisonDamage = time;

                        // 毒性伤害特效
                        const poisonText = this.add.text(monster.x, monster.y - 60, `-${monster.poisonDamage}`, {
                            fontSize: '14px',
                            fill: '#9acd32',
                            fontFamily: 'Arial'
                        }).setOrigin(0.5);

                        this.tweens.add({
                            targets: poisonText,
                            alpha: 0,
                            y: poisonText.y - 20,
                            duration: 800,
                            onComplete: () => poisonText.destroy()
                        });

                        // 检查是否死亡
                        if (monster.health <= 0) {
                            monster.destroy();
                            const monsterIndex = monsters.indexOf(monster);
                            if (monsterIndex > -1) {
                                monsters.splice(monsterIndex, 1);
                                monstersKilled++;
                            }
                            return;
                        }
                    }
                }
            }

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 10000, // 从2500增加到10000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.2, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;

                // 护盾系统
                if (playerStats.shield && playerStats.shield > 0) {
                    const shieldAbsorb = Math.min(damage, playerStats.shield);
                    playerStats.shield -= shieldAbsorb;
                    damage -= shieldAbsorb;

                    // 护盾特效
                    const shieldText = this.add.text(player.x, player.y - 80, `SHIELD -${shieldAbsorb}`, {
                        fontSize: '16px',
                        fill: '#3498db',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: shieldText,
                        alpha: 0,
                        y: shieldText.y - 20,
                        duration: 1000,
                        onComplete: () => shieldText.destroy()
                    });
                }

                // 剩余伤害作用于生命值
                if (damage > 0) {
                    playerHealth -= damage;
                }

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新玩家血条 - 修复缩放问题
        const healthPercent = playerHealth / maxPlayerHealth;
        this.playerHealthBar.scaleX = healthPercent;
        // 调整血条位置，使其从左边开始缩放
        this.playerHealthBar.x = 50 + (200 * healthPercent) / 2;
        this.playerHealthBar.fillColor = healthPercent > 0.5 ? 0x27ae60 :
                                        healthPercent > 0.25 ? 0xf39c12 : 0xe74c3c;

        // 更新文本
        this.levelText.setText(`关卡: ${currentLevel}`);
        this.waveText.setText(`波次: ${currentWave}`);
        this.healthText.setText(`生命: ${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        this.shieldText.setText(`护盾: ${playerStats.shield || 0}`);

        // 更新怪物血条 - 显示在头上
        this.monsterHealthBars.forEach(bar => bar.destroy());
        this.monsterHealthBars = [];

        monsters.forEach((monster, index) => {
            const healthPercent = monster.health / monster.maxHealth;

            // 血条背景
            const barBg = this.add.rectangle(
                monster.x,
                monster.y - 60, // 调整到头部上方
                40,
                6,
                0x333333
            );
            this.monsterHealthBars.push(barBg);

            // 血条前景
            const bar = this.add.rectangle(
                monster.x - 20 + (40 * healthPercent) / 2, // 从左边开始填充
                monster.y - 60,
                40 * healthPercent,
                6,
                healthPercent > 0.5 ? 0x27ae60 : 0xe74c3c
            );
            this.monsterHealthBars.push(bar);
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        if (monsters.length === 0 && !isChoosing) {
            currentWave++;
            let shouldShowChoice = false;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
                shouldShowChoice = true; // 每次升级时显示选择
            } else {
                // 每2波显示一次选择（第2波和第4波等）
                choiceCounter++;
                if (choiceCounter >= 2) {
                    shouldShowChoice = true;
                    choiceCounter = 0;
                }
            }

            if (shouldShowChoice) {
                // 延迟显示选择界面
                setTimeout(() => {
                    showChoiceUI.call(this);
                }, 1500);
            } else {
                // 创建新波次
                setTimeout(() => {
                    createWave.call(this);
                }, 1000);
            }
        }
    }

    // 3选1选择系统
    const choiceTypes = {
        ATTACK_DAMAGE: {
            name: '攻击强化',
            description: '攻击力 +5',
            icon: '⚔️',
            rarity: 'common',
            apply: () => { playerStats.attackDamage += 5; }
        },
        ATTACK_SPEED: {
            name: '攻击速度',
            description: '攻击速度 +20%',
            icon: '⚡',
            rarity: 'common',
            apply: () => { playerStats.attackSpeed *= 0.8; }
        },
        MAX_HEALTH: {
            name: '生命强化',
            description: '最大生命 +25',
            icon: '❤️',
            rarity: 'common',
            apply: () => {
                playerStats.maxHealth += 25;
                maxPlayerHealth += 25;
                playerHealth += 25;
            }
        },
        HEAL: {
            name: '生命恢复',
            description: '恢复 60% 生命值',
            icon: '🩹',
            rarity: 'common',
            apply: () => {
                playerHealth = Math.min(maxPlayerHealth, playerHealth + Math.floor(maxPlayerHealth * 0.6));
            }
        },
        CRIT_CHANCE: {
            name: '暴击几率',
            description: '暴击几率 +15%',
            icon: '💥',
            rarity: 'uncommon',
            apply: () => { playerStats.critChance += 0.15; }
        },
        LIFE_STEAL: {
            name: '生命偷取',
            description: '攻击回血 +12%',
            icon: '🧛',
            rarity: 'uncommon',
            apply: () => { playerStats.lifeSteal += 0.12; }
        },
        MULTI_SHOT: {
            name: '多重射击',
            description: '同时攻击多个敌人',
            icon: '🏹',
            rarity: 'rare',
            apply: () => { playerStats.multiShot += 1; }
        },
        BERSERKER: {
            name: '狂战士',
            description: '生命越低攻击越高',
            icon: '🔥',
            rarity: 'rare',
            apply: () => { playerStats.berserker = true; }
        },
        SHIELD: {
            name: '护盾',
            description: '获得护盾，抵挡伤害',
            icon: '🛡️',
            rarity: 'uncommon',
            apply: () => {
                if (!playerStats.shield) playerStats.shield = 0;
                playerStats.shield += 30;
            }
        },
        POISON: {
            name: '毒性攻击',
            description: '攻击附带持续伤害',
            icon: '☠️',
            rarity: 'rare',
            apply: () => { playerStats.poison = true; }
        },
        FREEZE: {
            name: '冰冻攻击',
            description: '攻击有几率冰冻敌人',
            icon: '❄️',
            rarity: 'rare',
            apply: () => { playerStats.freeze = 0.3; }
        },
        EXPLOSIVE: {
            name: '爆炸攻击',
            description: '攻击造成范围伤害',
            icon: '💣',
            rarity: 'epic',
            apply: () => { playerStats.explosive = true; }
        }
    };

    // 生成随机选择
    function generateChoices() {
        const rarityWeights = {
            'common': 50,
            'uncommon': 30,
            'rare': 15,
            'epic': 5
        };

        const selectedChoices = [];
        const allChoices = Object.keys(choiceTypes);

        // 生成3个不同的选项，考虑稀有度
        while (selectedChoices.length < 3) {
            let selectedChoice = null;

            // 根据稀有度权重选择
            const totalWeight = Object.values(rarityWeights).reduce((a, b) => a + b, 0);
            let random = Math.random() * totalWeight;

            for (const [rarity, weight] of Object.entries(rarityWeights)) {
                random -= weight;
                if (random <= 0) {
                    // 找到该稀有度的选项
                    const choicesOfRarity = allChoices.filter(key =>
                        choiceTypes[key].rarity === rarity && !selectedChoices.includes(key)
                    );

                    if (choicesOfRarity.length > 0) {
                        selectedChoice = choicesOfRarity[Math.floor(Math.random() * choicesOfRarity.length)];
                    }
                    break;
                }
            }

            // 如果没有找到合适的选项，随机选择一个未选择的
            if (!selectedChoice) {
                const availableChoices = allChoices.filter(choice => !selectedChoices.includes(choice));
                if (availableChoices.length > 0) {
                    selectedChoice = availableChoices[Math.floor(Math.random() * availableChoices.length)];
                }
            }

            if (selectedChoice && !selectedChoices.includes(selectedChoice)) {
                selectedChoices.push(selectedChoice);
            }
        }

        return selectedChoices.map(choice => choiceTypes[choice]);
    }

    // 显示3选1界面
    function showChoiceUI() {
        if (isChoosing) return;

        isChoosing = true;
        gameState = 'choosing';
        choiceOptions = generateChoices();

        // 创建半透明背景
        const overlay = this.add.rectangle(375, 667, 750, 1334, 0x000000, 0.8);

        // 创建选择界面容器
        choiceUI = this.add.container(375, 667);
        choiceUI.add(overlay);

        // 主背景面板
        const mainPanel = this.add.rectangle(0, 0, 650, 600, 0x1a1a2e);
        mainPanel.setStrokeStyle(4, 0x3498db);
        choiceUI.add(mainPanel);

        // 标题背景
        const titleBg = this.add.rectangle(0, -250, 500, 60, 0x2c3e50);
        titleBg.setStrokeStyle(2, 0x3498db);
        choiceUI.add(titleBg);

        // 标题
        const title = this.add.text(0, -250, '🎯 选择强化', {
            fontSize: '32px',
            fill: '#ecf0f1',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        choiceUI.add(title);

        // 副标题
        const subtitle = this.add.text(0, -200, '选择一项能力来强化你的角色', {
            fontSize: '18px',
            fill: '#95a5a6',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 当前状态显示
        const statusText = this.add.text(0, -170, `关卡 ${currentLevel} - 波次 ${currentWave}`, {
            fontSize: '16px',
            fill: '#f39c12',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
        choiceUI.add(subtitle);

        // 创建3个选择卡片
        choiceOptions.forEach((option, index) => {
            const cardY = -80 + index * 140;

            // 根据稀有度确定颜色
            const rarityColors = {
                'common': 0x95a5a6,
                'uncommon': 0x2ecc71,
                'rare': 0x3498db,
                'epic': 0x9b59b6
            };
            const borderColor = rarityColors[option.rarity] || 0x3498db;

            // 卡片背景 - 圆角矩形
            const cardBg = this.add.graphics();
            cardBg.fillStyle(0x2c3e50);
            cardBg.lineStyle(3, borderColor);
            cardBg.fillRoundedRect(-280, cardY - 50, 560, 100, 15);
            cardBg.strokeRoundedRect(-280, cardY - 50, 560, 100, 15);
            cardBg.setInteractive(new Phaser.Geom.Rectangle(-280, cardY - 50, 560, 100), Phaser.Geom.Rectangle.Contains);
            choiceUI.add(cardBg);

            // 图标背景圆圈
            const iconBg = this.add.circle(-220, cardY, 35, 0x34495e);
            iconBg.setStrokeStyle(2, 0x3498db);
            choiceUI.add(iconBg);

            // 图标
            const icon = this.add.text(-220, cardY, option.icon, {
                fontSize: '36px'
            }).setOrigin(0.5);
            choiceUI.add(icon);

            // 名称
            const name = this.add.text(-160, cardY - 15, option.name, {
                fontSize: '22px',
                fill: '#ecf0f1',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0, 0.5);
            choiceUI.add(name);

            // 描述
            const description = this.add.text(-160, cardY + 15, option.description, {
                fontSize: '16px',
                fill: '#bdc3c7',
                fontFamily: 'Arial'
            }).setOrigin(0, 0.5);
            choiceUI.add(description);

            // 稀有度标签
            const rarityText = this.add.text(250, cardY - 35, option.rarity.toUpperCase(), {
                fontSize: '12px',
                fill: borderColor,
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(1, 0.5);
            choiceUI.add(rarityText);

            // 选择按钮
            const selectBtn = this.add.rectangle(200, cardY, 100, 40, 0x27ae60);
            selectBtn.setStrokeStyle(2, 0x2ecc71);
            selectBtn.setInteractive();
            choiceUI.add(selectBtn);

            const selectText = this.add.text(200, cardY, '选择', {
                fontSize: '16px',
                fill: '#ffffff',
                fontFamily: 'Arial',
                fontWeight: 'bold'
            }).setOrigin(0.5);
            choiceUI.add(selectText);

            // 点击事件
            const clickHandler = () => {
                selectChoice.call(this, option);
            };

            cardBg.on('pointerdown', clickHandler);
            selectBtn.on('pointerdown', clickHandler);

            // 悬停效果
            const hoverIn = () => {
                cardBg.clear();
                cardBg.fillStyle(0x34495e);
                cardBg.lineStyle(3, 0x52c4ff);
                cardBg.fillRoundedRect(-280, cardY - 50, 560, 100, 15);
                cardBg.strokeRoundedRect(-280, cardY - 50, 560, 100, 15);

                selectBtn.setFillStyle(0x2ecc71);
                this.tweens.add({
                    targets: [iconBg, icon, name, description, selectBtn, selectText],
                    scaleX: 1.05,
                    scaleY: 1.05,
                    duration: 150
                });
            };

            const hoverOut = () => {
                cardBg.clear();
                cardBg.fillStyle(0x2c3e50);
                cardBg.lineStyle(3, 0x3498db);
                cardBg.fillRoundedRect(-280, cardY - 50, 560, 100, 15);
                cardBg.strokeRoundedRect(-280, cardY - 50, 560, 100, 15);

                selectBtn.setFillStyle(0x27ae60);
                this.tweens.add({
                    targets: [iconBg, icon, name, description, selectBtn, selectText],
                    scaleX: 1,
                    scaleY: 1,
                    duration: 150
                });
            };

            cardBg.on('pointerover', hoverIn);
            cardBg.on('pointerout', hoverOut);
            selectBtn.on('pointerover', hoverIn);
            selectBtn.on('pointerout', hoverOut);
        });

        // 添加入场动画
        choiceUI.setScale(0.8);
        choiceUI.setAlpha(0);
        this.tweens.add({
            targets: choiceUI,
            scaleX: 1,
            scaleY: 1,
            alpha: 1,
            duration: 300,
            ease: 'Back.easeOut'
        });
    }

    // 选择选项
    function selectChoice(selectedOption) {
        // 应用选择效果
        selectedOption.apply();

        // 销毁选择界面
        if (choiceUI) {
            choiceUI.destroy();
            choiceUI = null;
        }

        // 恢复游戏状态
        isChoosing = false;
        gameState = 'playing';

        // 显示选择结果提示
        const resultText = this.add.text(375, 300, `获得: ${selectedOption.name}`, {
            fontSize: '24px',
            fill: '#2ecc71',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 提示文字淡出效果
        this.tweens.add({
            targets: resultText,
            alpha: 0,
            y: 250,
            duration: 2000,
            onComplete: () => {
                resultText.destroy();
                // 选择完成后创建新波次
                createWave.call(this);
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
