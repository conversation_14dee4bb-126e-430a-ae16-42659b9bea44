<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗 + 合成大西瓜 融合版</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'playing';
    let monstersKilled = 0;
    let totalMonstersInWave = 3;
    let isCreatingWave = false;

    // 装备收集相关变量
    let equipments = [];
    let containerBounds = null;
    let storageSlots = [];
    let storageArea = null;
    let score = 0;

    // 装备配置
    const EQUIPMENT = [
        { emoji: '🩸', name: '血瓶', size: 16, points: 1, color: 0xff6b6b },
        { emoji: '🔫', name: '弹夹', size: 20, points: 3, color: 0x95a5a6 },
        { emoji: '💣', name: '手雷', size: 24, points: 6, color: 0x2c3e50 },
        { emoji: '🛡️', name: '盾牌', size: 28, points: 10, color: 0x3498db },
        { emoji: '⚔️', name: '武器', size: 32, points: 15, color: 0xe67e22 },
        { emoji: '💎', name: '宝石', size: 36, points: 21, color: 0x9b59b6 },
        { emoji: '🔋', name: '能量', size: 40, points: 28, color: 0xf1c40f },
        { emoji: '🎯', name: '瞄准器', size: 44, points: 36, color: 0xe74c3c },
        { emoji: '🚀', name: '火箭', size: 48, points: 45, color: 0x34495e },
        { emoji: '👑', name: '王冠', size: 52, points: 55, color: 0xf39c12 },
        { emoji: '💰', name: '宝箱', size: 56, points: 66, color: 0x27ae60 }
    ];

    // 装备收集游戏配置
    const CONTAINER_WIDTH = 500;  // 从300增加到500
    const CONTAINER_HEIGHT = 400; // 从350增加到400
    const WALL_THICKNESS = 8;
    const STORAGE_HEIGHT = 80;    // 从60增加到80
    const STORAGE_SLOTS = 7;

    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };

    // 游戏配置
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        physics: {
            default: 'matter',
            matter: {
                debug: false,
                gravity: { y: 0.8 }
            }
        },
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 尝试加载图片，如果失败则创建简单图形
        this.load.on('loaderror', () => {
            console.log('Image loading failed, using fallback graphics');
        });

        // 加载背景图片
        this.load.image('background', 'images/rpg/background.png');
        // 加载主角图片
        this.load.image('player', 'images/rpg/Character.png');
        // 加载怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/image_${i}.png`);
        }
        // 加载武器图片
        this.load.image('knife', 'images/knife/ak.png');

        // 创建备用图形纹理
        this.add.graphics()
            .fillStyle(0x3498db)
            .fillRect(0, 0, 50, 50)
            .generateTexture('player_fallback', 50, 50);

        this.add.graphics()
            .fillStyle(0xe74c3c)
            .fillRect(0, 0, 40, 40)
            .generateTexture('monster_fallback', 40, 40);

        this.add.graphics()
            .fillStyle(0xf39c12)
            .fillRect(0, 0, 30, 10)
            .generateTexture('weapon_fallback', 30, 10);
    }

    // 创建游戏场景
    function create() {
        // 添加背景
        try {
            const background = this.add.image(375, 667, 'background');
            background.setDisplaySize(750, 1334);
        } catch (e) {
            // 如果背景加载失败，创建渐变背景
            const graphics = this.add.graphics();
            graphics.fillGradientStyle(0x1e3c72, 0x1e3c72, 0x2a5298, 0x2a5298, 1);
            graphics.fillRect(0, 0, 750, 1334);
        }

        // 创建主角
        try {
            player = this.add.image(150, 400, 'player');
        } catch (e) {
            player = this.add.image(150, 400, 'player_fallback');
        }
        player.setScale(0.6);
        player.setOrigin(0.5, 1);
        player.setDepth(100 + player.y * 0.1);

        // 创建武器
        try {
            playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        } catch (e) {
            playerWeapon = this.add.image(player.x + 20, player.y - 30, 'weapon_fallback');
        }
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建装备收集游戏区域
        createEquipmentGame.call(this);
    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        // 创建新怪物
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y;

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;
            
            let monster;
            try {
                monster = this.add.image(xPos, yPos, `monster_${monsterImageIndex}`);
            } catch (e) {
                monster = this.add.image(xPos, yPos, 'monster_fallback');
            }
            
            monster.setScale(0.25);
            monster.setOrigin(0.5, 1);
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monsters.push(monster);
        }

        monstersKilled = 0;
        isCreatingWave = false;
    }

    // 创建装备收集游戏区域
    function createEquipmentGame() {
        // 初始化变量
        equipments = [];
        storageSlots = [];
        score = 0;

        // 设置物理世界边界
        this.matter.world.setBounds();

        // 创建容器区域
        createEquipmentContainer.call(this);

        // 创建暂存区
        createEquipmentStorageArea.call(this);
    }

    // 创建装备容器
    function createEquipmentContainer() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const containerY = 650;  // 从700调整到650，为更大的容器留出空间

        containerBounds = {
            left: containerX,
            right: containerX + CONTAINER_WIDTH,
            top: containerY,
            bottom: containerY + CONTAINER_HEIGHT
        };

        const wallOptions = {
            isStatic: true,
            label: 'wall',
            friction: 0.8,
            restitution: 0.1  // 降低弹性，减少回弹
        };

        // 创建物理墙壁
        this.matter.add.rectangle(
            containerX - WALL_THICKNESS/2,
            containerY + CONTAINER_HEIGHT/2,
            WALL_THICKNESS,
            CONTAINER_HEIGHT,
            wallOptions
        );

        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH + WALL_THICKNESS/2,
            containerY + CONTAINER_HEIGHT/2,
            WALL_THICKNESS,
            CONTAINER_HEIGHT,
            wallOptions
        );

        this.matter.add.rectangle(
            containerX + CONTAINER_WIDTH/2,
            containerY + CONTAINER_HEIGHT + WALL_THICKNESS/2,
            CONTAINER_WIDTH + WALL_THICKNESS*2,
            WALL_THICKNESS,
            wallOptions
        );

        // 绘制容器边框
        const graphics = this.add.graphics();
        graphics.lineStyle(3, 0x4ecdc4, 0.8);
        graphics.strokeRoundedRect(containerX - 2, containerY - 2, CONTAINER_WIDTH + 4, CONTAINER_HEIGHT + 4, 8);

        // 添加容器标题
        this.add.text(containerX + CONTAINER_WIDTH/2, containerY - 25, '⚔️ 装备收集区域', {
            fontSize: '20px',
            fill: '#4ecdc4',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);
    }

    // 创建装备暂存区
    function createEquipmentStorageArea() {
        const containerX = (750 - CONTAINER_WIDTH) / 2;
        const storageY = 650 + CONTAINER_HEIGHT + 20;  // 更新Y坐标以匹配新的容器位置
        const slotWidth = CONTAINER_WIDTH / STORAGE_SLOTS;

        storageSlots = new Array(STORAGE_SLOTS).fill(null);

        const graphics = this.add.graphics();
        graphics.fillStyle(0x34495e, 0.8);
        graphics.fillRoundedRect(containerX, storageY, CONTAINER_WIDTH, STORAGE_HEIGHT, 5);

        // 绘制格子分隔线
        graphics.lineStyle(1, 0x7f8c8d, 0.6);
        for (let i = 1; i < STORAGE_SLOTS; i++) {
            const x = containerX + i * slotWidth;
            graphics.moveTo(x, storageY);
            graphics.lineTo(x, storageY + STORAGE_HEIGHT);
        }
        graphics.strokePath();

        storageArea = {
            x: containerX,
            y: storageY,
            width: CONTAINER_WIDTH,
            height: STORAGE_HEIGHT,
            slotWidth: slotWidth
        };

        // 添加暂存区标题
        this.add.text(containerX + CONTAINER_WIDTH/2, storageY - 15, '装备暂存区 (点击装备收集，3个相同自动消除)', {
            fontSize: '18px',  // 增大字体
            fill: '#95a5a6',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 怪物死亡掉落装备
    function dropFruitFromMonster(monster) {
        if (!containerBounds) return;

        const equipmentType = Phaser.Math.Between(0, 4); // 只掉落前5种装备
        const equipmentConfig = EQUIPMENT[equipmentType];

        // 计算掉落位置：直接在容器内随机位置
        const targetX = Phaser.Math.Between(
            containerBounds.left + equipmentConfig.size + 10,
            containerBounds.right - equipmentConfig.size - 10
        );
        const targetY = containerBounds.top + 30; // 在容器顶部稍下方

        // 直接在目标位置创建装备，避免移动动画造成的回弹
        const equipment = this.add.circle(targetX, targetY, equipmentConfig.size, equipmentConfig.color);

        const emojiText = this.add.text(targetX, targetY, equipmentConfig.emoji, {
            fontSize: `${equipmentConfig.size * 1.5}px`
        }).setOrigin(0.5);

        equipment.emojiText = emojiText;
        equipment.equipmentType = equipmentType;

        setupEquipmentClick.call(this, equipment);

        // 立即添加物理体，让装备直接重力下落
        this.matter.add.gameObject(equipment, {
            shape: 'circle',
            radius: equipmentConfig.size,
            isStatic: false,
            restitution: 0.1, // 极低弹性，几乎无回弹
            friction: 0.9,    // 高摩擦力
            frictionAir: 0.01 // 空气阻力
        });

        equipments.push(equipment);

        // 掉落特效
        const dropEffect = this.add.text(monster.x, monster.y - 30, '📦', {
            fontSize: '20px'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: dropEffect,
            alpha: 0,
            y: dropEffect.y - 30,
            duration: 1000,
            onComplete: () => dropEffect.destroy()
        });
    }

    // 设置装备点击事件
    function setupEquipmentClick(equipment) {
        equipment.setInteractive();
        equipment.on('pointerdown', () => {
            collectEquipment.call(this, equipment);
        });
    }

    // 收集装备到暂存区
    function collectEquipment(equipment) {
        const emptySlotIndex = storageSlots.findIndex(slot => slot === null);

        if (emptySlotIndex === -1) {
            showMessage.call(this, '暂存区已满！', equipment.x, equipment.y - 50);
            return;
        }

        const slotX = storageArea.x + (emptySlotIndex + 0.5) * storageArea.slotWidth;
        const slotY = storageArea.y + storageArea.height / 2;

        // 收集特效
        const effect = this.add.text(equipment.x, equipment.y, '✨', {
            fontSize: '24px'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: effect,
            x: slotX,
            y: slotY,
            scaleX: 0.5,
            scaleY: 0.5,
            duration: 300,
            ease: 'Power2',
            onComplete: () => effect.destroy()
        });

        storageSlots[emptySlotIndex] = {
            equipmentType: equipment.equipmentType,
            emoji: EQUIPMENT[equipment.equipmentType].emoji,
            name: EQUIPMENT[equipment.equipmentType].name,
            displayObject: null
        };

        removeEquipment.call(this, equipment);

        this.time.delayedCall(300, () => {
            displayEquipmentInStorage.call(this, emptySlotIndex);
            checkForMatches.call(this);
        });
    }

    // 在暂存区显示装备
    function displayEquipmentInStorage(slotIndex) {
        const slotData = storageSlots[slotIndex];
        if (!slotData) return;

        const slotX = storageArea.x + (slotIndex + 0.5) * storageArea.slotWidth;
        const slotY = storageArea.y + storageArea.height / 2;

        const displayObject = this.add.text(slotX, slotY, slotData.emoji, {
            fontSize: '40px'  // 从32px增加到40px，适配更大的暂存区
        }).setOrigin(0.5);

        slotData.displayObject = displayObject;
    }

    // 检查匹配并消除
    function checkForMatches() {
        const fruitCounts = {};
        const fruitPositions = {};

        storageSlots.forEach((slot, index) => {
            if (slot !== null) {
                const type = slot.equipmentType;
                if (!fruitCounts[type]) {
                    fruitCounts[type] = 0;
                    fruitPositions[type] = [];
                }
                fruitCounts[type]++;
                fruitPositions[type].push(index);
            }
        });

        for (const [equipmentType, count] of Object.entries(fruitCounts)) {
            if (count >= 3) {
                eliminateEquipments.call(this, parseInt(equipmentType), fruitPositions[equipmentType]);
                break;
            }
        }
    }

    // 消除装备
    function eliminateEquipments(equipmentType, positions) {
        const eliminatePositions = positions.slice(0, 3);

        eliminatePositions.forEach(pos => {
            const slot = storageSlots[pos];
            if (slot && slot.displayObject) {
                this.tweens.add({
                    targets: slot.displayObject,
                    scaleX: 2,
                    scaleY: 2,
                    alpha: 0,
                    duration: 500,
                    ease: 'Power2',
                    onComplete: () => slot.displayObject.destroy()
                });
            }
        });

        this.time.delayedCall(500, () => {
            eliminatePositions.forEach(pos => {
                storageSlots[pos] = null;
            });

            rearrangeStorage.call(this);

            const points = EQUIPMENT[equipmentType].points * 3 + 50;
            updateScore.call(this, points);
            showScoreEffect.call(this, points);
        });
    }

    // 重新排列暂存区
    function rearrangeStorage() {
        const remainingFruits = [];
        storageSlots.forEach((slot, index) => {
            if (slot !== null) {
                remainingFruits.push(slot);
            }
        });

        storageSlots.forEach(slot => {
            if (slot && slot.displayObject) {
                slot.displayObject.destroy();
                slot.displayObject = null;
            }
        });

        storageSlots = new Array(STORAGE_SLOTS).fill(null);

        remainingFruits.forEach((equipmentData, newIndex) => {
            storageSlots[newIndex] = equipmentData;
            displayEquipmentInStorage.call(this, newIndex);
        });
    }

    // 移除装备
    function removeEquipment(equipment) {
        const index = equipments.indexOf(equipment);
        if (index > -1) {
            equipments.splice(index, 1);
        }
        if (equipment.emojiText) {
            equipment.emojiText.destroy();
        }
        equipment.destroy();
    }

    // 显示消息
    function showMessage(message, x, y) {
        const messageText = this.add.text(x, y, message, {
            fontSize: '20px',
            fill: '#ff6b6b',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: messageText,
            alpha: 0,
            y: y - 30,
            duration: 1500,
            onComplete: () => messageText.destroy()
        });
    }

    // 更新得分
    function updateScore(points) {
        score += points;
        if (game && game.scene && game.scene.scenes[0] && game.scene.scenes[0].scoreText) {
            game.scene.scenes[0].scoreText.setText(`装备得分: ${score}`);
        }
    }

    // 显示得分特效
    function showScoreEffect(points) {
        const scoreEffect = this.add.text(375, storageArea.y - 30, `+${points}`, {
            fontSize: '28px',
            fill: '#f39c12',
            fontStyle: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: scoreEffect,
            y: storageArea.y - 60,
            alpha: 0,
            scaleX: 1.5,
            scaleY: 1.5,
            duration: 1000,
            ease: 'Power2',
            onComplete: () => scoreEffect.destroy()
        });
    }

    // 移除自动合成功能，水果只能通过收集到暂存区进行消除

    // 创建UI元素
    function createUI() {
        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50);
        playerHealthBarBg.lineStyle(2, 0x000000);
        playerHealthBarBg.fillRoundedRect(50, 45, 200, 20, 10);
        playerHealthBarBg.strokeRoundedRect(50, 45, 200, 20, 10);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(150, 55, '', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 关卡信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8);
        levelBg.lineStyle(2, 0x000000);
        levelBg.fillRoundedRect(300, 15, 150, 60, 10);
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);

        // 装备收集得分显示
        this.scoreText = this.add.text(375, 600, `装备得分: ${score}`, {  // 从650调整到600
            fontSize: '24px',
            fill: '#4ecdc4',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 游戏说明
        this.add.text(375, 1300, '击杀怪物掉落装备 | 点击装备收集到暂存区 | 3个相同自动消除获得分数', {
            fontSize: '18px',
            fill: '#95a5a6',
            fontFamily: 'Arial'
        }).setOrigin(0.5);
    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 196;
        const currentWidth = barWidth * healthPercent;

        let barColor = 0x27ae60;
        if (healthPercent < 0.6) barColor = 0xf39c12;
        if (healthPercent < 0.3) barColor = 0xe74c3c;

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(52, 47, currentWidth, 16, 8);

        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 主角攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            const target = monsters[0];

            const weaponRightX = playerWeapon.x + 20;
            const weaponCenterY = playerWeapon.y - 10;
            const projectile = this.add.circle(weaponRightX, weaponCenterY, 5, 0xffff00);

            this.tweens.add({
                targets: projectile,
                x: target.x - 30,
                y: target.y - 30,
                duration: 200,
                onComplete: () => {
                    projectile.destroy();

                    let damage = playerStats.attackDamage + currentLevel * 2;
                    target.health -= damage;

                    // 显示伤害
                    const damageText = this.add.text(target.x, target.y - 60, `-${damage}`, {
                        fontSize: '18px',
                        fill: '#ff0000',
                        fontFamily: 'Arial',
                        fontWeight: 'bold'
                    }).setOrigin(0.5);

                    this.tweens.add({
                        targets: damageText,
                        alpha: 0,
                        y: damageText.y - 30,
                        duration: 1000,
                        onComplete: () => damageText.destroy()
                    });

                    // 怪物受击效果
                    this.tweens.add({
                        targets: target,
                        tint: 0xff0000,
                        duration: 100,
                        yoyo: true,
                        onComplete: () => target.clearTint()
                    });

                    // 检查怪物是否死亡
                    if (target.health <= 0) {
                        // 怪物死亡时掉落装备
                        dropFruitFromMonster.call(this, target);

                        target.destroy();
                        const index = monsters.indexOf(target);
                        if (index > -1) {
                            monsters.splice(index, 1);
                            monstersKilled++;
                        }
                    }
                }
            });

            // 主角攻击动画
            this.tweens.add({
                targets: player,
                x: player.x + 2,
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 武器后坐力
            if (playerWeapon) {
                const originalWeaponX = playerWeapon.x;
                this.tweens.add({
                    targets: playerWeapon,
                    x: originalWeaponX - 8,
                    duration: 80,
                    ease: 'Power2',
                    onComplete: () => {
                        this.tweens.add({
                            targets: playerWeapon,
                            x: originalWeaponX,
                            duration: 40,
                            ease: 'Power2'
                        });
                    }
                });
            }
        }
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新UI
        updatePlayerHealthBar.call(this);

        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }

        if (gameState === 'playing') {
            // 主角自动攻击
            if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
            if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
                playerAttack.call(this);
                this.lastPlayerAttack = time;
            }

            // 检查波次完成
            if (monsters.length === 0 && !isCreatingWave) {
                isCreatingWave = true;
                currentWave++;

                if (currentWave > 3) {
                    currentLevel++;
                    currentWave = 1;
                    totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
                }

                setTimeout(() => {
                    createWave.call(this);
                }, 1000);
            }

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新装备位置同步
        equipments = equipments.filter(equipment => equipment && equipment.active);
        equipments.forEach(equipment => {
            if (equipment && equipment.emojiText && equipment.body && equipment.body.position) {
                equipment.emojiText.x = equipment.body.position.x;
                equipment.emojiText.y = equipment.body.position.y;
            }
        });
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
