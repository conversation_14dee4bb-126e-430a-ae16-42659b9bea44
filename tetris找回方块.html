<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>俄罗斯方块弹球</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: Arial, sans-serif;
            overflow: hidden;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
            -webkit-touch-callout: none;
            -webkit-tap-highlight-color: transparent;
        }
        #game-container {
            /* border: 3px solid #333; */
            border-radius: 10px;
            /* box-shadow: 0 0 20px rgba(0,0,0,0.5); */
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>
    
    <script>
        // 防止多次初始化
        let gameInitialized = false;

        window.addEventListener('DOMContentLoaded', function() {
            if (gameInitialized) return;
            gameInitialized = true;

            // 逻辑分辨率（放大游戏区域）
            const GAME_WIDTH = 600;
            const GAME_HEIGHT = 900;

            // Phaser配置，直接用逻辑分辨率
            const config = {
                type: Phaser.AUTO,
                width: GAME_WIDTH,
                height: GAME_HEIGHT,
                parent: 'game-container',
                backgroundColor: '#000000',
                scene: {
                    preload: preload,
                    create: create,
                    update: update
                },
                scale: {
                    mode: Phaser.Scale.FIT,
                    autoCenter: Phaser.Scale.CENTER_BOTH
                }
            };

            // 游戏变量
            let gameGrid = [];
            let currentPiece = null;
            let nextPiece = null;

            // 物理小格子数组
            let physicsBlocks = [];

            // 挡板系统
            let paddles = [];
            let paddleWidth = 80;
            let paddleHeight = 12;
            let paddleSpeed = 8;
            let score = 0;
            let level = 1;
            let lines = 0;
            // 记录每种方块被弹球消除的数量
            let blockCollectCount = [0, 0, 0, 0, 0, 0, 0, 0]; // 0号不用
            let dropTime = 0;
            let dropInterval = 1000;
            let gameOver = false;
            let isPaused = false;
            let fastDrop = false; // 快速下落状态
            let isClearingLines = false; // 是否正在消除行
            let clearingLines = []; // 正在消除的行
            let fallingRows = []; // 正在下落的行
            let fallAnimationStartTime = 0; // 下落动画开始时间
            let fallDropDistances = []; // 每行下落距离

            // 动画参数
            const LINE_CLEAR_FLASH_DURATION = 150; // 行消除闪烁时长（更快）
            const LINE_CLEAR_FLASH_COUNT = 1; // 闪烁次数（2下）
            const FALL_ANIMATION_DURATION = 400; // 下落动画时长

            // 游戏区域配置（放大格子并居中）
            const GRID_WIDTH = 10;
            const GRID_HEIGHT = 19;
            const CELL_SIZE = 40;
            const GRID_X = Math.floor((GAME_WIDTH - GRID_WIDTH * CELL_SIZE) / 2) - 80;
            const GRID_Y = 120;

            // 方块颜色
            const COLORS = [
                0x000000, // 空
                0xFF0000, // 红色 - I
                0x00FF00, // 绿色 - O  
                0x0000FF, // 蓝色 - T
                0xFFFF00, // 黄色 - S
                0xFF00FF, // 紫色 - Z
                0x00FFFF, // 青色 - J
                0xFFA500  // 橙色 - L
            ];

            // 方块形状定义
            const PIECES = [
                // I 形状
                {
                    shape: [
                        [0,0,0,0],
                        [1,1,1,1],
                        [0,0,0,0],
                        [0,0,0,0]
                    ],
                    color: 1
                },
                // O 形状
                {
                    shape: [
                        [2,2],
                        [2,2]
                    ],
                    color: 2
                },
                // T 形状
                {
                    shape: [
                        [0,3,0],
                        [3,3,3],
                        [0,0,0]
                    ],
                    color: 3
                },
                // S 形状
                {
                    shape: [
                        [0,4,4],
                        [4,4,0],
                        [0,0,0]
                    ],
                    color: 4
                },
                // Z 形状
                {
                    shape: [
                        [5,5,0],
                        [0,5,5],
                        [0,0,0]
                    ],
                    color: 5
                },
                // J 形状
                {
                    shape: [
                        [6,0,0],
                        [6,6,6],
                        [0,0,0]
                    ],
                    color: 6
                },
                // L 形状
                {
                    shape: [
                        [0,0,7],
                        [7,7,7],
                        [0,0,0]
                    ],
                    color: 7
                }
            ];

            let scene;
            let graphics;
            // let scoreText, levelText, linesText;
            // let leftButton, rightButton, rotateButton, downButton, pauseButton;
            let parentSizer, logicSizer;

            // 多弹球相关变量
            // let balls = [];
            // function createBall() {
            //     // 随机初始方向
            //     let angle = Math.random() * Math.PI * 2;
            //     let speed = 4;
            //     return {
            //         x: GRID_X + (GRID_WIDTH * CELL_SIZE) / 2,
            //         y: GRID_Y + (GRID_HEIGHT * CELL_SIZE) / 2,
            //         vx: Math.cos(angle) * speed,
            //         vy: Math.sin(angle) * speed,
            //         radius: 12,
            //         color: 0xffffff
            //     };
            // }

            // 粒子特效数组
            // let effectParticles = [];

            // 音效相关
            // let sound_bgm, sound_hit, sound_drop, sound_clear;

            function preload() {
                scene = this;
                // 加载音效
                // this.load.audio('bgm', ['sound/俄罗斯方块/背景音乐.mp3']);
                // this.load.audio('hit', ['sound/俄罗斯方块/碰撞.mp3']);
                // this.load.audio('drop', ['sound/俄罗斯方块/下落.mp3']);
                // this.load.audio('clear', ['sound/俄罗斯方块/消除一行.mp3']);
            }

            function create() {
                graphics = scene.add.graphics();
                // 音效对象
                // sound_bgm = scene.sound.add('bgm', { loop: true, volume: 0.5 });
                // sound_hit = scene.sound.add('hit', { volume: 1 });
                // sound_drop = scene.sound.add('drop', { volume: 1 });
                // sound_clear = scene.sound.add('clear', { volume: 1 });
                // 播放背景音乐
                // if (!sound_bgm.isPlaying) sound_bgm.play();

                // 逻辑区域和父容器的 Size 适配
                parentSizer = new Phaser.Structs.Size(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer = new Phaser.Structs.Size(GAME_WIDTH, GAME_HEIGHT, Phaser.Structs.Size.FIT, parentSizer);
                parentSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);
                logicSizer.setSize(this.scale.gameSize.width, this.scale.gameSize.height);

                // 监听resize事件
                this.scale.on('resize', resizeGame, this);
                
                // 初始化游戏网格
                initGrid();

                // 暂时移除挡板初始化
                // initPaddles();

                // 创建UI
                createUI();

                // 生成第一个方块
                spawnPiece();

                // 设置输入
                setupInput();

                // 初始化弹球数组
                // balls = [createBall()];
            }

            function resizeGame(gameSize) {
                if (!parentSizer || !logicSizer) return;
                const width = gameSize.width;
                const height = gameSize.height;
                parentSizer.setSize(width, height);
                logicSizer.setSize(width, height);
                // 这里可以统一调整 camera、container、layer 的缩放和位置（如有）
                // 目前所有元素都用逻辑坐标，Phaser会自动缩放
            }

            function initGrid() {
                gameGrid = [];
                for (let y = 0; y < GRID_HEIGHT; y++) {
                    gameGrid[y] = [];
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        gameGrid[y][x] = 0;
                    }
                }

                // 不再生成预设布局，因为现在是纯物理游戏
                // generateNearFailureState();
            }

            function initPaddles() {
                paddles = [];
                const gameAreaWidth = GRID_WIDTH * CELL_SIZE;
                const gameAreaHeight = GRID_HEIGHT * CELL_SIZE;

                // 创建3个挡板：左、右、底部
                paddles.push({
                    x: GRID_X + gameAreaWidth * 0.2,
                    y: GRID_Y + gameAreaHeight * 0.3,
                    width: paddleWidth,
                    height: paddleHeight,
                    type: 'left',
                    color: 0x00ff00
                });

                paddles.push({
                    x: GRID_X + gameAreaWidth * 0.6,
                    y: GRID_Y + gameAreaHeight * 0.6,
                    width: paddleWidth,
                    height: paddleHeight,
                    type: 'right',
                    color: 0x0000ff
                });

                paddles.push({
                    x: GRID_X + gameAreaWidth * 0.4,
                    y: GRID_Y + gameAreaHeight * 0.85,
                    width: paddleWidth,
                    height: paddleHeight,
                    type: 'bottom',
                    color: 0xff0000
                });
            }

            function generateNearFailureState() {
                // 预设一个接近失败的方块布局
                // 每一行都有9个方块，留一个空位，但空位的位置不同，无法消除
                const patterns = [
                    [1,1,1,1,1,1,1,1,1,0], // 最后一个空
                    [0,1,1,1,1,1,1,1,1,1], // 第一个空
                    [1,1,1,1,0,1,1,1,1,1], // 中间空
                    [1,1,0,1,1,1,1,1,1,1], // 左边空
                    [1,1,1,1,1,1,0,1,1,1], // 右边空
                    [1,0,1,1,1,1,1,1,1,1], // 第二个空
                    [1,1,1,1,1,1,1,0,1,1], // 倒数第三个空
                    [1,1,1,0,1,1,1,1,1,1], // 第四个空
                ];

                // 从底部开始填充，留出顶部几行给新方块
                const startRow = GRID_HEIGHT - patterns.length;
                for (let i = 0; i < patterns.length; i++) {
                    const row = startRow + i;
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (patterns[i][x] === 1) {
                            // 随机选择方块颜色（1-7）
                            gameGrid[row][x] = Math.floor(Math.random() * 7) + 1;
                        }
                    }
                }
            }

            // 右侧面板X坐标，提升为全局变量，供createUI和render使用
            const rightPanelX = GAME_WIDTH - 160;
            function createUI() {
                // 标题
                scene.add.text(GAME_WIDTH * 0.5, 40, '俄罗斯方块++', {
                    fontSize: Math.floor(40) + 'px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    resolution: window.devicePixelRatio
                }).setOrigin(0.5);

                // 每种方块收集数量统计
                // 传统俄罗斯方块顶部信息
                scene.scoreText = scene.add.text(60, 80, `积分: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffff00',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.linesText = scene.add.text(220, 80, `行数: 0`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#00ffff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);
                scene.levelText = scene.add.text(380, 80, `等级: 1`, {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ff00ff',
                    fontStyle: 'bold'
                }).setOrigin(0, 0.5);

                // 下一个方块预览区域（放在右上角）
                scene.add.text(rightPanelX, 130, '下一个:', {
                    fontSize: Math.floor(20) + 'px',
                    fill: '#ffffff'
                });
                // 玩法说明
                const ruleY = 220;
                const ruleX = rightPanelX;
                const ruleText = [
                    '玩法说明:',
                    '1. 键盘←→移动',
                    '2. ↓加速下落',
                    '3. 空格释放方块',
                    '4. 物理堆叠游戏'
                ];
                for (let i = 0; i < ruleText.length; i++) {
                    scene.add.text(ruleX, ruleY + i * 26, ruleText[i], {
                        fontSize: Math.floor(i === 0 ? 18 : 16) + 'px',
                        fill: i === 0 ? '#ffff00' : '#ffffff'
                    });
                }
            }

            // 已移除 createControls 函数

            function setupInput() {
                // 键盘控制
                const cursors = scene.input.keyboard.createCursorKeys();
                const wasd = scene.input.keyboard.addKeys('W,S,A,D,SPACE,P,Q,E,Z,X,C');

                // 持续按键状态
                let keys = {};

                scene.input.keyboard.on('keydown', (event) => {
                    keys[event.code] = true;

                    if (gameOver || isPaused) return;

                    switch(event.code) {
                        case 'ArrowLeft':
                        case 'KeyA':
                            movePiece(-1, 0);
                            break;
                        case 'ArrowRight':
                        case 'KeyD':
                            movePiece(1, 0);
                            break;
                        case 'ArrowDown':
                        case 'KeyS':
                            startFastDrop();
                            break;
                        // 移除旋转控制
                        // case 'ArrowUp':
                        // case 'KeyW':
                        //     rotatePiece();
                        //     break;
                        case 'Space':
                            hardDrop();
                            break;
                        case 'KeyP':
                            togglePause();
                            break;
                    }
                });

                scene.input.keyboard.on('keyup', (event) => {
                    keys[event.code] = false;

                    switch(event.code) {
                        case 'ArrowDown':
                        case 'KeyS':
                            stopFastDrop();
                            break;
                    }
                });

                // 挡板控制更新函数
                scene.updatePaddles = function() {
                    if (gameOver || isPaused) return;

                    // 左挡板控制 (Q/E)
                    if (keys['KeyQ'] && paddles[0]) {
                        paddles[0].x -= paddleSpeed;
                        paddles[0].x = Math.max(GRID_X, paddles[0].x);
                    }
                    if (keys['KeyE'] && paddles[0]) {
                        paddles[0].x += paddleSpeed;
                        paddles[0].x = Math.min(GRID_X + GRID_WIDTH * CELL_SIZE - paddleWidth, paddles[0].x);
                    }

                    // 右挡板控制 (Z/C)
                    if (keys['KeyZ'] && paddles[1]) {
                        paddles[1].x -= paddleSpeed;
                        paddles[1].x = Math.max(GRID_X, paddles[1].x);
                    }
                    if (keys['KeyC'] && paddles[1]) {
                        paddles[1].x += paddleSpeed;
                        paddles[1].x = Math.min(GRID_X + GRID_WIDTH * CELL_SIZE - paddleWidth, paddles[1].x);
                    }

                    // 底部挡板控制 (左右箭头)
                    if (keys['ArrowLeft'] && paddles[2]) {
                        paddles[2].x -= paddleSpeed;
                        paddles[2].x = Math.max(GRID_X, paddles[2].x);
                    }
                    if (keys['ArrowRight'] && paddles[2]) {
                        paddles[2].x += paddleSpeed;
                        paddles[2].x = Math.min(GRID_X + GRID_WIDTH * CELL_SIZE - paddleWidth, paddles[2].x);
                    }
                };
            }

            function startFastDrop() {
                if (gameOver || isPaused) return;
                fastDrop = true;
            }

            function stopFastDrop() {
                fastDrop = false;
            }

            function spawnPiece() {
                if (!nextPiece) {
                    nextPiece = createRandomPiece();
                }

                currentPiece = nextPiece;
                currentPiece.x = Math.floor(GRID_WIDTH / 2) - Math.floor(currentPiece.shape[0].length / 2);
                currentPiece.y = 0;

                nextPiece = createRandomPiece();

                // 检查游戏结束
                if (checkCollision(currentPiece, 0, 0)) {
                    gameOver = true;
                    showGameOver();
                }
            }

            function createRandomPiece() {
                const pieceIndex = Math.floor(Math.random() * PIECES.length);
                return {
                    shape: PIECES[pieceIndex].shape.map(row => [...row]),
                    color: PIECES[pieceIndex].color,
                    x: 0,
                    y: 0
                };
            }

            // 物理小格子类
            class PhysicsBlock {
                constructor(x, y, color, subX = 0, subY = 0) {
                    this.x = x; // 屏幕像素坐标
                    this.y = y;
                    this.vx = (Math.random() - 0.5) * 8; // 随机水平速度
                    this.vy = Math.random() * -5 - 2; // 向上的初始速度
                    this.color = color;
                    this.size = (CELL_SIZE / 2) - 4; // 小格子大小
                    this.gravity = 0.3; // 重力
                    this.bounce = 0.6; // 弹性系数
                    this.friction = 0.98; // 摩擦力
                    this.life = 300; // 生命周期（帧数）
                    this.maxLife = 300;
                    // 移除旋转相关属性
                    // this.rotation = Math.random() * Math.PI * 2; // 旋转角度
                    // this.rotationSpeed = (Math.random() - 0.5) * 0.2; // 旋转速度
                }

                update(allBlocks) {
                    // 如果是粒子，减少生命值
                    if (this.isParticle) {
                        this.life--;
                        if (this.life <= 0) {
                            return false; // 粒子生命结束
                        }
                    }

                    // 应用重力
                    this.vy += this.gravity;

                    // 保存旧位置
                    const oldX = this.x;
                    const oldY = this.y;

                    // 限制最大速度，防止穿透
                    const maxSpeed = this.size * 0.5; // 最大速度不超过格子大小的一半
                    this.vx = Math.max(-maxSpeed, Math.min(maxSpeed, this.vx));
                    this.vy = Math.max(-maxSpeed, Math.min(maxSpeed, this.vy));

                    // 分步更新位置，防止高速穿透
                    const steps = Math.max(1, Math.ceil(Math.max(Math.abs(this.vx), Math.abs(this.vy)) / (this.size * 0.5)));
                    const stepVx = this.vx / steps;
                    const stepVy = this.vy / steps;

                    for (let step = 0; step < steps; step++) {
                        // 更新位置
                        this.x += stepVx;
                        this.y += stepVy;

                        // 边界碰撞检测
                        const gameAreaLeft = GRID_X;
                        const gameAreaRight = GRID_X + GRID_WIDTH * CELL_SIZE;
                        const gameAreaBottom = GRID_Y + GRID_HEIGHT * CELL_SIZE;
                        const gameAreaTop = GRID_Y;

                        // 粒子可以飞出边界
                        if (!this.isParticle) {
                            // 左边界
                            if (this.x < gameAreaLeft) {
                                this.x = gameAreaLeft;
                                this.vx *= -this.bounce;
                            }

                            // 右边界
                            if (this.x + this.size > gameAreaRight) {
                                this.x = gameAreaRight - this.size;
                                this.vx *= -this.bounce;
                            }

                            // 顶部边界
                            if (this.y < gameAreaTop) {
                                this.y = gameAreaTop;
                                this.vy *= -this.bounce;
                            }

                            // 底部边界 - 严格防止穿透
                            if (this.y + this.size > gameAreaBottom) {
                                this.y = gameAreaBottom - this.size;
                                this.vy = 0; // 完全停止垂直运动
                                this.vx *= this.friction;

                                // 如果速度很小，完全停止
                                if (Math.abs(this.vx) < 0.1) {
                                    this.vx = 0;
                                }
                                break; // 碰到底部就停止分步检测
                            }
                        }
                    }

                    // 只有非粒子才参与碰撞检测
                    if (!this.isParticle) {
                        this.checkBlockCollisions(allBlocks, oldX, oldY);
                    }

                    // 暂时移除挡板碰撞检测
                    // this.checkPaddleCollisions();

                    return true; // 返回true表示继续存在
                }

                checkBlockCollisions(allBlocks, oldX, oldY) {
                    // 如果当前方块已经静止，减少碰撞检测的敏感度
                    const isStationary = Math.abs(this.vx) < 0.1 && Math.abs(this.vy) < 0.1;

                    for (let other of allBlocks) {
                        if (other === this) continue;

                        // 如果两个方块都静止，跳过碰撞检测，避免"爆炸"
                        const otherStationary = Math.abs(other.vx) < 0.1 && Math.abs(other.vy) < 0.1;
                        if (isStationary && otherStationary) {
                            continue;
                        }

                        // 检查是否碰撞（增加一点容差，避免微小重叠）
                        const tolerance = 1;
                        if (this.x < other.x + other.size - tolerance &&
                            this.x + this.size > other.x + tolerance &&
                            this.y < other.y + other.size - tolerance &&
                            this.y + this.size > other.y + tolerance) {

                            // 计算重叠量
                            const overlapX = Math.min(this.x + this.size - other.x, other.x + other.size - this.x);
                            const overlapY = Math.min(this.y + this.size - other.y, other.y + other.size - this.y);

                            // 只有重叠量足够大才进行分离，避免微小重叠导致的抖动
                            if (overlapX > 2 || overlapY > 2) {
                                // 根据重叠量较小的方向进行分离
                                if (overlapX < overlapY) {
                                    // 水平分离
                                    if (this.x < other.x) {
                                        this.x = other.x - this.size;
                                    } else {
                                        this.x = other.x + other.size;
                                    }
                                    this.vx *= -this.bounce * 0.2; // 进一步减少能量
                                } else {
                                    // 垂直分离
                                    if (this.y < other.y) {
                                        this.y = other.y - this.size;
                                        // 如果是从上方碰撞，停止垂直运动
                                        if (this.vy > 0) {
                                            this.vy = 0; // 完全停止，实现堆叠
                                        }
                                    } else {
                                        this.y = other.y + other.size;
                                        this.vy *= -this.bounce * 0.2;
                                    }
                                }

                                // 添加更强的摩擦力，让方块更容易静止
                                this.vx *= 0.7;
                                this.vy *= 0.9;
                                if (Math.abs(this.vx) < 0.3) this.vx = 0;
                                if (Math.abs(this.vy) < 0.3) this.vy = 0;
                            }
                        }
                    }
                }

                checkPaddleCollisions() {
                    for (let paddle of paddles) {
                        if (this.x + this.size > paddle.x &&
                            this.x < paddle.x + paddle.width &&
                            this.y + this.size > paddle.y &&
                            this.y < paddle.y + paddle.height) {

                            // 计算碰撞面
                            const overlapLeft = (this.x + this.size) - paddle.x;
                            const overlapRight = (paddle.x + paddle.width) - this.x;
                            const overlapTop = (this.y + this.size) - paddle.y;
                            const overlapBottom = (paddle.y + paddle.height) - this.y;

                            const minOverlap = Math.min(overlapLeft, overlapRight, overlapTop, overlapBottom);

                            if (minOverlap === overlapTop || minOverlap === overlapBottom) {
                                // 垂直碰撞
                                this.vy *= -this.bounce;
                                if (minOverlap === overlapTop) {
                                    this.y = paddle.y - this.size;
                                } else {
                                    this.y = paddle.y + paddle.height;
                                }
                            } else {
                                // 水平碰撞
                                this.vx *= -this.bounce;
                                if (minOverlap === overlapLeft) {
                                    this.x = paddle.x - this.size;
                                } else {
                                    this.x = paddle.x + paddle.width;
                                }
                            }
                        }
                    }
                }

                draw(graphics) {
                    // 粒子有淡出效果，普通方块保持不透明
                    const alpha = this.isParticle ? (this.life / this.maxLife) : 1;

                    // 绘制简单的矩形小格子（不旋转）
                    graphics.fillStyle(this.color, alpha);
                    graphics.fillRect(this.x, this.y, this.size, this.size);

                    // 只有非粒子才有3D效果
                    if (!this.isParticle) {
                        // 简单的3D效果
                        const rgb = Phaser.Display.Color.IntegerToRGB(this.color);

                        // 高光效果
                        const highlightColor = Phaser.Display.Color.GetColor32(
                            Math.min(255, rgb.r + 60),
                            Math.min(255, rgb.g + 60),
                            Math.min(255, rgb.b + 60),
                            255
                        );
                        graphics.fillStyle(highlightColor, alpha);
                        graphics.fillRect(this.x, this.y, this.size, 2);
                        graphics.fillRect(this.x, this.y, 2, this.size);

                        // 阴影效果
                        const shadowColor = Phaser.Display.Color.GetColor32(
                            Math.max(0, rgb.r - 60),
                            Math.max(0, rgb.g - 60),
                            Math.max(0, rgb.b - 60),
                            255
                        );
                        graphics.fillStyle(shadowColor, alpha);
                        graphics.fillRect(this.x + this.size - 2, this.y, 2, this.size);
                        graphics.fillRect(this.x, this.y + this.size - 2, this.size, 2);
                    }
                }
            }

            function movePiece(dx, dy) {
                if (gameOver || isPaused || !currentPiece) return;

                if (!checkCollision(currentPiece, dx, dy)) {
                    currentPiece.x += dx;
                    currentPiece.y += dy;
                    return true;
                }
                return false;
            }

            function rotatePiece() {
                // 移除旋转功能，不做任何操作
                return;
            }

            function rotateMatrix(matrix) {
                const rows = matrix.length;
                const cols = matrix[0].length;
                const rotated = [];

                for (let i = 0; i < cols; i++) {
                    rotated[i] = [];
                    for (let j = 0; j < rows; j++) {
                        rotated[i][j] = matrix[rows - 1 - j][i];
                    }
                }
                return rotated;
            }

            function checkCollision(piece, dx, dy) {
                const newX = piece.x + dx;
                const newY = piece.y + dy;

                for (let y = 0; y < piece.shape.length; y++) {
                    for (let x = 0; x < piece.shape[y].length; x++) {
                        if (piece.shape[y][x] !== 0) {
                            const gridX = newX + x;
                            const gridY = newY + y;

                            // 检查边界
                            if (gridX < 0 || gridX >= GRID_WIDTH || gridY >= GRID_HEIGHT) {
                                return true;
                            }

                            // 检查与已有方块的碰撞
                            if (gridY >= 0 && gameGrid[gridY][gridX] !== 0) {
                                return true;
                            }
                        }
                    }
                }
                return false;
            }

            function placePiece() {
                if (!currentPiece) return;

                // 停止快速下落
                fastDrop = false;

                // 创建物理小格子效果
                createPhysicsBlocks();

                // 不再将方块放置到网格中，所有方块都是物理的

                // 清空当前方块
                currentPiece = null;

                // 直接生成新方块，不需要消除行逻辑
                spawnPiece();
            }

            function createPhysicsBlocks() {
                // 为当前方块的每个格子创建4个物理小格子
                for (let y = 0; y < currentPiece.shape.length; y++) {
                    for (let x = 0; x < currentPiece.shape[y].length; x++) {
                        if (currentPiece.shape[y][x] !== 0) {
                            const gridX = currentPiece.x + x;
                            const gridY = currentPiece.y + y;

                            if (gridY >= 0) {
                                const pixelX = GRID_X + gridX * CELL_SIZE;
                                const pixelY = GRID_Y + gridY * CELL_SIZE;
                                const smallCellSize = CELL_SIZE / 2;

                                // 创建4个小格子
                                for (let row = 0; row < 2; row++) {
                                    for (let col = 0; col < 2; col++) {
                                        const smallX = pixelX + col * smallCellSize + 2;
                                        const smallY = pixelY + row * smallCellSize + 2;

                                        const block = new PhysicsBlock(
                                            smallX,
                                            smallY,
                                            COLORS[currentPiece.color],
                                            col,
                                            row
                                        );

                                        // 给小格子一个小的随机初始速度
                                        block.vx = (Math.random() - 0.5) * 2;
                                        block.vy = Math.random() * -1;

                                        physicsBlocks.push(block);
                                    }
                                }
                            }
                        }
                    }
                }
            }

            function clearLinesAnimated(callback) {
                // 找出所有满行
                let linesToClear = [];
                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    let isFullLine = true;
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] === 0) {
                            isFullLine = false;
                            break;
                        }
                    }
                    if (isFullLine) {
                        linesToClear.push(y);
                    }
                }

                if (linesToClear.length === 0) {
                    // 没有满行，直接回调
                    if (callback) callback();
                    return;
                }

                isClearingLines = true;
                clearingLines = linesToClear;

                // 播放消除音效
                // if (sound_clear) sound_clear.play();
                // 开始闪烁动画
                let flashCount = 0;
                const flashInterval = setInterval(() => {
                    flashCount++;
                    if (flashCount >= LINE_CLEAR_FLASH_COUNT * 2) {
                        clearInterval(flashInterval);
                        // 闪烁结束，开始下落动画
                        startFallAnimation(callback);
                    }
                }, LINE_CLEAR_FLASH_DURATION / (LINE_CLEAR_FLASH_COUNT * 2));

                // 更新分数
                updateScore(linesToClear.length);
            }

            function startFallAnimation(callback) {
                // 计算每行需要下落的距离
                fallDropDistances = new Array(GRID_HEIGHT).fill(0);
                let clearedCount = 0;
                fallingRows = [];
                // 从下往上计算下落距离
                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    if (clearingLines.includes(y)) {
                        clearedCount++;
                    } else {
                        fallDropDistances[y] = clearedCount;
                        if (clearedCount > 0) {
                            fallingRows.push(y);
                        }
                    }
                }

                // 不提前清空和重排gameGrid，动画期间只做视觉偏移

                // 开始整行下落动画
                if (fallingRows.length > 0) {
                    fallAnimationStartTime = Date.now();
                    const fallInterval = setInterval(() => {
                        const currentTime = Date.now();
                        const elapsed = currentTime - fallAnimationStartTime;
                        const progress = Math.min(elapsed / FALL_ANIMATION_DURATION, 1);

                        if (progress >= 1) {
                            clearInterval(fallInterval);
                            // 动画完成后，真正重排gameGrid
                            // 1. 清除被消除的行
                            let newGrid = [];
                            for (let y = 0; y < GRID_HEIGHT; y++) {
                                if (!clearingLines.includes(y)) {
                                    newGrid.push([...gameGrid[y]]);
                                }
                            }
                            // 2. 在顶部补空行
                            while (newGrid.length < GRID_HEIGHT) {
                                newGrid.unshift(new Array(GRID_WIDTH).fill(0));
                            }
                            gameGrid = newGrid;

                            // 新增弹球：每消除一行加一个弹球（动画结束后再加，避免重复）
                            // for (let i = 0; i < clearingLines.length; i++) {
                            //     balls.push(createBall());
                            // }

                            fallingRows = [];
                            fallDropDistances = [];
                            isClearingLines = false;
                            clearingLines = [];
                            if (callback) callback();
                        }
                    }, 16); // 约60fps
                } else {
                    // 没有需要下落的行
                    isClearingLines = false;
                    clearingLines = [];
                    if (callback) callback();
                }
            }

            function clearLines() {
                let linesCleared = 0;

                for (let y = GRID_HEIGHT - 1; y >= 0; y--) {
                    let isFullLine = true;
                    for (let x = 0; x < GRID_WIDTH; x++) {
                        if (gameGrid[y][x] === 0) {
                            isFullLine = false;
                            break;
                        }
                    }

                    if (isFullLine) {
                        // 移除满行
                        gameGrid.splice(y, 1);
                        // 在顶部添加新的空行
                        gameGrid.unshift(new Array(GRID_WIDTH).fill(0));
                        linesCleared++;
                        y++; // 重新检查当前行
                    }
                }

                if (linesCleared > 0) {
                    // 更新分数和等级
                    updateScore(linesCleared);
                }
            }

            function updateScore(linesCleared) {
                const linePoints = [0, 100, 300, 500, 800];
                score += linePoints[linesCleared] * level;
                lines += linesCleared;

                // 每10行提升一个等级
                const newLevel = Math.floor(lines / 10) + 1;
                if (newLevel > level) {
                    level = newLevel;
                    dropInterval = Math.max(100, 1000 - (level - 1) * 100);
                }

                // 更新UI
                if (scene && scene.scoreText && scene.linesText && scene.levelText) {
                    scene.scoreText.setText(`积分: ${score}`);
                    scene.linesText.setText(`行数: ${lines}`);
                    scene.levelText.setText(`等级: ${level}`);
                }
            }

            function hardDrop() {
                if (gameOver || isPaused || !currentPiece) return;

                // 直接在当前位置分解成物理小格子，不移动到预定位置
                createPhysicsBlocks();

                // 清空当前方块
                currentPiece = null;

                // 直接生成新方块
                spawnPiece();
            }

            function togglePause() {
                isPaused = !isPaused;
                if (isPaused) {
                    scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏暂停', {
                        fontSize: Math.floor(48) + 'px',
                        fill: '#ffff00',
                        fontStyle: 'bold'
                    }).setOrigin(0.5).setName('pauseText');
                    // 游戏暂停时停止背景音乐
                    // stopBgm();
                } else {
                    const pauseText = scene.children.getByName('pauseText');
                    if (pauseText) pauseText.destroy();
                    // 游戏继续时重新播放背景音乐
                    // if (sound_bgm) sound_bgm.play();
                }
            }

            function showGameOver() {
                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5, '游戏结束', {
                    fontSize: Math.floor(48) + 'px',
                    fill: '#ff0000',
                    fontStyle: 'bold'
                }).setOrigin(0.5).setName('gameOverText');

                scene.add.text(GAME_WIDTH * 0.5, GAME_HEIGHT * 0.5 + 60, '点击重新开始', {
                    fontSize: Math.floor(24) + 'px',
                    fill: '#ffffff'
                }).setOrigin(0.5).setName('restartText');

                scene.input.once('pointerdown', restartGame);
                // 游戏结束时停止背景音乐
                // stopBgm();
            }

            function restartGame() {
                score = 0;
                level = 1;
                lines = 0;
                dropTime = 0;
                dropInterval = 1000;
                gameOver = false;
                isPaused = false;

                // 重置顶部统计
                if (scene && scene.scoreText && scene.linesText && scene.levelText) {
                    scene.scoreText.setText('积分: 0');
                    scene.linesText.setText('行数: 0');
                    scene.levelText.setText('等级: 1');
                }

                initGrid();
                spawnPiece();

                // 清除游戏结束和重新开始文本
                const gameOverText = scene.children.getByName('gameOverText');
                if (gameOverText) gameOverText.destroy();
                const restartText = scene.children.getByName('restartText');
                if (restartText) restartText.destroy();
                // 重新开始游戏时重新播放背景音乐
                // if (sound_bgm) sound_bgm.play();
            }

            function update(time, delta) {
                if (gameOver || isPaused) return;

                dropTime += delta;

                // 快速下落时使用更短的间隔
                const currentDropInterval = fastDrop ? Math.max(50, dropInterval / 10) : dropInterval;

                if (dropTime >= currentDropInterval) {
                    if (currentPiece) {
                        if (!movePiece(0, 1)) {
                            placePiece();
                            // 快速下落结束时给予额外分数
                            if (fastDrop) {
                                score += 1;
                                // scoreText.setText('分数: ' + score);
                            }
                        } else if (fastDrop) {
                            // 快速下落时给予分数
                            score += 1;
                            // scoreText.setText('分数: ' + score);
                        }
                    }
                    dropTime = 0;
                }

                // 更新物理小格子
                updatePhysicsBlocks();

                // 多弹球运动与碰撞检测
                // for (let ball of balls) {
                //     // 运动
                //     ball.x += ball.vx;
                //     ball.y += ball.vy;

                //     // 边界反弹（只在格子区域内）
                //     if (ball.x - ball.radius < GRID_X) {
                //         ball.x = GRID_X + ball.radius;
                //         ball.vx *= -1;
                //     }
                //     if (ball.x + ball.radius > GRID_X + GRID_WIDTH * CELL_SIZE) {
                //         ball.x = GRID_X + GRID_WIDTH * CELL_SIZE - ball.radius;
                //         ball.vx *= -1;
                //     }
                //     if (ball.y - ball.radius < GRID_Y) {
                //         ball.y = GRID_Y + ball.radius;
                //         ball.vy *= -1;
                //     }
                //     if (ball.y + ball.radius > GRID_Y + GRID_HEIGHT * CELL_SIZE) {
                //         ball.y = GRID_Y + GRID_HEIGHT * CELL_SIZE - ball.radius;
                //         ball.vy *= -1;
                //     }

                //     // 与已停止方块的碰撞检测
                //     // 只检测 gameGrid，不检测 currentPiece
                //     let gridX = Math.floor((ball.x - GRID_X) / CELL_SIZE);
                //     let gridY = Math.floor((ball.y - GRID_Y) / CELL_SIZE);
                //     if (
                //         gridX >= 0 && gridX < GRID_WIDTH &&
                //         gridY >= 0 && gridY < GRID_HEIGHT &&
                //         gameGrid[gridY][gridX] !== 0
                //     ) {
                //         // 播放碰撞音效
                //         // if (sound_hit) sound_hit.play();
                //         // 消除方块
                //         let colorIdx = gameGrid[gridY][gridX];
                //         let color = COLORS[colorIdx];
                //         gameGrid[gridY][gridX] = 0;
                //         // 统计收集数量
                //         blockCollectCount[colorIdx]++;
                //         // 更新UI
                //         if (scene.blockStatTexts && scene.blockStatTexts[colorIdx]) {
                //             scene.blockStatTexts[colorIdx].setText(`${blockCollectCount[colorIdx]}`);
                //             // 抖动特效
                //             if (scene.blockStatRects && scene.blockStatRects[colorIdx]) {
                //                 let rect = scene.blockStatRects[colorIdx];
                //                 let text = scene.blockStatTexts[colorIdx];
                //                 scene.tweens.add({
                //                     targets: [rect, text],
                //                     scaleX: 1.3,
                //                     scaleY: 1.3,
                //                     duration: 80,
                //                     yoyo: true,
                //                     ease: 'Quad.easeInOut',
                //                     onComplete: () => {
                //                         rect.setScale(1,1);
                //                         text.setScale(1,1);
                //                     }
                //                 });
                //             }
                //         }
                //         // 反弹（简单处理：反转y速度）
                //         ball.vy *= -1;
                //         // 生成爆炸粒子特效
                //         spawnBlockParticles(gridX, gridY, color);
                //     }
                // }

                // 更新粒子特效
                // for (let i = effectParticles.length - 1; i >= 0; i--) {
                //     let p = effectParticles[i];
                //     p.x += p.vx;
                //     p.y += p.vy;
                //     p.life--;
                //     if (p.life <= 0) {
                //         effectParticles.splice(i, 1);
                //     }
                // }

                render();
            }

            function updatePhysicsBlocks() {
                // 更新所有物理小格子，传递所有方块用于碰撞检测
                for (let i = physicsBlocks.length - 1; i >= 0; i--) {
                    const block = physicsBlocks[i];
                    if (!block.update(physicsBlocks)) {
                        // 如果小格子生命结束，从数组中移除
                        physicsBlocks.splice(i, 1);
                    }
                }

                // 检查并消除相同颜色的聚集方块
                checkAndExplodeBlocks();
            }

            function checkAndExplodeBlocks() {
                // 找出所有静止的方块
                const stationaryBlocks = physicsBlocks.filter(block =>
                    Math.abs(block.vx) < 0.1 && Math.abs(block.vy) < 0.1
                );

                // 按颜色分组
                const colorGroups = {};
                stationaryBlocks.forEach(block => {
                    if (!colorGroups[block.color]) {
                        colorGroups[block.color] = [];
                    }
                    colorGroups[block.color].push(block);
                });

                // 检查每个颜色组是否有足够的相邻方块
                Object.keys(colorGroups).forEach(color => {
                    const blocks = colorGroups[color];
                    if (blocks.length >= 4) { // 至少4个相同颜色的方块
                        const connectedGroups = findConnectedBlocks(blocks);

                        // 消除连接数量>=4的组
                        connectedGroups.forEach(group => {
                            if (group.length >= 4) {
                                explodeBlocks(group);
                            }
                        });
                    }
                });
            }

            function findConnectedBlocks(blocks) {
                const visited = new Set();
                const groups = [];

                blocks.forEach(block => {
                    if (!visited.has(block)) {
                        const group = [];
                        const stack = [block];

                        while (stack.length > 0) {
                            const current = stack.pop();
                            if (visited.has(current)) continue;

                            visited.add(current);
                            group.push(current);

                            // 查找相邻的同色方块
                            blocks.forEach(other => {
                                if (!visited.has(other) && areAdjacent(current, other)) {
                                    stack.push(other);
                                }
                            });
                        }

                        groups.push(group);
                    }
                });

                return groups;
            }

            function areAdjacent(block1, block2) {
                const dx = Math.abs(block1.x - block2.x);
                const dy = Math.abs(block1.y - block2.y);
                const size = block1.size;

                // 相邻定义：水平或垂直相邻，允许一些容差
                return (dx <= size + 2 && dy <= 2) || (dy <= size + 2 && dx <= 2);
            }

            function explodeBlocks(blocks) {
                // 创建爆炸粒子效果
                blocks.forEach(block => {
                    createExplosionParticles(block.x + block.size/2, block.y + block.size/2, block.color);
                });

                // 从物理方块数组中移除这些方块
                blocks.forEach(blockToRemove => {
                    const index = physicsBlocks.indexOf(blockToRemove);
                    if (index > -1) {
                        physicsBlocks.splice(index, 1);
                    }
                });

                // 增加分数
                score += blocks.length * 10;
                if (scene && scene.scoreText) {
                    scene.scoreText.setText(`积分: ${score}`);
                }
            }

            function createExplosionParticles(x, y, color) {
                // 创建爆炸粒子效果
                for (let i = 0; i < 8; i++) {
                    const angle = (Math.PI * 2 * i) / 8;
                    const speed = 3 + Math.random() * 2;

                    const particle = new PhysicsBlock(x, y, color);
                    particle.vx = Math.cos(angle) * speed;
                    particle.vy = Math.sin(angle) * speed;
                    particle.size = particle.size * 0.5; // 更小的粒子
                    particle.life = 60; // 短生命周期
                    particle.maxLife = 60;
                    particle.isParticle = true; // 标记为粒子，不参与碰撞

                    physicsBlocks.push(particle);
                }
            }

            function render() {
                graphics.clear();

                // 快速下落时的背景效果
                if (fastDrop) {
                    graphics.fillStyle(0x444444, 0.3);
                    graphics.fillRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);
                }

                // 绘制游戏区域边框
                graphics.lineStyle(Math.floor(3), fastDrop ? 0x00ff00 : 0xffffff);
                graphics.strokeRect(GRID_X - 3, GRID_Y - 3, GRID_WIDTH * CELL_SIZE + 6, GRID_HEIGHT * CELL_SIZE + 6);

                // 绘制网格背景
                graphics.lineStyle(Math.max(1, Math.floor(1)), 0x333333);
                for (let x = 0; x <= GRID_WIDTH; x++) {
                    graphics.moveTo(GRID_X + x * CELL_SIZE, GRID_Y);
                    graphics.lineTo(GRID_X + x * CELL_SIZE, GRID_Y + GRID_HEIGHT * CELL_SIZE);
                }
                for (let y = 0; y <= GRID_HEIGHT; y++) {
                    graphics.moveTo(GRID_X, GRID_Y + y * CELL_SIZE);
                    graphics.lineTo(GRID_X + GRID_WIDTH * CELL_SIZE, GRID_Y + y * CELL_SIZE);
                }
                graphics.strokePath();

                // 修正：闪烁阶段，先正常渲染所有未被消除的方块，再高亮被消除行
                if (isClearingLines && clearingLines.length > 0) {
                    // 先画所有未被消除的方块
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        if (clearingLines.includes(y)) continue;
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[y][x] !== 0) {
                                drawCell(x, y, COLORS[gameGrid[y][x]]);
                            }
                        }
                    }
                    // 再画被消除行的闪烁效果
                    const flashTime = Date.now() % (LINE_CLEAR_FLASH_DURATION / LINE_CLEAR_FLASH_COUNT);
                    const isVisible = flashTime < (LINE_CLEAR_FLASH_DURATION / LINE_CLEAR_FLASH_COUNT / 2);
                    if (isVisible) {
                        clearingLines.forEach(y => {
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    drawCell(x, y, 0xffffff, 0.8);
                                }
                            }
                        });
                    }
                    // 闪烁阶段直接return，后续不渲染下落动画和普通方块
                    return;
                }

                // 2. 闪烁结束后，才渲染下落动画或普通方块
                // 绘制正在下落的行（动画期间用偏移量）
                if (fallingRows.length > 0 && fallAnimationStartTime > 0) {
                    const currentTime = Date.now();
                    const elapsed = currentTime - fallAnimationStartTime;
                    const progress = Math.min(elapsed / FALL_ANIMATION_DURATION, 1);
                    // 使用缓动函数让动画更自然
                    const easeProgress = 1 - Math.pow(1 - progress, 3);
                    // 先画所有未下落、未消除的行
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        if (
                            (!fallingRows.includes(y)) &&
                            (!clearingLines.includes(y))
                        ) {
                            for (let x = 0; x < GRID_WIDTH; x++) {
                                if (gameGrid[y][x] !== 0) {
                                    drawCell(x, y, COLORS[gameGrid[y][x]]);
                                }
                            }
                        }
                    }
                    // 再画下落的行（用偏移）
                    fallingRows.forEach(row => {
                        const dropDistance = fallDropDistances[row];
                        const currentDrop = dropDistance * easeProgress;
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[row][x] !== 0) {
                                drawCell(x, row + currentDrop, COLORS[gameGrid[row][x]]);
                            }
                        }
                    });
                } else {
                    // 非动画期间，正常绘制所有未消除的行
                    for (let y = 0; y < GRID_HEIGHT; y++) {
                        for (let x = 0; x < GRID_WIDTH; x++) {
                            if (gameGrid[y][x] !== 0) {
                                drawCell(x, y, COLORS[gameGrid[y][x]]);
                            }
                        }
                    }
                }

                // 绘制幽灵方块（预览位置）
                if (currentPiece) {
                    const ghostPiece = {
                        ...currentPiece,
                        y: currentPiece.y
                    };

                    // 计算幽灵方块位置
                    while (!checkCollision(ghostPiece, 0, 1)) {
                        ghostPiece.y++;
                    }

                    // 绘制幽灵方块
                    for (let y = 0; y < ghostPiece.shape.length; y++) {
                        for (let x = 0; x < ghostPiece.shape[y].length; x++) {
                            if (ghostPiece.shape[y][x] !== 0) {
                                const gridX = ghostPiece.x + x;
                                const gridY = ghostPiece.y + y;
                                if (gridY >= 0 && gridY !== currentPiece.y + y) {
                                    drawGhostCell(gridX, gridY, COLORS[ghostPiece.color]);
                                }
                            }
                        }
                    }
                }

                // 绘制当前方块
                if (currentPiece) {
                    for (let y = 0; y < currentPiece.shape.length; y++) {
                        for (let x = 0; x < currentPiece.shape[y].length; x++) {
                            if (currentPiece.shape[y][x] !== 0) {
                                const gridX = currentPiece.x + x;
                                const gridY = currentPiece.y + y;
                                if (gridY >= 0) {
                                    drawCell(gridX, gridY, COLORS[currentPiece.color]);
                                }
                            }
                        }
                    }
                }

                // 绘制下一个方块预览（右上角）
                if (nextPiece) {
                    const previewX = rightPanelX;
                    const previewY = 160;
                    const previewCellSize = Math.floor(20);
                    for (let y = 0; y < nextPiece.shape.length; y++) {
                        for (let x = 0; x < nextPiece.shape[y].length; x++) {
                            if (nextPiece.shape[y][x] !== 0) {
                                graphics.fillStyle(COLORS[nextPiece.color]);
                                graphics.fillRect(
                                    previewX + x * previewCellSize,
                                    previewY + y * previewCellSize,
                                    previewCellSize - 1,
                                    previewCellSize - 1
                                );
                            }
                        }
                    }
                }

                // 绘制粒子特效
                // for (let p of effectParticles) {
                //     graphics.fillStyle(p.color, Math.max(0, p.life / 24));
                //     graphics.fillRect(p.x - p.size / 2, p.y - p.size / 2, p.size, p.size);
                // }

                // 绘制所有弹球
                // for (let ball of balls) {
                //     graphics.fillStyle(ball.color);
                //     graphics.fillCircle(ball.x, ball.y, ball.radius);
                //     graphics.lineStyle(2, 0x00ffff);
                //     graphics.strokeCircle(ball.x, ball.y, ball.radius);
                // }

                // 绘制物理小格子
                for (let block of physicsBlocks) {
                    block.draw(graphics);
                }
            }

            function drawCell(x, y, color, alpha = 1) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 每个格子由4个小格子构成（2x2）
                const smallCellSize = CELL_SIZE / 2;
                const gap = 2; // 小格子之间的间隙

                // 绘制4个小格子
                for (let row = 0; row < 2; row++) {
                    for (let col = 0; col < 2; col++) {
                        const smallX = pixelX + col * smallCellSize + gap;
                        const smallY = pixelY + row * smallCellSize + gap;
                        const smallSize = smallCellSize - gap * 2;

                        // 绘制小格子主体
                        graphics.fillStyle(color, alpha);
                        graphics.fillRect(smallX, smallY, smallSize, smallSize);

                        // 简化的3D效果
                        const rgb = Phaser.Display.Color.IntegerToRGB(color);

                        // 绘制高光效果
                        const highlightColor = Phaser.Display.Color.GetColor32(
                            Math.min(255, rgb.r + 60),
                            Math.min(255, rgb.g + 60),
                            Math.min(255, rgb.b + 60),
                            255
                        );
                        graphics.fillStyle(highlightColor, alpha);
                        graphics.fillRect(smallX, smallY, smallSize, 2);
                        graphics.fillRect(smallX, smallY, 2, smallSize);

                        // 绘制阴影效果
                        const shadowColor = Phaser.Display.Color.GetColor32(
                            Math.max(0, rgb.r - 60),
                            Math.max(0, rgb.g - 60),
                            Math.max(0, rgb.b - 60),
                            255
                        );
                        graphics.fillStyle(shadowColor, alpha);
                        graphics.fillRect(smallX + smallSize - 2, smallY, 2, smallSize);
                        graphics.fillRect(smallX, smallY + smallSize - 2, smallSize, 2);
                    }
                }
            }

            function drawGhostCell(x, y, color) {
                const pixelX = GRID_X + x * CELL_SIZE;
                const pixelY = GRID_Y + y * CELL_SIZE;

                // 每个幽灵格子也由4个小格子构成（2x2）
                const smallCellSize = CELL_SIZE / 2;
                const gap = 2;

                // 绘制4个小幽灵格子
                for (let row = 0; row < 2; row++) {
                    for (let col = 0; col < 2; col++) {
                        const smallX = pixelX + col * smallCellSize + gap;
                        const smallY = pixelY + row * smallCellSize + gap;
                        const smallSize = smallCellSize - gap * 2;

                        // 绘制更低透明度的幽灵小方块
                        graphics.fillStyle(color, 0.3);
                        graphics.fillRect(smallX, smallY, smallSize, smallSize);

                        // 绘制边框
                        graphics.lineStyle(1, color, 0.5);
                        graphics.strokeRect(smallX, smallY, smallSize, smallSize);
                    }
                }
            }

            // 生成爆炸粒子特效
            // function spawnBlockParticles(gridX, gridY, color) {
            //     const centerX = GRID_X + gridX * CELL_SIZE + CELL_SIZE / 2;
            //     const centerY = GRID_Y + gridY * CELL_SIZE + CELL_SIZE / 2;
            //     for (let i = 0; i < 10; i++) {
            //         const angle = Math.random() * Math.PI * 2;
            //         const speed = 2 + Math.random() * 2;
            //         effectParticles.push({
            //             x: centerX,
            //             y: centerY,
            //             vx: Math.cos(angle) * speed,
            //             vy: Math.sin(angle) * speed,
            //             color: color,
            //             size: 6 + Math.random() * 4,
            //             life: 18 + Math.floor(Math.random() * 8)
            //         });
            //     }
            // }

            // 游戏暂停/结束时停止背景音乐
            // function stopBgm() {
            //     if (sound_bgm && sound_bgm.isPlaying) sound_bgm.stop();
            // }
            // 在showGameOver和togglePause里可调用stopBgm()，如需暂停音乐

            // 启动游戏
            const game = new Phaser.Game(config);
        });
    </script>
</body>
</html>
