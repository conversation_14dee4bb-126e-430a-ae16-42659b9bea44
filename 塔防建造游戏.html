<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>塔防建造游戏</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.70.0/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: linear-gradient(135deg, #2d5016 0%, #1a3009 100%);
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            font-family: 'Arial', sans-serif;
            overflow: hidden;
        }

        #game-container {
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <div id="game-container"></div>

    <script>
        // 游戏配置
        const GAME_WIDTH = 750;
        const GAME_HEIGHT = 1334;
        const TILE_SIZE = 60;
        const TOWER_SLOT_COUNT = 5;
        const GRID_SIZE = 7;
        
        // 防御塔类型
        const TOWER_TYPES = [
            { color: 0x8B4513, emoji: '🏹', name: '弓箭塔', cost: 50 },
            { color: 0x4169E1, emoji: '🔮', name: '法师塔', cost: 80 },
            { color: 0xFF4500, emoji: '🔥', name: '火炮塔', cost: 120 },
            { color: 0x32CD32, emoji: '⚡', name: '闪电塔', cost: 100 },
            { color: 0x9932CC, emoji: '❄️', name: '冰霜塔', cost: 90 }
        ];
        
        // 敌人类型
        const ENEMY_TYPES = [
            { color: 0xFF0000, emoji: '👹', name: '小恶魔', hp: 30, speed: 1.5 },
            { color: 0x8B0000, emoji: '🧟', name: '僵尸', hp: 50, speed: 1.0 },
            { color: 0x006400, emoji: '🐲', name: '小龙', hp: 80, speed: 2.0 },
            { color: 0x4B0082, emoji: '👻', name: '幽灵', hp: 40, speed: 2.5 },
            { color: 0xFF8C00, emoji: '🦅', name: '飞鹰', hp: 25, speed: 3.0 }
        ];

        class GameScene extends Phaser.Scene {
            constructor() {
                super({ key: 'GameScene' });
                this.buildGrid = [];
                this.towerSlots = [];
                this.enemies = [];
                this.towers = [];
                this.gold = 200;
                this.wave = 1;
                this.enemiesKilled = 0;
                this.isWaveActive = false;
                this.selectedTowerType = null;
            }

            preload() {
                this.createTowerTextures();
                this.createEnemyTextures();
            }

            create() {
                // 设置背景（草地色调）
                this.add.rectangle(GAME_WIDTH/2, GAME_HEIGHT/2, GAME_WIDTH, GAME_HEIGHT, 0x228B22);

                // 创建UI
                this.createUI();
                
                // 创建敌人区域（显示小怪）
                this.createEnemyArea();

                // 创建防御塔选择区域
                this.createTowerSlotArea();

                // 创建建造网格
                this.createBuildGrid();

                // 创建控制按钮区域
                this.createControlArea();

                // 初始化游戏
                this.initializeGame();

                // 设置输入事件
                this.input.on('pointerdown', this.onGridClick, this);
            }

            createUI() {
                // 创建金币显示（右上角）
                this.goldText = this.add.text(GAME_WIDTH - 30, 30, '🪙 200', {
                    fontSize: '36px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(1, 0);

                // 创建波次显示（屏幕中央上方）
                this.waveText = this.add.text(GAME_WIDTH/2, 50, '第1波', {
                    fontSize: '48px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFD700',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建击杀数显示
                this.killText = this.add.text(30, 100, '击杀: 0', {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#ffffff',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0, 0);
            }

            createTowerTextures() {
                TOWER_TYPES.forEach((towerType, index) => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(towerType.color);
                    graphics.fillRoundedRect(0, 0, TILE_SIZE, TILE_SIZE, 8);
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeRoundedRect(2, 2, TILE_SIZE-4, TILE_SIZE-4, 6);
                    graphics.generateTexture(`tower_${index}`, TILE_SIZE, TILE_SIZE);
                    graphics.destroy();
                });
            }

            createEnemyTextures() {
                ENEMY_TYPES.forEach((enemyType, index) => {
                    const graphics = this.add.graphics();
                    graphics.fillStyle(enemyType.color);
                    graphics.fillCircle(25, 25, 20);
                    graphics.lineStyle(2, 0x000000, 0.3);
                    graphics.strokeCircle(25, 25, 18);
                    graphics.generateTexture(`enemy_${index}`, 50, 50);
                    graphics.destroy();
                });
            }

            createEnemyArea() {
                // 清理之前的敌人
                if (this.enemies) {
                    this.enemies.forEach(enemy => {
                        if (enemy.sprite) enemy.sprite.destroy();
                    });
                }

                // 敌人显示区域
                const enemyAreaY = 180;
                const enemyAreaHeight = 200;
                
                // 创建敌人区域背景
                const enemyBg = this.add.graphics();
                enemyBg.fillStyle(0x8B0000, 0.2);
                enemyBg.fillRoundedRect(40, enemyAreaY, GAME_WIDTH - 80, enemyAreaHeight, 15);
                enemyBg.lineStyle(2, 0x8B0000, 0.5);
                enemyBg.strokeRoundedRect(40, enemyAreaY, GAME_WIDTH - 80, enemyAreaHeight, 15);

                // 创建敌人标题
                this.add.text(GAME_WIDTH/2, enemyAreaY - 20, '即将到来的敌人', {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FF4444',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 重置敌人数组
                this.enemies = [];

                // 根据波次生成敌人预览
                this.generateEnemyPreview(enemyAreaY + 20, enemyAreaHeight - 40);
            }

            generateEnemyPreview(startY, areaHeight) {
                // 根据当前波次确定敌人数量和类型
                const enemyCount = Math.min(5 + this.wave * 2, 20);
                const rows = 3;
                const cols = Math.ceil(enemyCount / rows);
                const enemySize = 40;
                const spacing = 10;
                
                const totalWidth = cols * enemySize + (cols - 1) * spacing;
                const startX = (GAME_WIDTH - totalWidth) / 2;

                for (let i = 0; i < enemyCount; i++) {
                    const row = i % rows;
                    const col = Math.floor(i / rows);
                    
                    const x = startX + col * (enemySize + spacing);
                    const y = startY + row * (enemySize + spacing);
                    
                    // 随机选择敌人类型
                    const enemyTypeIndex = this.getEnemyTypeForWave(this.wave);
                    const enemyType = ENEMY_TYPES[enemyTypeIndex];
                    
                    // 创建敌人预览
                    const enemyContainer = this.add.container(x + enemySize/2, y + enemySize/2);
                    
                    const enemyBg = this.add.image(0, 0, `enemy_${enemyTypeIndex}`);
                    enemyBg.setScale(0.8);
                    
                    const enemyEmoji = this.add.text(0, 0, enemyType.emoji, {
                        fontSize: '24px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);
                    
                    enemyContainer.add([enemyBg, enemyEmoji]);
                    
                    this.enemies.push({
                        sprite: enemyContainer,
                        type: enemyTypeIndex,
                        hp: enemyType.hp,
                        maxHp: enemyType.hp,
                        speed: enemyType.speed,
                        x: x + enemySize/2,
                        y: y + enemySize/2,
                        isAlive: true
                    });
                }
            }

            getEnemyTypeForWave(wave) {
                if (wave <= 2) {
                    return Phaser.Math.Between(0, 1);
                } else if (wave <= 4) {
                    return Phaser.Math.Between(0, 2);
                } else if (wave <= 6) {
                    return Phaser.Math.Between(0, 3);
                } else {
                    return Phaser.Math.Between(0, 4);
                }
            }

            createTowerSlotArea() {
                // 防御塔选择区域
                const slotAreaY = 420;

                // 创建防御塔选择区域背景
                const slotBg = this.add.graphics();
                slotBg.fillStyle(0x8B4513, 0.3);
                slotBg.fillRoundedRect(40, slotAreaY - 40, GAME_WIDTH - 80, 80, 15);
                slotBg.lineStyle(2, 0x8B4513, 0.8);
                slotBg.strokeRoundedRect(40, slotAreaY - 40, GAME_WIDTH - 80, 80, 15);

                // 创建标题
                this.add.text(GAME_WIDTH/2, slotAreaY - 60, '选择防御塔', {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#8B4513',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 创建防御塔槽位
                this.towerSlots = [];
                const totalSlotWidth = TOWER_SLOT_COUNT * TILE_SIZE + (TOWER_SLOT_COUNT - 1) * 10;
                const startX = (GAME_WIDTH - totalSlotWidth) / 2 + TILE_SIZE / 2;

                for (let i = 0; i < TOWER_SLOT_COUNT; i++) {
                    const slotX = startX + i * (TILE_SIZE + 10);
                    const slotY = slotAreaY;
                    const towerType = TOWER_TYPES[i];

                    // 创建防御塔槽位容器
                    const slotContainer = this.add.container(slotX, slotY);

                    // 槽位背景
                    const slotBg = this.add.image(0, 0, `tower_${i}`);
                    slotBg.setInteractive();

                    // 防御塔表情
                    const towerEmoji = this.add.text(0, 0, towerType.emoji, {
                        fontSize: '32px',
                        fontFamily: 'Arial, sans-serif',
                        resolution: 2
                    }).setOrigin(0.5);

                    // 价格显示
                    const costText = this.add.text(0, TILE_SIZE/2 + 15, `${towerType.cost}`, {
                        fontSize: '16px',
                        fontFamily: 'Arial, sans-serif',
                        color: '#FFD700',
                        fontStyle: 'bold',
                        resolution: 2
                    }).setOrigin(0.5);

                    slotContainer.add([slotBg, towerEmoji, costText]);

                    const slot = {
                        container: slotContainer,
                        background: slotBg,
                        towerType: i,
                        cost: towerType.cost,
                        isSelected: false
                    };

                    // 添加点击事件
                    slotBg.on('pointerdown', () => {
                        this.selectTowerType(i);
                    });

                    // 添加悬停效果
                    slotBg.on('pointerover', () => {
                        if (this.gold >= towerType.cost) {
                            slotContainer.setScale(1.1);
                        }
                    });

                    slotBg.on('pointerout', () => {
                        slotContainer.setScale(1.0);
                    });

                    this.towerSlots.push(slot);
                }
            }

            selectTowerType(towerTypeIndex) {
                const towerType = TOWER_TYPES[towerTypeIndex];

                // 检查金币是否足够
                if (this.gold < towerType.cost) {
                    this.showMessage('金币不足！', '#FF4444');
                    return;
                }

                // 重置所有槽位选择状态
                this.towerSlots.forEach(slot => {
                    slot.isSelected = false;
                    slot.container.setAlpha(1.0);
                });

                // 选中当前槽位
                this.towerSlots[towerTypeIndex].isSelected = true;
                this.towerSlots[towerTypeIndex].container.setAlpha(0.7);
                this.selectedTowerType = towerTypeIndex;

                this.showMessage(`已选择 ${towerType.name}`, '#44FF44');
            }

            createBuildGrid() {
                // 创建7x7建造网格
                this.buildGrid = [];
                const buildAreaY = 520;
                const buildAreaHeight = 420;
                const gridSpacing = (GAME_WIDTH - 80) / GRID_SIZE;
                const startX = 40 + gridSpacing / 2;
                const startY = buildAreaY + 20 + gridSpacing / 2;

                // 创建网格背景
                const gridBg = this.add.graphics();
                gridBg.fillStyle(0x90EE90, 0.3);
                gridBg.fillRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);
                gridBg.lineStyle(2, 0x228B22, 0.8);
                gridBg.strokeRoundedRect(40, buildAreaY, GAME_WIDTH - 80, buildAreaHeight, 15);

                for (let row = 0; row < GRID_SIZE; row++) {
                    this.buildGrid[row] = [];
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const x = startX + col * gridSpacing;
                        const y = startY + row * gridSpacing;

                        // 创建网格格子
                        const gridCell = this.add.graphics();
                        gridCell.fillStyle(0xFFFFFF, 0.1);
                        gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        gridCell.lineStyle(1, 0x228B22, 0.5);
                        gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                        // 设置交互
                        gridCell.setInteractive(new Phaser.Geom.Rectangle(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE), Phaser.Geom.Rectangle.Contains);

                        const cell = {
                            x: x,
                            y: y,
                            row: row,
                            col: col,
                            background: gridCell,
                            tower: null,
                            isEmpty: true
                        };

                        // 添加悬停效果
                        gridCell.on('pointerover', () => {
                            if (cell.isEmpty && this.selectedTowerType !== null) {
                                gridCell.clear();
                                gridCell.fillStyle(0x90EE90, 0.6);
                                gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                gridCell.lineStyle(2, 0x228B22, 0.8);
                                gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            }
                        });

                        gridCell.on('pointerout', () => {
                            if (cell.isEmpty) {
                                gridCell.clear();
                                gridCell.fillStyle(0xFFFFFF, 0.1);
                                gridCell.fillRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                                gridCell.lineStyle(1, 0x228B22, 0.5);
                                gridCell.strokeRoundedRect(x - TILE_SIZE/2, y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                            }
                        });

                        this.buildGrid[row][col] = cell;
                    }
                }
            }

            createControlArea() {
                // 控制按钮区域（在屏幕最下方）
                const controlAreaY = GAME_HEIGHT - 100;
                const buttonWidth = 120;
                const buttonHeight = 60;
                const buttonSpacing = 40;
                const totalButtonsWidth = 2 * buttonWidth + buttonSpacing;
                const startButtonX = (GAME_WIDTH - totalButtonsWidth) / 2;

                // 创建开始波次按钮
                const startWaveButton = this.createControlButton(
                    startButtonX, controlAreaY, buttonWidth, buttonHeight,
                    '开始波次', 0x4CAF50, () => this.startWave()
                );

                // 创建重置按钮
                const resetButton = this.createControlButton(
                    startButtonX + buttonWidth + buttonSpacing, controlAreaY, buttonWidth, buttonHeight,
                    '重置', 0xFF5722, () => this.resetGame()
                );

                this.controlButtons = {
                    startWave: startWaveButton,
                    reset: resetButton
                };
            }

            createControlButton(x, y, width, height, text, color, callback) {
                // 创建按钮背景
                const buttonBg = this.add.graphics();
                buttonBg.fillStyle(color);
                buttonBg.fillRoundedRect(x, y, width, height, 10);
                buttonBg.lineStyle(3, color - 0x333333);
                buttonBg.strokeRoundedRect(x, y, width, height, 10);

                // 设置按钮可交互
                buttonBg.setInteractive(new Phaser.Geom.Rectangle(x, y, width, height), Phaser.Geom.Rectangle.Contains);

                // 创建按钮文字
                const buttonText = this.add.text(x + width/2, y + height/2, text, {
                    fontSize: '20px',
                    fontFamily: 'Arial, sans-serif',
                    color: '#FFFFFF',
                    fontStyle: 'bold',
                    resolution: 2
                }).setOrigin(0.5);

                // 添加点击事件
                buttonBg.on('pointerdown', callback);

                return {
                    background: buttonBg,
                    text: buttonText,
                    destroy: function() {
                        buttonBg.destroy();
                        buttonText.destroy();
                    }
                };
            }

            showMessage(text, color = '#FFFFFF') {
                // 显示临时消息
                const message = this.add.text(GAME_WIDTH/2, 150, text, {
                    fontSize: '24px',
                    fontFamily: 'Arial, sans-serif',
                    color: color,
                    fontStyle: 'bold',
                    backgroundColor: '#000000',
                    padding: { x: 15, y: 8 },
                    resolution: 2
                }).setOrigin(0.5);
                message.setDepth(1000);

                // 2秒后消失
                this.time.delayedCall(2000, () => {
                    if (message) {
                        this.tweens.add({
                            targets: message,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                message.destroy();
                            }
                        });
                    }
                });
            }

            initializeGame() {
                // 重置游戏状态
                this.gold = 200;
                this.wave = 1;
                this.enemiesKilled = 0;
                this.isWaveActive = false;
                this.selectedTowerType = null;

                // 更新UI
                this.goldText.setText(`🪙 ${this.gold}`);
                this.waveText.setText(`第${this.wave}波`);
                this.killText.setText(`击杀: ${this.enemiesKilled}`);

                // 清理已建造的塔
                this.towers.forEach(tower => {
                    if (tower.container) tower.container.destroy();
                });
                this.towers = [];

                // 重置建造网格
                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        if (cell.tower) {
                            cell.tower.container.destroy();
                            cell.tower = null;
                        }
                        cell.isEmpty = true;

                        // 重置格子背景
                        cell.background.clear();
                        cell.background.fillStyle(0xFFFFFF, 0.1);
                        cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                        cell.background.lineStyle(1, 0x228B22, 0.5);
                        cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                    }
                }

                // 重置防御塔选择状态
                this.towerSlots.forEach(slot => {
                    slot.isSelected = false;
                    slot.container.setAlpha(1.0);
                });

                // 重新生成敌人预览
                this.createEnemyArea();
            }

            onGridClick(pointer) {
                if (this.isWaveActive) return;

                // 找到被点击的建造格子
                let clickedCell = null;

                for (let row = 0; row < GRID_SIZE; row++) {
                    for (let col = 0; col < GRID_SIZE; col++) {
                        const cell = this.buildGrid[row][col];
                        if (cell.background.getBounds().contains(pointer.x, pointer.y)) {
                            clickedCell = cell;
                            break;
                        }
                    }
                    if (clickedCell) break;
                }

                if (clickedCell && clickedCell.isEmpty && this.selectedTowerType !== null) {
                    this.buildTower(clickedCell);
                }
            }

            buildTower(cell) {
                const towerType = TOWER_TYPES[this.selectedTowerType];

                // 检查金币是否足够
                if (this.gold < towerType.cost) {
                    this.showMessage('金币不足！', '#FF4444');
                    return;
                }

                // 扣除金币
                this.gold -= towerType.cost;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 创建防御塔
                const towerContainer = this.add.container(cell.x, cell.y);

                // 防御塔背景
                const towerBg = this.add.image(0, 0, `tower_${this.selectedTowerType}`);

                // 防御塔表情
                const towerEmoji = this.add.text(0, 0, towerType.emoji, {
                    fontSize: '32px',
                    fontFamily: 'Arial, sans-serif',
                    resolution: 2
                }).setOrigin(0.5);

                towerContainer.add([towerBg, towerEmoji]);

                // 更新格子状态
                cell.isEmpty = false;
                cell.tower = {
                    container: towerContainer,
                    type: this.selectedTowerType,
                    damage: 20 + this.selectedTowerType * 10,
                    range: 80 + this.selectedTowerType * 20,
                    fireRate: 1000 - this.selectedTowerType * 100
                };

                // 更新格子背景
                cell.background.clear();
                cell.background.fillStyle(0x8B4513, 0.3);
                cell.background.fillRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);
                cell.background.lineStyle(2, 0x8B4513, 0.8);
                cell.background.strokeRoundedRect(cell.x - TILE_SIZE/2, cell.y - TILE_SIZE/2, TILE_SIZE, TILE_SIZE, 5);

                // 添加建造动画
                towerContainer.setScale(0);
                this.tweens.add({
                    targets: towerContainer,
                    scaleX: 1,
                    scaleY: 1,
                    duration: 300,
                    ease: 'Back.easeOut'
                });

                this.showMessage(`建造了 ${towerType.name}！`, '#44FF44');

                // 重置选择状态
                this.selectedTowerType = null;
                this.towerSlots.forEach(slot => {
                    slot.isSelected = false;
                    slot.container.setAlpha(1.0);
                });
            }

            startWave() {
                if (this.isWaveActive) return;

                this.isWaveActive = true;
                this.showMessage(`第${this.wave}波开始！`, '#FFD700');

                // 开始敌人移动和战斗逻辑
                this.spawnEnemies();
            }

            spawnEnemies() {
                // 简化的战斗逻辑：直接模拟战斗结果
                this.time.delayedCall(3000, () => {
                    this.endWave();
                });
            }

            endWave() {
                this.isWaveActive = false;

                // 给予奖励金币
                const reward = 50 + this.wave * 10;
                this.gold += reward;
                this.goldText.setText(`🪙 ${this.gold}`);

                // 增加击杀数
                this.enemiesKilled += this.enemies.length;
                this.killText.setText(`击杀: ${this.enemiesKilled}`);

                this.showMessage(`波次完成！获得 ${reward} 金币`, '#44FF44');

                // 进入下一波
                this.wave++;
                this.waveText.setText(`第${this.wave}波`);

                // 重新生成敌人预览
                this.createEnemyArea();
            }

            resetGame() {
                this.initializeGame();
                this.showMessage('游戏已重置', '#FFFF44');
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: GAME_WIDTH,
            height: GAME_HEIGHT,
            parent: 'game-container',
            backgroundColor: '#228B22',
            scene: GameScene,
            scale: {
                mode: Phaser.Scale.FIT,
                autoCenter: Phaser.Scale.CENTER_BOTH
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>
