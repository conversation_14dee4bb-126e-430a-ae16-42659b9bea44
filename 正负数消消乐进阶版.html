<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正负数拖拽消消乐</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/phaser/3.70.0/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Arial', sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
        }
        .game-title {
            color: white;
            font-size: 2.5em;
            font-weight: bold;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
            margin-bottom: 20px;
        }
        .game-info {
            color: white;
            text-align: center;
            margin-top: 30px;
            background: rgba(255,255,255,0.1);
            padding: 10px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }
        #game-container {
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            overflow: hidden;
        }
    </style>
</head>
<body>
    <h1 class="game-title">正负数拖拽消消乐 🎮</h1>
   
    <div id="game-container"></div>
    <div class="game-info">
        <p>🎯 游戏规则：拖拽选择数字，让它们相加等于0来消除！
        💡 最多同时选择5个数字 | 🎲 支持分数和小数</p>
    </div>
    <script>
        class NumberDragGame extends Phaser.Scene {
            constructor() {
                super({ key: 'NumberDragGame' });
                this.gridSize = 8;
                this.tileSize = 70;
                this.grid = [];
                this.selectedTiles = [];
                this.isDragging = false;
                this.score = 0;
                this.moves = 0;
                this.tilesRemaining = 64;
                this.gameStartTime = 0;
                this.gameWon = false;
                this.selectionPath = [];
                this.maxSelection = 5;
                
                // 更复杂的数字池，包含分数和小数
                this.numberPool = [
                    { value: 1, display: '1' },
                    { value: -1, display: '-1' },
                    { value: 2, display: '2' },
                    { value: -2, display: '-2' },
                    { value: 3, display: '3' },
                    { value: -3, display: '-3' },
                    { value: 0.5, display: '0.5' },
                    { value: -0.5, display: '-0.5' },
                    { value: 1.5, display: '1.5' },
                    { value: -1.5, display: '-1.5' },
                    { value: 2.5, display: '2.5' },
                    { value: -2.5, display: '-2.5' },
                    { value: 0.25, display: '1/4' },
                    { value: -0.25, display: '-1/4' },
                    { value: 0.75, display: '3/4' },
                    { value: -0.75, display: '-3/4' },
                    { value: 1/3, display: '1/3' },
                    { value: -1/3, display: '-1/3' },
                    { value: 2/3, display: '2/3' },
                    { value: -2/3, display: '-2/3' }
                ];
            }

            preload() {
                this.createNumberTextures();
            }

            create() {
                // 设置背景
                this.add.rectangle(450, 375, 900, 750, 0x2c3e50);
                
                // 创建信息面板背景
                this.add.rectangle(450, 60, 880, 60, 0x34495e);

                // 显示信息
                this.scoreText = this.add.text(80, 60, '分数: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                
                this.movesText = this.add.text(250, 60, '移动: 0', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.tilesText = this.add.text(420, 60, '剩余: 64', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                this.timeText = this.add.text(590, 60, '时间: 0:00', {
                    fontSize: '22px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold'
                }).setOrigin(0.5);

                // 当前选择的数字和总和显示
                this.selectionText = this.add.text(450, 680, '当前选择: 无', {
                    fontSize: '18px',
                    fill: '#ecf0f1',
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    padding: { x: 10, y: 5 }
                }).setOrigin(0.5);

                // 刷新按钮
                const refreshBtn = this.add.rectangle(770, 60, 140, 45, 0xe74c3c);
                refreshBtn.setStrokeStyle(3, 0xc0392b);
                this.add.text(770, 60, '刷新', {
                    fontSize: '18px',
                    fill: '#ffffff',
                    fontStyle: 'bold'
                }).setOrigin(0.5);
                refreshBtn.setInteractive();
                refreshBtn.on('pointerdown', () => this.shuffleGrid());

                // 初始化游戏
                this.initializeGrid();
                this.createGrid();
                
                // 设置输入事件
                this.input.on('pointerup', () => this.endDrag());
                
                // 开始计时
                this.gameStartTime = this.time.now;
                this.timeEvent = this.time.addEvent({
                    delay: 1000,
                    callback: this.updateTime,
                    callbackScope: this,
                    loop: true
                });
            }

            createNumberTextures() {
                const graphics = this.add.graphics();
                
                // 创建正数纹理（蓝色系）
                graphics.clear();
                graphics.fillStyle(0x3498db);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(4, 0x2980b9);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.fillGradientStyle(0x5dade2, 0x3498db, 0x2980b9, 0x1f3a93, 1);
                graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                graphics.generateTexture('positive', this.tileSize - 6, this.tileSize - 6);

                // 创建负数纹理（红色系）
                graphics.clear();
                graphics.fillStyle(0xe74c3c);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(4, 0xc0392b);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.fillGradientStyle(0xff6b6b, 0xe74c3c, 0xc0392b, 0xa93226, 1);
                graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                graphics.generateTexture('negative', this.tileSize - 6, this.tileSize - 6);

                // 创建零纹理（绿色系）
                graphics.clear();
                graphics.fillStyle(0x27ae60);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(4, 0x229954);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.fillGradientStyle(0x58d68d, 0x27ae60, 0x229954, 0x186a3b, 1);
                graphics.fillRoundedRect(2, 2, this.tileSize - 10, this.tileSize - 10, 10);
                graphics.generateTexture('zero', this.tileSize - 6, this.tileSize - 6);

                // 创建选中效果纹理
                graphics.clear();
                graphics.fillStyle(0xf1c40f);
                graphics.fillRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.lineStyle(6, 0xf39c12);
                graphics.strokeRoundedRect(0, 0, this.tileSize - 6, this.tileSize - 6, 12);
                graphics.generateTexture('selected', this.tileSize - 6, this.tileSize - 6);

                graphics.destroy();
            }

            initializeGrid() {
                this.grid = [];
                
                for (let row = 0; row < this.gridSize; row++) {
                    this.grid[row] = [];
                    for (let col = 0; col < this.gridSize; col++) {
                        const randomNumber = Phaser.Utils.Array.GetRandom(this.numberPool);
                        this.grid[row][col] = {
                            value: randomNumber.value,
                            display: randomNumber.display,
                            sprite: null,
                            text: null,
                            isEmpty: false,
                            row: row,
                            col: col,
                            isSelected: false
                        };
                    }
                }
            }

            createGrid() {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;

                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        const tile = this.grid[row][col];
                        if (!tile.isEmpty) {
                            this.createTile(tile, startX, startY);
                        }
                    }
                }
            }

            createTile(tile, startX, startY) {
                const x = startX + tile.col * this.tileSize + this.tileSize / 2;
                const y = startY + tile.row * this.tileSize + this.tileSize / 2;

                let textureName;
                if (tile.value > 0) {
                    textureName = 'positive';
                } else if (tile.value < 0) {
                    textureName = 'negative';
                } else {
                    textureName = 'zero';
                }

                const sprite = this.add.image(x, y, textureName);
                
                // 添加数字文本
                const text = this.add.text(x, y, tile.display, {
                    fontSize: '16px',
                    fill: '#ffffff',
                    fontStyle: 'bold',
                    fontFamily: 'Arial, sans-serif',
                    stroke: tile.value >= 0 ? '#2980b9' : '#c0392b',
                    strokeThickness: 1,
                    shadow: {
                        offsetX: 1,
                        offsetY: 1,
                        color: '#000000',
                        blur: 2,
                        fill: true
                    }
                }).setOrigin(0.5);

                // 设置交互事件
                this.setupTileInteraction(sprite, tile);
                
                tile.sprite = sprite;
                tile.text = text;
                tile.isSelected = false; // 确保重置选择状态
            }

            setupTileInteraction(sprite, tile) {
                sprite.setInteractive();
                sprite.removeAllListeners(); // 移除之前的监听器
                
                sprite.on('pointerdown', () => this.startDrag(tile));
                sprite.on('pointerover', () => {
                    if (this.isDragging) {
                        this.addToSelection(tile);
                    }
                    if (!tile.isEmpty && !tile.isSelected) {
                        sprite.setScale(1.05);
                    }
                });
                sprite.on('pointerout', () => {
                    if (!tile.isEmpty && !tile.isSelected) {
                        sprite.setScale(1);
                    }
                });
            }

            startDrag(tile) {
                if (tile.isEmpty) return;
                
                this.isDragging = true;
                this.clearSelection(); // 先清除之前的选择
                this.selectedTiles = [];
                this.selectionPath = [];
                this.addToSelection(tile);
            }

            addToSelection(tile) {
                if (tile.isEmpty || tile.isSelected || this.selectedTiles.length >= this.maxSelection) {
                    return;
                }

                // 检查是否相邻（只有第一个可以任意选择）
                if (this.selectedTiles.length > 0) {
                    const lastTile = this.selectedTiles[this.selectedTiles.length - 1];
                    if (!this.isAdjacent(lastTile, tile)) {
                        return;
                    }
                }

                this.selectedTiles.push(tile);
                tile.isSelected = true;
                if (tile.sprite) {
                    // 在JavaScript中，颜色值需要用十六进制数字表示，而不是以 # 开头
                    tile.sprite.setTint(0xFF00FF);
                    tile.sprite.setScale(1.2);
                    // tile.sprite.setPipeline('Light2D');
                    // tile.sprite.pipeline.setFloat('intensity', 0.8);
                }
                
                this.updateSelectionDisplay();
            }

            isAdjacent(tile1, tile2) {
                const rowDiff = Math.abs(tile1.row - tile2.row);
                const colDiff = Math.abs(tile1.col - tile2.col);
                return (rowDiff === 1 && colDiff === 0) || (rowDiff === 0 && colDiff === 1);
            }

            endDrag() {
                if (!this.isDragging) return;
                
                this.isDragging = false;
                
                // 检查选中的数字是否相加等于0
                const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);
                
                if (Math.abs(sum) < 0.001 && this.selectedTiles.length > 1) { // 考虑浮点数精度
                    this.eliminateSelectedTiles();
                } else {
                    this.clearSelection();
                    if (this.selectedTiles.length > 1) {
                        this.showMessage(`总和不为0 (${sum.toFixed(3)})，请重新选择！`, 0xff0000);
                    }
                }
            }

            eliminateSelectedTiles() {
                // 计算分数
                const bonus = this.selectedTiles.length * 10;
                this.score += bonus;
                
                // 添加消除动画
                this.selectedTiles.forEach(tile => {
                    if (tile.sprite && tile.text) {
                        this.tweens.add({
                            targets: [tile.sprite, tile.text],
                            scaleX: 0,
                            scaleY: 0,
                            alpha: 0,
                            duration: 300,
                            ease: 'Power2',
                            onComplete: () => {
                                if (tile.sprite) {
                                    tile.sprite.destroy();
                                    tile.sprite = null;
                                }
                                if (tile.text) {
                                    tile.text.destroy();
                                    tile.text = null;
                                }
                                tile.isEmpty = true;
                                tile.isSelected = false;
                            }
                        });
                    } else {
                        tile.isEmpty = true;
                        tile.sprite = null;
                        tile.text = null;
                        tile.isSelected = false;
                    }
                });

                // 更新统计
                this.tilesRemaining -= this.selectedTiles.length;
                this.moves++;
                this.scoreText.setText(`分数: ${this.score}`);
                this.movesText.setText(`移动: ${this.moves}`);
                this.tilesText.setText(`剩余: ${this.tilesRemaining}`);

                // 显示成功消息
                const displayValues = this.selectedTiles.map(tile => tile.display).join(' + ');
                this.showMessage(`消除成功！ ${displayValues} = 0`, 0x00ff00);

                this.clearSelection();

                // 检查游戏是否结束
                if (this.tilesRemaining === 0) {
                    this.gameWon = true;
                    this.showGameComplete();
                } else {
                    // 延迟应用重力效果，等待消除动画完成
                    this.time.delayedCall(350, () => {
                        this.applyGravity();
                    });
                }
            }

            clearSelection() {
                this.selectedTiles.forEach(tile => {
                    if (!tile.isEmpty && tile.sprite) {
                        tile.isSelected = false;
                        tile.sprite.clearTint();
                        tile.sprite.setScale(1);
                    }
                });
                this.selectedTiles = [];
                this.updateSelectionDisplay();
            }

            updateSelectionDisplay() {
                if (this.selectedTiles.length === 0) {
                    this.selectionText.setText('当前选择: 无');
                } else {
                    const displayValues = this.selectedTiles.map(tile => tile.display);
                    const sum = this.selectedTiles.reduce((total, tile) => total + tile.value, 0);
                    this.selectionText.setText(`当前选择: ${displayValues.join(' + ')} = ${sum.toFixed(3)}`);
                }
            }

            applyGravity() {
                const startX = 450 - (this.gridSize * this.tileSize) / 2;
                const startY = 100;
                
                // 处理重力效果
                for (let col = 0; col < this.gridSize; col++) {
                    const column = [];
                    
                    // 收集该列的非空方块
                    for (let row = this.gridSize - 1; row >= 0; row--) {
                        if (!this.grid[row][col].isEmpty) {
                            column.push({
                                value: this.grid[row][col].value,
                                display: this.grid[row][col].display,
                                sprite: this.grid[row][col].sprite,
                                text: this.grid[row][col].text
                            });
                        }
                    }
                    
                    // 清空该列
                    for (let row = 0; row < this.gridSize; row++) {
                        this.grid[row][col] = {
                            value: 0,
                            display: '0',
                            sprite: null,
                            text: null,
                            isEmpty: true,
                            row: row,
                            col: col,
                            isSelected: false
                        };
                    }
                    
                    // 从底部开始填充
                    for (let i = 0; i < column.length; i++) {
                        const newRow = this.gridSize - 1 - i;
                        this.grid[newRow][col] = {
                            value: column[i].value,
                            display: column[i].display,
                            sprite: column[i].sprite,
                            text: column[i].text,
                            isEmpty: false,
                            row: newRow,
                            col: col,
                            isSelected: false
                        };
                        
                        // 更新精灵位置并重新设置事件
                        const newX = startX + col * this.tileSize + this.tileSize / 2;
                        const newY = startY + newRow * this.tileSize + this.tileSize / 2;
                        
                        if (column[i].sprite && column[i].text) {
                            // 重新设置交互事件
                            this.setupTileInteraction(column[i].sprite, this.grid[newRow][col]);
                            
                            this.tweens.add({
                                targets: [column[i].sprite, column[i].text],
                                x: newX,
                                y: newY,
                                duration: 300,
                                ease: 'Bounce.easeOut'
                            });
                        }
                    }
                }
            }

            shuffleGrid() {
                // 收集所有非空方块的数据
                const tiles = [];
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (!this.grid[row][col].isEmpty) {
                            tiles.push({
                                value: this.grid[row][col].value,
                                display: this.grid[row][col].display
                            });
                            // 安全销毁精灵
                            if (this.grid[row][col].sprite) {
                                this.grid[row][col].sprite.destroy();
                            }
                            if (this.grid[row][col].text) {
                                this.grid[row][col].text.destroy();
                            }
                        }
                        // 重置网格
                        this.grid[row][col] = {
                            value: 0,
                            display: '0',
                            sprite: null,
                            text: null,
                            isEmpty: true,
                            row: row,
                            col: col,
                            isSelected: false
                        };
                    }
                }
                
                // 打乱并重新填充
                Phaser.Utils.Array.Shuffle(tiles);
                
                let tileIndex = 0;
                for (let row = 0; row < this.gridSize; row++) {
                    for (let col = 0; col < this.gridSize; col++) {
                        if (tileIndex < tiles.length) {
                            this.grid[row][col].value = tiles[tileIndex].value;
                            this.grid[row][col].display = tiles[tileIndex].display;
                            this.grid[row][col].isEmpty = false;
                            this.grid[row][col].isSelected = false;
                            tileIndex++;
                        }
                    }
                }
                
                this.createGrid();
                this.clearSelection();
                this.showMessage('重新排列！', 0x00ffff);
            }

            updateTime() {
                if (!this.gameWon) {
                    const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                    const minutes = Math.floor(elapsed / 60);
                    const seconds = elapsed % 60;
                    this.timeText.setText(`时间: ${minutes}:${seconds.toString().padStart(2, '0')}`);
                }
            }

            showGameComplete() {
                const elapsed = Math.floor((this.time.now - this.gameStartTime) / 1000);
                const minutes = Math.floor(elapsed / 60);
                const seconds = elapsed % 60;
                
                if (this.timeEvent) {
                    this.timeEvent.remove();
                }
                
                const winMessage = this.add.text(450, 400, 
                    `🎉 恭喜通关！🎉\n用时: ${minutes}:${seconds.toString().padStart(2, '0')}\n移动次数: ${this.moves}\n最终分数: ${this.score}`, 
                    {
                        fontSize: '28px',
                        fill: '#ffffff',
                        fontStyle: 'bold',
                        align: 'center',
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        padding: { x: 25, y: 20 }
                    }
                ).setOrigin(0.5);
                
                this.tweens.add({
                    targets: winMessage,
                    alpha: 0.5,
                    duration: 500,
                    yoyo: true,
                    repeat: -1
                });
            }

            showMessage(text, color) {
                const colorString = `#${color.toString(16).padStart(6, '0')}`;
                const message = this.add.text(450, 180, text, {
                    fontSize: '20px',
                    fill: colorString,
                    fontStyle: 'bold',
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    padding: { x: 15, y: 8 }
                }).setOrigin(0.5);

                this.tweens.add({
                    targets: message,
                    alpha: 0,
                    y: 150,
                    duration: 2000,
                    ease: 'Power2',
                    onComplete: () => message.destroy()
                });
            }
        }

        // 游戏配置
        const config = {
            type: Phaser.AUTO,
            width: 900,
            height: 700,
            resolution: window.devicePixelRatio || 1,
            render: {
                pixelArt: true,
                antialias: false
            },
            parent: 'game-container',
            backgroundColor: '#2c3e50',
            scene: NumberDragGame,
            physics: {
                default: 'arcade',
                arcade: {
                    gravity: { y: 0 }
                }
            }
        };

        // 启动游戏
        const game = new Phaser.Game(config);
    </script>
</body>
</html>