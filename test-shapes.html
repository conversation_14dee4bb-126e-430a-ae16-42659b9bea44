<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>俄罗斯方块形状测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #2c3e50;
            color: white;
        }
        .shape-container {
            display: inline-block;
            margin: 10px;
            padding: 10px;
            border: 1px solid #34495e;
            border-radius: 5px;
            background: #34495e;
        }
        .shape-grid {
            display: grid;
            gap: 2px;
            margin-bottom: 5px;
        }
        .cell {
            width: 20px;
            height: 20px;
            border-radius: 3px;
        }
        .filled {
            background: #3498db;
            border: 1px solid #2980b9;
        }
        .empty {
            background: transparent;
        }
        .shape-name {
            text-align: center;
            font-size: 12px;
            color: #bdc3c7;
        }
    </style>
</head>
<body>
    <h1>俄罗斯方块形状测试</h1>
    <p>以下是游戏中使用的所有方块形状：</p>
    <div id="shapes-display"></div>

    <script>
        // 俄罗斯方块经典7种形状定义（包含旋转变体）
        const tetrisShapes = {
            // I形方块 (直线)
            I: [
                [[1, 1, 1, 1]],
                [[1], [1], [1], [1]]
            ],
            // O形方块 (正方形)
            O: [
                [[1, 1], [1, 1]]
            ],
            // T形方块
            T: [
                [[0, 1, 0], [1, 1, 1]],
                [[1, 0], [1, 1], [1, 0]],
                [[1, 1, 1], [0, 1, 0]],
                [[0, 1], [1, 1], [0, 1]]
            ],
            // S形方块
            S: [
                [[0, 1, 1], [1, 1, 0]],
                [[1, 0], [1, 1], [0, 1]]
            ],
            // Z形方块
            Z: [
                [[1, 1, 0], [0, 1, 1]],
                [[0, 1], [1, 1], [1, 0]]
            ],
            // J形方块
            J: [
                [[1, 0, 0], [1, 1, 1]],
                [[1, 1], [1, 0], [1, 0]],
                [[1, 1, 1], [0, 0, 1]],
                [[0, 1], [0, 1], [1, 1]]
            ],
            // L形方块
            L: [
                [[0, 0, 1], [1, 1, 1]],
                [[1, 0], [1, 0], [1, 1]],
                [[1, 1, 1], [1, 0, 0]],
                [[1, 1], [0, 1], [0, 1]]
            ]
        };

        // 将所有形状和旋转变体展开为一个数组
        const blockShapes = [];
        Object.keys(tetrisShapes).forEach(shapeType => {
            tetrisShapes[shapeType].forEach((rotation, index) => {
                blockShapes.push({
                    shape: rotation,
                    name: `${shapeType}-${index}`,
                    type: shapeType
                });
            });
        });

        // 添加一些小方块用于填充
        blockShapes.push(
            { shape: [[1]], name: '单方块', type: 'SINGLE' },
            { shape: [[1, 1]], name: '双方块', type: 'DOUBLE' },
            { shape: [[1], [1]], name: '竖双方块', type: 'DOUBLE_V' },
            { shape: [[1, 1, 1]], name: '三方块', type: 'TRIPLE' },
            { shape: [[1], [1], [1]], name: '竖三方块', type: 'TRIPLE_V' }
        );

        function displayShapes() {
            const container = document.getElementById('shapes-display');
            
            blockShapes.forEach(blockData => {
                const shapeDiv = document.createElement('div');
                shapeDiv.className = 'shape-container';
                
                const gridDiv = document.createElement('div');
                gridDiv.className = 'shape-grid';
                gridDiv.style.gridTemplateColumns = `repeat(${blockData.shape[0].length}, 20px)`;
                
                blockData.shape.forEach(row => {
                    row.forEach(cell => {
                        const cellDiv = document.createElement('div');
                        cellDiv.className = `cell ${cell ? 'filled' : 'empty'}`;
                        gridDiv.appendChild(cellDiv);
                    });
                });
                
                const nameDiv = document.createElement('div');
                nameDiv.className = 'shape-name';
                nameDiv.textContent = blockData.name;
                
                shapeDiv.appendChild(gridDiv);
                shapeDiv.appendChild(nameDiv);
                container.appendChild(shapeDiv);
            });
        }

        displayShapes();
    </script>
</body>
</html>
