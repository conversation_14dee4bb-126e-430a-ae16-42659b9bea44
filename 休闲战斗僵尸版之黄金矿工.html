<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>休闲战斗僵尸版之黄金矿工</title>
    <script src="https://cdn.jsdelivr.net/npm/phaser@3.80.1/dist/phaser.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #1e3c72, #2a5298);
            overflow: hidden;
            font-family: Arial, sans-serif;
        }
        canvas {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            height: 100vh;
        }
    </style>
</head>
<body>

<script>
    // 游戏变量
    let game;
    let player, playerWeapon, monsters = [];
    let playerHealth = 500, maxPlayerHealth = 500;
    let currentLevel = 1, currentWave = 1;
    let gameState = 'playing';
    let monstersKilled = 0;
    let totalMonstersInWave = 8;
    let isCreatingWave = false; // 防止重复创建波次的标志位

    // 昼夜系统变量
    let dayNightCycle = 'day'; // 'day' 或 'night'
    let dayNightTimer = 0;
    let dayDuration = 30000; // 白天持续30秒
    let nightDuration = 20000; // 夜晚持续20秒
    let transitionDuration = 2000; // 过渡时间2秒

    // 黄金矿工游戏变量
    let miningGame = {
        hook: null,
        hookLine: null,
        isHookMoving: false,
        isHookExtending: false,
        hookAngle: 0,
        hookSpeed: 0.02,
        hookLength: 0,
        initialHookLength: 80, // 钩子绳子的初始长度
        maxHookLength: 700,
        hookRetractSpeed: 100,
        treasures: [],
        playerGold: 0,
        miningArea: { x: 50, y: 550, width: 650, height: 650 },
        hookStartX: 0, // 钩子初始X位置
        hookStartY: 0, // 钩子初始Y位置
        caughtTreasure: null // 被钩住的道具
    };



    // 玩家属性
    let playerStats = {
        attackDamage: 15,
        attackSpeed: 600,
        maxHealth: 100,
        critChance: 0,
        lifeSteal: 0,
        multiShot: 1
    };



    // 玩家拥有的枪支数量
    let playerGuns = 0;
    // 存储所有玩家武器的数组
    let playerWeapons = [];

    // 游戏配置 - 竖屏优化
    const config = {
        type: Phaser.AUTO,
        width: 750,
        height: 1334,
        backgroundColor: '#1a1a2e',
        parent: 'game-container',
        scene: {
            preload: preload,
            create: create,
            update: update
        }
    };

    // 预加载函数
    function preload() {
        // 加载背景图片
        this.load.image('background', 'images/rpg/background8.png');

        // 加载主角图片
        this.load.image('player', 'images/rpg/Character3.png');
        this.load.image('player_mining', 'images/rpg/Character4.png');

        // 加载15个怪物图片
        for (let i = 1; i <= 15; i++) {
            this.load.image(`monster_${i}`, `images/rpg/js (${i}).png`);
        }

        // 加载武器图片
        this.load.image('knife', 'images/knife/kong.png');

        // 加载黄金矿工相关图片 - 只使用实际存在的文件
        this.load.image('big_gold', 'images/rpg/黄金矿工/大块金块.png');
        this.load.image('small_gold', 'images/rpg/黄金矿工/小块金块.png');
        this.load.image('small_stone', 'images/rpg/黄金矿工/小块石块.png');
        this.load.image('diamond', 'images/rpg/黄金矿工/钻石.png');
        this.load.image('explosive', 'images/rpg/黄金矿工/炸药.png');
        this.load.image('mixed_gold_stone', 'images/rpg/黄金矿工/金块石块混合.png');
    }

    // 创建游戏场景
    function create() {
        // 添加背景图片
        const background = this.add.image(375, 667, 'background');
        background.setDisplaySize(750, 1334); // 适配屏幕大小

        // 创建主角 - 左边位置，向下移动（初始使用挖矿图片）
        player = this.add.image(150, 500, 'player_mining');
        player.setScale(0.8); // 再缩小一半
        player.setOrigin(0.5, 1); // 设置旋转中心在底部
        player.setDepth(100 + player.y * 0.1);

        // 创建武器 - 位置在角色右中，与角色重叠
        playerWeapon = this.add.image(player.x + 20, player.y - 30, 'knife');
        playerWeapon.setScale(0.8);
        playerWeapon.setOrigin(0.5, 1);
        playerWeapon.setDepth(100 + player.y * 0.1 + 1);

        // 创建怪物
        createWave.call(this);

        // 创建UI
        createUI.call(this);

        // 创建黄金矿工游戏区域
        createMiningGame.call(this);

        // 初始化昼夜系统
        initDayNightSystem.call(this);

        // 初始状态设置为白天，隐藏怪物，显示挖矿游戏
        hideMonsters.call(this);
        generateTreasures.call(this); // 初始化时生成宝藏
        showMiningGame.call(this);
    }

    // 创建波次怪物
    function createWave() {
        isCreatingWave = true;

        // 清除现有怪物和血条
        monsters.forEach(monster => monster.destroy());
        monsters = [];

        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.destroy();
            });
            this.monsterHealthBars = [];
        }

        // 创建新怪物 - 排成一行，底部与角色对齐
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y;

        for (let i = 0; i < totalMonstersInWave; i++) {
            const xPos = startX + i * monsterSpacing;
            const yPos = startY;

            // 随机选择怪物图片 (1-15)
            const monsterImageIndex = Math.floor(Math.random() * 15) + 1;

            let monster = this.add.image(xPos, yPos, `monster_${monsterImageIndex}`);
            monster.setScale(0.5); // 恢复原来的尺寸
            monster.setOrigin(0.5, 1);
            monster.setDepth(100 + yPos * 0.1);

            monster.health = 30 + currentLevel * 10;
            monster.maxHealth = monster.health;
            monster.lastAttack = 0;
            monster.isMoving = false;
            monster.originalX = xPos;
            monster.originalY = yPos;
            monster.jumpTween = null;

            // 根据当前昼夜状态设置怪物可见性
            monster.setVisible(dayNightCycle === 'night');

            monsters.push(monster);
        }

        monstersKilled = 0;

        setTimeout(() => {
            updateMonsterHealthBars.call(this);
            isCreatingWave = false;
        }, 50);
    }


    // 主角远程攻击系统
    function playerAttack() {
        if (monsters.length > 0) {
            // 简化攻击系统：只使用基础武器和多重射击
            const targetsToAttack = Math.min(playerStats.multiShot, monsters.length);

            for (let i = 0; i < targetsToAttack; i++) {
                const target = monsters[i % monsters.length]; // 如果武器多于怪物，循环攻击

                // 简化武器发射：只使用主武器
                let weaponX, weaponY;
                if (playerWeapon) {
                    weaponX = playerWeapon.x + 40;
                    weaponY = playerWeapon.y - 50;
                } else {
                    // 默认位置
                    weaponX = player.x + 60;
                    weaponY = player.y - 30;
                }

                const projectile = this.add.circle(weaponX, weaponY, 4, 0xffff00);

                // 攻击动画
                this.tweens.add({
                    targets: projectile,
                    x: target.x - 30,
                    y: target.y - 100, // 击中怪物中间位置，而不是底部
                    duration: 200, // 从500减少到200，子弹飞行更快
                    delay: i * 50, // 从100减少到50，多重射击间隔更短
                    onComplete: () => {
                        projectile.destroy();

                        // 计算基础伤害
                        let damage = playerStats.attackDamage + currentLevel * 2;

                        // 暴击判定
                        const isCrit = Math.random() < playerStats.critChance;
                        if (isCrit) {
                            damage *= 2;
                            // 暴击特效
                            const critText = this.add.text(target.x, target.y - 80, 'CRIT!', {
                                fontSize: '20px',
                                fill: '#e74c3c',
                                fontFamily: 'Arial',
                                fontWeight: 'bold'
                            }).setOrigin(0.5);

                            this.tweens.add({
                                targets: critText,
                                alpha: 0,
                                y: critText.y - 30,
                                duration: 1000,
                                onComplete: () => critText.destroy()
                            });
                        }

                        // 造成伤害
                        target.health -= damage;

                        // 显示伤害飘字
                        showDamageText.call(this, target, damage);

                        // 怪物受击效果：闪红并停顿
                        monsterHitEffect.call(this, target);

                        // 更新血条
                        updateMonsterHealthBars.call(this);

                        // 生命偷取
                        if (playerStats.lifeSteal > 0) {
                            const healAmount = Math.floor(damage * playerStats.lifeSteal);
                            playerHealth = Math.min(maxPlayerHealth, playerHealth + healAmount);

                            // 治疗特效
                            if (healAmount > 0) {
                                const healText = this.add.text(player.x, player.y - 60, `+${healAmount}`, {
                                    fontSize: '16px',
                                    fill: '#2ecc71',
                                    fontFamily: 'Arial'
                                }).setOrigin(0.5);

                                this.tweens.add({
                                    targets: healText,
                                    alpha: 0,
                                    y: healText.y - 20,
                                    duration: 1000,
                                    onComplete: () => healText.destroy()
                                });
                            }
                        }

                        // 伤害特效 - 减小闪动幅度
                        this.tweens.add({
                            targets: target,
                            scaleX: 0.27, // 轻微放大，避免过大闪动
                            scaleY: 0.27,
                            duration: 150,
                            yoyo: true
                        });

                        // 检查怪物是否死亡
                        if (target.health <= 0) {
                            target.destroy();
                            const index = monsters.indexOf(target);
                            if (index > -1) {
                                monsters.splice(index, 1);
                                monstersKilled++;
                            }
                        }
                    }
                });
            }

            // 主角攻击动画 - 更轻微的抖动
            this.tweens.add({
                targets: player,
                x: player.x + 2, // 减少到2像素，更轻微
                duration: 60,
                yoyo: true,
                ease: 'Power2'
            });

            // 主武器开火抖动 - 后坐力效果
            if (playerWeapon) {
                animateWeaponRecoil.call(this, playerWeapon);
            }
        }
    }

    // 武器后坐力动画
    function animateWeaponRecoil(weapon) {
        const originalWeaponX = weapon.x;
        const originalWeaponY = weapon.y;

        // 水平后坐力
        this.tweens.add({
            targets: weapon,
            x: originalWeaponX - 8, // 向后抖动8像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    x: originalWeaponX,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });

        // 垂直抖动
        this.tweens.add({
            targets: weapon,
            y: originalWeaponY - 4, // 向上抖动4像素
            duration: 80,
            ease: 'Power2',
            onComplete: () => {
                // 回到原位
                this.tweens.add({
                    targets: weapon,
                    y: originalWeaponY,
                    duration: 40,
                    ease: 'Power2'
                });
            }
        });
    }

    // 游戏更新循环
    function update(time, delta) {
        // 更新昼夜系统
        updateDayNightCycle.call(this, time, delta);

        // 更新UI（无论什么状态都要更新）
        updateUI.call(this);

        // 更新血条
        updatePlayerHealthBar.call(this);
        updateEnemyHealthBar.call(this);

        if (gameState === 'playing') {
            if (dayNightCycle === 'day') {
                // 白天：更新黄金矿工游戏
                updateMiningGame.call(this, time, delta);
            } else if (dayNightCycle === 'night') {
                // 夜晚：更新战斗逻辑
                updateBattle.call(this, time, delta);
                // 检查波次完成
                checkWaveComplete.call(this);
            }

            // 检查游戏结束
            if (playerHealth <= 0) {
                gameState = 'gameOver';
                this.add.text(375, 667, 'GAME OVER', {
                    fontSize: '48px',
                    fill: '#e74c3c',
                    fontFamily: 'Arial'
                }).setOrigin(0.5);
            }
        }

        // 更新关卡和波次显示
        if (this.levelText) {
            this.levelText.setText(`关卡 ${currentLevel}`);
        }
        if (this.waveText) {
            this.waveText.setText(`波次 ${currentWave}`);
        }
    }

    // 初始化昼夜系统
    function initDayNightSystem() {
        // 创建昼夜状态显示
        this.dayNightText = this.add.text(375, 100, '白天 - 挖矿时间', {
            fontSize: '24px',
            fill: '#ffdc35',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 创建时间进度条背景
        this.timeBarBg = this.add.graphics();
        this.timeBarBg.fillStyle(0x2c3e50);
        this.timeBarBg.fillRoundedRect(275, 120, 200, 15, 7);
        this.timeBarBg.lineStyle(2, 0x000000);
        this.timeBarBg.strokeRoundedRect(275, 120, 200, 15, 7);

        // 创建时间进度条
        this.timeBar = this.add.graphics();

        // 创建金币显示
        this.goldText = this.add.text(50, 150, `金币: ${miningGame.playerGold}`, {
            fontSize: '20px',
            fill: '#ffd700',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 创建游戏说明
        this.instructionText = this.add.text(375, 180, '白天点击屏幕挖矿，夜晚自动防御僵尸', {
            fontSize: '16px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        dayNightTimer = 0;
    }

    // 更新昼夜循环
    function updateDayNightCycle(time, delta) {
        dayNightTimer += delta;

        const currentDuration = dayNightCycle === 'day' ? dayDuration : nightDuration;
        const progress = Math.min(dayNightTimer / currentDuration, 1);

        // 更新时间进度条
        if (this.timeBar) {
            this.timeBar.clear();
            const barColor = dayNightCycle === 'day' ? 0xf39c12 : 0x8e44ad;
            this.timeBar.fillStyle(barColor);
            this.timeBar.fillRoundedRect(277, 122, 196 * progress, 11, 5);
        }

        // 检查是否需要切换昼夜
        if (dayNightTimer >= currentDuration) {
            switchDayNight.call(this);
        }

        // 更新昼夜状态显示
        if (this.dayNightText) {
            const timeLeft = Math.ceil((currentDuration - dayNightTimer) / 1000);
            if (dayNightCycle === 'day') {
                this.dayNightText.setText(`白天 - 挖矿时间 (${timeLeft}s)`);
                this.dayNightText.setFill('#ffdc35');
            } else {
                this.dayNightText.setText(`夜晚 - 防御时间 (${timeLeft}s)`);
                this.dayNightText.setFill('#8e44ad');
            }
        }

        // 更新金币显示
        if (this.goldText) {
            this.goldText.setText(`金币: ${miningGame.playerGold}`);
        }
    }

    // 切换昼夜
    function switchDayNight() {
        dayNightTimer = 0;

        if (dayNightCycle === 'day') {
            // 切换到夜晚
            dayNightCycle = 'night';
            // 切换主角图片为战斗状态
            if (player) {
                player.setTexture('player');
            }
            // 隐藏挖矿游戏元素
            hideMiningGame.call(this);
            // 确保金币显示在夜晚时保持可见
            if (this.goldText) {
                this.goldText.setVisible(true);
            }
            // 显示怪物
            showMonsters.call(this);
            // 创建新波次怪物
            if (monsters.length === 0) {
                createWave.call(this);
            }
        } else {
            // 切换到白天
            dayNightCycle = 'day';
            // 切换主角图片为挖矿状态
            if (player) {
                player.setTexture('player_mining');
            }
            // 隐藏怪物
            hideMonsters.call(this);
            // 确保金币显示在白天时也保持可见
            if (this.goldText) {
                this.goldText.setVisible(true);
            }
            // 显示挖矿游戏元素
            showMiningGame.call(this);
            // 重新生成宝藏
            generateTreasures.call(this);
        }
    }

    // 更新战斗逻辑
    function updateBattle(time, delta) {
        // 主角自动攻击 - 使用动态攻击速度
        if (!this.lastPlayerAttack) this.lastPlayerAttack = 0;
        if (time - this.lastPlayerAttack > playerStats.attackSpeed && monsters.length > 0) {
            playerAttack.call(this);
            this.lastPlayerAttack = time;
        }

        // 怪物AI - 移动到主角身边并攻击
        monsters.forEach((monster, index) => {

            const distanceToPlayer = Phaser.Math.Distance.Between(monster.x, monster.y, player.x, player.y);

            // 如果距离主角太远，移动靠近
            if (distanceToPlayer > 80 && !monster.isMoving) {
                monster.isMoving = true;

                // 计算移动目标位置，避免重叠
                let targetX, targetY;
                let attempts = 0;
                let validPosition = false;

                while (!validPosition && attempts < 10) {
                    // 根据怪物索引分配不同的角度，避免重叠
                    const angle = (index * (Math.PI * 2 / totalMonstersInWave)) + Math.random() * 0.5;
                    const distance =  30+Math.random() * 30;

                    targetX = player.x+80 + Math.cos(angle) * distance;
                    targetY = player.y + Math.sin(angle) * 10; // Y轴范围限制在±20

                    // 检查是否与其他怪物位置冲突
                    validPosition = true;
                    for (let otherMonster of monsters) {
                        if (otherMonster !== monster) {
                            const distanceToOther = Phaser.Math.Distance.Between(targetX, targetY, otherMonster.x, otherMonster.y);
                            if (distanceToOther < 50) { // 最小间距50像素
                                validPosition = false;
                                break;
                            }
                        }
                    }
                    attempts++;
                }

                // 如果找不到合适位置，使用默认位置
                if (!validPosition) {
                    targetX = player.x + 60 + index * 30;
                    targetY = player.y + (index % 2 === 0 ? -15 : 15);
                }

                // 蹦蹦跳跳的移动动画 - 进一步减慢速度
                this.tweens.add({
                    targets: monster,
                    x: targetX,
                    y: targetY,
                    duration: 25000, // 从10000增加到15000，移动更慢
                    ease: 'Power2',
                    onComplete: () => {
                        monster.isMoving = false;
                        // 停止跳跃动画
                        if (monster.jumpTween) {
                            monster.jumpTween.stop();
                            monster.jumpTween = null;
                        }
                        // 移动完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 添加跳跃效果 - 上下弹跳
                monster.jumpTween = this.tweens.add({
                    targets: monster,
                    scaleY: 0.52, // 压扁效果（适配新的基础尺寸0.25）
                    duration: 200,
                    yoyo: true,
                    repeat: -1, // 无限重复
                    ease: 'Sine.easeInOut'
                });
            }

            // 如果靠近主角，进行攻击
            if (distanceToPlayer <= 100 && time - monster.lastAttack > 2500) { // 每2.5秒攻击一次
                let damage = 12 + currentLevel;
                playerHealth = Math.max(0, playerHealth - damage); // 确保血量不会变成负数

                monster.lastAttack = time;

                // 停止跳跃动画（如果正在跳跃）
                if (monster.jumpTween) {
                    monster.jumpTween.stop();
                    monster.jumpTween = null;
                }

                // 攻击倾斜动画 - 向主角方向倾斜
                this.tweens.add({
                    targets: monster,
                    rotation: -0.4, // 向左倾斜约23度
                    duration: 150,
                    yoyo: true,
                    ease: 'Power2'
                });

                // 近战攻击动画 - 向主角方向冲刺
                const currentX = monster.x;
                const currentY = monster.y;
                this.tweens.add({
                    targets: monster,
                    x: currentX - 30,
                    y: currentY,
                    duration: 200,
                    yoyo: true,
                    ease: 'Power2',
                    onComplete: () => {
                        // 攻击动画完成后更新血条位置
                        updateHealthBarPositions.call(this);
                    }
                });

                // 攻击时的缩放效果
                this.tweens.add({
                    targets: monster,
                    scaleX: 0.28, // 攻击时稍微放大（适配新的基础尺寸0.25）
                    scaleY: 0.28,
                    duration: 150,
                    yoyo: true
                });

                // 主角受击效果
                this.tweens.add({
                    targets: player,
                    tint: 0xff0000,
                    duration: 100,
                    yoyo: true,
                    onComplete: () => {
                        player.clearTint();
                    }
                });
            }
        });
    }

    // 更新UI
    function updateUI() {
        // 更新血条位置以跟随怪物移动
        updateHealthBarPositions.call(this);
        // 更新怪物深度层级
        updateMonsterDepths.call(this);
    }

    // 创建黄金矿工游戏
    function createMiningGame() {
        const area = miningGame.miningArea;

        // 不再创建挖矿区域背景，让背景更干净

        // 创建钩子线
        miningGame.hookLine = this.add.graphics();

        // 创建钩子（直接使用图形，因为没有钩子图片）
        miningGame.hook = this.add.graphics();
        // 钩子主体 - 增大尺寸
        miningGame.hook.fillStyle(0x888888);
        miningGame.hook.fillCircle(0, 0, 12);
        // 钩子尖端
        miningGame.hook.fillStyle(0x666666);
        miningGame.hook.fillTriangle(-8, 8, 8, 8, 0, 16);
        // 钩子中心
        miningGame.hook.fillStyle(0x444444);
        miningGame.hook.fillCircle(0, 0, 6);

        // 设置钩子初始位置（在主角位置）
        miningGame.hookStartX = player.x+70;
        miningGame.hookStartY = player.y - 100;
        // 设置钩子初始长度和位置
        miningGame.hookLength = miningGame.initialHookLength;
        miningGame.hook.x = miningGame.hookStartX + Math.sin(miningGame.hookAngle) * miningGame.initialHookLength;
        miningGame.hook.y = miningGame.hookStartY + Math.cos(miningGame.hookAngle) * miningGame.initialHookLength;

        // 不在这里生成宝藏，等待昼夜系统初始化后自动生成

        // 添加点击事件来发射钩子
        this.input.on('pointerdown', () => {
            if (dayNightCycle === 'day' && !miningGame.isHookExtending && !miningGame.isHookMoving) {
                launchHook.call(this);
            }
        });
    }

    // 生成宝藏
    function generateTreasures() {
        // 清除现有宝藏
        miningGame.treasures.forEach(treasure => {
            if (treasure.sprite) treasure.sprite.destroy();
        });
        miningGame.treasures = [];

        const area = miningGame.miningArea;
        const treasureTypes = [
            { type: 'small_gold', value: 30, image: 'small_gold', size: 0.3, probability: 0.3 },
            { type: 'big_gold', value: 100, image: 'big_gold', size: 0.35, probability: 0.2 },
            { type: 'diamond', value: 200, image: 'diamond', size: 0.32, probability: 0.15 },
            { type: 'small_stone', value: 5, image: 'small_stone', size: 0.38, probability: 0.25 },
            { type: 'explosive', value: 150, image: 'explosive', size: 0.3, probability: 0.05 },
            { type: 'mixed_gold_stone', value: 60, image: 'mixed_gold_stone', size: 0.32, probability: 0.05 }
        ];

        // 生成10-15个宝藏，确保数量控制
        const maxTreasures = 12;
        const placedPositions = []; // 记录已放置的位置
        const minDistance = 40; // 道具之间的最小距离
        let successfulPlacements = 0;

        // 尝试放置道具，直到达到目标数量或尝试次数过多
        for (let attempt = 0; attempt < 100 && successfulPlacements < maxTreasures; attempt++) {
            // 随机选择宝藏类型
            const rand = Math.random();
            let treasureType = treasureTypes[3]; // 默认小石头
            let cumulative = 0;
            for (let type of treasureTypes) {
                cumulative += type.probability;
                if (rand <= cumulative) {
                    treasureType = type;
                    break;
                }
            }

            // 寻找不重叠的位置
            let x, y;
            let positionAttempts = 0;
            let validPosition = false;

            while (!validPosition && positionAttempts < 20) {
                x = area.x + 50 + Math.random() * (area.width - 100);
                y = area.y + 50 + Math.random() * (area.height - 100);

                // 检查与已放置道具的距离
                validPosition = true;
                for (let pos of placedPositions) {
                    const distance = Math.sqrt((x - pos.x) ** 2 + (y - pos.y) ** 2);
                    if (distance < minDistance) {
                        validPosition = false;
                        break;
                    }
                }
                positionAttempts++;
            }

            // 如果找不到合适位置，跳过这次尝试
            if (!validPosition) {
                continue;
            }

            // 记录位置
            placedPositions.push({ x, y });

            // 创建宝藏精灵（使用图片，如果图片不存在则使用备用图形）
            let treasureSprite;
            try {
                treasureSprite = this.add.image(x, y, treasureType.image);
                treasureSprite.setScale(treasureType.size);
                treasureSprite.setOrigin(0.5, 0.5);
            } catch (error) {
                // 备用方案：使用更好看的图形
                treasureSprite = this.add.graphics();
                treasureSprite.x = x;
                treasureSprite.y = y;

                switch(treasureType.type) {
                    case 'small_gold':
                        treasureSprite.fillStyle(0xFFD700);
                        treasureSprite.fillCircle(0, 0, 8);
                        treasureSprite.fillStyle(0xFFA500);
                        treasureSprite.fillCircle(0, 0, 5);
                        break;
                    case 'big_gold':
                        treasureSprite.fillStyle(0xFFD700);
                        treasureSprite.fillCircle(0, 0, 15);
                        treasureSprite.fillStyle(0xFFA500);
                        treasureSprite.fillCircle(0, 0, 10);
                        break;
                    case 'diamond':
                        treasureSprite.fillStyle(0x00FFFF);
                        treasureSprite.fillPolygon([0, -15, -10, 0, 0, 15, 10, 0]);
                        break;
                    case 'small_stone':
                        treasureSprite.fillStyle(0x808080);
                        treasureSprite.fillCircle(0, 0, 10);
                        treasureSprite.fillStyle(0x606060);
                        treasureSprite.fillCircle(-2, -2, 5);
                        break;
                    case 'explosive':
                        treasureSprite.fillStyle(0x8B0000);
                        treasureSprite.fillCircle(0, 0, 12);
                        treasureSprite.fillStyle(0xFF0000);
                        treasureSprite.fillCircle(0, 0, 8);
                        treasureSprite.fillStyle(0xFFFF00);
                        treasureSprite.fillCircle(0, -8, 3);
                        break;
                    case 'mixed_gold_stone':
                        treasureSprite.fillStyle(0x808080);
                        treasureSprite.fillCircle(-5, 0, 8);
                        treasureSprite.fillStyle(0xFFD700);
                        treasureSprite.fillCircle(5, 0, 8);
                        break;
                    default:
                        treasureSprite.fillStyle(0xFFFFFF);
                        treasureSprite.fillCircle(0, 0, 12);
                }
            }

            miningGame.treasures.push({
                sprite: treasureSprite,
                type: treasureType.type,
                value: treasureType.value,
                x: x,
                y: y,
                size: treasureType.size,
                hitRadius: 15 // 按显示大小设置碰撞半径
            });

            // 成功放置一个道具，增加计数
            successfulPlacements++;
        }

        // 调试信息：显示实际生成的道具数量
        console.log(`成功生成 ${successfulPlacements} 个道具`);
    }

    // 发射钩子
    function launchHook() {
        if (miningGame.isHookExtending || miningGame.isHookMoving) return;

        miningGame.isHookExtending = true;
        // 从初始长度开始延伸
        miningGame.hookLength = miningGame.initialHookLength;

        // 计算钩子方向（当前摆动角度）
        const hookX = miningGame.hookStartX + Math.sin(miningGame.hookAngle) * miningGame.hookLength;
        const hookY = miningGame.hookStartY + Math.cos(miningGame.hookAngle) * miningGame.hookLength;

        miningGame.hook.x = hookX;
        miningGame.hook.y = hookY;
    }

    // 更新黄金矿工游戏
    function updateMiningGame(time, delta) {
        if (dayNightCycle !== 'day') return;

        // 更新钩子摆动 - 只在完全空闲时摆动
        if (!miningGame.isHookExtending && !miningGame.isHookMoving) {
            miningGame.hookAngle += miningGame.hookSpeed;
            if (miningGame.hookAngle > Math.PI / 4 || miningGame.hookAngle < -Math.PI / 4) {
                miningGame.hookSpeed = -miningGame.hookSpeed;
            }

            // 更新钩子摆动位置（当完全空闲时）
            miningGame.hookLength = miningGame.initialHookLength;
            miningGame.hook.x = miningGame.hookStartX + Math.sin(miningGame.hookAngle) * miningGame.initialHookLength;
            miningGame.hook.y = miningGame.hookStartY + Math.cos(miningGame.hookAngle) * miningGame.initialHookLength;
        }

        // 更新钩子位置
        if (miningGame.isHookExtending) {
            miningGame.hookLength += delta * 0.3; // 钩子延伸速度

            // 计算钩子位置
            const hookX = miningGame.hookStartX + Math.sin(miningGame.hookAngle) * miningGame.hookLength;
            const hookY = miningGame.hookStartY + Math.cos(miningGame.hookAngle) * miningGame.hookLength;

            // 检查是否超出屏幕边界
            const screenWidth = 750;
            const screenHeight = 1334;
            const isOutOfBounds = hookX < 0 || hookX > screenWidth || hookY < 0 || hookY > screenHeight;

            if (miningGame.hookLength >= miningGame.maxHookLength || isOutOfBounds) {
                // 钩子到达最大长度或超出屏幕，开始收回
                miningGame.isHookExtending = false;
                retractHook.call(this);
            } else {
                // 更新钩子位置
                miningGame.hook.x = hookX;
                miningGame.hook.y = hookY;

                // 检查是否碰到宝藏
                checkTreasureCollision.call(this);
            }
        }

        // 绘制钩子线
        if (miningGame.hookLine && miningGame.hook) {
            miningGame.hookLine.clear();
            miningGame.hookLine.lineStyle(3, 0x654321);
            miningGame.hookLine.lineBetween(
                miningGame.hookStartX, miningGame.hookStartY,
                miningGame.hook.x, miningGame.hook.y
            );
        }
    }

    // 更新怪物和主角深度层级，根据Y轴位置
    function updateMonsterDepths() {
        // 更新主角深度
        if (player) {
            player.setDepth(100 + player.y * 0.1);
        }

        // 更新武器深度
        if (playerWeapon) {
            playerWeapon.setDepth(100 + player.y * 0.1 + 1);
        }

        // 更新怪物深度
        monsters.forEach(monster => {
            // Y轴越大（越靠下）深度越大，显示在前面
            // 基础深度100，加上Y轴位置的0.1倍作为偏移
            monster.setDepth(100 + monster.y * 0.1);
        });
    }

    // 更新血条位置以跟随怪物移动
    function updateHealthBarPositions() {
        if (!this.monsterHealthBars) return;

        // 更新武器位置跟随主角
        if (playerWeapon && player) {
            playerWeapon.x = player.x + 20;
            playerWeapon.y = player.y - 30;
        }

        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 更新血条背景位置和深度
                if (this.monsterHealthBars[baseIndex]) {
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex].setDepth(100 + monster.y * 0.1 + 50);
                }

                // 更新血条前景位置和深度
                if (this.monsterHealthBars[baseIndex + 1]) {
                    const healthPercent = monster.health / monster.maxHealth;
                    const currentWidth = 48 * healthPercent;
                    this.monsterHealthBars[baseIndex + 1].x = monster.x - 25; // 血条左边缘位置
                    this.monsterHealthBars[baseIndex + 1].y = monster.y - 140;
                    this.monsterHealthBars[baseIndex + 1].width = currentWidth;
                    this.monsterHealthBars[baseIndex + 1].setDepth(100 + monster.y * 0.1 + 51);
                }

                // 移除血量数字位置更新
            }
        });
    }

    // 更新怪物头顶血条 - 优化版本，减少闪动
    function updateMonsterHealthBars() {
        // 初始化血条数组
        if (!this.monsterHealthBars) {
            this.monsterHealthBars = [];
        }

        // 清除多余的血条（当怪物数量减少时）
        while (this.monsterHealthBars.length > monsters.length * 2) {
            const bar = this.monsterHealthBars.pop();
            if (bar) bar.destroy();
        }

        // 为每个怪物更新或创建血条
        monsters.forEach((monster, index) => {
            if (monster.health > 0) {
                const healthPercent = monster.health / monster.maxHealth;
                const baseIndex = index * 2; // 每个怪物2个元素：背景、前景

                // 血条背景
                if (!this.monsterHealthBars[baseIndex]) {
                    const barBg = this.add.rectangle(
                        monster.x,
                        monster.y - 140,
                        50,
                        8,
                        0x2c3e50
                    );
                    barBg.setStrokeStyle(1, 0x000000);
                    // 血条深度比对应怪物高一些，确保显示在怪物上方
                    barBg.setDepth(100 + monster.y * 0.1 + 50);
                    // 根据昼夜状态设置血条可见性
                    barBg.setVisible(dayNightCycle === 'night');
                    this.monsterHealthBars[baseIndex] = barBg;
                } else {
                    // 更新位置
                    this.monsterHealthBars[baseIndex].x = monster.x;
                    this.monsterHealthBars[baseIndex].y = monster.y - 140;
                }

                // 血条前景
                const currentWidth = 48 * healthPercent;
                if (currentWidth > 0) {
                    // 血条颜色默认为红色
                    let barColor = 0xe74c3c; // 红色

                    if (!this.monsterHealthBars[baseIndex + 1]) {
                        const bar = this.add.rectangle(
                            monster.x - 25, // 血条左边缘位置
                            monster.y - 140,
                            currentWidth,
                            6,
                            barColor
                        );
                        bar.setOrigin(0, 0.5); // 设置原点在左边中间，这样血条从左边开始填充
                        bar.setDepth(100 + monster.y * 0.1 + 51);
                        // 根据昼夜状态设置血条可见性
                        bar.setVisible(dayNightCycle === 'night');
                        this.monsterHealthBars[baseIndex + 1] = bar;
                    } else {
                        // 更新血条
                        const bar = this.monsterHealthBars[baseIndex + 1];
                        bar.x = monster.x - 25; // 血条左边缘位置
                        bar.y = monster.y - 140;
                        bar.width = currentWidth;
                        bar.fillColor = barColor;
                        bar.setVisible(true);
                    }
                } else if (this.monsterHealthBars[baseIndex + 1]) {
                    // 血量为0时隐藏血条
                    this.monsterHealthBars[baseIndex + 1].setVisible(false);
                }

                // 移除血量数字显示
            }
        });
    }

    // 检查波次完成
    function checkWaveComplete() {
        // 如果怪物全部死亡且没有正在创建新波次
        if (monsters.length === 0 && !isCreatingWave) {
            isCreatingWave = true; // 设置标志位，防止重复触发

            currentWave++;

            if (currentWave > 3) {
                currentLevel++;
                currentWave = 1;
                totalMonstersInWave = Math.min(6, 3 + Math.floor(currentLevel / 2));
            }

            // 创建新波次
            setTimeout(() => {
                createWave.call(this);
                isCreatingWave = false; // 创建完成后重置标志位
            }, 1000);
        }
    }

    // 创建UI元素
    function createUI() {
        // 左上角玩家头像
        const playerAvatarBg = this.add.graphics();
        playerAvatarBg.fillStyle(0xffffff); // 蓝色背景
        playerAvatarBg.lineStyle(3, 0xffffff); // 黑色边框
        playerAvatarBg.fillRoundedRect(15, 15, 80, 80, 40); // 圆角矩形
        playerAvatarBg.strokeRoundedRect(15, 15, 80, 80, 40);

        const playerAvatar = this.add.text(55, 55, '🤴', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const playerLabel = this.add.text(105, 20, '玩家10986', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        });

        // 玩家血条背景
        const playerHealthBarBg = this.add.graphics();
        playerHealthBarBg.fillStyle(0x2c3e50); // 深色背景
        playerHealthBarBg.lineStyle(2, 0x000000); // 黑色边框
        playerHealthBarBg.fillRoundedRect(100, 45, 150, 15, 7); // 圆角矩形
        playerHealthBarBg.strokeRoundedRect(100, 45, 150, 15, 7);

        // 玩家血条
        this.playerHealthBar = this.add.graphics();
        updatePlayerHealthBar.call(this);

        // 玩家血条文字
        this.playerHealthText = this.add.text(175, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 右上角敌方头像和血条
        const enemyAvatarBg = this.add.graphics();
        enemyAvatarBg.fillStyle(0xe74c3c); // 红色背景
        enemyAvatarBg.lineStyle(3, 0x000000); // 黑色边框
        enemyAvatarBg.fillRoundedRect(655, 15, 80, 80, 40); // 圆角矩形
        enemyAvatarBg.strokeRoundedRect(655, 15, 80, 80, 40);

        const enemyAvatar = this.add.text(695, 60, '🧟', {
            fontSize: '80px'
        }).setOrigin(0.5);

        const enemyLabel = this.add.text(605, 30, '敌方', {
            fontSize: '20px',
            fill: '#000000',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 敌方血条背景 - 移到头像右侧同一行
        const healthBarBg = this.add.graphics();
        healthBarBg.fillStyle(0x2c3e50); // 深色背景
        healthBarBg.lineStyle(2, 0x000000); // 黑色边框
        healthBarBg.fillRoundedRect(480, 45, 150, 15, 7); // 圆角矩形，与头像同一行
        healthBarBg.strokeRoundedRect(480, 45, 150, 15, 7);

        // 敌方血条
        this.enemyHealthBar = this.add.graphics();
        updateEnemyHealthBar.call(this);

        // 血条文字
        this.enemyHealthText = this.add.text(555, 52, '', {
            fontSize: '15px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        // 上方中间关卡和波次信息
        const levelBg = this.add.graphics();
        levelBg.fillStyle(0x34495e, 0.8); // 半透明深色背景
        levelBg.lineStyle(2, 0x000000); // 黑色边框
        levelBg.fillRoundedRect(300, 15, 150, 60, 10); // 圆角矩形
        levelBg.strokeRoundedRect(300, 15, 150, 60, 10);

        this.levelText = this.add.text(375, 35, `关卡 ${currentLevel}`, {
            fontSize: '26px',
            fill: '#ffffff',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.waveText = this.add.text(375, 58, `波次 ${currentWave}`, {
            fontSize: '14px',
            fill: '#ecf0f1',
            fontFamily: 'Arial'
        }).setOrigin(0.5);




    }

    // 更新玩家血条
    function updatePlayerHealthBar() {
        if (!this.playerHealthBar) return;

        this.playerHealthBar.clear();

        const healthPercent = Math.max(0, playerHealth) / maxPlayerHealth;
        const barWidth = 146; // 血条宽度（减去边框）
        const currentWidth = barWidth * healthPercent;

        // 血条颜色根据血量变化
        let barColor = 0x27ae60; // 绿色
        if (healthPercent < 0.6) barColor = 0xf39c12; // 橙色
        if (healthPercent < 0.3) barColor = 0xe74c3c; // 红色

        this.playerHealthBar.fillStyle(barColor);
        this.playerHealthBar.fillRoundedRect(102, 47, currentWidth, 11, 5); // 圆角血条

        // 更新血条文字
        if (this.playerHealthText) {
            this.playerHealthText.setText(`${Math.max(0, playerHealth)}/${maxPlayerHealth}`);
        }
    }

    // 更新敌方进度条（波次怪物进度）
    function updateEnemyHealthBar() {
        if (!this.enemyHealthBar) return;

        this.enemyHealthBar.clear();

        // 计算波次进度：已击杀的怪物 / 总怪物数
        const totalMonstersInWave = 3 + currentLevel; // 每波怪物总数
        const remainingMonsters = monsters.length; // 剩余怪物数
        const killedMonsters = totalMonstersInWave - remainingMonsters; // 已击杀怪物数

        const progressPercent = killedMonsters / totalMonstersInWave;
        const barWidth = 146; // 进度条宽度（减去边框）
        const currentWidth = barWidth * progressPercent;

        // 进度条颜色根据进度变化
        let barColor = 0xe74c3c; // 红色（开始）
        if (progressPercent > 0.3) barColor = 0xf39c12; // 橙色
        if (progressPercent > 0.6) barColor = 0x27ae60; // 绿色（接近完成）

        this.enemyHealthBar.fillStyle(barColor);
        this.enemyHealthBar.fillRoundedRect(482, 47, currentWidth, 11, 5); // 圆角进度条

        // 更新进度条文字
        if (this.enemyHealthText) {
            this.enemyHealthText.setText(`${killedMonsters}/${totalMonstersInWave}`);
        }
    }
    // 重新排列怪物位置
    function repositionMonsters() {
        const monsterSpacing = 80;
        const startX = 450;
        const startY = player.y; // 与角色底部对齐

        monsters.forEach((monster, index) => {
            const newX = startX + index * monsterSpacing;

            // 平滑移动到新位置
            this.tweens.add({
                targets: monster,
                x: newX,
                y: startY, // 确保Y位置也对齐
                duration: 500,
                ease: 'Power2',
                onComplete: () => {
                    // 移动完成后更新血条位置
                    updateHealthBarPositions.call(this);
                }
            });
        });
    }





    // 怪物受击效果：闪红并停顿
    function monsterHitEffect(monster) {
        // 停止怪物的移动动画
        if (monster.jumpTween) {
            monster.jumpTween.pause();
        }

        // 暂停怪物移动状态
        const wasMoving = monster.isMoving;
        monster.isMoving = false;

        // 闪红效果
        monster.setTint(0xff0000); // 设置红色

        // 轻微震动效果
        const originalX = monster.x;
        const originalY = monster.y;

        this.tweens.add({
            targets: monster,
            x: originalX + 3,
            duration: 50,
            yoyo: true,
            repeat: 2, // 震动3次
            ease: 'Power2',
            onComplete: () => {
                // 恢复原色
                monster.clearTint();

                // 恢复移动状态
                monster.isMoving = wasMoving;
                if (monster.jumpTween && wasMoving) {
                    monster.jumpTween.resume();
                }
            }
        });
    }

    // 显示怪物伤害飘字
    function showDamageText(monster, damage) {
        // 创建伤害文字
        const damageText = this.add.text(
            monster.x + (Math.random() - 0.5) * 20, // 随机偏移位置
            monster.y - 60,
            `-${Math.floor(damage)}`,
            {
                fontSize: '18px',
                fill: '#ff4444',
                fontFamily: 'Arial',
                fontWeight: 'bold',
                stroke: '#000000',
                strokeThickness: 2
            }
        ).setOrigin(0.5);

        // 设置深度确保显示在最上层
        damageText.setDepth(200);

        // 飘字动画
        this.tweens.add({
            targets: damageText,
            y: damageText.y - 40, // 向上飘
            alpha: 0, // 逐渐透明
            scale: 1.2, // 稍微放大
            duration: 800,
            ease: 'Power2',
            onComplete: () => {
                damageText.destroy();
            }
        });
    }

















    // 创建玩家武器
    function createPlayerWeapon(weaponNumber) {
        // 计算武器位置，围绕主角排列
        const angle = (weaponNumber - 1) * (Math.PI * 2 / 8); // 最多8把武器围成圆圈
        const radius = 40; // 武器距离主角的半径
        const weaponX = player.x + Math.cos(angle) * radius;
        const weaponY = player.y + Math.sin(angle) * radius;

        // 创建武器图像
        const weapon = this.add.image(weaponX, weaponY, 'knife');
        weapon.setScale(0.6);
        weapon.setOrigin(0.5, 1);
        weapon.setDepth(100 + player.y * 0.1 + weaponNumber);

        // 存储武器
        playerWeapons.push(weapon);

        // 武器跟随主角的逻辑会在updatePlayerWeapons中处理
    }

    // 更新所有玩家武器位置
    function updatePlayerWeapons() {
        playerWeapons.forEach((weapon, index) => {
            if (weapon && player) {
                const angle = index * (Math.PI * 2 / Math.max(8, playerWeapons.length));
                const radius = 40;
                weapon.x = player.x + Math.cos(angle) * radius;
                weapon.y = player.y + Math.sin(angle) * radius;
                weapon.setDepth(100 + player.y * 0.1 + index + 1);
            }
        });
    }

    // 检查宝藏碰撞
    function checkTreasureCollision() {
        const hookX = miningGame.hook.x;
        const hookY = miningGame.hook.y;

        for (let i = 0; i < miningGame.treasures.length; i++) {
            const treasure = miningGame.treasures[i];
            const distance = Phaser.Math.Distance.Between(hookX, hookY, treasure.x, treasure.y);

            // 调试信息 - 可以在控制台看到距离
            // console.log(`钩子位置: (${hookX}, ${hookY}), 宝藏位置: (${treasure.x}, ${treasure.y}), 距离: ${distance}`);

            if (distance < treasure.hitRadius + 12) { // 钩子碰撞半径按显示大小
                // 钩住宝藏，开始拖拽回去
                miningGame.caughtTreasure = treasure;

                // 从宝藏列表中移除，避免重复检测
                miningGame.treasures.splice(i, 1);

                // 开始收回钩子，带着宝藏
                miningGame.isHookExtending = false;
                retractHookWithTreasure.call(this, treasure);
                break;
            }
        }
    }

    // 收回钩子（没有抓到东西）
    function retractHook() {
        if (miningGame.hookLength <= miningGame.initialHookLength) {
            miningGame.hookLength = miningGame.initialHookLength;
            miningGame.hook.x = miningGame.hookStartX + Math.sin(miningGame.hookAngle) * miningGame.initialHookLength;
            miningGame.hook.y = miningGame.hookStartY + Math.cos(miningGame.hookAngle) * miningGame.initialHookLength;
            miningGame.isHookMoving = false;
            return;
        }

        // 标记钩子正在移动
        miningGame.isHookMoving = true;

        // 记录钩子被固定的角度和当前位置
        const fixedAngle = miningGame.hookAngle;
        const startLength = miningGame.hookLength;
        const targetLength = miningGame.initialHookLength;

        // 创建自定义的收回动画对象
        const retractAnimation = { progress: 0 };

        // 逐渐收回钩子到初始长度，完全不摇晃
        this.tweens.add({
            targets: retractAnimation,
            progress: 1,
            duration: (startLength - targetLength) * 4, // 空钩子收回速度快一些
            ease: 'Power1.easeOut',
            onUpdate: () => {
                // 根据进度计算当前长度
                const currentLength = startLength - (startLength - targetLength) * retractAnimation.progress;
                miningGame.hookLength = currentLength;

                // 使用完全固定的角度和长度计算位置
                const hookX = miningGame.hookStartX + Math.sin(fixedAngle) * currentLength;
                const hookY = miningGame.hookStartY + Math.cos(fixedAngle) * currentLength;
                miningGame.hook.x = hookX;
                miningGame.hook.y = hookY;

                // 在收回过程中也检测碰撞
                checkTreasureCollision.call(this);
            },
            onComplete: () => {
                // 收回完成，设置最终状态
                miningGame.hookLength = targetLength;
                miningGame.hookAngle = fixedAngle;
                miningGame.hook.x = miningGame.hookStartX + Math.sin(fixedAngle) * targetLength;
                miningGame.hook.y = miningGame.hookStartY + Math.cos(fixedAngle) * targetLength;
                miningGame.isHookMoving = false; // 重置移动状态
            }
        });
    }

    // 收回钩子（带着宝藏）
    function retractHookWithTreasure(treasure) {
        if (miningGame.hookLength <= miningGame.initialHookLength) {
            // 钩子已经回到初始位置，获得金币
            collectTreasure.call(this, treasure);
            return;
        }

        // 停止钩子的其他动画
        miningGame.isHookExtending = false;
        miningGame.isHookMoving = true; // 标记钩子正在移动，防止其他操作

        // 记录钩子被固定的角度和当前状态
        const fixedAngle = miningGame.hookAngle;
        const startLength = miningGame.hookLength;
        const targetLength = miningGame.initialHookLength;

        // 立即将宝藏移动到钩子当前位置
        if (treasure.sprite) {
            treasure.sprite.x = miningGame.hook.x;
            treasure.sprite.y = miningGame.hook.y + 25;
        }

        // 创建自定义的收回动画对象
        const retractAnimation = { progress: 0 };

        // 逐渐收回钩子，钩子和宝藏绑定一起慢慢收回，完全不摇晃
        this.tweens.add({
            targets: retractAnimation,
            progress: 1,
            duration: (startLength - targetLength) * 15, // 更慢的收回速度，有拉重物的感觉
            ease: 'Power1.easeOut',
            onUpdate: () => {
                // 根据进度计算当前长度
                const currentLength = startLength - (startLength - targetLength) * retractAnimation.progress;
                miningGame.hookLength = currentLength;

                // 使用完全固定的角度和长度计算位置
                const hookX = miningGame.hookStartX + Math.sin(fixedAngle) * currentLength;
                const hookY = miningGame.hookStartY + Math.cos(fixedAngle) * currentLength;
                miningGame.hook.x = hookX;
                miningGame.hook.y = hookY;

                // 宝藏紧跟钩子，保持固定距离
                if (treasure.sprite) {
                    treasure.sprite.x = hookX;
                    treasure.sprite.y = hookY + 25; // 宝藏在钩子下方固定距离
                }
            },
            onComplete: () => {
                // 收回完成，设置最终状态
                miningGame.hookLength = targetLength;
                miningGame.hookAngle = fixedAngle;
                const finalHookX = miningGame.hookStartX + Math.sin(fixedAngle) * targetLength;
                const finalHookY = miningGame.hookStartY + Math.cos(fixedAngle) * targetLength;
                miningGame.hook.x = finalHookX;
                miningGame.hook.y = finalHookY;

                // 重置状态
                miningGame.isHookMoving = false;

                // 收回完成，获得金币
                collectTreasure.call(this, treasure);
            }
        });
    }

    // 收集宝藏，获得金币
    function collectTreasure(treasure) {
        // 获得金币
        miningGame.playerGold += treasure.value;

        // 显示获得金币的文字（在钩子当前位置）
        const goldText = this.add.text(miningGame.hook.x, miningGame.hook.y - 30, `+${treasure.value}`, {
            fontSize: '20px',
            fill: '#ffd700',
            fontFamily: 'Arial',
            fontWeight: 'bold'
        }).setOrigin(0.5);

        this.tweens.add({
            targets: goldText,
            y: goldText.y - 50,
            alpha: 0,
            duration: 1000,
            onComplete: () => goldText.destroy()
        });

        // 移除宝藏精灵
        if (treasure.sprite) {
            treasure.sprite.destroy();
        }

        // 清除被抓住的宝藏引用
        miningGame.caughtTreasure = null;
    }

    // 显示挖矿游戏元素
    function showMiningGame() {
        if (miningGame.hook) miningGame.hook.setVisible(true);
        if (miningGame.hookLine) miningGame.hookLine.setVisible(true);

        miningGame.treasures.forEach(treasure => {
            if (treasure.sprite) treasure.sprite.setVisible(true);
        });
    }

    // 隐藏挖矿游戏元素
    function hideMiningGame() {
        if (miningGame.hook) miningGame.hook.setVisible(false);
        if (miningGame.hookLine) miningGame.hookLine.setVisible(false);

        // 确保所有道具都被隐藏
        miningGame.treasures.forEach(treasure => {
            if (treasure.sprite && treasure.sprite.setVisible) {
                treasure.sprite.setVisible(false);
            }
        });

        // 额外检查：如果有被钩住的道具，也要隐藏
        if (miningGame.caughtTreasure && miningGame.caughtTreasure.sprite) {
            miningGame.caughtTreasure.sprite.setVisible(false);
        }
    }

    // 显示怪物
    function showMonsters() {
        monsters.forEach(monster => {
            if (monster) monster.setVisible(true);
        });

        // 显示怪物血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.setVisible(true);
            });
        }
    }

    // 隐藏怪物
    function hideMonsters() {
        monsters.forEach(monster => {
            if (monster) monster.setVisible(false);
        });

        // 隐藏怪物血条
        if (this.monsterHealthBars) {
            this.monsterHealthBars.forEach(bar => {
                if (bar) bar.setVisible(false);
            });
        }
    }

    // 启动游戏
    window.onload = function() {
        game = new Phaser.Game(config);
    };
</script>

</body>
</html>
